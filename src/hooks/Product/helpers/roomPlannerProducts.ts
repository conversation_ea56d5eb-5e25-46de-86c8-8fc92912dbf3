import {CabinetMesh} from 'components/customer/RoomPlanner/types';
import cameraSetup from 'components/customer/RoomPlanner/lib/useCameraSetup';
import {deleteCabinetMesh} from 'components/customer/RoomPlanner/helpers/deleteCabinetMesh';

export const updateUserData = (
    cabinetGroups: CabinetMesh[],
    jobCabinetId: number
) => {
    // attach correct job id to cabinet groups
    if (cabinetGroups && Array.isArray(cabinetGroups)) {
        const totalCabinetsGroups = cabinetGroups.length;
        cabinetGroups.forEach((cabinetGroup, index) => {
            if (totalCabinetsGroups > 1) {
                cabinetGroup.userData.id = `${jobCabinetId}-${index}`;
                cabinetGroup.userData.values = {
                    ...cabinetGroup.userData.values,
                    job_cabinet_id: `${jobCabinetId}-${index}`,
                    id: `${jobCabinetId}-${index}`,
                };
            } else {
                cabinetGroup.userData.id = jobCabinetId;
                cabinetGroup.userData.values = {
                    ...cabinetGroup.userData.values,
                    job_cabinet_id: jobCabinetId,
                    id: jobCabinetId,
                };
            }
        });
    }
};

export const removeMeshFromScene = (cabinetGroups: CabinetMesh[]) => {
    if (cabinetGroups && cameraSetup.scene) {
        if (Array.isArray(cabinetGroups)) {
            cabinetGroups.forEach((cabinetGroup) => {
                deleteCabinetMesh(cabinetGroup);
            });
        }
    }
};
