import {useGetQuickProductsQuery} from 'components/customer/Product/store/productApi';
import {useAppContext} from 'contexts';
import {useCallback, useEffect, useState} from 'react';
import {getProductStyles} from 'service';

export const usePreCacheProducts = () => {
    const {userProfile} = useAppContext();
    const [loading, setLoading] = useState(true);
    const {isLoading, isUninitialized} = useGetQuickProductsQuery();

    const loadData = useCallback(async () => {
        setLoading(true);
        try {
            await getProductStyles();
        } catch (error) {
            // eslint-disable-next-line no-console -- Logging error
            console.error('Error pre-caching products:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        if (userProfile && userProfile.isAddProductAvailable) {
            void loadData();
        }
    }, [userProfile]);

    return {
        loading: loading || isLoading || isUninitialized,
    };
};
