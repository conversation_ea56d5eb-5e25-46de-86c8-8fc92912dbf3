<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64">
  <!-- Generator: Adobe Illustrator 29.3.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 151)  -->
  <defs>
    <style>
      .st0 {
        fill: none;
      }

      .st1 {
        fill: #1392cd;
      }

      .st2 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="st0" y="1061.125" width="29.39" height="18.875"/>
    </clipPath>
  </defs>
  <g class="st2">
    <path class="st1" d="M23.298,1078.2993c-2.421,0-4.391-1.97-4.391-4.391s1.97-4.391,4.391-4.391,4.391,1.97,4.391,4.391-1.97,4.391-4.391,4.391M14.64,1073.9083c-.706,0-1.277-.572-1.277-1.277s.571-1.277,1.277-1.277c.705,0,1.276.572,1.276,1.277s-.571,1.277-1.276,1.277M6.092,1078.2993c-2.421,0-4.391-1.97-4.391-4.391s1.97-4.391,4.391-4.391,4.391,1.97,4.391,4.391-1.97,4.391-4.391,4.391M28.274,1070.4003l.005.002-.065-.087c-.034-.046-.07-.092-.105-.138l-5.507-7.254-.003.002c-.834-1.093-2.15-1.8-3.628-1.8-1.268,0-2.416.52-3.244,1.358-.343-.109-.708-.169-1.087-.169-.348,0-.684.051-1.002.143-.826-.823-1.964-1.332-3.219-1.332-1.478,0-2.794.707-3.629,1.8l-.002-.002-5.507,7.254c-.036.046-.071.092-.105.138l-.066.087.006-.002c-.702.993-1.116,2.202-1.116,3.508,0,3.359,2.733,6.092,6.092,6.092,2.698,0,4.99-1.764,5.789-4.198l.002.006.439-1.328c.625.53,1.433.851,2.318.851.925,0,1.768-.35,2.404-.926l.463,1.403.002-.006c.799,2.434,3.091,4.198,5.789,4.198,3.359,0,6.092-2.733,6.092-6.092,0-1.306-.414-2.515-1.116-3.508"/>
  </g>
  <path class="st1" d="M50.03905,49.42373c-5.07643,0-9.20719-4.13076-9.20719-9.20719s4.13076-9.20719,9.20719-9.20719,9.20719,4.13076,9.20719,9.20719-4.13076,9.20719-9.20719,9.20719M31.88468,40.21654c-1.48036,0-2.67766-1.19939-2.67766-2.67765s1.19729-2.67765,2.67766-2.67765c1.47827,0,2.67556,1.19939,2.67556,2.67765s-1.19729,2.67765-2.67556,2.67765M13.96095,49.42373c-5.07643,0-9.20719-4.13076-9.20719-9.20719s4.13076-9.20719,9.20719-9.20719,9.20719,4.13076,9.20719,9.20719-4.13076,9.20719-9.20719,9.20719M60.47289,32.86085l.01048.0042-.1363-.18242c-.07129-.09646-.14678-.19291-.22017-.28936l-11.54726-15.21042-.00629.0042c-1.74876-2.29184-4.50819-3.7743-7.60731-3.7743-2.65878,0-5.06594,1.09035-6.80212,2.8475-.71921-.22856-1.48456-.35437-2.27926-.35437-.7297,0-1.43423.10694-2.10103.29985-1.73198-1.72569-4.11818-2.79298-6.7497-2.79298-3.09912,0-5.85855,1.48246-7.6094,3.7743l-.0042-.0042-11.54726,15.21042c-.07548.09646-.14887.19291-.22017.28936l-.13839.18242.01258-.0042c-1.47198,2.08215-2.34006,4.61722-2.34006,7.35569,0,7.04326,5.73064,12.7739,12.7739,12.7739,5.65725,0,10.46319-3.69881,12.13856-8.8025l.00419.01258.92051-2.78459c1.31052,1.11132,3.00476,1.7844,4.86046,1.7844,1.93957,0,3.7072-.73389,5.04079-1.94167l.97083,2.94186.00419-.01258c1.67537,5.10369,6.48131,8.8025,12.13857,8.8025,7.04326,0,12.7739-5.73064,12.7739-12.7739,0-2.73846-.86809-5.27353-2.34006-7.35569"/>
</svg>
