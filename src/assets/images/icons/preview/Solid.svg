<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64">
  <defs>
    <style>
      .cls-1 {
        fill: #204380;
      }

      .cls-1, .cls-2 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#radial-gradient);
      }
    </style>
    <radialGradient id="radial-gradient" cx="27.4" cy="23.9" fx="27.4" fy="23.9" r="16.7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0" stop-color="rgba(255, 255, 255, 1)" stop-opacity="1"/>
      <stop offset=".1" stop-color="rgba(255, 255, 255, .8)" stop-opacity=".8"/>
      <stop offset=".2" stop-color="rgba(255, 255, 255, .6)" stop-opacity=".6"/>
      <stop offset=".4" stop-color="rgba(255, 255, 255, .4)" stop-opacity=".4"/>
      <stop offset=".5" stop-color="rgba(255, 255, 255, .2)" stop-opacity=".2"/>
      <stop offset=".6" stop-color="rgba(255, 255, 255, .1)" stop-opacity=".1"/>
      <stop offset=".7" stop-color="rgba(255, 255, 255, 0)" stop-opacity="0"/>
      <stop offset=".9" stop-color="rgba(255, 255, 255, 0)" stop-opacity="0"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <g>
    <path class="cls-1" d="M32.1,57.3c-14,0-25.3-11.4-25.3-25.3S18.1,6.7,32.1,6.7s25.3,11.4,25.3,25.3-11.4,25.3-25.3,25.3Z"/>
    <path class="cls-1" d="M32.1,8c13.2,0,24,10.8,24,24s-10.8,24-24,24-24-10.8-24-24,10.8-24,24-24M32.1,5.3c-14.8,0-26.7,12-26.7,26.7s12,26.7,26.7,26.7,26.7-12,26.7-26.7S46.8,5.3,32.1,5.3h0Z"/>
  </g>
  <circle class="cls-2" cx="27.4" cy="23.9" r="16.7"/>
</svg>
