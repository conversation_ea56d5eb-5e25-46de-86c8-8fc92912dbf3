<!-- By <PERSON> (@sherb), for everyone. More @ http://goo.gl/7AJzbL -->
<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient x1="8.042%" y1="0%" x2="65.682%" y2="23.865%" id="a">
            <stop stop-color="#fff" stop-opacity="0" offset="0%"/>
            <stop stop-color="#fff" stop-opacity=".631" offset="63.146%"/>
            <stop stop-color="#fff" offset="100%"/>
        </linearGradient>
    </defs>
    <circle cx="40" cy="40" r="40" fill="#1392CD"/>
    <g transform="translate(13 13)" stroke="#FFF" stroke-width="1.5">
        <circle cx="42.601" cy="11.462" r="5" fill-opacity="1" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="1;0;0;0;0;0;0;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="49.063" cy="27.063" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;1;0;0;0;0;0;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="42.601" cy="42.663" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;0;1;0;0;0;0;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="27" cy="49.125" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;0;0;1;0;0;0;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="11.399" cy="42.663" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;0;0;0;1;0;0;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="4.938" cy="27.063" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;0;0;0;0;1;0;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="11.399" cy="11.462" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;0;0;0;0;0;1;0" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
        <circle cx="27" cy="5" r="5" fill-opacity="0" fill="#fff">
            <animate attributeName="fill-opacity"
                     begin="0s" dur="1.3s"
                     values="0;0;0;0;0;0;0;1" calcMode="linear"
                     repeatCount="indefinite" />
        </circle>
    </g>
</svg>
