import Upload from './images/upload.svg';
import Uploads from './images/Uploads.png';
import SearchWhite from './images/search.png';
import ProductSizeDefaultsPreview from './images/Product-Size-Defaults-Preview.png';
import DoubleBase from './images/DoubleBase.png';
import DoubleUpper from './images/DoubleUpper.png';
import Drawer from './images/Drawer.png';
import SingleBase from './images/SingleBase.png';
import ErrorGIF from './images/ErrorGIF.gif';
import FreightToClient from './images/FreightToClient.svg';
import FreightToMyAddress from './images/FreightToMyAddress.svg';
import FreightToOtherAddress from './images/FreightToOtherAddress.svg';
import PickUp from './images/PickUp.svg';
import HingeBlack from './images/HingeBlack.png';
import HingeWhite from './images/HingeWhite.png';
import KDMax from './images/kdmaximport.png';
import ArrowDown from './images/arrow-down.png';
import ArrowUp from './images/arrow-up.png';
import ArrowRight from './images/arrow-right.png';
import ArrowLeft from './images/arrow-left.png';
import Gap from './images/Gap.png';
import LoginBackground from './images/login-background.jpg';
import Logo from './images/logo.svg';
import SecurePassword from './images/icons/Secure_Password.svg';
import EyeVisible from './images/icons/Eye_Visible.svg';
import EyeHidden from './images/icons/Eye_Hiden.svg';
import ButtonPassword from './images/icons/Button-Password.svg';
import ChevronRightWhite from './images/icons/chevron-right-white.svg';
import ChevronRight from './images/icons/chevron-right.svg';
import ChevronLeftWhite from './images/icons/chevron-left-white.svg';
import ChevronLeft from './images/icons/chevron-left.svg';
import Cart from './images/icons/Cart.svg';
import CartWhite from './images/icons/Cart-White.svg';
import ChevronDown from './images/icons/Chevron-down.svg';
import ChevronUp from './images/icons/Chevron-up.svg';
import Edit from './images/icons/Options-Edit.svg';
import CopyClipboard from './images/icons/Copy-clipboard.svg';
import AddButton from './images/icons/Button-Add.svg';
import EditButton from './images/icons/Button-Edit.svg';
import Delete from './images/icons/Options-Delete.svg';
import Spinner from './images/icons/tail-spin.svg';
import ButtonRotateAntiClockWise from './images/icons/Button-Rotate-Anti-Clockwise.svg';
import XeroOption from './images/renditionDownload.png';
import Currency from './images/icons/payment_received_button.svg';
import Beta from './images/BETA.svg';
import NoImageAvailable from './images/NoImageAvailable.jpg';
import NoImageAvailablePotrait from './images/potrait_no_image.jpg';
import ReverseRefresh from './images/icons/Reverse-Refresh.svg';
import Person from './images/icons/Person.svg';

// edge profile icons
import CovedSplashbackSolidTriangle from './images/icons/benchtop_profile_icons/CovedSplashbackSolidTriangle.svg';
import CovedSplashback from './images/icons/benchtop_profile_icons/CovedSplashback.svg';
import DropdownFrontSolidTriangle from './images/icons/benchtop_profile_icons/DropdownFrontSolidTriangle.svg';
import DropdownFront from './images/icons/benchtop_profile_icons/DropdownFront.svg';
import EdgesToBePostFormedSolidCircle from './images/icons/benchtop_profile_icons/EdgestoBePostFormedSolidCircle.svg';
import EdgesToBePostFormed from './images/icons/benchtop_profile_icons/EdgesToBePostFormed.svg';
import EdgesToBeSquareLaminated from './images/icons/benchtop_profile_icons/EdgesToBeSquareLaminated.svg';
import LaminatedEdgeSolidSquare from './images/icons/benchtop_profile_icons/LaminatedEdgeSolidSquare.svg';
import LaminatedEdge from './images/icons/benchtop_profile_icons/LaminatedEdge.svg';
import P from './images/icons/benchtop_profile_icons/P.svg';
import ProfileEdges from './images/icons/benchtop_profile_icons/ProfileEdges.svg';
import RawEdge from './images/icons/benchtop_profile_icons/RawEdge.svg';
import SeenEdge from './images/icons/benchtop_profile_icons/SeenEdge.svg';
import SingleLine from './images/icons/benchtop_profile_icons/SingleLine.svg';
import UnseenEdge from './images/icons/benchtop_profile_icons/UnseenEdge.svg';
import NoImage from './images/icons/benchtop_profile_icons/NoImage.svg';
import XeroLogo from './images/icons/Xero.png';
import ChevronDownWhite from './images/icons/ChevronDownWhite.svg';

// room assistant images
import BaseHeight from './images/room_assistant/base_carcase_height.jpg';
import BaseDepth from './images/room_assistant/base_carcase_depth.jpg';
import BaseTopMargin from './images/room_assistant/base_top_margin.jpg';
import BaseBottomMargin from './images/room_assistant/base_bottom_margin.jpg';
import UpperHeight from './images/room_assistant/upper_carcase_height.jpg';
import UpperDepth from './images/room_assistant/upper_carcase_depth.jpg';
import UpperTopMargin from './images/room_assistant/upper_top_margin.jpg';
import UpperBottomMargin from './images/room_assistant/upper_bottom_margin.jpg';
import TallHeight from './images/room_assistant/tall_carcase_height.jpg';
import TallDepth from './images/room_assistant/tall_carcase_depth.jpg';
import CabinetTop from './images/room_assistant/solid_top.jpg';
import ToeKickHeight from './images/room_assistant/toe_kick_height.jpg';
import BorderWidthTop from './images/room_assistant/border_width_top.jpg';
import BorderWidthBottom from './images/room_assistant/border_width_bottom.jpg';
import BorderWidthLeft from './images/room_assistant/border_width_left.jpg';
import BorderWidthRight from './images/room_assistant/border_width_right.jpg';
import DoorGap from './images/room_assistant/door_gap.jpg';
import DrawerGap from './images/room_assistant/drawer_gap.jpg';
import LeftGap from './images/room_assistant/left_gap.jpg';
import RightGap from './images/room_assistant/right_gap.jpg';
import RailHeight from './images/room_assistant/rail_height.jpg';
import RailWidth from './images/room_assistant/rail_width.jpg';
import ShelfSetBack from './images/room_assistant/shelf_setback.jpg';
import RoomAssistantImage1 from './images/RoomAssistant1.gif';
import RoomAssistantImage2 from './images/RoomAssistant2.jpg';
import RoomAssistantImage3 from './images/RoomAssistant3.jpg';
import Favourite from './images/Favourite-item.png';
import FavouriteSelected from './images/Favourite-item-selected.png';

import DoorFaces from './images/icons/DoorFaces.svg';
import DrawerFaces from './images/icons/DrawerFaces.svg';

import PerspectiveRight from './images/icons/preview/Perspective-Right.svg';
import DrawerClosed from './images/icons/preview/Drawers Closed.svg';
import DrawerOpen from './images/icons/preview/Drawers Open.svg';
import IsometricLeft from './images/icons/preview/Isometric-Left.svg';
import IsometricRight from './images/icons/preview/Isometric-Right.svg';
import PerspectiveFront from './images/icons/preview/Perspective-Front.svg';
import PerspectiveLeft from './images/icons/preview/Perspective-Left.svg';
import Solid from './images/icons/preview/Solid.svg';
import Wireframe from './images/icons/preview/Wireframe.svg';
import Expand from './images/icons/Layout-Full-Screen.svg';
import Collapse from './images/icons/Collapse.svg';

import LShapedRoom from './images/icons/LShapedRoom.svg';
import RectangularRoom from './images/icons/RectangularRoom.svg';

import DepthChanges from './images/gfp/depth_changes_new_system.png';
import COIntroduction from './images/gfp/Introducingthe3DProductVisualiser-ezgif.com-crop.gif';
import COWelcome from './images/gfp/Welcome.gif';
import COMoreOptions from './images/gfp/MoreOptions.png';
import COCopyJobs from './images/gfp/Copy-Jobs.png';
import RoomPlannerFloorTexture from './images/room_planner_floor_texture.jpg';

export {
    Uploads,
    Upload,
    SearchWhite,
    ProductSizeDefaultsPreview,
    DoubleBase,
    DoubleUpper,
    Drawer,
    SingleBase,
    ErrorGIF,
    FreightToClient,
    FreightToMyAddress,
    FreightToOtherAddress,
    PickUp,
    HingeBlack,
    HingeWhite,
    KDMax,
    ArrowDown,
    ArrowLeft,
    ArrowRight,
    ArrowUp,
    Gap,
    LoginBackground,
    Logo,
    SecurePassword,
    EyeVisible,
    EyeHidden,
    ChevronRightWhite,
    ChevronRight,
    ChevronLeftWhite,
    ChevronLeft,
    ChevronUp,
    ChevronDown,
    Cart,
    CartWhite,
    Edit,
    Delete,
    AddButton,
    Spinner,
    CopyClipboard,
    XeroOption,
    EditButton,
    ButtonRotateAntiClockWise,
    ButtonPassword,
    Beta,
    NoImageAvailable,
    Currency,
    CovedSplashbackSolidTriangle,
    CovedSplashback,
    DropdownFrontSolidTriangle,
    DropdownFront,
    EdgesToBePostFormedSolidCircle,
    EdgesToBePostFormed,
    EdgesToBeSquareLaminated,
    LaminatedEdgeSolidSquare,
    LaminatedEdge,
    P,
    ProfileEdges,
    RawEdge,
    SeenEdge,
    SingleLine,
    UnseenEdge,
    NoImage,
    BaseHeight,
    BaseDepth,
    BaseTopMargin,
    BaseBottomMargin,
    UpperHeight,
    UpperDepth,
    UpperTopMargin,
    UpperBottomMargin,
    TallHeight,
    TallDepth,
    CabinetTop,
    ToeKickHeight,
    BorderWidthTop,
    BorderWidthBottom,
    BorderWidthLeft,
    BorderWidthRight,
    DoorGap,
    DrawerGap,
    LeftGap,
    RightGap,
    RailHeight,
    RailWidth,
    ShelfSetBack,
    RoomAssistantImage1,
    RoomAssistantImage2,
    RoomAssistantImage3,
    NoImageAvailablePotrait,
    XeroLogo,
    ChevronDownWhite,
    Favourite,
    FavouriteSelected,
    DoorFaces,
    DrawerFaces,
    PerspectiveRight,
    DrawerClosed,
    DrawerOpen,
    IsometricLeft,
    IsometricRight,
    PerspectiveFront,
    PerspectiveLeft,
    Solid,
    Wireframe,
    Expand,
    Collapse,
    ReverseRefresh,
    LShapedRoom,
    RectangularRoom,
    DepthChanges,
    COIntroduction,
    COWelcome,
    COMoreOptions,
    COCopyJobs,
    Person,
    RoomPlannerFloorTexture,
};
