/**
 * @flow
 *
 * @export
 * @class GapSize
 */
export default class Product {
    altCodes: string[];
    code: string;
    defaultWidth: number;
    doors: number;
    favourites: number;
    hidden: number;
    id: number;
    image: string;
    maxWidth: number;
    minWidth: number;
    productType: number;
    style: number;
    subStyle: string;
    subStyleId: number;
    text: string;
    changed_name: string;
    changedImage: string;
    quickProducts: number;
    isRecessedRail: number;
    template_3d: [
        {
            attributes: {
                template: string,
                variables: string,
            },
        },
        string
    ];
    cabinet: {
        attributes: {
            code: string,
            door_hang: string,
            style: number,
            original_name: string,
        },
    };
}
