{"stores": [{"name": "data", "options": {"keyPath": "id", "autoIncrement": true}}, {"name": "client"}, {"name": "customer"}, {"name": "countries"}, {"name": "sundry_category", "indexes": [{"name": "parentCategoryId", "keyPath": "parentCategoryId", "options": {"unique": false}}]}, {"name": "sundry_items_brands"}, {"name": "sundry_items", "indexes": [{"name": "brandId", "keyPath": "brandId", "options": {"unique": false}}, {"name": "sundryCategoryId", "keyPath": "sundryCategoryId", "options": {"unique": false}}, {"name": "favourites", "keyPath": "favourites", "options": {"unique": false}}]}, {"name": "sundry_items_search", "options": {"keyPath": "id", "autoIncrement": true}, "indexes": [{"name": "searchCategory", "keyPath": ["category", "search"], "option": {"unique": true}}]}, {"name": "manufacturer_hardware_options", "options": {"keyPath": "id", "autoIncrement": true}}, {"name": "gap_sizes_defaults"}, {"name": "job"}, {"name": "product_sizes_defaults"}, {"name": "drawer_system"}, {"name": "cabinet_top"}, {"name": "products", "indexes": [{"name": "subStyleId", "keyPath": "subStyleId", "options": {"unique": false}}, {"name": "hidden", "keyPath": "hidden", "options": {"unique": false}}, {"name": "text", "keyPath": "text", "options": {"unique": false}}, {"name": "code", "keyPath": "code", "options": {"unique": false}}, {"name": "productType", "keyPath": "productType", "options": {"unique": false}}, {"name": "favourites", "keyPath": "favourites", "options": {"unique": false}}, {"name": "quickProducts", "keyPath": "quickProducts", "options": {"unique": false}}, {"name": "style", "keyPath": "style", "options": {"unique": false}}, {"name": "altCodes", "keyPath": "altCodes", "options": {"unique": false, "multiEntry": true}}]}, {"name": "materials", "options": {"keyPath": "id", "autoIncrement": true}, "indexes": [{"name": "input, storeVariable", "keyPath": ["input", "storeVariable"], "options": {"unique": true}}]}, {"name": "product_styles", "options": {"keyPath": "id", "autoIncrement": true}}, {"name": "quick_product_types", "options": {"keyPath": "id", "autoIncrement": true}, "indexes": [{"name": "type", "keyPath": "type", "options": {"unique": true}}]}, {"name": "fingerpull_styles", "options": {"keyPath": "id", "autoIncrement": false}, "indexes": [{"name": "deleted", "keyPath": "deleted", "options": {"unique": false}}, {"name": "hidden", "keyPath": "hidden", "options": {"unique": false}}]}, {"name": "more_info", "options": {"keyPath": "id", "autoIncrement": false}}, {"name": "appliances", "options": {"keyPath": "order", "autoIncrement": true}, "indexes": [{"name": "id", "keyPath": "id", "options": {"unique": true}}, {"name": "type", "keyPath": "type", "options": {"unique": false}}]}, {"name": "kdmax_product", "options": {"keyPath": "id", "autoIncrement": false}, "indexes": [{"name": "job", "keyPath": "jobId", "options": {"unique": false}}, {"name": "room", "keyPath": "roomId", "options": {"unique": false}}]}, {"name": "form_fields", "options": {"keyPath": "id", "autoIncrement": true}}]}