export const renderBenchtopMaterials = async (element: HTMLElement) => {
    const modules = await import('../BenchtopModule/Material/BenchtopMaterial');

    modules.default(element);
};
export const renderBenchtopOutsourceMaterials = async (
    element: HTMLElement
) => {
    const modules = await import(
        '../BenchtopModule/OutsourceMaterial/BenchtopOutsourceMaterial'
    );

    modules.default(element);
};

export const renderBenchtopEdgeFinish = async (element: HTMLElement) => {
    const modules = await import('../BenchtopModule/EdgeProfiles/EdgeProfiles');

    modules.default(element);
};

export const renderBenchtopEndOptions = async (element: HTMLElement) => {
    const modules = await import('../BenchtopModule/EndOption/EndOption');

    modules.default(element);
};

export const renderBenchtopJoins = async (element: HTMLElement) => {
    const modules = await import('components/admin/BenchtopModule/Join/Join');

    modules.default(element);
};

export const renderHeader = async (element: HTMLElement, role: number) => {
    const modules = await import('./HeaderReact');

    modules.default(element, role);
};

export const renderSurveys = async (element: HTMLElement) => {
    const modules = await import('../Survey');

    modules.default(element);
};
