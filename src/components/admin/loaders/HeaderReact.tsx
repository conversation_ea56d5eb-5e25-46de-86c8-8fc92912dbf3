import React from 'react';
import {Header} from 'components/admin/Header';
import {createRoot, hydrateRoot} from 'react-dom/client';
import {Role} from 'components/admin/Header/entity/Role';

// Use this function to render the header in the browser
// This is only used to test the header as HtmlWebpackPlugin
// does not emit new static html when files are changed.
export const renderHeader = () => {
    const element = document.getElementById('app');
    if (element) {
        const root = createRoot(element);

        root.render(<Header role={Role.ADMIN} />);
    }
};

// this is used by compatibility script in the admin and manu dashboard
export default (element: HTMLElement, role: Role) => {
    hydrateRoot(element, <Header role={role} />);
};
