import React, {useCallback} from 'react';
import {EditButton} from 'components/admin/shared/Table/EditButton';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {useFormikContext} from 'formik';
import {OptionButton} from 'components/admin/shared/Table/OptionButton';
import {useInlineEditorContext} from 'components/admin/shared/Table/InlineEditor';

interface HideOptionProps {
    survey: Survey;
}

export const EditOption = ({survey}: HideOptionProps) => {
    const {values, setValues, submitForm} = useFormikContext<Survey>();
    const {isSaving} = useInlineEditorContext();

    const setEditingSurvey = useCallback(() => {
        void setValues(survey);
    }, [survey, setValues]);

    const cancelEdit = useCallback(() => {
        void setValues({});
    }, [setValues]);

    if (Object.keys(values).length > 0 && values.id == survey.id) {
        return (
            <>
                <OptionButton
                    title="Update Survey"
                    icon="Button-Tick-Secondary.svg"
                    onClick={submitForm}
                    disabled={isSaving}
                />
                <OptionButton
                    title="Cancel Survey"
                    icon="Button-Error-Gray.svg"
                    onClick={cancelEdit}
                    disabled={isSaving}
                    style={{order: 1}}
                />
            </>
        );
    }

    return <EditButton title="Edit Survey" onClick={setEditingSurvey} />;
};
