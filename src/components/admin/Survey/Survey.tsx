import React from 'react';
import {
    HorizontalLine,
    SectionHeader,
} from 'shared/components/StyledComponents';
import styled from 'styled-components';
import {useSurveyData} from 'components/admin/Survey/helper/useSurveyData';
import {Filters} from 'components/admin/Survey/Filters';
import {InlineSurveyEditor} from 'components/admin/Survey/InlineSurveyEditor';
import {Table} from 'components/admin/Survey/Table';

export const Survey = () => {
    const {
        currentPage,
        setCurrentPage,
        pageSize,
        setPageSizeHandler,
        isLoading,
        isFetching,
        isUninitialized,
        surveys,
        filters,
        setFiltersHandler: setFilters,
    } = useSurveyData();

    return (
        <Container>
            <SectionHeader data-cy="header">Surveys</SectionHeader>
            <HorizontalLine />

            <Filters filters={filters} setFilters={setFilters} />

            <InlineSurveyEditor>
                <Table
                    isLoading={isLoading}
                    isFetching={isFetching}
                    isUninitialized={isUninitialized}
                    surveys={surveys}
                    pageSize={pageSize}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    setPageSizeHandler={setPageSizeHandler}
                />
            </InlineSurveyEditor>
        </Container>
    );
};

const Container = styled.div`
    background: white;
    margin: -10px;
    padding: 50px;
`;
