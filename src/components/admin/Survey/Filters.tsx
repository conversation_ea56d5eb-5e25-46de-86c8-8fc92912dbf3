import React, {useEffect, useMemo} from 'react';
import {Filter} from 'components/admin/Survey/entity/Filter';
import {Formik} from 'formik';
import {
    FilterTextField,
    Flex,
    InputGroupVert,
} from 'shared/components/StyledComponents';
import SelectField from 'shared/components/SelectField';

interface FiltersProps {
    filters: Filter;
    setFilters: (values: Filter) => void;
}

export const Filters = ({filters, setFilters}: FiltersProps) => {
    const options = useMemo(() => {
        return [
            {value: '1', label: 'Yes'},
            {value: '0', label: 'No'},
        ];
    }, []);

    return (
        <Formik initialValues={filters} onSubmit={setFilters}>
            {({values, submitForm, handleBlur}) => {
                useEffect(() => {
                    void submitForm();
                }, [values]);

                return (
                    <Flex
                        $justifyContent="flex-end"
                        $margin="15px 0"
                        $gap="15px">
                        <InputGroupVert>
                            <strong>Search survey</strong>
                            <FilterTextField
                                name="name"
                                data-cy="surveyNameSearch"
                                placeholder="Search survey by name or survey Id"
                                $width="285px"
                                onBlur={handleBlur}
                            />
                        </InputGroupVert>
                        <InputGroupVert>
                            <strong>Hidden</strong>
                            <SelectField
                                name="isHidden"
                                id="isHiddenField"
                                placeholder="Select is hidden"
                                options={options}
                                styleType="filterStyle"
                            />
                        </InputGroupVert>
                    </Flex>
                );
            }}
        </Formik>
    );
};
