import React, {useCallback} from 'react';
import {DeleteButton} from 'components/admin/shared/Table/DeleteButton';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {useDialogContext} from 'contexts';
import {useUpdateSurveyMutation} from 'components/admin/Survey/store/surveyApi';

interface DeleteOptionProps {
    survey: Survey;
}

export const DeleteOption = ({survey}: DeleteOptionProps) => {
    const {showDialog} = useDialogContext();

    const [deleteSurvey] = useUpdateSurveyMutation();

    const handleDeleteAction = useCallback(() => {
        showDialog({
            title: 'Delete Survey!',
            className: 'confirm-modal',
            message: 'Are you sure you want to delete this Survey?',
            hideYesButton: true,
            hideNoButton: true,
            buttons: [
                {
                    show: true,
                    controlledHideDialog: false,
                    name: 'No',
                    variant: 'secondary',
                },
                {
                    show: true,
                    controlledHideDialog: false,
                    name: 'Yes',
                    action: async () => {
                        const deleteModel: Partial<Survey> = {
                            id: survey.id,
                            is_deleted: true,
                        };

                        await deleteSurvey(deleteModel);
                    },
                },
            ],
        });
    }, [survey, showDialog]);

    return <DeleteButton title="Delete Survey" onClick={handleDeleteAction} />;
};
