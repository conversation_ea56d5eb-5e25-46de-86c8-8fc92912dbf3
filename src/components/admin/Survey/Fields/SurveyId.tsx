import React from 'react';
import {useField} from 'formik';
import styled from 'styled-components';
import {OverlayTrigger} from 'shared';

export const SurveyId = () => {
    const [field, meta] = useField({name: 'survey'});

    if (typeof field.value == 'undefined') {
        return null;
    }

    if (meta.touched && meta.error) {
        return (
            <OverlayTrigger
                overlay={meta.error}
                placement="top-start"
                className="error-popover">
                <Input {...field} $error={true} autoFocus />
            </OverlayTrigger>
        );
    }

    return <Input {...field} autoFocus />;
};

const Input = styled.input<{$error?: boolean}>`
    width: 100%;
    height: 100%;
    background-color: ${({$error, theme}) =>
        $error ? theme.colors.error.main : 'initial'} !important;
`;
