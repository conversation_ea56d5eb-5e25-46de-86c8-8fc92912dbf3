import React from 'react';
import DataTable from 'react-data-table-component';
import {useSurveyColumns} from 'components/admin/Survey/helper/useSurveyColumns';
import {CustomStyle} from 'components/admin/shared/Table/CustomStyle';
import styled from 'styled-components';
import {Loader} from 'shared/helpers';
import {ApiResponse} from 'store/dashboard/appApi';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {useInlineEditorContext} from 'components/admin/shared/Table/InlineEditor';

interface TableProps {
    currentPage: number;
    setCurrentPage: (pageNumber: number) => void;
    pageSize: number;
    setPageSizeHandler: (pageNumber: number) => void;
    isLoading: boolean;
    isFetching: boolean;
    isUninitialized: boolean;
    surveys: ApiResponse<Survey>;
}

export const Table = ({
    isLoading,
    isFetching,
    isUninitialized,
    surveys,
    pageSize,
    currentPage,
    setCurrentPage,
    setPageSizeHandler,
}: TableProps) => {
    const {columns} = useSurveyColumns();
    const {isSaving} = useInlineEditorContext();

    return (
        <DataTable
            paginationComponentOptions={{noRowsPerPage: true}}
            progressPending={
                isSaving || isLoading || isFetching || isUninitialized
            }
            progressComponent={<CustomLoader loader={true} />}
            columns={columns}
            data={surveys?.items}
            customStyles={CustomStyle}
            pagination
            paginationServer
            paginationTotalRows={surveys?.pagination.total_count}
            paginationPerPage={pageSize}
            paginationDefaultPage={currentPage}
            onChangePage={setCurrentPage}
            onChangeRowsPerPage={setPageSizeHandler}
        />
    );
};

const CustomLoader = styled(Loader)`
    margin: 50px 0;
`;
