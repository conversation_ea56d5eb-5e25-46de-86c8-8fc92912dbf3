import React from 'react';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {DeleteOption} from 'components/admin/Survey/DeleteOption';
import {HideOption} from 'components/admin/Survey/HideOption';
import {EditOption} from 'components/admin/Survey/EditOption';
import {OptionsContainer} from 'components/admin/shared/Table/CustomStyle';

export const Options = (row: Survey) => {
    return (
        <OptionsContainer>
            <EditOption survey={row} />
            <HideOption survey={row} />
            <DeleteOption survey={row} />
        </OptionsContainer>
    );
};
