import React, {useCallback, useMemo} from 'react';
import {Options} from 'components/admin/Survey/Options';
import {TableColumn} from 'react-data-table-component';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {DateTime, Format} from '@cabinetsbycomputer/datetime';
import {useFormikContext} from 'formik';
import {SurveyId} from 'components/admin/Survey/Fields/SurveyId';

export const useSurveyColumns = () => {
    const {values} = useFormikContext<Survey>();

    const isEditing = useCallback(
        (survey: Survey) => {
            return Object.keys(values).length > 0 && survey.id == values.id;
        },
        [values]
    );

    const columns = useMemo<TableColumn<Survey>[]>(
        () => [
            {name: 'Name', selector: (row) => row.name},
            {
                name: 'Survey Id',
                cell: (row) => (isEditing(row) ? <SurveyId /> : row.survey),
            },
            {
                name: 'Created Date',
                selector: (row) =>
                    DateTime.parseCustom(row.created_date, Format.DateTime)
                        .format(Format.DayMonthYearAtTimeFormat)
                        .toString(),
            },
            {
                name: 'Updated Date',
                selector: (row) =>
                    DateTime.parseCustom(row.updated_date, Format.DateTime)
                        .format(Format.DayMonthYearAtTimeFormat)
                        .toString(),
            },
            {
                name: 'Options',
                id: 'options',
                maxWidth: '115px',
                minWidth: '115px',
                cell: Options,
            },
        ],
        [values, isEditing]
    );

    return {
        columns,
    };
};
