import {useCallback, useState} from 'react';
import {useFetchSurveysQuery} from 'components/admin/Survey/store/surveyApi';
import {Filter} from 'components/admin/Survey/entity/Filter';

export const useSurveyData = () => {
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(30);
    const [filters, setFilters] = useState<Filter>({
        isHidden: 0,
        name: '',
    });

    const {
        data: surveys,
        isLoading,
        isFetching,
        isUninitialized,
    } = useFetchSurveysQuery({
        current_page: currentPage,
        page_size: pageSize,
        ...filters,
    });

    const setFiltersHandler = useCallback((values: Filter) => {
        setFilters(values);
    }, []);

    const setPageSizeHandler = useCallback((currentPageSize: number) => {
        setPageSize(currentPageSize);
    }, []);

    return {
        currentPage,
        setCurrentPage,
        pageSize,
        setPageSizeHandler,
        isLoading,
        isFetching,
        isUninitialized,
        surveys,
        filters,
        setFiltersHandler,
    };
};
