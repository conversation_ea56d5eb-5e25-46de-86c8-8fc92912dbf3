import React, {useCallback} from 'react';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {useDialogContext} from 'contexts';
import {useUpdateSurveyMutation} from 'components/admin/Survey/store/surveyApi';
import {HideButton} from 'components/admin/shared/Table/HideButton';

interface HideOptionProps {
    survey: Survey;
}

export const HideOption = ({survey}: HideOptionProps) => {
    const {showDialog} = useDialogContext();

    const [deleteSurvey] = useUpdateSurveyMutation();

    const handleHideAction = useCallback(() => {
        showDialog({
            title: `${survey.is_hidden ? 'Unhide' : 'Hide'} Survey!`,
            className: 'confirm-modal',
            message: `Are you sure you want to ${
                survey.is_hidden ? 'unhide' : 'hide'
            } this Survey?`,
            hideYesButton: true,
            hideNoButton: true,
            buttons: [
                {
                    show: true,
                    controlledHideDialog: false,
                    name: 'No',
                    variant: 'secondary',
                },
                {
                    show: true,
                    controlledHideDialog: false,
                    name: 'Yes',
                    action: async () => {
                        const hideModel: Partial<Survey> = {
                            id: survey.id,
                            is_hidden: !survey.is_hidden,
                        };

                        await deleteSurvey(hideModel);
                    },
                },
            ],
        });
    }, [survey, showDialog]);

    return (
        <HideButton
            hidden={survey.is_hidden}
            title={survey.is_hidden ? 'Unhide Survey' : 'Hide Survey'}
            onClick={handleHideAction}
        />
    );
};
