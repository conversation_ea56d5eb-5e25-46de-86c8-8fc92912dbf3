import React, {ReactNode, useCallback, useMemo, useState} from 'react';
import {InlineEditor} from 'components/admin/shared/Table/InlineEditor';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {useUpdateSurveyMutation} from 'components/admin/Survey/store/surveyApi';
import {object, string} from 'yup';

interface InlineSurveyEditorProps {
    children: ReactNode;
}

export const InlineSurveyEditor = ({children}: InlineSurveyEditorProps) => {
    const [updateSurvey, {isLoading}] = useUpdateSurveyMutation();
    const [resetValues, setResetValues] = useState(false);

    const validationSchema = useMemo(() => {
        return object<Survey>({
            survey: string().required(),
        });
    }, []);

    const onSubmitHandler = useCallback(
        async (values: Survey) => {
            setResetValues(false);
            const updatingSurvey: Partial<Survey> = {
                id: values.id,
                survey: values.survey,
            };

            await update<PERSON>urvey(updatingSurvey);
            setResetValues(true);
        },
        [updateSurvey]
    );

    return (
        <InlineEditor
            resetValues={resetValues}
            onSubmitHandler={onSubmitHandler}
            validationSchema={validationSchema}
            isSaving={isLoading}>
            {children}
        </InlineEditor>
    );
};
