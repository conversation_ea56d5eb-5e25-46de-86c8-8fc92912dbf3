import {ApiResponse, Pagination, appApi} from 'store/dashboard/appApi';
import {Survey} from 'components/admin/Survey/entity/Survey';
import {Filter} from 'components/admin/Survey/entity/Filter';

const surveyApi = appApi.injectEndpoints({
    endpoints: (build) => ({
        fetchSurveys: build.query<ApiResponse<Survey>, Pagination & Filter>({
            query: ({
                current_page: currentPage,
                page_size: pageSize,
                isHidden,
                name,
            }) => {
                const filterGroups: string[] = [
                    `((is_hidden:equals:${isHidden}))`,
                ];

                if (name) {
                    filterGroups.push(
                        `((name:contains:${name}) OR (survey:contains:${name}))`
                    );
                }

                return {
                    url: 'admin/surveys',
                    method: 'GET',
                    params: {
                        current_page: currentPage,
                        page_size: pageSize,
                        filter_groups: filterGroups.join(' AND '),
                    },
                };
            },
            providesTags: ['Surveys'],
        }),
        updateSurvey: build.mutation<Survey, Partial<Survey>>({
            query: (survey) => ({
                url: 'admin/surveys',
                method: 'PUT',
                body: survey,
            }),
            invalidatesTags: ['Surveys'],
        }),
    }),
});

export const {useFetchSurveysQuery, useUpdateSurveyMutation} = surveyApi;
