import React, {useMemo} from 'react';
import styled from 'styled-components';

export const Favourite = ({
    hover,
    isBookmarked,
}: {
    hover: boolean;
    isBookmarked: boolean;
}) => {
    const image = useMemo(() => {
        if (hover || isBookmarked) {
            return '/v2/dist-compat/header-images/Favourite-item-selected.png';
        } else {
            return '/v2/dist-compat/header-images/Favourite-item.png';
        }
    }, [isBookmarked, hover]);

    return <Image src={image} alt="Favourite.svg" />;
};

const Image = styled.img`
    cursor: pointer;
    width: 15px;
    height: 18px;
`;
