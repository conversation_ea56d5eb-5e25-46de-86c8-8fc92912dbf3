import React, {useCallback} from 'react';
import {SetState, useHeaderContext} from './index';
import styled from 'styled-components';
import {Menu} from './entity/Menu';

enum Event {
    EDIT = 'EDIT',
    LINK_NEW_TAB = 'LINK_NEW_TAB',
    LINK_NEW_WINDOW = 'LINK_NEW_WINDOW',
    LINK_COPY = 'LINK_COPY',
}

interface ShortcutContextMenuProps {
    top: number;
    left: number;
    width: number;
    display?: boolean;
    menu: Menu;
    setSelected: SetState<boolean>;
}

interface ContainerProps {
    $top?: number;
    $left?: number;
    $width?: number;
    $display?: boolean;
    $active?: boolean;
}

export const ShortcutContextMenu = ({
    top,
    left,
    width,
    display = false,
    menu,
    setSelected,
}: ShortcutContextMenuProps) => {
    const {config, setShortcutMenu} = useHeaderContext();

    const clickHandler = useCallback(
        (event: Event) => () => {
            switch (event) {
                case Event.EDIT:
                    setSelected(true);
                    setShortcutMenu(menu);
                    break;

                case Event.LINK_NEW_TAB:
                    window.open(menu.url, '_blank');
                    break;

                case Event.LINK_NEW_WINDOW:
                    window.open(
                        menu.url,
                        '_blank',
                        'location=yes,height=570,width=520,scrollbars=yes,status=yes'
                    );
                    break;

                case Event.LINK_COPY:
                    const baseUrl = window.location.origin;

                    navigator.clipboard
                        .writeText(baseUrl + menu.url)
                        .then(() => {
                            // console.log('URL copied to clipboard');
                        })
                        .catch(() => {
                            // console.log('Failed to copy URL: ', err);
                        });
                    break;
            }
        },
        [menu]
    );

    if (
        config &&
        'DASHBOARD' in config &&
        'contextMenu' in config.DASHBOARD &&
        Array.isArray(config.DASHBOARD.contextMenu)
    ) {
        return (
            <Container
                $top={top}
                $left={left}
                $width={width}
                $display={display}>
                <MenuItem $active={true}>{menu.name}</MenuItem>
                {config.DASHBOARD.contextMenu.map((menu) => {
                    if (menu.type == 'break') {
                        return <hr key={menu.name} />;
                    }

                    return (
                        <MenuItem
                            key={menu.name}
                            onMouseDown={clickHandler(menu.event as Event)}>
                            {menu.icon ? (
                                <Image
                                    src={`/v2/dist-compat/header-images/${menu.icon}`}
                                    alt={menu.icon}
                                />
                            ) : null}
                            {menu.name}
                        </MenuItem>
                    );
                })}
            </Container>
        );
    }
};

const Image = styled.img`
    width: 20px;
`;

const MenuItem = styled.div<ContainerProps>`
    color: white;
    padding: 5px 25px;
    display: flex;
    align-items: center;
    justify-contents: flex-start;
    gap: 15px;
    font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};
    font-size: 1.1em;
    cursor: pointer;

    &:hover {
        background: ${({theme}) => theme.colors.menuSecondaryColor.main};
    }

    border-radius: 8px;

    ${({$active, theme}) =>
        $active && `background: ${theme.colors.menuSecondaryColor.main};`}
`;

const Container = styled.div<ContainerProps>`
    position: fixed;
    top: ${({$top = 0}) => $top}px;
    left: ${({$left = 0}) => $left}px;
    display: ${({$display}) => ($display ? 'flex' : 'none')};
    width: ${({$width = 150}) => $width}px;
    background: ${({theme}) => theme.colors.menuPrimaryColor.main};
    z-index: 1;
    border-radius: 8px;
    box-shadow: 1px 1px 8px -2px black;
    padding: 5px;
    flex-direction: column;
    gap: 5px;
`;
