/* eslint-disable @typescript-eslint/triple-slash-reference */
// we are disabling this here because we are using global js file
// to define some global variables and functions, but we are also
// compiling this hook through a component using webpack which
// does not respect tsconfig.json file of the project.
//
// we are disabling this as three slashes is not compliant with
// our coding style and lint will fail this.
// eslint-disable-next-line spaced-comment
/// <reference path="./helper/type.d.ts" />
import React, {memo, useEffect, useState} from 'react';
import {mobile} from '../../../shared/Breakpoints';
import styled from 'styled-components';

const Logo = () => {
    const [logo, setLogo] = useState('');

    useEffect(() => {
        if (typeof applicationLogo !== 'undefined' && applicationLogo !== '') {
            setLogo(applicationLogo);
        } else {
            setLogo('/v2/dist-compat/header-images/header_logo.png');
        }
    }, []);

    return (
        <Container>
            <Image src={logo} alt="Cabinetry.Online logo" $show={logo != ''} />
        </Container>
    );
};

export default memo(Logo);

const Container = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;

    ${mobile`
        order: 1;
        justify-content: flex-start;
        align-items: flex-start;
    `};
`;

const Image = styled.img<{$show: boolean}>`
    max-width: 18vw;
    height: 7vh;
    opacity: ${({$show}) => ($show ? 1 : 0)};

    ${mobile`
        max-width: 60vw;
    `};
`;
