import React, {useRef} from 'react';
import styled from 'styled-components';
import Menu from './Menu';
import {useHeaderContext} from './index';
import {Shortcuts} from './Shortcuts';
import {Menu as MenuInterface} from './entity/Menu';
import {mobile, tablet} from '../../../shared/Breakpoints';

export const Navigation = ({showNavigation}: {showNavigation: boolean}) => {
    const {config} = useHeaderContext();

    const navigationRef = useRef<HTMLDivElement>(null);

    if (
        config &&
        'DASHBOARD' in config &&
        'adminAndManuMenu' in config.DASHBOARD &&
        config.DASHBOARD.adminAndManuMenu &&
        Array.isArray(config.DASHBOARD.adminAndManuMenu)
    ) {
        return (
            <NavContainer $show={showNavigation}>
                <Nav ref={navigationRef}>
                    {config.DASHBOARD.adminAndManuMenu.map(
                        (item: MenuInterface) => (
                            <Menu key={item.id} menu={item} />
                        )
                    )}
                    <Shortcuts />
                </Nav>
            </NavContainer>
        );
    }
};

const NavContainer = styled.div<{$show: boolean}>`
    background: ${({theme}) => theme.colors.primary.main};
    ${({$show}) =>
        mobile(`
            display: ${$show ? 'block' : 'none'};
    `)};
`;

const Nav = styled.div`
    display: flex;
    align-items: end;
    background: ${({theme}) => theme.colors.headerColour.main};
    gap: 3px;

    ${tablet`
        flex-wrap: wrap;
    `};

    ${({theme}) =>
        mobile(`
            background: ${theme.colors.primary.main};
            flex-direction: column;
            gap: 0;
    `)};
`;
