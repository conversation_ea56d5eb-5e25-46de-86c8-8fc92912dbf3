import React from 'react';
import {Menu} from './entity/Menu';
import {useMenuIcon} from './helper/useMenuIcon';
import styled, {keyframes} from 'styled-components';

interface MenuIconProps {
    menu: Menu;
    hover: boolean;
    showSubMenu: boolean;
}

export const MenuIcon = ({menu, hover, showSubMenu}: MenuIconProps) => {
    const {image, showAnimation} = useMenuIcon(menu, hover || showSubMenu);

    if (menu.icon) {
        return (
            <Image src={image} alt={menu.icon} $showAnimation={showAnimation} />
        );
    }
};

const vibrate = keyframes`
from {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
}
10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
}
17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
}
33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
}
45% {
    transform: scale(1);
    animation-timing-function: ease-out;
}
`;

const Image = styled.img<{
    $showAnimation?: boolean;
}>`
    width: 25px;
    animation-name: ${vibrate};

    ${({$showAnimation}) => {
        if ($showAnimation) {
            return `
                animation-duration: 0.8s;
                animation-iteration-count: infinite;
            `;
        }
    }}
`;
