import {Shortcut} from '../entity/Shortcut';
import {Menu} from '../entity/Menu';

// NOTE: using fetch here as rendering through webpack
// cannot use tsconfig paths.

export const addShortcut = async (shortcut: Shortcut, id: string) => {
    let method = 'POST';
    let url = '';

    if (!isNaN(parseInt(id))) {
        method = 'PUT';
        url = id;
    }

    const response = await fetch(`/api/favourites/menu/${url}`, {
        method,
        headers: {
            'Content-type': 'application/json',
        },
        body: JSON.stringify(shortcut),
    });

    if (!response.ok) {
        const errorResponse = (await response.json()) as {
            error: string;
        };
        throw new Error(errorResponse.error);
    }
};

const localStorageKey = 'shortcuts';

export const clearStorage = () => {
    localStorage.removeItem(localStorageKey);
};

export const getShortcuts = async () => {
    try {
        const storageData = localStorage.getItem(localStorageKey);
        let data: Shortcut[] = [];

        if (!storageData) {
            const response = await fetch('/api/favourites/menus', {
                method: 'GET',
            });

            if (response) {
                const responseData = (await response.json()) as {
                    items: Shortcut[];
                };

                data = responseData.items;
                localStorage.setItem(
                    localStorageKey,
                    btoa(JSON.stringify(data))
                );
            }
        } else {
            data = JSON.parse(atob(storageData)) as Shortcut[];
        }

        return data.map((item) => {
            return {
                id: String(item.id),
                order: item.order,
                name: item.name,
                url: item.link,
            } as Menu;
        });
    } catch (e) {
        // handle this here
    }
};
