import React, {memo} from 'react';
import styled from 'styled-components';

const PoweredBy = () => {
    return (
        <Container>
            <Text>Powered By</Text>
            <Image
                src="/v2/dist-compat/header-images/header_logo.png"
                alt="PoweredBy.svg"
            />
        </Container>
    );
};

export default memo(PoweredBy);

const Image = styled.img`
    width: 145px;
`;

const Text = styled.div`
    color: ${({theme}) => theme.colors.primary.main};
    font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};
    font-size: 0.85em;
`;

const Container = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
`;
