import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Menu as MenuInterface} from './entity/Menu';
import Menu from './Menu';
import {useHeaderContext} from './index';
import {ShortcutContextMenu} from './ShortcutContextMenu';
import {useContextMenu} from './helper/useContextMenu';
import {produce} from 'immer';

export const Shortcut = ({menu}: {menu: MenuInterface}) => {
    const {setShortcutMenu, savingShortcut} = useHeaderContext();
    const [selected, setSelected] = useState(false);
    const {left, top, width, handleContextMenu, showMenu} = useContextMenu();

    const menuItem = useMemo(() => {
        if (savingShortcut && selected) {
            return produce(menu, (draft) => ({
                ...draft,
                icon: 'tail-spin.svg',
            }));
        }

        return menu;
    }, [menu, selected, savingShortcut]);

    const handleSortcut = useCallback(
        (event: React.MouseEvent) => {
            if (menu.url == '#') {
                event.preventDefault();
                setSelected(true);
                setShortcutMenu(menu);
            }
        },
        [menu, setShortcutMenu]
    );

    useEffect(() => {
        if (!savingShortcut) {
            setSelected(false);
        }
    }, [savingShortcut]);

    return (
        <>
            <Menu
                menu={menuItem}
                onClick={handleSortcut}
                onContextMenu={handleContextMenu}
            />
            <ShortcutContextMenu
                menu={menu}
                width={width}
                left={left}
                top={top}
                display={showMenu}
                setSelected={setSelected}
            />
        </>
    );
};
