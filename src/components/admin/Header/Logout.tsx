/* eslint-disable @typescript-eslint/triple-slash-reference */
// we are disabling this here because we are using global js file
// to define some global variables and functions, but we are also
// compiling this hook through a component using webpack which
// does not respect tsconfig.json file of the project.
//
// we are disabling this as three slashes is not compliant with
// our coding style and lint will fail this.
// eslint-disable-next-line spaced-comment
/// <reference path="./helper/type.d.ts" />
import React, {memo, useEffect, useState} from 'react';
import styled from 'styled-components';
import {Image, IconContainer} from './Search';
import {mobile} from '../../../shared/Breakpoints';

interface LogoutProps {
    logoutHandler?: () => void;
}

const defaultLogoutHandler = () => {
    window.location.href = '/logoutPage.php';
};

const Logout = ({logoutHandler = defaultLogoutHandler}: LogoutProps) => {
    const [name, setName] = useState('');

    useEffect(() => {
        if (typeof manufacturerName !== 'undefined') {
            setName(manufacturerName);
        } else {
            setName('Logout');
        }
    }, []);

    return (
        <Container
            data-cy="logout-button"
            onClick={logoutHandler}
            title="Logout">
            <IconContainerLogout>
                <Image
                    src="/v2/dist-compat/header-images/Logout.svg"
                    alt="Logout.svg"
                />
            </IconContainerLogout>
            <Name>{name}</Name>
        </Container>
    );
};

export default memo(Logout);

const Name = styled.div`
    background: ${({theme}) => theme.colors.secondary.main};
    color: white;
    align-content: center;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    text-align: center;
    padding: 0 15px;
    font-size: 1.4em;
    font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};
    flex: 9;

    ${mobile`
        font-size: 1.2em;
    `};
`;

const Container = styled.div`
    display: flex;
    cursor: pointer;

    ${mobile`
        flex: 9;
    `};

    &:hover > div:last-child {
        background: ${({theme}) => theme.colors.primary.main};
    }
`;

const IconContainerLogout = styled(IconContainer)`
    background: ${({theme}) => theme.colors.primary.main};
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    ${({theme}) =>
        mobile(`
        background: ${theme.colors.secondary.main};
    `)};
`;
