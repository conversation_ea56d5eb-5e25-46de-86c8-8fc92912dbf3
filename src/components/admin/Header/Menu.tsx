import React, {useCallback, useMemo} from 'react';
import styled from 'styled-components';
import {Menu as MenuInterface} from './entity/Menu';
import {useMenu} from './helper/useMenu';
import {Favourite} from './Favourite';
import {mobile, size, tablet} from '../../../shared/Breakpoints';
import {MenuIcon} from './MenuIcon';
import {useHeaderContext} from './index';
import {Role} from './entity/Role';
import {MenuPopover} from './MenuPopover';

interface NavMenuProps {
    menu: MenuInterface;
    isSubmenu?: boolean;
    onClick?: (event: React.MouseEvent) => void;
    onContextMenu?: (event: React.MouseEvent) => void;
    hideParentMenu?: () => void;
}

const Menu = ({
    menu,
    isSubmenu = false,
    onClick,
    onContextMenu,
    hideParentMenu,
}: NavMenuProps) => {
    const {role} = useHeaderContext();
    const {
        hideMenu,
        hideManual,
        identifier,
        isBookmarked,
        hover,
        showSubMenu,
        showMenu,
        onMouseOut,
        onMouseOver,
        showFavouriteIcon,
        handleClick,
    } = useMenu(menu, onClick, isSubmenu, hideParentMenu);

    const menuName = useMemo(() => {
        if (role == Role.MANUFACTURER && menu.hasOwnProperty('alternateName')) {
            return menu.alternateName;
        }

        return menu.name;
    }, [menu, role]);

    if (showMenu) {
        return null;
    }

    const handleLinkClick = useCallback(
        (event: React.MouseEvent<HTMLAnchorElement>) => {
            if (menu.external) {
                event.stopPropagation();
            }
        },
        [menu.external]
    );

    return (
        <Item
            $hideManual={hideManual}
            id={`${identifier}-menu`}
            $showSubMenu={showSubMenu}
            onContextMenu={onContextMenu}
            onMouseEnter={onMouseOver}
            onMouseLeave={onMouseOut}
            onClick={handleClick}>
            <Content $nowrap={!isSubmenu}>
                <MenuIcon menu={menu} hover={hover} showSubMenu={showSubMenu} />
                {menu.url ? (
                    <a
                        href={menu.url}
                        target={menu.external ? '_blank' : '_self'}
                        onClick={handleLinkClick}
                        rel="noreferrer">
                        {menuName}
                        {isSubmenu && showFavouriteIcon ? (
                            <Favourite
                                hover={hover}
                                isBookmarked={isBookmarked}
                            />
                        ) : null}
                    </a>
                ) : (
                    <span>{menuName}</span>
                )}
                {menu.submenu ? (
                    <DownArrowImage
                        src={`/v2/dist-compat/header-images/${
                            isSubmenu
                                ? 'chevron-right-white.svg'
                                : 'ChevronDownWhite.svg'
                        }`}
                        alt="Menu button"
                    />
                ) : null}
            </Content>

            <MenuPopover menu={menu} />

            {menu.submenu ? (
                <Submenu>
                    {menu.submenu.map((menu) => {
                        return (
                            <Menu
                                key={menu.id}
                                menu={menu}
                                isSubmenu={true}
                                hideParentMenu={
                                    isSubmenu ? hideParentMenu : hideMenu
                                }
                            />
                        );
                    })}
                </Submenu>
            ) : null}
        </Item>
    );
};

export default Menu;

const DownArrowImage = styled.img`
    width: 12px;
    margin-right: 5px;
`;

const Submenu = styled.section`
    display: none;
    padding: 0 0 5px;
    position: absolute;
    background: ${({theme}) => theme.colors.primary.main};
    z-index: 2;

    top: 100%;
    left: 0;
    right: 0;

    ${mobile`
        position: initial;
    `};

    > div {
        padding: 0 !important;
        border-radius: 0 !important;

        a,
        span {
            border-radius: 0;
            color: white;
            text-align: left;
            font-size: 1.05em;
            font-weight: normal;
            cursor: pointer;
            font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};

            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        &:hover,
        &:focus {
            background: ${({theme}) => theme.colors.secondary.main};
        }

        section {
            top: 0;
            left: 100%;
            right: initial;
            min-width: 150px;

            background: ${({theme}) => theme.colors.secondary.main};

            > div {
                > div {
                    border-radius: 0 !important;
                    background: ${({theme}) => theme.colors.secondary.main};

                    &:hover {
                        background: ${({theme}) => theme.colors.primary.main};
                    }
                }
            }
        }
    }
`;

const Content = styled.div<{$nowrap?: boolean}>`
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;

    > span,
    > a {
        flex: 1;
        color: white;
        text-decoration: none;
        pointer: cursor;
        font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};
        text-wrap: ${({$nowrap = true}) => ($nowrap ? 'nowrap' : 'initial')};
    }
`;

const Item = styled.div<{$showSubMenu?: boolean; $hideManual?: boolean}>`
    flex: 2;
    position: relative;

    > div {
        font-weight: bold;
        font-size: 1.2em;
        text-align: center;
        background: ${({theme}) => theme.colors.primary.main};
        padding: 5px 10px;
        border-radius: 15px 15px 0 0;

        ${mobile`
            border-radius: 0;
        `};
    }

    &:hover {
        color: white;
        text-decoration: none;

        > div {
            background: ${({theme}) => theme.colors.secondary.main};
        }
        > section {
            @media (min-width: ${size.sm + 1}px) {
                display: ${({$hideManual}) => ($hideManual ? 'none' : 'block')};
            }
            box-shadow: rgba(106, 106, 106, 0.75) 0 5px 7px -2px;
        }
    }

    ${({$showSubMenu, theme}) =>
        tablet(`
            flex: 1;
            > div {
                background: ${$showSubMenu && theme.colors.secondary.main};
            }

            > section {
                display: ${$showSubMenu ? 'block' : 'none'};
            }
    `)};

    ${({$showSubMenu, theme}) =>
        mobile(`
            flex: 1;
            width: 100%;
            position: initial;

            > div {
                background: ${$showSubMenu && theme.colors.secondary.main};
            }

            > section {
                display: ${$showSubMenu ? 'block' : 'none'};
            }
    `)};
`;
