import {useCallback, useEffect, useRef, useState} from 'react';
import {clearStorage, getShortcuts} from '../service/shortcut';
import {Menu} from '../entity/Menu';
import {SetState} from '../index';

export const useFetchShortcuts = (
    shortcutMenu: Menu,
    setSavingShortcut: SetState<boolean>
) => {
    const [shortcuts, setShortcuts] = useState<Menu[]>([]);
    const [fetched, setFetched] = useState(false);
    const invalidateCache = useRef(true);

    const fetchShortcuts = useCallback(async () => {
        if (invalidateCache.current) {
            // do this initially to invalidate cache that might be
            // stored in localStorage from other sessions
            invalidateCache.current = false;
            clearStorage();
        }

        const data = await getShortcuts();
        setShortcuts(data);
        setFetched(true);
        setSavingShortcut(false);
    }, []);

    useEffect(() => {
        if (typeof shortcutMenu === 'undefined') {
            void fetchShortcuts();
        }
    }, [shortcutMenu]);

    return {
        fetched,
        shortcuts,
    };
};
