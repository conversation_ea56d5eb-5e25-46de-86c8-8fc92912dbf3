import {useEffect, useMemo, useState} from 'react';
import {Menu} from '../entity/Menu';
import {useHeaderContext} from '../index';
import {useFetchSVG} from '../../../../shared/helpers/useFetchSVG';

export const useMenuIcon = (menu: Menu, focus: boolean) => {
    const {shortcutMenu} = useHeaderContext();

    // TODO: use Icon component in shared directory when entire
    // application is migrated to react.
    const [primaryColor, setPrimaryColor] = useState<string>();
    const [secondaryColor, setSecondaryColor] = useState<string>();
    const [image, setImage] = useState<string>(
        `/v2/dist-compat/header-images/secondary-icons/${menu.icon}`
    );

    const imageSource = useMemo(() => {
        if (menu.icon) {
            if (focus) {
                return `/v2/dist-compat/header-images/primary-icons/${menu.icon}`;
            } else {
                return `/v2/dist-compat/header-images/secondary-icons/${menu.icon}`;
            }
        }

        return null;
    }, [focus, menu.icon]);

    const {imageData, fetchIcon} = useFetchSVG(primaryColor, secondaryColor);

    const showAnimation = useMemo(() => {
        return typeof shortcutMenu !== 'undefined' && menu && 'submenu' in menu;
    }, [menu, shortcutMenu]);

    useEffect(() => {
        const branding = getComputedStyle(document.documentElement);

        if (branding) {
            setPrimaryColor(
                branding.getPropertyValue('--primary_colour').trim()
            );
            setSecondaryColor(
                branding.getPropertyValue('--secondary_colour').trim()
            );
        }
    }, []);

    useEffect(() => {
        if (menu.icon) {
            if (imageData) {
                setImage(imageData);
            } else {
                setImage(imageSource);
            }
        }
    }, [menu, imageData, imageSource]);

    useEffect(() => {
        if (primaryColor && secondaryColor) {
            void fetchIcon(imageSource, 'URL');
        }
    }, [imageSource, primaryColor, secondaryColor]);

    return {
        image,
        showAnimation,
    };
};
