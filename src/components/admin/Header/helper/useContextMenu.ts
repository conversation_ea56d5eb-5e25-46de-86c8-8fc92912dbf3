import {useCallback, useEffect, useState} from 'react';

export const useContextMenu = () => {
    const width = 250;
    const height = 30;
    const [showMenu, setShowMenu] = useState(false);
    const [top, setTop] = useState(0);
    const [left, setLeft] = useState(0);

    const handleContextMenu = useCallback((event: React.MouseEvent) => {
        event.preventDefault();
        const {innerWidth} = window;
        const {pageX, pageY} = event;

        if (pageX + width > innerWidth) {
            setLeft(pageX - width);
        } else {
            setLeft(pageX);
        }

        setTop(pageY);
        setShowMenu(true);
    }, []);

    useEffect(() => {
        const handleClickOutside = () => {
            setShowMenu(false);
        };

        // Add the event listener when the component mounts
        document.addEventListener('mousedown', handleClickOutside);

        // Remove the event listener when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return {
        showMenu,
        top,
        left,
        width,
        height,
        handleContextMenu,
    };
};
