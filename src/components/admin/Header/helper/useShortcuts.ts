import {useEffect, useState} from 'react';
import {Menu} from '../entity/Menu';
import {keyBy, merge, values} from 'lodash';
import {useHeaderContext} from '..';

export const useShortcuts = () => {
    const {shortcutsFetched, shortcuts} = useHeaderContext();
    const [menu, setMenu] = useState<Menu[]>([
        {
            order: 1,
            id: 'Shortcut-1',
            name: '',
            url: '#',
            icon: 'tail-spin.svg',
        },
        {
            order: 2,
            id: 'Shortcut-2',
            name: '',
            url: '#',
            icon: 'tail-spin.svg',
        },
    ]);

    useEffect(() => {
        if (shortcutsFetched) {
            const staticMenu = [
                {
                    order: 1,
                    id: 'Shortbut-1',
                    name: 'Shortcut',
                    url: '#',
                    icon: 'Shortcuts-1.svg',
                },
                {
                    order: 2,
                    id: 'Shortbut-2',
                    name: 'Shortcut',
                    url: '#',
                    icon: 'Shortcuts-2.svg',
                },
            ];

            if (shortcuts && shortcuts.length > 0) {
                const updatedMenu = values(
                    merge(keyBy(staticMenu, 'order'), keyBy(shortcuts, 'order'))
                );
                setMenu(updatedMenu);
            } else {
                setMenu(staticMenu);
            }
        }
    }, [shortcutsFetched, shortcuts]);

    return {
        menu,
    };
};
