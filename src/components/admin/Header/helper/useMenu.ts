/* eslint-disable @typescript-eslint/triple-slash-reference */
// we are disabling this here because we are using global js file
// to define some global variables and functions, but we are also
// compiling this hook through a component using webpack which
// does not respect tsconfig.json file of the project.
//
// we are disabling this as three slashes is not compliant with
// our coding style and lint will fail this.
// eslint-disable-next-line spaced-comment
/// <reference path="./type.d.ts" />
import {useMemo, useState, useCallback} from 'react';
import {useHeaderContext} from '../index';
import {Menu} from '../entity/Menu';
import {addShortcut, clearStorage} from '../service/shortcut';

export const useMenu = (
    menu: Menu,
    onClick: (event: React.MouseEvent) => void,
    isSubMenu = false,
    hideParentMenu?: () => void
) => {
    const {role, shortcutMenu, setShortcutMenu, shortcuts, setSavingShortcut} =
        useHeaderContext();

    const [hover, setHover] = useState(false);
    const [showSubMenu, setShowSubMenu] = useState(false);
    const [hideManual, setHideManual] = useState(false);

    const showMenu = useMemo(() => {
        return menu.role && !menu.role.includes(role);
    }, [menu, role]);

    const identifier = useMemo(
        () => menu && menu.name && menu.name.toLowerCase().replace(/ /g, '-'),
        [menu]
    );

    const isBookmarked = useMemo(() => {
        return (
            shortcuts && shortcuts.some((shortcut) => shortcut.url === menu.url)
        );
    }, [menu, shortcuts]);

    const showFavouriteIcon = useMemo(() => {
        return (
            typeof shortcutMenu !== 'undefined' &&
            menu &&
            typeof menu.url !== 'undefined'
        );
    }, [menu, shortcutMenu]);

    const hideMenu = useCallback(() => {
        if (!isSubMenu) {
            setHideManual(true);
        }
    }, [isSubMenu]);

    const toggleSubMenu = useCallback(() => {
        setShowSubMenu((show) => !show);
    }, []);

    const onMouseOver = useCallback(() => {
        setHideManual(false);
        setHover(true);
    }, []);

    const onMouseOut = useCallback(() => {
        setHover(false);
    }, []);

    const handleClick = useCallback(
        (event: React.MouseEvent) => {
            event.stopPropagation();

            if (isSubMenu && typeof hideParentMenu === 'function') {
                hideParentMenu();
            }

            if (shortcutMenu && menu.url) {
                // if shortcutMenu context is set, clicking on
                // link is meant to add that menu as shortcut
                event.preventDefault();

                if (
                    shortcuts &&
                    !shortcuts
                        .map((shortcut) => shortcut.url)
                        .includes(menu.url)
                ) {
                    setSavingShortcut(true);
                    addShortcut(
                        {
                            order: shortcutMenu.order,
                            name: String(menu.name),
                            link: menu.url,
                        },
                        shortcutMenu.id
                    )
                        .then(() => {
                            clearStorage();
                        })
                        .catch((e: Error) => {
                            // types are defined in type.d.ts in
                            // gocabinets-ui/src/components/admin/Header/helper/type.d.ts
                            // global js file message.js:16 check goCabinets project
                            addMessage(
                                e.message,
                                // defined in global js file general.js:95 check goCabinets project
                                MESSAGE_ERROR
                            );
                        })
                        .finally(() => {
                            setShortcutMenu(undefined);
                        });
                }
            }

            if (menu.submenu) {
                toggleSubMenu();
            }

            if (typeof onClick === 'function') {
                onClick(event);
            }
        },
        [
            menu,
            shortcuts,
            shortcutMenu,
            toggleSubMenu,
            onClick,
            isSubMenu,
            hideParentMenu,
        ]
    );

    return {
        hideMenu,
        hideManual,
        identifier,
        hover,
        showSubMenu,
        showMenu,
        showFavouriteIcon,
        onMouseOut,
        onMouseOver,
        toggleSubMenu,
        handleClick,
        isBookmarked,
    };
};
