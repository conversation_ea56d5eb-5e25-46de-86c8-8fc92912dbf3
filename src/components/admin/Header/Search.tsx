import React, {memo, useCallback, useEffect, useRef} from 'react';
import {mobile} from '../../../shared/Breakpoints';
import styled from 'styled-components';

const Search = () => {
    const eventAttached = useRef(false);
    const searchButton = useRef<HTMLImageElement>(null);
    const searchHandler = useCallback((event: KeyboardEvent) => {
        if (event.key === '/') {
            if (
                !(
                    event.target instanceof HTMLInputElement ||
                    event.target instanceof HTMLTextAreaElement
                )
            ) {
                event.preventDefault();

                if (searchButton.current) {
                    searchButton.current.click();
                }
            }
        }
    }, []);

    useEffect(() => {
        if (!eventAttached.current) {
            eventAttached.current = true;

            document.addEventListener('keydown', searchHandler);

            return () => {
                document.removeEventListener('keydown', searchHandler);
            };
        }
    }, []);

    return (
        <IconContainer>
            <Image
                ref={searchButton}
                id="quick_search_job"
                src="/v2/dist-compat/header-images/Search.svg"
                alt="Search.svg"
            />
        </IconContainer>
    );
};

export default memo(Search);

export const Image = styled.img`
    width: 2vw;

    ${mobile`
        width: 8vw;
    `};
`;

export const IconContainer = styled.div`
    background: ${({theme}) => theme.colors.secondary.main};
    padding: 3px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &:hover {
        background: ${({theme}) => theme.colors.primary.main};
    }

    ${mobile`
        flex: 1;
    `};
`;
