import React, {useMemo} from 'react';
import styled from 'styled-components';

export const Backdrop = ({
    parentRef,
    showBackdrop,
}: {
    parentRef: React.RefObject<HTMLDivElement>;
    showBackdrop: boolean;
}) => {
    const top = useMemo(() => {
        if (showBackdrop) {
            const parent = parentRef.current;
            const parentRect = parent?.getBoundingClientRect();

            if (parentRect) {
                return parentRect.height;
            }
        }

        return 0;
    }, [showBackdrop, parentRef]);

    return <Overlay style={{top, display: showBackdrop ? 'block' : 'none'}} />;
};

const Overlay = styled.div`
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: 80px;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 1;
`;
