import React, {useCallback} from 'react';
import {useHeaderContext} from './index';
import {Menu} from './entity/Menu';
import styled from 'styled-components';

interface MenuPopoverProps {
    menu: Menu;
}

export const MenuPopover = ({menu}: MenuPopoverProps) => {
    const {shortcutMenu, setShortcutMenu} = useHeaderContext();

    const handleCancel = useCallback((event: React.MouseEvent) => {
        event.stopPropagation();
        setShortcutMenu(undefined);
    }, []);

    if (shortcutMenu && menu.id == shortcutMenu.id) {
        return (
            <Absolute>
                Select a menu item from the toolbar to save as a shortcut
                <CancelButton onClick={handleCancel}>Cancel</CancelButton>
            </Absolute>
        );
    }
};

const CancelButton = styled.button`
    background: ${({theme}) => theme.colors.primary.main};
    border: 0;
    padding: 0px 5px;
    margin: 0 0 0 5px;
    color: white;
    font-weight: normal;
    text-shadow: none;

    &:hover,
    &:focus {
        background: ${({theme}) => theme.colors.secondary.main};
    }
`;

const Absolute = styled.div`
    position: absolute;
    background: #dadedf !important;
    border: 1px solid #c5c9ca !important;
    border-radius: 4px !important;
    color: #868686 !important;
    bottom: 55px;
    z-index: 1;
    right: 10px;
    font-weight: normal !important;
    box-shadow: 0 0 4px -1px #999;

    &:after {
        content: '';
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 10px solid rgb(197, 201, 202);
        bottom: -11px;
        position: absolute;
    }
`;
