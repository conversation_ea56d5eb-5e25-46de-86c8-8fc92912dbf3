/* eslint-disable @typescript-eslint/triple-slash-reference */
// we are disabling this here because we are using global js file
// to define some global variables and functions, but we are also
// compiling this hook through a component using webpack which
// does not respect tsconfig.json file of the project.
//
// we are disabling this as three slashes is not compliant with
// our coding style and lint will fail this.
// eslint-disable-next-line spaced-comment
/// <reference path="./helper/type.d.ts" />
import React, {memo, useContext, useEffect, useRef, useState} from 'react';
import styled, {DefaultTheme, ThemeProvider} from 'styled-components';
import {Navigation} from './Navigation';
import {Role} from './entity/Role';
// passing config from here into inner components as I don't want to import
// with this long path in every component.
import BaseConfig from '../../../config/base-config.json';
import Logo from './Logo';
import Search from './Search';
import Logout from './Logout';
import PoweredBy from './PoweredBy';
import MenuToggle from './MenuToggle';
import {Menu as MenuInterface} from './entity/Menu';
import {mobile, size} from '../../../shared/Breakpoints';
import {useMediaQuery} from 'react-responsive';
import {useFetchShortcuts} from './helper/useFetchShortcuts';
import {Backdrop} from './Backdrop';

// only using subset of theme as I cannot get tsconfig paths to work here.
// use proper theme from /src/theme when possible.
const baseTheme: DefaultTheme = {
    colors: {
        primary: {
            main: 'var(--primary_colour)',
        },
        secondary: {
            main: 'var(--secondary_colour)',
        },
        headerColour: {
            main: 'var(--header_colour)',
        },
        menuPrimaryColor: {
            main: 'var(--menu_primary_colour)',
        },
        menuSecondaryColor: {
            main: 'var(--menu_secondary_colour)',
        },
    },
    typography: {
        fontStyle: {
            fontFamily: 'Poppins',
        },
    },
};

type SetStateAction<S> = S | ((prevState: S) => S);
type Dispatch<A> = (value: A) => void;

// The type of a state setter function for a state of type S
export type SetState<S> = Dispatch<SetStateAction<S>>;

export const HeaderContext = React.createContext<{
    role?: Role;
    config?: typeof BaseConfig;
    shortcutMenu?: MenuInterface;
    setShortcutMenu?: SetState<MenuInterface>;
    shortcutsFetched?: boolean;
    shortcuts?: MenuInterface[];
    savingShortcut?: boolean;
    setSavingShortcut?: SetState<boolean>;
}>({});

export const useHeaderContext = () => useContext(HeaderContext);

// NOTE: This component is rendered using server api renderToString and
// client renderer. So, side effect won't run when generating static html.

// events attached to elements will work only after hydration. and won't
// work on webpack rendered html.
export const Header = ({role}: {role: Role}) => {
    const headerReference = useRef<HTMLDivElement>();

    const [showNavigation, setShowNavigation] = useState(false);
    const [shortcutMenu, setShortcutMenu] = useState<MenuInterface>();
    const [showMenu, setShowMenu] = useState(true);
    const [savingShortcut, setSavingShortcut] = useState(false);
    const [environmentText, setEnvironmentText] = useState<string>('');

    const {fetched, shortcuts} = useFetchShortcuts(
        shortcutMenu,
        setSavingShortcut
    );

    const mobile = useMediaQuery({
        maxWidth: size.sm,
    });

    useEffect(() => {
        setShowMenu(!mobile);
    }, [mobile]);

    useEffect(() => {
        if (environment) {
            setEnvironmentText(environment);
        }
    }, []);

    return (
        <HeaderContext.Provider
            value={{
                role,
                config: BaseConfig,
                shortcutMenu,
                setShortcutMenu,
                shortcutsFetched: fetched,
                shortcuts,
                savingShortcut,
                setSavingShortcut,
            }}>
            <ThemeProvider theme={baseTheme}>
                <HeaderElement ref={headerReference}>
                    <Branding>
                        <Logo />
                        <Link $expand={environmentText == ''} href="/index.php">
                            Dashboard
                        </Link>

                        {environmentText ? (
                            <Environment>
                                <div>{environmentText}</div>
                            </Environment>
                        ) : (
                            ''
                        )}

                        <Menu $display={showMenu}>
                            {role === Role.MANUFACTURER ? <PoweredBy /> : null}
                            <Search />
                            <Logout />
                        </Menu>
                    </Branding>
                    <MenuToggle setShowNavigation={setShowNavigation} />
                    <Navigation showNavigation={showNavigation} />
                    <Backdrop
                        parentRef={headerReference}
                        showBackdrop={typeof shortcutMenu !== 'undefined'}
                    />
                </HeaderElement>
            </ThemeProvider>
        </HeaderContext.Provider>
    );
};

// NOTE: Remove fonts definition here when we move to proper react app later
// and use proper theme from DashboardTheme
const HeaderElement = styled.div`
    background: ${({theme}) => theme.colors.headerColour.main};
    box-shadow: rgba(0, 0, 0, 0.75) 0px 0px 7px 1px;

    @font-face {
        font-family: 'Poppins';
        src: url(/v2/dist-compat/assets/Poppins-Light.ttf);
    }

    @font-face {
        font-family: Poppins;
        src: url(/v2/dist-compat/assets/Poppins-Regular.ttf);
        font-weight: 400;
    }

    @font-face {
        font-family: Poppins;
        src: url(/v2/dist-compat/assets/Poppins-SemiBold.ttf);
        font-weight: 500;
    }

    @font-face {
        font-family: Poppins;
        src: url(/v2/dist-compat/assets/Poppins-Bold.ttf);
        font-weight: bold;
    }

    @font-face {
        font-family: Poppins;
        src: url(/v2/dist-compat/assets/Poppins-ExtraBold.ttf);
        font-weight: 900;
    }
`;

const Branding = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: 1vw 1.5vw;
    gap: 30px;

    ${mobile`
        flex-wrap: wrap;
        gap: 5px;
    `}
`;

const Menu = memo(styled.div<{$display: boolean}>`
    display: ${({$display}) => ($display ? 'flex' : 'none')};
    align-items: center;
    gap: 10px;
    order: 4;

    ${mobile`
        display: none;
    `}
`);

const Link = styled.a<{$expand: boolean}>`
    color: ${({theme}) => theme.colors.secondary.main} !important;
    font-weight: bold;
    text-decoration: none;
    font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};
    font-size: 1.2em;
    order: 2;
    flex: ${({$expand}) => ($expand ? 1 : 'inherit')};

    &:hover {
        color: ${({theme}) => theme.colors.primary.main} !important;
        text-decoration: none;
    }

    ${mobile`
        font-size: 1em;
        text-align: center;
    `};
`;

const Environment = styled.div`
    order: 3;
    flex: 1;

    > div {
        width: fit-content;
        padding: 10px;
        background: white;
        font-size: 2em;
        font-weight: bold;
        border: 8px solid;
        border-image-outset: 0;
        border-image-repeat: stretch;
        border-image-slice: 100%;
        border-image-source: none;
        border-image-width: 1;
        border-image: repeating-linear-gradient(
                45deg,
                #000,
                #000 10px,
                #ff0 10px,
                #ff0 20px
            )
            10;
    }

    ${mobile`
        display: none;
    `}
`;
