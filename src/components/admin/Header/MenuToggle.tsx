import React, {memo, useCallback} from 'react';
import {mobile} from '../../../shared/Breakpoints';
import styled from 'styled-components';
import Search from './Search';
import Logout from './Logout';
import {useMenuIcon} from './helper/useMenuIcon';

const MenuToggle = ({
    setShowNavigation,
}: {
    setShowNavigation: (show: boolean | ((show: boolean) => boolean)) => void;
}) => {
    const {image} = useMenuIcon(
        {
            name: '',
            icon: 'Header-Menu.svg',
        },
        false
    );

    const handleClick = useCallback(() => {
        setShowNavigation((show) => !show);
    }, [setShowNavigation]);

    return (
        <Container>
            <ImageContainer data-cy="menu-toggle" onClick={handleClick}>
                Menu <img src={image} alt="Menu.svg" />
            </ImageContainer>
            <SearchContainer>
                <Search />
                <Logout />
            </SearchContainer>
        </Container>
    );
};

export default memo(MenuToggle);

const ImageContainer = styled.div`
    display: flex;
    flex: 1;
    cursor: pointer;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
`;

const SearchContainer = styled.div`
    display: flex;
    gap: 5px;
`;

const Container = styled.div`
    display: none;

    background: ${({theme}) => theme.colors.primary.main};
    color: white;
    padding: 5px 5px 5px 15px;
    font-family: ${({theme}) => theme.typography.fontStyle.fontFamily};
    justify-content: flex-start;
    align-items: center;
    font-size: 1.2em;
    gap: 15px;

    ${mobile`
        display: flex;
    ;`}
`;
