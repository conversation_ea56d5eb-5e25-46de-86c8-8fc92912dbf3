import {TableStyles} from 'react-data-table-component';
import styled from 'styled-components';

export const CustomStyle: TableStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
            textAlign: 'center',
            color: '#222',
            '>div>div': {
                whiteSpace: 'unset',
            },
            '&:not(:last-child)': {
                borderRight: '1px solid rgba(0, 0, 0, 0.08)',
            },
            padding: '10px',
        },
    },
    pagination: {
        pageButtonsStyle: {
            background: 'transparent',
        },
        style: {
            borderTop: 'none',
        },
    },
    header: {
        style: {
            fontSize: '22px',
            minHeight: '56px',
            paddingLeft: '16px',
            paddingRight: '8px',
            whiteSpace: 'unset',
        },
    },
    cells: {
        style: {
            '&:not(:last-child)': {
                borderRight: '1px solid rgba(0, 0, 0, 0.08)',
            },
            padding: '10px',
        },
    },
    rows: {
        style: {
            '&:not(:last-of-type)': {
                borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
            },
        },
    },
    headRow: {
        style: {
            borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
        },
    },
    responsiveWrapper: {
        style: {
            border: '1px solid rgba(0, 0, 0, 0.08)',
            borderRadius: '10px',
            overflow: 'visible',
        },
    },
};

export const OptionsContainer = styled.div`
    display: flex;
    gap: 5px;
`;
