import React, {CSSProperties, useMemo} from 'react';
import {TableButton} from 'components/admin/shared/Table/entity/TableButton';
import {OverlayTrigger} from 'shared/components/OverlayTrigger';
import Icon from 'shared/Icon';

export const OptionButton = ({
    title,
    onClick,
    icon,
    'data-cy': dataCy,
    placement = 'top',
    disabled = false,
    style,
}: TableButton) => {
    const cssStyle = useMemo<CSSProperties>(() => {
        return {
            width: '20px',
            opacity: disabled ? 0.5 : 1,
            cursor: disabled ? 'not-allowed' : 'pointer',
        };
    }, [disabled]);

    const handleClick = () => {
        void onClick();
    };

    return (
        <OverlayTrigger overlay={title} placement={placement}>
            <div style={style}>
                <Icon
                    iconName={icon}
                    onClick={disabled ? undefined : handleClick}
                    data-cy={dataCy}
                    style={cssStyle}
                />
            </div>
        </OverlayTrigger>
    );
};
