import React, {useMemo} from 'react';
import {TableButton} from 'components/admin/shared/Table/entity/TableButton';
import {OptionButton} from 'components/admin/shared/Table/OptionButton';

interface HideButtonProps {
    hidden?: boolean;
}

export const HideButton = ({
    hidden = false,
    ...props
}: TableButton & HideButtonProps) => {
    const icon = useMemo(() => {
        if (hidden) {
            return 'Button-Suspend.svg';
        } else {
            return 'Button-Unsuspend.svg';
        }
    }, [hidden]);

    return <OptionButton {...props} icon={icon} />;
};
