import {Formik} from 'formik';
import React, {
    ReactNode,
    createContext,
    useContext,
    useEffect,
    useMemo,
} from 'react';
import {ObjectSchema} from 'yup';

interface InlineEditorProps<T> {
    children: ReactNode;
    resetValues: boolean;
    onSubmitHandler: (value: T) => Promise<void> | void;
    validationSchema: ObjectSchema<T>;
}

interface InlineEditorContextType {
    isSaving?: boolean;
}

const InlineEditorContext = createContext<InlineEditorContextType>({});

export const useInlineEditorContext = () => useContext(InlineEditorContext);

export const InlineEditor = <T,>({
    children,
    onSubmitHandler,
    validationSchema,
    resetValues,
    isSaving,
}: InlineEditorProps<T> & InlineEditorContextType) => {
    const initialValues = useMemo<T | object>(() => ({}), []);

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmitHand<PERSON>}
            enableReinitialize={true}
            validationSchema={validationSchema}>
            {({setValues}) => {
                useEffect(() => {
                    if (resetValues) {
                        void setValues({});
                    }
                }, [resetValues]);

                return (
                    <InlineEditorContext.Provider value={{isSaving}}>
                        {children}
                    </InlineEditorContext.Provider>
                );
            }}
        </Formik>
    );
};
