import React, {useEffect, useState} from 'react';
import {Button, Image, Row} from 'react-bootstrap';
import {AddButton} from 'assets';
import DataTable from 'react-data-table-component';
import ImageComponent from 'components/manufacturer/shared/images/Image';
import {
    MaterialGenericInterface,
    useAddTypeMutation,
    useGetTypesQuery,
    useEditTypeMutation,
} from './store/benchtopModuleApi';
import {Columns} from './shared/Columns';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {AddNewButton} from './Material';
import {Form, Formik} from 'formik';
import TextField, {
    FieldType,
    FormLabel,
} from 'components/manufacturer/shared/TextField';

const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
        },
    },
};

export interface MaterialTypeGenericInterface extends MaterialGenericInterface {
    is_join_allowed: string[];
}
const Type = ({setShowPrompt}) => {
    const [showTypeForm, setShowTypeForm] = useState<boolean>(false);
    const [deleteNotice, setDeleteNotice] = useState<string>('');
    const [type, setType] = useState<MaterialTypeGenericInterface>({
        name: '',
        is_join_allowed: [],
        is_deleted: false,
    });
    const cols = Columns(
        [
            {
                name: 'Image',
                cell: (row) =>
                    row.image ? (
                        <Image
                            style={{height: '50px'}}
                            src={row.image[0].imageUrl}
                            thumbnail
                        />
                    ) : (
                        <></>
                    ),
                center: true,
                style: {
                    height: '50px',
                },
            },
            {
                name: 'Name',
                selector: (row) => row.name,
                center: true,
            },
            {
                name: 'Allow Join',
                selector: (row) => (row.is_join_allowed.length ? 'Yes' : 'No'),
                center: true,
            },
        ],
        (row: MaterialTypeGenericInterface) => editButton(row),
        window?.isSuperAdmin
            ? (row: MaterialTypeGenericInterface) => {
                  setDeleteNotice(
                      `Are you sure you want to perform this action on the selected item? This will also delete ${
                          row.material_count
                      } material${
                          parseInt(row.material_count) > 1 ? `s` : ``
                      } linked to this type.`
                  );
                  setType(row);
              }
            : null
    );

    const dispatch = useAppDispatch();

    const {
        data: types = [],
        isLoading: loadAll,
        isFetching: fetchAll,
    } = useGetTypesQuery({withAllOption: false});

    const [
        addType,
        {
            isLoading: loadAdd,
            error: addError,
            isLoading: addHappening,
            reset: addReset,
        },
    ] = useAddTypeMutation();
    const [
        editType,
        {
            isLoading: loadEdit,
            error: editError,
            isLoading: editHappening,
            reset: editReset,
        },
    ] = useEditTypeMutation();

    const initialValues: MaterialTypeGenericInterface = type;

    const saveButton = async (values: MaterialTypeGenericInterface) => {
        const data = {
            ...type,
            ...{
                name: values.name,
                is_join_allowed: values.is_join_allowed?.length,
                image_id: values?.image ? values?.image[0]?.id : null,
            },
        };

        if (type?.id) {
            try {
                await editType(data).unwrap();
                dispatch(
                    addNotification(
                        `Type edited Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopType'
                    )
                );
            } catch (e) {
                throw e;
            }
        } else {
            try {
                await addType(data).unwrap();
                dispatch(
                    addNotification(
                        `Type added Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopType'
                    )
                );
            } catch (e) {
                throw e;
            }
        }

        setShowTypeForm(!showTypeForm);
    };

    // Edit button click handler
    const editButton = (row: MaterialTypeGenericInterface) => {
        setType(row);
        setShowTypeForm(true);
    };

    // Delete click handler
    const deleteButton = async () => {
        const data = {...type, ...{is_deleted: true}};
        try {
            setDeleteNotice('');
            await editType(data).unwrap();
            dispatch(
                addNotification(
                    `Type deleted Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopType'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addError)) {
            dispatch(
                addNotification(
                    addError.data.error,
                    MessageVariant.ERROR,
                    'benchtopType'
                )
            );
            addReset();
        }
        if (!isEmpty(editError)) {
            dispatch(
                addNotification(
                    editError.data.error,
                    MessageVariant.ERROR,
                    'benchtopType'
                )
            );
            editReset();
        }
    }, [addError, editError]);

    if (!isEmpty(deleteNotice)) {
        return (
            <>
                <h2>{deleteNotice}</h2>
                <AddNewButton
                    onClick={deleteButton}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Yes
                </AddNewButton>
                <AddNewButton
                    onClick={() => setDeleteNotice('')}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    No
                </AddNewButton>
            </>
        );
    }

    return (
        <>
            <Notification filterType="benchtopType" />
            <Button
                onClick={() => {
                    setShowPrompt(false);
                    // reset states
                    setType({
                        name: '',
                        is_deleted: false,
                    });
                    setShowTypeForm(false);
                    // remove all notifications of this type
                    dispatch(deleteAllNotificationsOfType('benchtopType'));
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {showTypeForm ? (
                <>
                    <h2>{type?.id ? 'Edit' : 'New'} Type</h2>
                    <Formik
                        initialValues={initialValues}
                        onSubmit={async (
                            values: MaterialTypeGenericInterface,
                            actions
                        ) => {
                            try {
                                await saveButton(values);
                                setShowTypeForm(!showTypeForm);
                                actions.setSubmitting(false);
                                actions.resetForm();
                            } catch (e) {
                                throw e;
                            }
                        }}>
                        {(props: FormikProps<any>) => (
                            <Form>
                                <Row className="row">
                                    <TextField
                                        name="name"
                                        placeholder={`Enter Type`}
                                        classes="column small-12"
                                    />
                                </Row>

                                <Row className="row">
                                    <div
                                        className="column"
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                        }}>
                                        <FormLabel
                                            style={{fontWeight: 'bold'}}
                                            label="Image"
                                        />
                                        <div className="form_image">
                                            <ImageComponent
                                                name="image"
                                                imageButtonText={`${
                                                    props.values.image
                                                        ? `Update`
                                                        : `Add`
                                                } Image`}
                                                showThumbnail={false}
                                                allowMultipleImages={
                                                    false
                                                }></ImageComponent>
                                        </div>
                                    </div>
                                </Row>
                                <Row className="row">
                                    <div
                                        className="column"
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                        }}>
                                        <FormLabel
                                            style={{
                                                fontWeight: 'bold',
                                                marginRight: '25px',
                                            }}
                                            label="Allow join"
                                            classes="column small-3"
                                        />
                                        <TextField
                                            name="is_join_allowed"
                                            type={FieldType.CHECKBOX}
                                            classes="column small-3"
                                            value="1"
                                        />
                                    </div>
                                </Row>
                                <Row>
                                    <AddNewButton
                                        disabled={props.isSubmitting}
                                        style={{
                                            padding: '0 21px',
                                            margin: '6px 3px',
                                        }}
                                        type="submit">
                                        Save
                                    </AddNewButton>
                                    <AddNewButton
                                        disabled={props.isSubmitting}
                                        style={{
                                            padding: '0 21px',
                                            margin: '6px 3px',
                                        }}
                                        type="button"
                                        onClick={() => {
                                            setShowTypeForm(false);
                                        }}>
                                        Cancel
                                    </AddNewButton>
                                </Row>
                            </Form>
                        )}
                    </Formik>
                </>
            ) : (
                <>
                    <AddNewButton
                        onClick={() => {
                            setType({
                                name: '',
                                is_deleted: false,
                            });
                            setShowTypeForm(true);
                        }}>
                        <Image
                            className="icon_button"
                            style={{
                                backgroundColor: 'transparent',
                            }}
                            src={`data:image/svg+xml;base64, ${btoa(
                                AddButton
                            )}`}
                        />
                        Add New Type
                    </AddNewButton>
                    <DataTable
                        columns={cols}
                        data={types}
                        striped={true}
                        highlightOnHover={true}
                        dense={true}
                        customStyles={customStyles}
                        progressPending={
                            loadAll || fetchAll || loadAdd || loadEdit
                        }
                    />
                </>
            )}
        </>
    );
};

export default Type;
