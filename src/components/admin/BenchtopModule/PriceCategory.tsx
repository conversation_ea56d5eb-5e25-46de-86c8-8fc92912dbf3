import React, {useEffect, useState} from 'react';
import {Button, Image} from 'react-bootstrap';
import {AddButton, Spinner} from 'assets';
import DataTable from 'react-data-table-component';
import styled from 'styled-components';
import {
    MaterialGenericInterface,
    useAddPriceCategoryMutation,
    useGetPriceCategoriesQuery,
    useEditPriceCategoryMutation,
} from './store/benchtopModuleApi';
import {Columns} from './shared/Columns';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {AddNewButton} from './Material';
const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
        },
    },
};

const StyledField = styled.input`
    padding: 10px;
    width: 100%;
    border-radius: 5px;
    border: 2px solid;
    border-color: #000 !important;
    box-shadow: none !important;
    font-weight: normal !important;
`;

const PriceCategory = ({setShowPrompt}) => {
    const [showPriceCategoryForm, setShowPriceCategoryForm] =
        useState<boolean>(false);
    const [deleteNotice, setDeleteNotice] = useState<string>('');
    const [priceCategory, setPriceCategory] =
        useState<MaterialGenericInterface>({
            name: '',
            is_deleted: false,
        });
    const cols = Columns(
        [
            {
                name: 'Name',
                selector: (row) => row.name,
                center: true,
            },
        ],
        (row: MaterialGenericInterface) => editButton(row),
        window?.isSuperAdmin
            ? (row: MaterialGenericInterface) => {
                  setDeleteNotice(`Are you sure you want to perform this action on the selected
                  item? This will also remove current price category from ${
                      row.material_count
                  } material${parseInt(row.material_count) > 1 ? `s` : ``}.`);
                  setPriceCategory(row);
              }
            : null
    );
    const [priceCategoryName, setPriceCategoryName] = useState<string>('');

    const dispatch = useAppDispatch();

    const {
        data: priceCategories = [],
        isLoading: loadAll,
        isFetching: fetchAll,
    } = useGetPriceCategoriesQuery({withAllOption: false});

    const [
        addPriceCategory,
        {
            isLoading: loadAdd,
            error: addError,
            isLoading: addHappening,
            reset: addReset,
        },
    ] = useAddPriceCategoryMutation();
    const [
        editPriceCategory,
        {
            isLoading: loadEdit,
            error: editError,
            isLoading: editHappening,
            reset: editReset,
        },
    ] = useEditPriceCategoryMutation();

    const saveButton = async () => {
        const data = {...priceCategory, ...{name: priceCategoryName}};

        if (priceCategory?.id) {
            try {
                await editPriceCategory(data).unwrap();
                dispatch(
                    addNotification(
                        `PriceCategory edited Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopPriceCategory'
                    )
                );
            } catch (e) {
                throw e;
            }
        } else {
            try {
                await addPriceCategory(data).unwrap();
                dispatch(
                    addNotification(
                        `PriceCategory added Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopPriceCategory'
                    )
                );
            } catch (e) {
                throw e;
            }
        }

        setPriceCategoryName('');
        setShowPriceCategoryForm(!showPriceCategoryForm);
    };

    // Edit button click handler
    const editButton = (row: MaterialGenericInterface) => {
        setPriceCategory(row);
        setPriceCategoryName(row?.name);
        setShowPriceCategoryForm(true);
    };

    // Delete click handler
    const deleteButton = async () => {
        const data = {...priceCategory, ...{is_deleted: true}};
        try {
            setDeleteNotice('');
            await editPriceCategory(data).unwrap();
            dispatch(
                addNotification(
                    `PriceCategory deleted Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopPriceCategory'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addError)) {
            dispatch(
                addNotification(
                    addError.data.error,
                    MessageVariant.ERROR,
                    'benchtopPriceCategory'
                )
            );
            addReset();
        }
        if (!isEmpty(editError)) {
            dispatch(
                addNotification(
                    editError.data.error,
                    MessageVariant.ERROR,
                    'benchtopPriceCategory'
                )
            );
            editReset();
        }
    }, [addError, editError]);

    if (!isEmpty(deleteNotice)) {
        return (
            <>
                <h2>{deleteNotice}</h2>
                <AddNewButton
                    onClick={deleteButton}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Yes
                </AddNewButton>
                <AddNewButton
                    onClick={() => setDeleteNotice('')}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    No
                </AddNewButton>
            </>
        );
    }

    return (
        <>
            <Notification filterType="benchtopPriceCategory" />
            <Button
                onClick={() => {
                    setShowPrompt(false);
                    // reset states
                    setPriceCategory({
                        name: '',
                        is_deleted: false,
                    });
                    setShowPriceCategoryForm(false);
                    // remove all notifications of this type
                    dispatch(
                        deleteAllNotificationsOfType('benchtopPriceCategory')
                    );
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {showPriceCategoryForm ? (
                <>
                    <h2>{priceCategory?.id ? 'Edit' : 'New'} Price Category</h2>
                    <div>
                        <StyledField
                            type="text"
                            placeholder={`Enter Price Category`}
                            onChange={(e) =>
                                setPriceCategoryName(e.target.value)
                            }
                            value={priceCategoryName}
                        />
                        {addHappening || editHappening ? (
                            <Image
                                className="icon_button"
                                style={{
                                    backgroundColor: 'transparent',
                                }}
                                src={`data:image/svg+xml;base64, ${btoa(
                                    Spinner
                                )}`}
                            />
                        ) : (
                            <AddNewButton
                                disabled={addHappening || editHappening}
                                style={{
                                    padding: '0 21px',
                                    margin: '6px 3px',
                                }}
                                onClick={saveButton}>
                                Save
                            </AddNewButton>
                        )}

                        <AddNewButton
                            style={{
                                padding: '0 21px',
                                margin: '6px 3px',
                            }}
                            onClick={() => {
                                setPriceCategoryName('');
                                setShowPriceCategoryForm(false);
                            }}>
                            Cancel
                        </AddNewButton>
                    </div>
                </>
            ) : (
                <>
                    <AddNewButton
                        onClick={() => {
                            setPriceCategory({
                                name: '',
                                is_deleted: false,
                            });
                            setShowPriceCategoryForm(true);
                        }}>
                        <Image
                            className="icon_button"
                            style={{
                                backgroundColor: 'transparent',
                            }}
                            src={`data:image/svg+xml;base64, ${btoa(
                                AddButton
                            )}`}
                        />
                        Add New Price Category
                    </AddNewButton>
                    <DataTable
                        columns={cols}
                        data={priceCategories}
                        striped={true}
                        highlightOnHover={true}
                        dense={true}
                        customStyles={customStyles}
                        progressPending={
                            loadAll || fetchAll || loadAdd || loadEdit
                        }
                    />
                </>
            )}
        </>
    );
};

export default PriceCategory;
