import React, {useEffect, useMemo, useState} from 'react';
import Notification from 'components/manufacturer/shared/Notification';
import DataTable, {TableColumn} from 'react-data-table-component';
import {MaterialOutsourceInterface} from './Entity';
import {ConfirmationButton} from 'shared/ConfirmationButton';
import {Import} from './Import';
import {AddNewButton} from '../Material';
import {ButtonPassword, Delete, Edit, Upload} from 'assets';
import {
    useDeleteOutsourceMaterialMutation,
    useGetFormFactorsQuery,
    useGetManufacturersQuery,
    useGetOutsourceMaterialDepthQuery,
    useGetOutsourceMaterialPrefixesQuery,
    useGetOutsourceMaterialsQuery,
    useGetOutsourceMaterialThicknessQuery,
    useGetPriceCategoriesQuery,
    useGetSubstratesQuery,
    useLazyGetOutsourceMaterialsQuery,
} from '../store/benchtopModuleApi';
import {isEmpty, uniq} from 'lodash';
import EditMaterial from './EditMaterial';
import {useAppDispatch} from 'store/dashboard';
import FilterComponent from '../Material/Filter';
import {
    addNotification,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';

import {OutsourceFilter} from './Entity';
import {downloadToCSV} from '../shared/DownloadToCSV';

const BenchtopOutsourceMaterial = () => {
    const [perPage, setPerPage] = useState(25);
    const [totalRows, setTotalRows] = useState(0);
    const [toggledClearRows, setToggleClearRows] = useState(false);
    const [selectedMaterials, setSelectedMaterials] = useState<
        MaterialOutsourceInterface[]
    >([]);
    const dispatch = useAppDispatch();

    const {data: thicknesses = [], isSuccess: thicknessFetchedSuccessfully} =
        useGetOutsourceMaterialThicknessQuery();

    const {
        data: priceCategories = [],
        isLoading: loadPriceCats,
        isFetching: fetchPriceCats,
        isSuccess: successPriceCats,
    } = useGetPriceCategoriesQuery({withAllOption: false});

    const {data: formFactors = []} = useGetFormFactorsQuery({
        withAllOption: false,
    });

    const {data: depths = []} = useGetOutsourceMaterialDepthQuery();

    const {data: manufacturers = []} = useGetManufacturersQuery({
        withExtraOptions: {
            withAllOption: true,
            filters: ['(allowBenchtopEditPricingMatrix:equals:1)'],
        },
    });

    const [deleteMaterial, {error: deleteMaterialError}] =
        useDeleteOutsourceMaterialMutation();

    const [filters, setFilters] = useState<OutsourceFilter>({
        currentPage: 1,
        pageSize: perPage,
    });

    const {data: prefixes = []} = useGetOutsourceMaterialPrefixesQuery(filters);
    const {
        data: {data: materials, pagination} = {
            data: [],
            pagination: {total_count: 0},
        },
        isLoading,
        isFetching,
        isSuccess: materialsFetchedSuccessfully,
    } = useGetOutsourceMaterialsQuery(filters);

    const [fetchExportPriceMatrix] = useLazyGetOutsourceMaterialsQuery();

    useEffect(() => {
        if (pagination.total_count) {
            setTotalRows(pagination.total_count);
        }
    }, [pagination]);

    const paginationComponentOptions = {
        noRowsPerPage: true,
    };

    const handlePageChange = (page: number) => {
        setFilters((state) => ({
            ...state,
            currentPage: page,
        }));
    };

    const [header, setHeader] = useState<
        TableColumn<MaterialOutsourceInterface>[]
    >([
        {
            name: 'Name',
            selector: (row) => row.name,
            compact: true,
            wrap: true,
        },
        {
            name: 'Depth',
            selector: (row) => row.depth,
            compact: true,
            wrap: true,
            center: true,
        },
    ]);

    useEffect(() => {
        let headerWithActions: TableColumn<MaterialOutsourceInterface>[] = [];
        if (!isEmpty(thicknesses)) {
            const thicknessHeader = thicknesses.map((item) => {
                return {
                    name: String(item),
                    compact: true,
                    wrap: true,
                    center: true,
                    selector: (row: MaterialOutsourceInterface) =>
                        row[String(item)],
                };
            });
            if (window?.allowOutsourcePriceMatrix) {
                headerWithActions = [
                    ...thicknessHeader,
                    {
                        name: 'Action',
                        cell: (row) => [
                            <>
                                <ConfirmationButton
                                    onClick={() => {}}
                                    title="Edit Material"
                                    message=""
                                    displayCloseButton={true}
                                    classes="x-center small-11 medium-6 large-6"
                                    options={{hideFooter: true}}
                                    image={Edit}>
                                    {({setShow}) => (
                                        <EditMaterial
                                            hideDialog={() => setShow(false)}
                                            material={row}
                                        />
                                    )}
                                </ConfirmationButton>
                                <ConfirmationButton
                                    onClick={() =>
                                        deleteMaterials([{ids: row.ids}])
                                    }
                                    title="Delete Material"
                                    displayCloseButton={true}
                                    message="Are you sure you want to perform this action on the selected item?"
                                    classes="x-center small-11 medium-5 large-5"
                                    image={Delete}
                                />
                            </>,
                        ],
                        center: true,
                    },
                ];
            } else {
                headerWithActions = [...thicknessHeader];
            }
        }
        setHeader((state) => [...state, ...headerWithActions]);
    }, [thicknessFetchedSuccessfully]);

    useEffect(() => {
        setHeader((state) => {
            return state.map((col) => ({
                ...col,
                omit:
                    col.name.toLowerCase() == 'action' &&
                    parseInt(filters?.manufacturer)
                        ? true
                        : false,
            }));
        });
    }, [filters]);

    const showRowCheckboxes = useMemo(() => {
        if (window?.allowOutsourcePriceMatrix) {
            if (filters?.manufacturer && parseInt(filters?.manufacturer) != 0) {
                return false;
            }
            return true;
        }
        return false;
    }, [filters]);

    const deleteMaterials = async (
        selectedMaterials: MaterialOutsourceInterface[]
    ) => {
        setToggleClearRows(!toggledClearRows);
        const materialIds = selectedMaterials
            .map((material) => material.ids)
            .join(',');

        try {
            await deleteMaterial({
                id: materialIds,
            }).unwrap();
            setSelectedMaterials([]);
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(deleteMaterialError)) {
            dispatch(
                addNotification(
                    `Error - ${deleteMaterialError.data.error}`,
                    MessageVariant.ERROR,
                    'benchtopOutsourceMaterial'
                )
            );
        }
    }, [deleteMaterialError]);

    const exportPriceMatrix = async () => {
        dispatch(
            addNotification(
                `Downloading materials list, please wait`,
                MessageVariant.INFO,
                'benchtopOutsourceMaterial'
            )
        );
        // only 1000 records are requested in single request
        const totalRequestedRecords = 1000;
        let exportPriceMatrix: MaterialOutsourceInterface[] = [];

        if (pagination.total_count <= totalRequestedRecords) {
            const fetchMaterialRequest = await fetchExportPriceMatrix(
                {
                    ...filters,
                    pageSize: totalRequestedRecords,
                },
                true
            );

            if (fetchMaterialRequest.isSuccess) {
                exportPriceMatrix = [...fetchMaterialRequest.data.data];
            }
            if (fetchMaterialRequest.isError) {
                dispatch(
                    addNotification(
                        `Error: Something went wrong when exporting price matrix. Contact admin for more information.
                        Error Type : ${fetchMaterialRequest.error.data.error}`,
                        MessageVariant.ERROR,
                        'benchtopOutsourceMaterial'
                    )
                );
                return;
            }
        } else {
            const totalRequests = Math.ceil(
                pagination.total_count / totalRequestedRecords
            );

            for (let request = 0; request < totalRequests; request++) {
                const fetchMaterialRequest = await fetchExportPriceMatrix(
                    {
                        ...filters,
                        pageSize: totalRequestedRecords,
                        currentPage: request + 1,
                    },
                    true
                );

                if (fetchMaterialRequest.isSuccess) {
                    exportPriceMatrix = [
                        ...exportPriceMatrix,
                        ...fetchMaterialRequest.data.data,
                    ];
                }

                if (fetchMaterialRequest.isError) {
                    dispatch(
                        addNotification(
                            `Error: Something went wrong when exporting price matrix. Contact admin for more information.
                            Error Type : ${fetchMaterialRequest.error.data.error}`,
                            MessageVariant.ERROR,
                            'benchtopOutsourceMaterial'
                        )
                    );
                    return;
                }
            }
        }

        const header = [
            'Prefix',
            'PriceCategory',
            'FormFactor',
            'DoubleSided',
            'Thickness',
        ];
        let thicknessWithNumbers: string[] = [];
        if (!isEmpty(thicknesses)) {
            thicknessWithNumbers = [...thicknesses];
            header.push(...thicknessWithNumbers);
        }

        const data = exportPriceMatrix.map(function (
            obj: MaterialOutsourceInterface
        ) {
            const prices = thicknessWithNumbers.map((element) => {
                return obj.thicknesses[element];
            });
            const nameSplitArray = obj.name.split('_');
            /* name = PREFIX_PRICECAT_FF_DS 
                or name = PRICECAT_FF_DS   when prefix is null
            */
            const priceCatName = !isEmpty(obj.prefix)
                ? nameSplitArray[1]
                : nameSplitArray[0];
            const ffName = !isEmpty(obj.prefix)
                ? nameSplitArray[2]
                : nameSplitArray[1];
            return [
                obj.prefix,
                priceCatName,
                ffName,
                Number(obj.is_double_sided) ? 'DS' : '',
                obj.depth,
                ...prices,
            ];
        });
        // 2nd row is also header so add it with data
        downloadToCSV(header, [['', '', '', '', 'Depth'], ...data], 'matrix');

        dispatch(
            addNotification(
                `Download complete`,
                MessageVariant.SUCCESS,
                'benchtopOutsourceMaterial'
            )
        );
    };

    return (
        <>
            <Notification filterType="benchtopOutsourceMaterial" />

            <div className="floating_item_body" style={{margin: '10px 0px'}}>
                <h1 className="form_header" style={{display: 'inline'}}>
                    Outsource Materials
                </h1>
                <div className="floatR pageButtons">
                    <a href="benchtopModule_materials.php">
                        <AddNewButton>Material List</AddNewButton>
                    </a>
                </div>
                <div className="floatR pageButtons">
                    <ConfirmationButton
                        onClick={exportPriceMatrix}
                        component={AddNewButton}
                        title="Download Price Matrix"
                        message="Are you sure you want to download?"
                        classes="x-center small-11 medium-5 large-5"
                        image={Upload}
                        buttonText="Export"
                    />
                </div>
                <hr className="form_horizontal_rule"></hr>
                <div className="floatL pageButtons">
                    {window?.allowOutsourcePriceMatrix ? (
                        <span>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Import materials"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={Upload}
                                buttonText="Import">
                                {({setShow}) => {
                                    return (
                                        <Import
                                            hideDialog={() => setShow(false)}
                                        />
                                    );
                                }}
                            </ConfirmationButton>
                        </span>
                    ) : (
                        <></>
                    )}
                </div>
                <div
                    className="pageButtons"
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                        alignItems: 'baseline',
                    }}>
                    {window?.isAdmin ? (
                        <div>
                            <strong style={{margin: '5px 15px'}}>
                                Manufacturers
                            </strong>
                            <select
                                style={{padding: '0 3px'}}
                                name="filter_manufacturer"
                                id="filter_manufacturer"
                                onChange={(e) =>
                                    setFilters((state) => ({
                                        ...state,
                                        currentPage: 1,
                                        prefix: '',
                                        manufacturer: e.target.value,
                                    }))
                                }>
                                {manufacturers.map((manufacturer) => (
                                    <option
                                        key={manufacturer.id}
                                        value={manufacturer.id}>
                                        {manufacturer.value}
                                    </option>
                                ))}
                            </select>
                        </div>
                    ) : (
                        <></>
                    )}

                    {prefixes.length ? (
                        <div>
                            <strong style={{margin: '5px 15px'}}>Prefix</strong>
                            <FilterComponent
                                name="filter_price_prefix"
                                multipleSelection={true}
                                options={[
                                    {name: '(No Prefix)', id: 0},
                                    ...prefixes,
                                ]} // Add empty prefix
                                size="4"
                                changeFn={(
                                    e: React.ChangeEvent<HTMLSelectElement>
                                ) => {
                                    setFilters((state) => ({
                                        ...state,
                                        currentPage: 1,
                                        prefix: [...e.target.options]
                                            .filter((x) => x.selected)
                                            .map((x) => x.value)
                                            .join(),
                                    }));
                                }}
                            />
                        </div>
                    ) : null}

                    <div>
                        <strong style={{margin: '5px 15px'}}>
                            Price Categories
                        </strong>
                        <FilterComponent
                            name="filter_price_category"
                            multipleSelection={true}
                            options={priceCategories}
                            size="4"
                            changeFn={(
                                e: React.ChangeEvent<HTMLSelectElement>
                            ) => {
                                setFilters((state) => ({
                                    ...state,
                                    currentPage: 1,
                                    priceCategories: [...e.target.options]
                                        .filter((x) => x.selected)
                                        .map((x) => x.value)
                                        .join(),
                                }));
                            }}
                        />
                    </div>
                    <div>
                        <strong style={{margin: '5px 15px'}}>Depth</strong>
                        <FilterComponent
                            name="filter_depth"
                            multipleSelection={true}
                            options={depths}
                            size="4"
                            changeFn={(
                                e: React.ChangeEvent<HTMLSelectElement>
                            ) => {
                                setFilters((state) => ({
                                    ...state,
                                    currentPage: 1,
                                    depth: [...e.target.options]
                                        .filter((x) => x.selected)
                                        .map((x) => x.value)
                                        .join(),
                                }));
                            }}
                        />
                    </div>
                    <div>
                        <strong style={{margin: '5px 15px'}}>Profiles</strong>
                        <FilterComponent
                            name="filter_formfactors"
                            multipleSelection={true}
                            options={formFactors}
                            size="2"
                            changeFn={(
                                e: React.ChangeEvent<HTMLSelectElement>
                            ) => {
                                setFilters((state) => ({
                                    ...state,
                                    currentPage: 1,
                                    formFactors: [...e.target.options]
                                        .filter((x) => x.selected)
                                        .map((x) => x.value)
                                        .join(),
                                }));
                            }}
                        />
                    </div>

                    <div>
                        <strong style={{margin: '5px 15px'}}>
                            Double Sided
                        </strong>
                        <FilterComponent
                            name="filter_double_sided"
                            multipleSelection={true}
                            options={[
                                {name: 'Yes', id: 1},
                                {name: 'No', id: 0},
                            ]}
                            size="2"
                            changeFn={(
                                e: React.ChangeEvent<HTMLSelectElement>
                            ) => {
                                setFilters((state) => ({
                                    ...state,
                                    currentPage: 1,
                                    doubleSided: [...e.target.options]
                                        .filter((x) => x.selected)
                                        .map((x) => x.value)
                                        .join(),
                                }));
                            }}
                        />
                    </div>
                </div>
                <DataTable
                    columns={header}
                    data={materials}
                    striped={true}
                    highlightOnHover={true}
                    progressPending={isLoading || isFetching}
                    selectableRows={showRowCheckboxes}
                    selectableRowsVisibleOnly={true}
                    selectableRowsHighlight={true}
                    onSelectedRowsChange={(state) =>
                        setSelectedMaterials(state.selectedRows)
                    }
                    clearSelectedRows={toggledClearRows}
                    pagination
                    paginationServer
                    paginationPerPage={perPage}
                    paginationTotalRows={totalRows}
                    paginationComponentOptions={paginationComponentOptions}
                    onChangePage={handlePageChange}
                />
                {!isEmpty(selectedMaterials) ? (
                    <ConfirmationButton
                        onClick={() => deleteMaterials(selectedMaterials)}
                        classes="x-center small-8 medium-6 large-6"
                        title="Delete Materials"
                        message={`Are you sure you want to perform this action on the selected materials?`}
                        component={AddNewButton}
                        image={ButtonPassword}
                        displayCloseButton={true}
                        buttonText="Delete Materials"></ConfirmationButton>
                ) : (
                    <></>
                )}
            </div>
        </>
    );
};

export default BenchtopOutsourceMaterial;
