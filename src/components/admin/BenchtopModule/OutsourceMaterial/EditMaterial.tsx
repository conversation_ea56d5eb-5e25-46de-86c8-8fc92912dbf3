import {MaterialOutsourceInterface} from './Entity';
import React, {useEffect, useState} from 'react';
import {Form, Formik, FormikProps} from 'formik';
import {useAppDispatch} from 'store/dashboard';
import {
    addNotification,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import {isEmpty} from 'lodash';
import {Row} from 'react-bootstrap';
import TextField from 'components/manufacturer/shared/TextField';
import {
    Element,
    SubmitButton,
} from 'components/manufacturer/Coupon/AddEditCoupon';
import {useEditOutsourceMaterialMutation} from '../store/benchtopModuleApi';
const EditMaterial = ({
    hideDialog,
    material,
}: {
    hideDialog: () => void;
    material: MaterialOutsourceInterface;
}) => {
    const [error, setError] = useState([]);
    const dispatch = useAppDispatch();
    const [editMaterial, {error: editMaterialError}] =
        useEditOutsourceMaterialMutation();
    let initials: MaterialOutsourceInterface = {
        name: '',
        prefix: '',
        price_category_id: null,
        formfactor_id: null,
        depth: null,
        is_double_sided: null,
        thicknesses: {},
    };

    // populate data when material provided
    if (!isEmpty(material)) {
        initials = {
            ...material,
        };
    }

    const submitForm = async (values: MaterialOutsourceInterface) => {
        const materialData: MaterialOutsourceInterface = {
            name: values.name,
            prefix: values.prefix,
            formfactor_id: values.formfactor_id,
            price_category_id: values.price_category_id,
            is_double_sided: values.is_double_sided,
            depth: values.depth,
        };
        materialData.thicknesses = Object.assign(
            {},
            ...Object.entries(values.thicknesses).map((thicknessWithCost) => {
                const castValue =
                    values[thicknessWithCost[0]] == ''
                        ? null
                        : values[thicknessWithCost[0]];
                return {
                    [thicknessWithCost[0]]: castValue,
                };
            })
        );

        try {
            await editMaterial(materialData).unwrap();
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(editMaterialError)) {
            setError(editMaterialError.data.error);
        }
    }, [editMaterialError]);
    return (
        <div>
            {isEmpty(error) ? (
                <></>
            ) : (
                <div className="error" onClick={() => setError([])}>
                    {error}
                </div>
            )}
            <Formik
                initialValues={initials}
                // validationSchema={validationSchema}
                onSubmit={async (
                    values: MaterialOutsourceInterface,
                    actions
                ) => {
                    try {
                        await submitForm(values);
                        hideDialog();
                        actions.setSubmitting(false);
                        actions.resetForm();
                        setError([]);
                        dispatch(
                            addNotification(
                                `Material ${
                                    isEmpty(material) ? `Added` : `Edited`
                                } Successfully`,
                                MessageVariant.SUCCESS,
                                'benchtopOutsourceMaterial'
                            )
                        );
                    } catch (e) {
                        setError(e.data.error);
                    }
                }}>
                {(props: FormikProps<any>) => (
                    <Form>
                        <Row className="row">
                            <TextField
                                name="name"
                                style={{fontWeight: 'bold'}}
                                label="Material Name"
                                disabled={true}
                                classes="column small-6"
                            />

                            <TextField
                                name="depth"
                                style={{fontWeight: 'bold'}}
                                label="Material Depth"
                                disabled={true}
                                classes="column small-6"
                            />
                        </Row>
                        <Row className="row">
                            {Object.entries(props.values.thicknesses).map(
                                (thickness, index) => {
                                    return (
                                        <React.Fragment key={index}>
                                            <TextField
                                                name={thickness[0]}
                                                style={{fontWeight: 'bold'}}
                                                label={thickness[0]}
                                                classes="column small-4"
                                            />
                                        </React.Fragment>
                                    );
                                }
                            )}
                        </Row>
                        <hr />
                        <Element align={`justify-content: space-between;`}>
                            <SubmitButton
                                type="button"
                                className="form_50_button_left "
                                onClick={() => {
                                    props.resetForm();
                                    hideDialog();
                                }}>
                                Cancel
                            </SubmitButton>
                            <SubmitButton
                                type="submit"
                                disabled={props.isSubmitting}
                                className="form_50_button_right">
                                Save
                            </SubmitButton>
                        </Element>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default EditMaterial;
