import {SubmitButton} from 'components/manufacturer/Coupon/AddEditCoupon';
import Notification from 'components/manufacturer/shared/Notification';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import {FormLabel} from 'components/manufacturer/shared/TextField';
import React, {useEffect, useState} from 'react';
import {Button} from 'react-bootstrap';
import {useAppDispatch} from 'store/dashboard';
import styled from 'styled-components';
import {series} from 'async';
import {difference, differenceBy, groupBy, isEmpty, split} from 'lodash';
import {outsourceImport, OutsourceMaterialImport} from './Entity';
import {
    useGetFormFactorsQuery,
    useGetMaterialThicknessesQuery,
    useGetPriceCategoriesQuery,
    useImportOutsourceMaterialMutation,
} from '../store/benchtopModuleApi';

const FileField = styled.input`
    padding: 10px;
`;

export const Import = ({hideDialog}: {hideDialog: () => void}) => {
    const dispatch = useAppDispatch();
    const [fileupload, setFileUpload] = useState<File>();
    const [requiredComponentsFetched, setRequiredComponentsFetched] =
        useState<boolean>(false);
    const [importMaterial] = useImportOutsourceMaterialMutation();

    const [extraThicknesses, setExtraThicknesses] = useState([]);
    const {data: materialThicknesses = [], isSuccess: thicknessSuccess} =
        useGetMaterialThicknessesQuery({withAllOption: false});
    useEffect(() => {
        if (thicknessSuccess) {
            setRequiredComponentsFetched(true);
        }
    }, [thicknessSuccess]);
    const uploader = async () => {
        if (
            fileupload instanceof File &&
            (fileupload.type == 'text/csv' ||
                fileupload.type == 'application/vnd.ms-excel')
        ) {
            // remove all notifications of this type
            dispatch(deleteAllNotificationsOfType('benchtopOutsourceUploads'));
            const results = await readDataFromFile();
            const [first, ...lines] = results[0].split(/\r\n|\n/);
            const headers = first
                .split(',')
                .map((header) => header.toLowerCase());

            const filteredData = results[0]
                .split('\n') // split with new lines
                .filter((data) => data); // remove empty rows

            const thickness = filteredData[0]
                .split(',')
                .splice(2)
                .map((thick) => parseInt(thick.trim()))
                .filter((t) => !isNaN(t));
            // check if there are extra thicknesses coming from CSV which are not linked to any ofg the benchtop materials
            const extraThicknessesFromCSV = differenceBy(
                thickness,
                materialThicknesses.map((thickness) => thickness.id),
                Math.round
            );

            if (!isEmpty(extraThicknessesFromCSV)) {
                setExtraThicknesses(extraThicknessesFromCSV);
                return;
            }

            let mainContent: OutsourceMaterialImport[][];
            /**
             * Different header formats are supported
             *
             * prefix | pricecategory | formfactor | doublesided | thickness | ...
             *        | thickness | ...
             */

            // check if price category, formfactor and double sided columns are explicitly defined in CSV
            const fullDataSet =
                headers.includes('pricecategory') &&
                headers.includes('formfactor') &&
                headers.includes('doublesided');
            if (fullDataSet) {
                const indexes = {
                    prefix: headers.indexOf('prefix'),
                    price_cat_name: headers.indexOf('pricecategory'),
                    formfactor: headers.indexOf('formfactor'),
                    double_side: headers.indexOf('doublesided'),
                    depth: headers.indexOf('thickness'),
                };
                mainContent = lines
                    .filter((line) => line)
                    .slice(1) // remove 2nd line as that's still heading
                    .map((line) => {
                        const data = line.split(',');
                        return thickness.map((thick) => {
                            // find current index in headers for this thickness which will be used to get cost
                            const costIndex = headers.findIndex(
                                (headerThickness) =>
                                    headerThickness == thick.toString()
                            );
                            const name = [
                                String(data[indexes.price_cat_name]),
                                String(data[indexes.formfactor]),
                            ];
                            if (data[indexes.double_side]) {
                                name.push(String(data[indexes.double_side]));
                            }
                            if (data[indexes.prefix]) {
                                name.unshift(data[indexes.prefix]);
                            }
                            return {
                                prefix: data[indexes.prefix]
                                    ? data[indexes.prefix]
                                    : null, // prefix
                                price_cat_name: String(
                                    data[indexes.price_cat_name]
                                ), // price category
                                formfactor: String(data[indexes.formfactor]), // form factor
                                double_side: String(data[indexes.double_side]), // DS
                                name: name.join('_'), //
                                depth: Number(data[indexes.depth]), // depth
                                thickness: thick, // current thickness
                                cost: Number(data[costIndex]),
                            };
                        });
                    });
            } else {
                // main content starting from 3rd row in csv
                mainContent = filteredData.splice(2).map((content) => {
                    const data = content.split(',');
                    return thickness.map((thick) => {
                        const costIndex = headers.findIndex(
                            (headerThickness) =>
                                headerThickness == thick.toString()
                        );
                        const splitName = String(data[0]).split('_');
                        const isDoubleSided = String(data[0]).endsWith('_DS');
                        if (isDoubleSided) {
                            splitName.pop();
                        }
                        if (splitName.length < 3) {
                            // no prefix in name
                            splitName.unshift(null);
                        }

                        const [prefix, pricecategory, formfactor]: [
                            string | number,
                            string,
                            string
                        ] = [splitName[0], splitName[1], splitName[2]]; // destructring with indexes to avoid typescript error

                        return {
                            prefix: prefix ? prefix : null, // prefix
                            price_cat_name: pricecategory, // price category
                            formfactor: formfactor, // form factor
                            double_side: isDoubleSided ? 'DS' : null, // DS
                            name: String(data[0]), // name
                            depth: Number(data[1]), // depth
                            thickness: thick, // current thickness
                            cost: Number(data[costIndex]),
                        };
                    });
                });
            }

            // flat big object and group by thickness
            const data: outsourceImport = groupBy(
                mainContent.flat(),
                'thickness'
            );
            await importMaterial(data);
            hideDialog();
            dispatch(
                addNotification(
                    `Outsource Material import has been started. Please refresh the page in a minute.`,
                    MessageVariant.SUCCESS,
                    `benchtopOutsourceMaterial`
                )
            );
        } else {
            dispatch(
                addNotification(
                    `Incorrect file format`,
                    MessageVariant.ERROR,
                    `benchtopOutsourceUploads`
                )
            );
        }
    };

    const readDataFromFile: () => Promise<string> = async () => {
        const contents = [];

        contents.push((cb: (error: string | null, result?: string) => void) => {
            const reader = new FileReader();

            reader.readAsText(fileupload);
            reader.onload = () => {
                const result = reader.result as string;

                // Normalize and filter out rows that contain only separators or whitespace
                const filteredResult = result
                    .split('\n')
                    .filter((row) => {
                        // Check if the row contains any meaningful data
                        const columns = row.split(','); // Assuming the file is comma-separated
                        return columns.some((col) => col.trim() !== ''); // Keep rows with at least one non-empty column
                    })
                    .join('\n');

                cb(null, filteredResult);
            };

            reader.onerror = () => {
                cb(reader.error.message);
            };
        });

        try {
            return await series(contents);
        } catch (error) {
            dispatch(
                addNotification(
                    `Error reading file`,
                    MessageVariant.ERROR,
                    `benchtopOutsourceUploads`
                )
            );
        }
    };
    if (!requiredComponentsFetched) {
        return <h1>Loading...</h1>;
    }
    return (
        <>
            <Notification filterType="benchtopOutsourceUploads" />
            <Button
                onClick={() => {
                    hideDialog();
                    // remove all notifications of this type
                    dispatch(
                        deleteAllNotificationsOfType('benchtopOutsourceUploads')
                    );
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {isEmpty(extraThicknesses) ? (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                    }}>
                    <FormLabel
                        label="Upload File"
                        required={true}
                        style={{fontWeight: 'bold'}}
                    />
                    <FileField
                        type="file"
                        onChange={(e) => {
                            setFileUpload(e.target.files[0]);
                        }}
                    />
                    <SubmitButton
                        type="button"
                        className="form_50_button_left"
                        onClick={uploader}
                        style={{margin: '10px 0 0 0;'}}>
                        Upload
                    </SubmitButton>
                </div>
            ) : (
                <div>
                    <h4>
                        Following thicknesses are not linked to any benchtop
                        materials in database. Please remove these rows from CSV
                        before importing.
                    </h4>
                    <ul>
                        {extraThicknesses.map((thickness: string, i) => {
                            return (
                                <React.Fragment key={i}>
                                    <li>
                                        <h6>{parseInt(thickness)} </h6>
                                    </li>
                                </React.Fragment>
                            );
                        })}
                    </ul>
                </div>
            )}
        </>
    );
};
