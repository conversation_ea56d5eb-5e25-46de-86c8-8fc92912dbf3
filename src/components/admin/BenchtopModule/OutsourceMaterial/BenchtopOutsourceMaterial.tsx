import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import store from 'store/dashboard';
import BenchtopOutsourceMaterial from './index';
import ThemeProvider from 'theme/DashboardTheme';

const benchtopOutsourceMaterialRenderer = (element: HTMLElement) => {
    const root = createRoot(element);

    root.render(
        <Provider store={store}>
            <ThemeProvider>
                <BenchtopOutsourceMaterial />
            </ThemeProvider>
        </Provider>
    );
};

export default benchtopOutsourceMaterialRenderer;
