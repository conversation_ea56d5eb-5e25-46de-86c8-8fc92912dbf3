export interface MaterialOutsourceInterface {
    id?: number;
    prefix?: string;
    name?: string;
    formfactor_id?: number;
    price_category_id?: number;
    is_double_sided?: number;
    depth?: number;
    manufacturer_id?: number;
    thicknesses?: {
        [key: string]: string | number;
    };
    cost?: number;
    is_deleted?: number;
    [key: string]: string | number;
}

export interface OutsourceMaterialImport {
    prefix: string;
    price_cat_name: string;
    formfactor: string;
    double_side: string;
    name: string;
    thickness: number;
    cost: number;
    depth: number;
}

export interface outsourceImport {
    [thickness: string]: OutsourceMaterialImport[];
}

export interface OutsourceFilter {
    pageSize?: number;
    currentPage?: number;
    prefix?: string;
    priceCategories?: string;
    depth?: string;
    formFactors?: string;
    doubleSided?: string;
    manufacturer?: string;
}
