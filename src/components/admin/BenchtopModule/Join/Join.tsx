import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import store from 'store/dashboard';
import BenchtopJoin from 'components/admin/BenchtopModule/Join';
import ThemeProvider from 'theme/DashboardTheme';

const benchtopJoinRenderer = (element: HTMLElement) => {
    const root = createRoot(element);

    root.render(
        <Provider store={store}>
            <ThemeProvider>
                <BenchtopJoin />
            </ThemeProvider>
        </Provider>
    );
};

export default benchtopJoinRenderer;
