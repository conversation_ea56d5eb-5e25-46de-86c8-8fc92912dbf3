import React, {useCallback, useEffect} from 'react';
import DataTable, {TableColumn} from 'react-data-table-component';
import {useAppDispatch} from 'store/dashboard';
import {
    JoinInterface,
    useEditJoinMutation,
    useGetJoinsQuery,
} from 'components/admin/BenchtopModule/store/benchtopModuleJoinApi';
import {formatPrice} from 'shared/helpers';
import {ConfirmationButton} from 'shared/ConfirmationButton';
import AddEditJoin from './AddEditJoin';
import {Delete, Edit} from 'assets';
import {
    MessageVariant,
    addNotification,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';

const BenchtopJoin = () => {
    const dispatch = useAppDispatch();
    const {
        data: {data: joins = []} = {
            data: [],
            pagination: {total_count: 0},
        },
        isLoading,
        isFetching,
    } = useGetJoinsQuery();
    const [editJoin, {error: editJoinError}] = useEditJoinMutation();

    const paginationComponentOptions = {
        noRowsPerPage: true,
    };

    const emptyFunction = useCallback(() => {
        return;
    }, []);

    useEffect(() => {
        if (editJoinError) {
            if ('status' in editJoinError) {
                const errMsg: {
                    error?: string;
                } =
                    'error' in editJoinError
                        ? editJoinError
                        : editJoinError.data;
                dispatch(
                    addNotification(
                        errMsg.error,
                        MessageVariant.ERROR,
                        'BenchtopJoins'
                    )
                );
            } else {
                dispatch(
                    addNotification(
                        editJoinError.message,
                        MessageVariant.ERROR,
                        'BenchtopJoins'
                    )
                );
            }
        }
    }, [editJoinError]);

    const cols: TableColumn<JoinInterface>[] = [
        {
            name: 'ID',
            selector: (row) => row.id,
            compact: true,
            wrap: true,
            center: true,
        },

        {
            name: 'Name',
            selector: (row) => row.name,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Identifier',
            selector: (row) => row.identifier,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Direction',
            selector: (row) => (row.direction ? row.direction : '-'),
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Minimum distance',
            selector: (row) =>
                row.minimum_edge_distance ? row.minimum_edge_distance : '-',
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Cost',
            selector: (row) => {
                return `${formatPrice(row.price, {})}`;
            },
            center: true,
            compact: true,
            wrap: true,
        },
        {
            name: 'Action',
            cell: (row) => [
                <React.Fragment key={row.id}>
                    <ConfirmationButton
                        onClick={emptyFunction}
                        classes="x-center small-11 medium-8 large-8"
                        title="Edit Join"
                        message=""
                        options={{hideFooter: true}}
                        image={Edit}>
                        {({setShow}) => (
                            <AddEditJoin
                                hideDialog={() => setShow(false)}
                                join={row}
                            />
                        )}
                    </ConfirmationButton>
                    {window?.isAdmin ? (
                        <ConfirmationButton
                            onClick={deleteJoin(row)}
                            title="Delete Join"
                            message="Are you sure you want to perform this action on the selected item?"
                            options={{hideFooter: false}}
                            classes="x-center small-11 medium-5 large-5"
                            image={Delete}
                        />
                    ) : null}
                </React.Fragment>,
            ],
            center: true,
        },
    ];
    const deleteJoin = useCallback(
        (row: JoinInterface) => () => {
            const data = {...row, ...{is_deleted: true}};
            const setValue = (async function () {
                await editJoin(data).unwrap();
                dispatch(
                    addNotification(
                        `Join deleted Successfully`,
                        MessageVariant.SUCCESS,
                        'BenchtopJoins'
                    )
                );
            })();
        },
        []
    );
    return (
        <>
            <Notification filterType="BenchtopJoins" />
            <div className="floating_item_body" style={{margin: '10px 0px'}}>
                <h1 className="form_header" style={{display: 'inline'}}>
                    Joins
                </h1>
                <DataTable
                    columns={cols}
                    progressPending={isLoading || isFetching}
                    data={joins}
                    striped={true}
                    highlightOnHover={true}
                    paginationComponentOptions={paginationComponentOptions}
                    pagination
                    paginationServer
                    selectableRows={false}
                />
            </div>
        </>
    );
};

export default BenchtopJoin;
