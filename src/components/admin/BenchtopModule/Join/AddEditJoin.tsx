import React, {useCallback, useEffect, useState} from 'react';
import {
    Element,
    SubmitButton,
} from 'components/manufacturer/Coupon/AddEditCoupon';
import {Field, Form, Formik, FormikHelpers, FormikProps} from 'formik';
import TextField from 'components/manufacturer/shared/TextField';
import {
    MessageVariant,
    addNotification,
} from 'components/manufacturer/shared/NotificationSlice';
import {
    JoinInterface,
    useAddJoinMutation,
    useEditJoinMutation,
} from '../store/benchtopModuleJoinApi';
import {useAppDispatch} from 'store/dashboard';
import {StyledRow} from '../EdgeProfiles/AddEditEdgeProfile';
import {cloneDeep, isEmpty} from 'lodash';
import {JoinType} from 'components/customer/BTM/entity/JoinType';

const AddEditJoin = ({
    hideDialog,
    join,
}: {
    hideDialog: () => void;
    join?: JoinInterface;
}) => {
    const dispatch = useAppDispatch();
    const [error, setError] = useState('');
    const [addJoin, {error: addJoinError}] = useAddJoinMutation();
    const [editJoin, {error: editJoinError}] = useEditJoinMutation();
    const [initialValues, setInitialValues] = useState<JoinInterface>({
        id: 0,
        name: '',
        identifier: '',
        direction: null,
        minimum_edge_distance: null,
        price: 0,
        is_deleted: false,
        is_hidden: false,
    });

    useEffect(() => {
        if (!isEmpty(join)) {
            setInitialValues((values) => ({
                ...values,
                ...cloneDeep(join),
            }));
        }
    }, [join]);

    useEffect(() => {
        if (addJoinError) {
            if ('status' in addJoinError) {
                const errMsg: {
                    error?: string;
                } = 'error' in addJoinError ? addJoinError : addJoinError.data;
                setError(errMsg.error);
            } else {
                setError(addJoinError.message);
            }
        }
    }, [addJoinError]);

    useEffect(() => {
        if (editJoinError) {
            if ('status' in editJoinError) {
                const errMsg: {
                    error?: string;
                } =
                    'error' in editJoinError
                        ? editJoinError
                        : editJoinError.data;
                setError(errMsg.error);
            } else {
                setError(editJoinError.message);
            }
        }
    }, [editJoinError]);

    const removeError = useCallback(() => {
        return setError('');
    }, []);

    const submitForm = async (values: JoinInterface) => {
        // sanitize values
        const joinData: JoinInterface = {
            name: values.name,
            identifier: values.identifier,
            direction: values.direction,
            price: values.price,
            minimum_edge_distance:
                values.identifier.toUpperCase() == JoinType.BUTT_JOIN
                    ? values.minimum_edge_distance
                    : null,
            is_deleted: values.is_deleted,
            is_hidden: values.is_hidden,
        };

        if (!isEmpty(join)) {
            joinData.id = join.id;
        }
        try {
            if (isEmpty(join)) {
                await addJoin(joinData).unwrap();
            } else {
                await editJoin(joinData).unwrap();
            }
        } catch (e) {
            throw e;
        }
    };

    const submit = useCallback(
        async (
            values: JoinInterface,
            actions: FormikHelpers<JoinInterface>
        ) => {
            try {
                await submitForm(values);
                hideDialog();
                actions.setSubmitting(false);
                actions.resetForm();
                setError('');
                dispatch(
                    addNotification(
                        `Join ${
                            isEmpty(join) ? `Added` : `Edited`
                        } Successfully`,
                        MessageVariant.SUCCESS,
                        'BenchtopJoins'
                    )
                );
            } catch (e) {}
        },
        []
    );

    return (
        <div>
            {error ? (
                <div className="error" onClick={removeError}>
                    {error}
                </div>
            ) : null}

            <Formik
                initialValues={initialValues}
                enableReinitialize={true}
                onSubmit={submit}>
                {(props: FormikProps<JoinInterface>) => (
                    <Form>
                        <StyledRow className="row">
                            <TextField
                                name="name"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                }}
                                label="Name"
                                classes="column small-4"
                            />
                            <TextField
                                name="identifier"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                }}
                                disabled={true}
                                label="Identifier"
                                classes="column small-4"
                            />
                            <TextField
                                name="direction"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                }}
                                label="Direction"
                                disabled={true}
                                classes="column small-4"
                            />
                        </StyledRow>
                        <StyledRow>
                            <TextField
                                name="price"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '14px',
                                    alignSelf: 'center',
                                }}
                                label="Cost"
                                classes="column small-4"
                            />
                            {props.values.identifier.toUpperCase() ==
                            JoinType.BUTT_JOIN ? (
                                <TextField
                                    name="minimum_edge_distance"
                                    displayFlex={true}
                                    style={{
                                        fontWeight: 'bold',
                                        marginRight: '14px',
                                        alignSelf: 'center',
                                    }}
                                    label="Edge clearance"
                                    classes="column small-4"
                                />
                            ) : null}
                        </StyledRow>
                        <Element align={`justify-content: space-between;`}>
                            <SubmitButton
                                type="button"
                                className="form_50_button_left "
                                onClick={() => {
                                    props.resetForm();
                                    hideDialog();
                                }}>
                                Cancel
                            </SubmitButton>
                            <SubmitButton
                                type="submit"
                                disabled={props.isSubmitting}
                                className="form_50_button_right">
                                Save
                            </SubmitButton>
                        </Element>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default AddEditJoin;
