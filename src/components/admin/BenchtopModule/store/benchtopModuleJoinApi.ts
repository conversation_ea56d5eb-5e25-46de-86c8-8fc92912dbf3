import {isEmpty} from 'lodash';
import {APIResponse, Pagination} from 'store/customer/api';
import {appApi} from 'store/dashboard/appApi';

enum Direction {
    Horizontal,
    Vertical,
}

export interface JoinInterface {
    id?: number;
    name: string;
    identifier: string;
    direction: Direction;
    price: number;
    minimum_edge_distance: number;
    is_deleted: boolean;
    is_hidden: boolean;
}

const benchtopJoinApi = appApi.injectEndpoints({
    endpoints: (build) => ({
        getJoins: build.query<APIResponse<JoinInterface[]>, void>({
            query: () => `benchtop/joins?filter_groups=((is_deleted:equals:0))`,
            transformResponse: (responseData: {
                items: JoinInterface[];
                pagination: Pagination;
            }) => {
                return {
                    data: responseData.items,
                    pagination: responseData.pagination,
                };
            },
            providesTags: () => [{type: 'BenchtopJoins'}],
        }),
        addJoin: build.mutation<JoinInterface, Partial<JoinInterface>>({
            query(body) {
                return {
                    url: `benchtop/joins`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopJoins'];
                }
            },
        }),
        editJoin: build.mutation<JoinInterface, Partial<JoinInterface>>({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/joins/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopJoins'];
                }
            },
        }),
    }),
});

export const {useGetJoinsQuery, useAddJoinMutation, useEditJoinMutation} =
    benchtopJoinApi;
