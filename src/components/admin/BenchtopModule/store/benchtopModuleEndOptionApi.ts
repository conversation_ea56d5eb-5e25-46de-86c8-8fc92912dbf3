import {isEmpty} from 'lodash';
import {Pagination} from 'store/customer/api';
import {appApi} from 'store/dashboard/appApi';

export interface EndOptionInterface {
    id?: number;
    name: string;
    manufacturer_id?: number;
    parent_id?: number;
    default_radius_start: number;
    default_radius_end: number;
    is_deleted?: boolean;
    is_locked: boolean;
    is_arc: boolean;
}

const benchtopEndOptionApi = appApi.injectEndpoints({
    endpoints: (build) => ({
        getBenchtopEndOption: build.query<EndOptionInterface[], void>({
            query: () => `benchtop/endOptions`,
            transformResponse: (responseData: {
                items: EndOptionInterface[];
                pagination: Pagination;
            }) => responseData.items,
            providesTags: () => [{type: 'BenchtopEndoptions'}],
        }),
        editEndOption: build.mutation<
            EndOptionInterface,
            Partial<EndOptionInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/endOptions/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopEndoptions'];
                }
            },
        }),
    }),
});

export const {useGetBenchtopEndOptionQuery, useEditEndOptionMutation} =
    benchtopEndOptionApi;
