import {isEmpty} from 'lodash';
import {APIResponse, Pagination} from 'store/customer/api';
import {appApi} from 'store/dashboard/appApi';

export interface EndProfileOptions {
    id: number;
    default_radius_end?: number;
    default_radius_start: number;
    is_arc: boolean;
    is_deleted: boolean;
    is_locked: boolean;
    manufacturer_id?: number;
    name: string;
    parent_id: number;
}
export interface EdgeProfileInterface {
    id?: number;
    code: string;
    prefix: string;
    name: string;
    symbol: string;
    edge_highlight: boolean | number;
    image_id: number;
    application_cost: number;
    area_handling_cost: number;
    handling_cost: number;
    length_cost: number;
    cost_multiplier: number;
    restrict_adjacent: boolean;
    restrict_corner_clip: boolean;
    restrict_corner_notch: boolean;
    is_postformed_profile: boolean;
    is_hidden?: boolean;
    is_end_only: boolean;
    end_id: number;
    is_end_roll_available: boolean;
    is_default_filter_by_type: boolean;
    is_default_filter_by_brand: boolean;
    is_default_filter_by_finish: boolean;
    is_default_filter_by_substrate: boolean;
    is_default_filter_by_thickness: boolean;
    typeRestrictions?: [];
    brandRestrictions?: [];
    finishRestrictions?: [];
    substrateRestrictions?: [];
    thicknessRestrictions?: [];
    types?: [];
    brands?: [];
    finishes?: [];
    substrates?: [];
    end_option?: EndProfileOptions;
}

export interface EdgeProfileFilterInterface {
    manufacturer?: {id: number; name: string};
    show?: {id: number; name: string};
    pageSize?: number;
    currentPage?: number;
}

const transformEdgeProfileData = (edgeProfile: EdgeProfileInterface) => {
    if (edgeProfile.brandRestrictions) {
        const brandData = [];
        edgeProfile.brandRestrictions.map((edge) => {
            return brandData.push({
                id: edge.brand_id,
                edge_profile_id: edge.edge_profile_id,
            });
        });
        edgeProfile.brandRestrictions = brandData;
    }

    if (edgeProfile.typeRestrictions) {
        const typeData = [];
        edgeProfile.typeRestrictions.map((edge) => {
            return typeData.push({
                id: edge.type_id,
                edge_profile_id: edge.edge_profile_id,
            });
        });
        edgeProfile.typeRestrictions = typeData;
    }

    if (edgeProfile.finishRestrictions) {
        const finishData = [];
        edgeProfile.finishRestrictions.map((edge) => {
            return finishData.push({
                id: edge.finish_id,
                edge_profile_id: edge.edge_profile_id,
            });
        });
        edgeProfile.finishRestrictions = finishData;
    }

    if (edgeProfile.substrateRestrictions) {
        const substrateData = [];
        edgeProfile.substrateRestrictions.map((edge) => {
            return substrateData.push({
                id: edge.substrate_id,
                edge_profile_id: edge.edge_profile_id,
            });
        });
        edgeProfile.substrateRestrictions = substrateData;
    }

    if (edgeProfile.thicknessRestrictions) {
        const thicknessData = [];
        edgeProfile.thicknessRestrictions.map((edge) => {
            return thicknessData.push({
                id: edge.thickness,
                edge_profile_id: edge.edge_profile_id,
            });
        });
        edgeProfile.thicknessRestrictions = thicknessData;
    }

    if (edgeProfile.image_id > 0) {
        edgeProfile.image = [
            {
                ...edgeProfile.image,
                ...{
                    imageUrl: `${edgeProfile.image.image_url}${edgeProfile.image.name}`,
                },
            },
        ];
    }
    return edgeProfile;
};

const benchtopEdgeProfileApi = appApi.injectEndpoints({
    endpoints: (build) => ({
        getEdgeProfiles: build.query<
            APIResponse<EdgeProfileInterface[]>,
            EdgeProfileFilterInterface
        >({
            query: (edgeProfileFilter) => {
                const filterGroups = [`((is_deleted:equals:0))`];
                if (edgeProfileFilter?.manufacturer?.id) {
                    filterGroups.push(
                        `((manufacturers:json_contains:${edgeProfileFilter.manufacturer.id}))`
                    );
                }
                if (
                    !isNaN(edgeProfileFilter?.show?.id) &&
                    typeof edgeProfileFilter?.show?.id == 'number'
                ) {
                    if (edgeProfileFilter.show.id == 1) {
                        filterGroups.push(
                            `((is_hidden:empty)OR(is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((is_hidden:equals:${edgeProfileFilter.show.id}))`
                        );
                    }
                }
                return `benchtop/edge/profiles?filter_groups=${filterGroups.join(
                    'AND'
                )}&page_size=${
                    edgeProfileFilter?.pageSize
                        ? edgeProfileFilter?.pageSize
                        : 25
                }&current_page=${
                    edgeProfileFilter?.currentPage
                        ? edgeProfileFilter?.currentPage
                        : 1
                }&sort_orders=id ASC`;
            },
            transformResponse: (responseData: {
                items: EdgeProfileInterface[];
                pagination: Pagination;
            }) => {
                return {
                    data: responseData.items.map(
                        (edgeProfile: EdgeProfileInterface) => {
                            return transformEdgeProfileData(edgeProfile);
                        }
                    ),
                    pagination: responseData.pagination,
                };
            },
            providesTags: () => [{type: 'BenchtopEdgeProfiles'}],
        }),
        addEdgeProfile: build.mutation<
            EdgeProfileInterface,
            Partial<EdgeProfileInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/edge/profiles`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopEdgeProfiles'];
                }
            },
        }),
        editEdgeProfile: build.mutation<
            EdgeProfileInterface,
            Partial<EdgeProfileInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/edge/profiles/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopEdgeProfiles'];
                }
            },
        }),
        massHideShow: build.mutation<
            {success: boolean},
            {edgeProfileIds: string}
        >({
            query(body) {
                return {
                    url: `benchtop/edge/profiles/mass-hide-show`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopEdgeProfiles'];
                }
            },
        }),
    }),
});

export const {
    useGetEdgeProfilesQuery,
    useAddEdgeProfileMutation,
    useEditEdgeProfileMutation,
    useMassHideShowMutation,
} = benchtopEdgeProfileApi;
