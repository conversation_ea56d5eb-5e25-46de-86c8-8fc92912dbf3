import {ImageDetailsInterface} from 'components/manufacturer/shared/images/Image';
import {isEmpty, isNull, mapValues} from 'lodash';
import {APIResponse, Pagination} from 'store/customer/api';
import {appApi} from 'store/dashboard/appApi';
import {
    MaterialOutsourceInterface,
    OutsourceFilter,
    outsourceImport,
} from '../OutsourceMaterial/Entity';

import {TableColumn} from 'react-data-table-component';
import {MaterialTypeGenericInterface} from '../Type';

export interface MaterialGenericInterface {
    created_by?: string;
    created_date?: string;
    id?: number;
    image_id?: number;
    image?: ImageDetailsInterface;
    is_deleted?: boolean;
    modified_by?: string;
    modified_date?: string;
    name?: string;
    material_count?: string;
    [key: string]: string | number | boolean | ImageDetailsInterface;
}

interface MaterialImport {
    materials: string[];
    newBenchtopComponents: string[];
    oldBenchtopComponents: {
        types: MaterialGenericInterface[];
        brands: MaterialGenericInterface[];
        finishes: MaterialGenericInterface[];
        formfactors: MaterialGenericInterface[];
        substrates: MaterialGenericInterface[];
        priceCategories: MaterialGenericInterface[];
    };
}

interface MaterialImportResponse {
    message: string;
    errors?: Record<string, string[]>;
}

interface MaterialChangeInterface {
    id?: number;
    item_code?: string;
    area_cost: number;
    area_handling_cost: number;
    area_installation_cost: number;
    min_job_area: number;
    measurement: string;
    allow_butt_join: number;
    max_butt_join_piece: number;
    is_hidden: string;
    round_up?: number;
    max_depth: number;
    max_length: number;
    actualChangesHappen?: boolean;
}

export interface MaterialInterface {
    id?: number;
    item_code?: string;
    change_id?: number;
    name: string;
    type_id: number;
    type_name?: string;
    brand_name?: string;
    finish_name?: string;
    substrate_name?: string;
    form_factor_name?: string;
    price_category_name?: string;
    brand_id: number;
    finish_id: number;
    substrate_id: number;
    formfactor_ids: (string | number)[];
    price_cat_id?: number;
    round_up?: number;
    thickness: number;
    image_id: number;
    is_deleted: number;
    is_blank: number;
    is_grained: number;
    is_double_sided: number;
    area_handling_cost: number;
    area_cost: number;
    area_installation_cost: number;
    max_depth: number;
    max_length: number;
    min_job_area: number;
    measurement: string;
    allow_butt_join: number;
    max_butt_join_piece: number;
    manufacturers: (string | number)[];
    type?: MaterialGenericInterface;
    brand?: MaterialGenericInterface;
    finish?: MaterialGenericInterface;
    formfactors?: MaterialGenericInterface[];
    substrate?: MaterialGenericInterface;
    priceCategory?: MaterialGenericInterface;
    image?: ImageDetailsInterface;
    change?: MaterialChangeInterface;
}

const transformMaterialData = (material: MaterialInterface) => {
    material.type_name = material.type?.name;
    material.brand_name = material.brand?.name;
    material.finish_name = material.finish?.name;
    material.substrate_name = material.substrate?.name;
    material.price_category_name = material?.priceCategory?.name;

    material.form_factor_name = material?.formfactors
        ?.map((ff) => ff.name)
        .join(',');

    material.is_grained = material.is_grained ? 1 : 0;
    material.is_blank = material.is_blank ? 1 : 0;
    material.is_double_sided = material.is_double_sided ? 1 : 0;
    material.allow_butt_join = material.allow_butt_join ? 1 : 0;

    if (material.image_id > 0) {
        // TODO: get image url from api and get rid of hard coded value here
        // this is not working for image selected from folders :(
        material.image = [
            {
                ...material.image,
                imageUrl: `${material.image.image_url}${material.image.name}`,
            },
        ];
    }

    return material;
};

const transformOutsourceMaterialData = (
    material: MaterialOutsourceInterface
) => {
    return {
        ...material,
        ...{id: material.ids}, // add id column for react loop
        ...mapValues(material.thicknesses, function (price) {
            return price ? `$${price}` : null;
        }),
    };
};

const transformThicknessData = (thicknesses: MaterialGenericInterface[]) => {
    const thicknessData: MaterialChangeInterface[] = [];
    thicknesses.map((thickness) => {
        thicknessData.push({
            id: thickness,
            name: thickness,
        });
    });
    return thicknessData;
};

export interface Filter {
    type?: {id: number; name: string};
    brand?: {id: number; name: string};
    finish?: {id: number; name: string};
    substrate?: {id: number; name: string};
    priceCategory?: {id: number; name: string};
    thickness?: {id: number; name: string};
    manufacturer?: {id: number; name: string};
    show?: {id: number; name: string};
    withAllOption?: boolean;
    withEmptyOption?: boolean;
    getMaterialCount?: boolean;
    materialIds?: string;
    hiddenStatus?: number;
    name?: string;
    pageSize?: number;
    currentPage?: number;
    area_handling_cost?: boolean;
    area_installation_cost?: boolean;
    area_cost?: boolean;
    calcType?: {id: number; name: string};
    adjustValue?: number;
    sortBy?: string;
    orderBy?: string;
}

const benchtopApi = appApi.injectEndpoints({
    endpoints: (build) => ({
        // get all materials
        getMaterials: build.query<APIResponse<MaterialInterface[]>, Filter>({
            query: (filters) => {
                const sort = filters?.sortBy ? filters?.sortBy : 'name';
                const order = filters?.orderBy ? filters?.orderBy : 'asc';
                const filterGroups = [`((is_deleted:equals:0))`];
                if (filters?.name) {
                    filterGroups.push(`((name:contains:${filters.name}))`);
                }
                if (filters?.type?.id) {
                    filterGroups.push(`((type_id:equals:${filters.type.id}))`);
                }

                if (filters?.brand?.id) {
                    filterGroups.push(
                        `((brand_id:equals:${filters.brand.id}))`
                    );
                }

                if (filters?.finish?.id) {
                    filterGroups.push(
                        `((finish_id:equals:${filters.finish.id}))`
                    );
                }

                if (filters?.substrate?.id) {
                    filterGroups.push(
                        `((substrate_id:equals:${filters.substrate.id}))`
                    );
                }
                if (
                    filters?.priceCategory?.id ||
                    isNull(filters?.priceCategory?.id)
                ) {
                    if (!isNull(filters?.priceCategory?.id)) {
                        filterGroups.push(
                            `((price_cat_id:equals:${filters.priceCategory.id}))`
                        );
                    } else {
                        filterGroups.push(`((price_cat_id:empty))`);
                    }
                }

                if (filters?.thickness?.id) {
                    filterGroups.push(
                        `((thickness:equals:${filters.thickness.id}))`
                    );
                }

                if (filters?.manufacturer?.id) {
                    filterGroups.push(
                        `((manufacturers:json_contains:${filters.manufacturer.id}))`
                    );
                }

                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((changes.id:empty)OR(changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                }

                return `benchtop/materials?related_includes=type,brand,finish,substrate,priceCategory,formfactors,image&filter_groups=${filterGroups.join(
                    'AND'
                )}${
                    filters?.pageSize ? `&page_size=${filters?.pageSize}` : ``
                }${
                    filters?.currentPage
                        ? `&current_page=${filters?.currentPage}`
                        : ``
                }&sort_orders=${sort}%20${order}`;
            },
            transformResponse: (responseData: {
                items: MaterialInterface[];
                pagination: Pagination;
            }) => {
                return {
                    data: responseData.items.map(
                        (material: MaterialInterface) => {
                            return transformMaterialData(material);
                        }
                    ),
                    pagination: responseData.pagination,
                };
            },
            providesTags: () => [{type: 'BenchtopMaterials'}],
        }),
        // add new material
        addMaterial: build.mutation<
            MaterialInterface,
            Partial<MaterialInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/materials`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return [
                        'BenchtopMaterials',
                        'BenchtopBrands',
                        'BenchtopFinishes',
                        'BenchtopFormFactors',
                        'BenchtopTypes',
                        'BenchtopSubstrates',
                        'BenchtopPriceCategories',
                    ];
                }
            },
        }),
        // import material
        importMaterial: build.mutation<
            MaterialImportResponse,
            Partial<MaterialImport>
        >({
            query(body) {
                return {
                    url: `benchtop/materials/import`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return [
                        'BenchtopMaterials',
                        'BenchtopBrands',
                        'BenchtopFinishes',
                        'BenchtopFormFactors',
                        'BenchtopTypes',
                        'BenchtopSubstrates',
                        'BenchtopPriceCategories',
                    ];
                }
            },
        }),
        // edit material
        editMaterial: build.mutation<
            MaterialInterface,
            Partial<MaterialInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/material/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return [
                        'BenchtopMaterials',
                        'BenchtopBrands',
                        'BenchtopFinishes',
                        'BenchtopFormFactors',
                        'BenchtopTypes',
                        'BenchtopSubstrates',
                        'BenchtopPriceCategories',
                    ];
                }
            },
        }),
        // get all brands
        getBrands: build.query<MaterialGenericInterface[], Filter>({
            query: (filters) => {
                const filterGroups = [];
                if (filters?.type?.id) {
                    filterGroups.push(
                        `((materials.type.id:equals:${filters.type.id}))`
                    );
                }
                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((materials.changes.id:empty)OR(materials.changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((materials.changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                    if (filters?.manufacturer?.id) {
                        filterGroups.push(
                            `((materials.changes.manufacturer_id:equals:${filters.manufacturer.id}))`
                        );
                    }
                }
                if (!isEmpty(filterGroups)) {
                    filterGroups.push(`((materials.is_deleted:equals:0))`);
                    return `benchtop/brands?filter_groups=(${filterGroups.join(
                        'AND'
                    )})&sort_orders=name ASC`;
                }
                return `benchtop/brands?sort_orders=name ASC`;
            },
            keepUnusedDataFor: 300,
            transformResponse: (
                responseData: {
                    items: MaterialGenericInterface[];
                },
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                return responseData.items;
            },
            providesTags: () => [{type: 'BenchtopBrands'}],
        }),
        // add new brand
        addBrand: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/brands`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopBrands'];
                }
            },
        }),
        // edit brand
        editBrand: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/brand/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error, args) => {
                if (isEmpty(error)) {
                    if (args.is_deleted) {
                        return ['BenchtopBrands', 'BenchtopMaterials'];
                    }
                    return ['BenchtopBrands'];
                }
            },
        }),
        // get all finishes
        getFinishes: build.query<MaterialGenericInterface[], Filter>({
            query: (filters) => {
                const filterGroups = [];
                if (filters?.type?.id) {
                    filterGroups.push(
                        `((materials.type.id:equals:${filters.type.id}))`
                    );
                }
                if (filters?.brand?.id) {
                    filterGroups.push(
                        `((materials.brand.id:equals:${filters.brand.id}))`
                    );
                }
                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((materials.changes.id:empty)OR(materials.changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((materials.changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                    if (filters?.manufacturer?.id) {
                        filterGroups.push(
                            `((materials.changes.manufacturer_id:equals:${filters.manufacturer.id}))`
                        );
                    }
                }

                if (!isEmpty(filterGroups)) {
                    filterGroups.push(`((materials.is_deleted:equals:0))`);
                    return `benchtop/finishes?filter_groups=(${filterGroups.join(
                        'AND'
                    )})&sort_orders=name ASC${
                        filters?.pageSize
                            ? `&page_size=${filters?.pageSize}`
                            : ``
                    }`;
                }

                return `benchtop/finishes?sort_orders=name ASC${
                    filters?.pageSize ? `&page_size=${filters?.pageSize}` : ``
                }`;
            },
            transformResponse: (
                responseData: {
                    items: MaterialGenericInterface[];
                },
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                return responseData.items;
            },
            keepUnusedDataFor: 300,
            providesTags: () => [{type: 'BenchtopFinishes'}],
        }),
        // add new finish
        addFinish: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/finishes`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopFinishes'];
                }
            },
        }),
        // edit finish
        editFinish: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/finish/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error, args) => {
                if (isEmpty(error)) {
                    if (args.is_deleted) {
                        return ['BenchtopFinishes', 'BenchtopMaterials'];
                    }
                    return ['BenchtopFinishes'];
                }
            },
        }),
        // get all formfactors
        getFormFactors: build.query<MaterialGenericInterface[], Filter>({
            query: () => `benchtop/formfactors?sort_orders=name ASC`,
            transformResponse: (
                responseData: {
                    items: MaterialGenericInterface[];
                },
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                return responseData.items;
            },
            keepUnusedDataFor: 300,
            providesTags: () => [{type: 'BenchtopFormFactors'}],
        }),
        // add new formfactor
        addFormFactor: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/formfactors`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopFormFactors'];
                }
            },
        }),
        // edit formfactor
        editFormFactor: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/formfactor/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error, args) => {
                if (isEmpty(error)) {
                    if (args.is_deleted) {
                        return ['BenchtopFormFactors', 'BenchtopMaterials'];
                    }
                    return ['BenchtopFormFactors'];
                }
            },
        }),
        // get all types
        getTypes: build.query<MaterialTypeGenericInterface[], Filter>({
            query: (filters) => {
                const filterGroups = [];
                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((materials.changes.id:empty)OR(materials.changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((materials.changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                    if (filters?.manufacturer?.id) {
                        filterGroups.push(
                            `((materials.changes.manufacturer_id:equals:${filters.manufacturer.id}))`
                        );
                    }
                }
                if (!isEmpty(filterGroups)) {
                    return `benchtop/types?filter_groups=(${filterGroups.join(
                        'AND'
                    )})&related_includes=image&sort_orders=name ASC`;
                }
                return `benchtop/types?related_includes=image&sort_orders=name ASC`;
            },
            transformResponse: (
                responseData: {
                    items: MaterialTypeGenericInterface[];
                },
                _meta,
                args
            ) => {
                const items = responseData.items.map((element) => {
                    if (element.image_id > 0) {
                        let imageUrl = element.image.image_url;
                        if (element.image.type_name) {
                            imageUrl += `${element.image.type_name}`;
                        }
                        element.image = [
                            {
                                ...element.image,
                                imageUrl: `${imageUrl}${element.image.name}`,
                            },
                        ];
                    }
                    if (element.is_join_allowed) {
                        // formik saves checkboxes data as array
                        element.is_join_allowed = ['1'];
                    } else {
                        element.is_join_allowed = [];
                    }
                    return element;
                });
                if (args.withAllOption) {
                    return [{id: 0, name: 'All', is_deleted: false}, ...items];
                }
                return items;
            },
            keepUnusedDataFor: 300,
            providesTags: () => [{type: 'BenchtopTypes'}],
        }),
        // add new type
        addType: build.mutation<
            MaterialTypeGenericInterface,
            Partial<MaterialTypeGenericInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/types`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopTypes'];
                }
            },
        }),
        // edit type
        editType: build.mutation<
            MaterialTypeGenericInterface,
            Partial<MaterialTypeGenericInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/type/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error, args) => {
                if (isEmpty(error)) {
                    if (args.is_deleted) {
                        return ['BenchtopTypes', 'BenchtopMaterials'];
                    }
                    return ['BenchtopTypes'];
                }
            },
        }),
        // get all substrates
        getSubstrates: build.query<MaterialGenericInterface[], Filter>({
            query: (filters) => {
                const filterGroups = [];
                if (filters?.type?.id) {
                    filterGroups.push(
                        `((materials.type.id:equals:${filters.type.id}))`
                    );
                }
                if (filters?.brand?.id) {
                    filterGroups.push(
                        `((materials.brand.id:equals:${filters.brand.id}))`
                    );
                }
                if (filters?.finish?.id) {
                    filterGroups.push(
                        `((materials.finish.id:equals:${filters.finish.id}))`
                    );
                }

                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((materials.changes.id:empty)OR(materials.changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((materials.changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                    if (filters?.manufacturer?.id) {
                        filterGroups.push(
                            `((materials.changes.manufacturer_id:equals:${filters.manufacturer.id}))`
                        );
                    }
                }
                if (!isEmpty(filterGroups)) {
                    filterGroups.push(`((materials.is_deleted:equals:0))`);
                    return `benchtop/substrates?filter_groups=(${filterGroups.join(
                        'AND'
                    )})&sort_orders=name ASC`;
                }
                return `benchtop/substrates?sort_orders=name ASC`;
            },
            transformResponse: (
                responseData: {
                    items: MaterialGenericInterface[];
                },
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                return responseData.items;
            },
            keepUnusedDataFor: 300,

            providesTags: () => [{type: 'BenchtopSubstrates'}],
        }),
        // add new substrate
        addSubstrate: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/substrates`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopSubstrates'];
                }
            },
        }),
        // edit substrate
        editSubstrate: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/substrate/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error, args) => {
                if (isEmpty(error)) {
                    if (args.is_deleted) {
                        return ['BenchtopSubstrates', 'BenchtopMaterials'];
                    }
                    return ['BenchtopSubstrates'];
                }
            },
        }),
        // get all pricecategories
        getPriceCategories: build.query<MaterialGenericInterface[], Filter>({
            query: () => `benchtop/pricecategories?sort_orders=name ASC`,
            transformResponse: (
                responseData: {
                    items: MaterialGenericInterface[];
                },
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                if (args.withEmptyOption) {
                    return [
                        {id: null, name: '', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                return responseData.items;
            },
            keepUnusedDataFor: 300,

            providesTags: () => [{type: 'BenchtopPriceCategories'}],
        }),
        // get all pricecategories linked to materials
        getPriceCategoriesLinkedToMaterials: build.query<
            MaterialGenericInterface[],
            Filter
        >({
            query: (filters) => {
                const filterGroups = [];
                if (filters?.type?.id) {
                    filterGroups.push(
                        `((materials.type.id:equals:${filters.type.id}))`
                    );
                }
                if (filters?.brand?.id) {
                    filterGroups.push(
                        `((materials.brand.id:equals:${filters.brand.id}))`
                    );
                }
                if (filters?.finish?.id) {
                    filterGroups.push(
                        `((materials.finish.id:equals:${filters.finish.id}))`
                    );
                }
                if (filters?.substrate?.id) {
                    filterGroups.push(
                        `((materials.substrate.id:equals:${filters.substrate.id}))`
                    );
                }

                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((materials.changes.id:empty)OR(materials.changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((materials.changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                    if (filters?.manufacturer?.id) {
                        filterGroups.push(
                            `((materials.changes.manufacturer_id:equals:${filters.manufacturer.id}))`
                        );
                    }
                }
                if (!isEmpty(filterGroups)) {
                    filterGroups.push(`((materials.is_deleted:equals:0))`);
                    return `benchtop/pricecategories/materials?filter_groups=(${filterGroups.join(
                        'AND'
                    )})&sort_orders=name ASC`;
                }
                return `benchtop/pricecategories/materials?sort_orders=name ASC`;
            },
            transformResponse: (
                responseData: {
                    items: MaterialGenericInterface[];
                },
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...responseData.items,
                    ];
                }
                return responseData.items;
            },
            keepUnusedDataFor: 300,

            providesTags: () => [{type: 'BenchtopPriceCategories'}],
        }),
        // add new pricecategory
        addPriceCategory: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(body) {
                return {
                    url: `benchtop/pricecategories`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopPriceCategories'];
                }
            },
        }),
        // edit pricecategory
        editPriceCategory: build.mutation<
            MaterialGenericInterface,
            Partial<MaterialGenericInterface>
        >({
            query(data) {
                const {id, ...body} = data;
                return {
                    url: `benchtop/pricecategory/${id}`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error, args) => {
                if (isEmpty(error)) {
                    if (args.is_deleted) {
                        return ['BenchtopPriceCategories', 'BenchtopMaterials'];
                    }
                    return ['BenchtopPriceCategories'];
                }
            },
        }),
        getThickness: build.query<number[], void>({
            query: () => `benchtop/materials/substrate-thicknesses`,
            transformResponse: (response: {items: {thickness: number}[]}) => {
                response.items
                    .map((item) => item.thickness)
                    .sort((a, b) => b - a);
                return transformSubstrateThicknessData(response.items);
            },
            providesTags: (result, error) => [{type: 'BenchtopThickness'}],
        }),

        getManufacturers: build.query<
            {id: number; value: string}[],
            {
                withExtraOptions: {
                    withAllOption?: boolean;
                    WithNoManufacturerOption?: boolean;
                    noBenchTopOption?: boolean;
                    pageSize?: number;
                    filters?: string[];
                    noBaseFilter?: boolean;
                    sortOrders?: string;
                };
            }
        >({
            query: (data) => {
                const pageSize =
                    data?.withExtraOptions.pageSize !== undefined
                        ? data?.withExtraOptions.pageSize
                        : 100;

                const filterGroups = !!data?.withExtraOptions?.noBaseFilter
                    ? []
                    : ['(inActive:equals:0)', '(hidden:equals:0)'];

                if (data && !isEmpty(data?.withExtraOptions?.filters)) {
                    filterGroups.push(...data.withExtraOptions.filters);
                }

                if (
                    data &&
                    data?.withExtraOptions.noBenchTopOption === undefined
                ) {
                    filterGroups.push('(benchtopDesigner:equals:1)');
                }

                return {
                    url: 'admin/manufacturers',
                    params: {
                        filter_groups: filterGroups.join('AND'),
                        page_size: pageSize,
                        sort_orders:
                            data?.withExtraOptions?.sortOrders || 'name ASC',
                    },
                };
            },

            transformResponse: (
                responseData: {
                    items: {id: number; name: string; suspended?: boolean}[];
                },
                _meta,
                args
            ) => {
                const manufacturers = responseData.items?.map((item) => {
                    return {
                        id: item.id,
                        value: item.name,
                        suspended: item.suspended,
                    };
                });
                const newOptions = [];
                if (args?.withExtraOptions?.withAllOption) {
                    newOptions.push({id: 0, value: 'All'});
                }
                if (args?.withExtraOptions?.WithNoManufacturerOption) {
                    newOptions.push({id: -1, value: '-- No Manufacturer --'});
                }
                return [...newOptions, ...manufacturers];
            },
            keepUnusedDataFor: 30000, // this won't change very frequently so stay there forever and map from this response
        }),
        // apply mass hide show
        massHideShow: build.mutation<{success: boolean}, Partial<Filter>>({
            query(body) {
                return {
                    url: `benchtop/materials/mass-hide-show`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopMaterials'];
                }
            },
        }),
        // reset to default route
        resetToDefault: build.mutation<{success: boolean}, Partial<Filter>>({
            query(body) {
                return {
                    url: `benchtop/materials/reset-to-default`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopMaterials'];
                }
            },
        }),
        // mass cost editor
        massCostEditor: build.mutation<{success: boolean}, Partial<Filter>>({
            query(body) {
                return {
                    url: `benchtop/materials/mass-cost-edit`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return ['BenchtopMaterials'];
                }
            },
        }),
        // get materials count on reset to default with filter
        getMaterialCountOnResetToDefault: build.query<number, Filter>({
            query: (filters) => {
                const filterGroups = [];
                if (filters?.name) {
                    filterGroups.push(
                        `((material.name:contains:${filters.name}))`
                    );
                }
                if (filters?.type?.id) {
                    filterGroups.push(
                        `((material.type_id:equals:${filters.type.id}))`
                    );
                }

                if (filters?.brand?.id) {
                    filterGroups.push(
                        `((material.brand_id:equals:${filters.brand.id}))`
                    );
                }

                if (filters?.finish?.id) {
                    filterGroups.push(
                        `((material.finish_id:equals:${filters.finish.id}))`
                    );
                }

                if (filters?.substrate?.id) {
                    filterGroups.push(
                        `((material.substrate_id:equals:${filters.substrate.id}))`
                    );
                }

                if (
                    filters?.priceCategory?.id ||
                    isNull(filters?.priceCategory?.id)
                ) {
                    if (!isNull(filters?.priceCategory?.id)) {
                        filterGroups.push(
                            `((material.price_cat_id:equals:${filters.priceCategory.id}))`
                        );
                    } else {
                        filterGroups.push(`((material.price_cat_id:empty))`);
                    }
                }

                if (filters?.thickness?.id) {
                    filterGroups.push(
                        `((material.thickness:equals:${filters.thickness.id}))`
                    );
                }

                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(`((is_hidden:equals:1))`);
                    } else {
                        filterGroups.push(
                            `((is_hidden:equals:${filters.show.id}))`
                        );
                    }
                }

                return `benchtop/materials/reset-to-default/count${
                    filterGroups.length
                        ? `?filter_groups=${filterGroups.join('AND')}`
                        : ``
                }`;
            },
            transformResponse: (responseData: {count: number}) =>
                responseData.count,
        }),
        // import outsource material
        importOutsourceMaterial: build.mutation<
            outsourceImport,
            Partial<outsourceImport>
        >({
            query(body) {
                return {
                    url: `benchtop/outsourced-materials/import`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return [
                        'BenchtopOutsourceMaterialThicknesses',
                        'BenchtopOutsourceMaterials',
                        'BenchtopOutsourceMaterialPrefix',
                    ];
                }
            },
        }),
        // get outsource materials
        getOutsourceMaterials: build.query<
            APIResponse<MaterialOutsourceInterface[]>,
            OutsourceFilter
        >({
            query: (filters) => {
                const filterGroups = [`((is_deleted:equals:0))`];
                if (filters?.priceCategories) {
                    filterGroups.push(
                        `((price_cat_id:either:${filters.priceCategories}))`
                    );
                }
                if (filters?.depth) {
                    filterGroups.push(`((depth:either:${filters.depth}))`);
                }
                if (filters?.formFactors) {
                    filterGroups.push(
                        `((formfactor_id:either:${filters.formFactors}))`
                    );
                }
                if (filters?.doubleSided) {
                    filterGroups.push(
                        `((is_double_sided:either:${filters.doubleSided}))`
                    );
                }
                if (filters?.prefix) {
                    if (filters?.prefix == '0') {
                        // user requesting empty prefixes records
                        filterGroups.push(`((prefix:empty))`);
                    } else {
                        // check if selected options has no prefix (0)
                        const selectedOptions = filters.prefix.split(',');
                        // create new array without empty prefix
                        const selectedOptionsWithoutEmptyPrefix =
                            selectedOptions
                                .filter((option) => option != '0')
                                .join(',');
                        if (selectedOptions.indexOf('0') > -1) {
                            filterGroups.push(
                                `((prefix:empty)OR(prefix:either:${selectedOptionsWithoutEmptyPrefix}))`
                            );
                        } else {
                            // no empty prefix
                            filterGroups.push(
                                `((prefix:either:${selectedOptionsWithoutEmptyPrefix}))`
                            );
                        }
                    }
                }
                if (parseInt(filters?.manufacturer)) {
                    filterGroups.push(
                        `((manufacturer_id:equals:${filters.manufacturer}))`
                    );
                }
                return `benchtop/outsource-materials?filter_groups=${filterGroups.join(
                    'AND'
                )}${
                    filters?.pageSize ? `&page_size=${filters?.pageSize}` : ``
                }${
                    filters?.currentPage
                        ? `&current_page=${filters?.currentPage}`
                        : ``
                }`;
            },
            transformResponse: (responseData: {
                items: MaterialOutsourceInterface[];
                pagination: Pagination;
            }) => {
                return {
                    data: responseData.items.map(
                        (material: MaterialOutsourceInterface) => {
                            return transformOutsourceMaterialData(material);
                        }
                    ),
                    pagination: responseData.pagination,
                };
            },
            providesTags: () => [{type: 'BenchtopOutsourceMaterials'}],
        }),
        // get outsource material thickness
        getOutsourceMaterialThickness: build.query<string[], void>({
            query: () => {
                return `benchtop/outsource-materials/thickness`;
            },
            transformResponse: (responseData: {items: string[]}) => {
                return responseData.items;
            },
            providesTags: () => [
                {type: 'BenchtopOutsourceMaterialThicknesses'},
            ],
        }),
        // get outsource material prefixes
        getOutsourceMaterialPrefixes: build.query<
            TableColumn<MaterialOutsourceInterface>[],
            OutsourceFilter
        >({
            query: (filters) => {
                const filterGroups = [
                    `((is_deleted:equals:0))`,
                    `((prefix:not_empty))`,
                ];
                if (filters?.manufacturer) {
                    filterGroups.push(
                        `((manufacturer_id:equals:${filters.manufacturer}))`
                    );
                }
                return `benchtop/outsource-materials/prefix?filter_groups=(${filterGroups.join(
                    'AND'
                )})`;
            },
            transformResponse: (responseData: {
                items: string[];
                pagination: Pagination;
            }) => {
                return responseData.items?.map((item) => {
                    return {
                        name: item,
                        id: item,
                    };
                });
            },
            providesTags: () => [{type: 'BenchtopOutsourceMaterialPrefix'}],
        }),
        // get outsource material depth
        getOutsourceMaterialDepth: build.query<
            TableColumn<MaterialOutsourceInterface>[],
            void
        >({
            query: () => {
                return `benchtop/outsource-materials/depth`;
            },
            transformResponse: (responseData: {
                items: string[];
                pagination: Pagination;
            }) => {
                return responseData.items?.map((item) => {
                    return {
                        name: item,
                        id: item,
                    };
                });
            },
            providesTags: () => [{type: 'BenchtopOutsourceMaterialDepths'}],
        }),
        // edit outsource material
        editOutsourceMaterial: build.mutation<
            MaterialOutsourceInterface,
            Partial<MaterialOutsourceInterface>
        >({
            query(data) {
                const {name, ...body} = data;
                return {
                    url: `benchtop/outsource-materials`,
                    method: 'PUT',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return [
                        'BenchtopOutsourceMaterials',
                        'BenchtopOutsourceMaterialThicknesses',
                    ];
                }
            },
        }),

        // delete outsource materials
        deleteOutsourceMaterial: build.mutation<
            MaterialOutsourceInterface,
            {
                id: string;
            }
        >({
            query(data) {
                const {name, ...body} = data;
                return {
                    url: `benchtop/outsource-materials/delete`,
                    method: 'POST',
                    body,
                };
            },
            invalidatesTags: (result, error) => {
                if (isEmpty(error)) {
                    return [
                        'BenchtopOutsourceMaterials',
                        'BenchtopOutsourceMaterialThicknesses',
                    ];
                }
            },
        }),
        // get outsource material thickness
        getMaterialThicknesses: build.query<string[], Filter>({
            query: (filters) => {
                const filterGroups = [];
                if (filters?.type?.id) {
                    filterGroups.push(`((type_id:equals:${filters.type.id}))`);
                }
                if (filters?.brand?.id) {
                    filterGroups.push(
                        `((brand_id:equals:${filters.brand.id}))`
                    );
                }
                if (filters?.finish?.id) {
                    filterGroups.push(
                        `((finish_id:equals:${filters.finish.id}))`
                    );
                }
                if (filters?.substrate?.id) {
                    filterGroups.push(
                        `((substrate_id:equals:${filters.substrate.id}))`
                    );
                }

                if (
                    filters?.priceCategory?.id ||
                    isNull(filters?.priceCategory?.id)
                ) {
                    if (!isNull(filters?.priceCategory?.id)) {
                        filterGroups.push(
                            `((price_cat_id:equals:${filters.priceCategory.id}))`
                        );
                    } else {
                        filterGroups.push(`((price_cat_id:empty))`);
                    }
                }

                if (
                    !isNaN(filters?.show?.id) &&
                    typeof filters?.show?.id == 'number'
                ) {
                    if (filters.show.id == 1) {
                        filterGroups.push(
                            `((changes.id:empty)OR(changes.is_hidden:equals:1))`
                        );
                    } else {
                        filterGroups.push(
                            `((changes.is_hidden:equals:${filters.show.id}))`
                        );
                    }
                    if (filters?.manufacturer?.id) {
                        filterGroups.push(
                            `((changes.manufacturer_id:equals:${filters.manufacturer.id}))`
                        );
                    }
                }
                if (!isEmpty(filterGroups)) {
                    filterGroups.push(`((is_deleted:equals:0))`);
                    return `benchtop/materials/thicknesses?filter_groups=(${filterGroups.join(
                        'AND'
                    )})&sort_orders=name ASC`;
                }
                return `benchtop/materials/thicknesses`;
            },
            transformResponse: (
                responseData: {items: MaterialGenericInterface[]},
                _meta,
                args
            ) => {
                if (args.withAllOption) {
                    return [
                        {id: 0, name: 'All', is_deleted: false},
                        ...transformThicknessData(responseData.items),
                    ];
                }
                return transformThicknessData(responseData.items);
            },
            providesTags: () => [
                {type: 'BenchtopOutsourceMaterialThicknesses'},
            ],
        }),
    }),
});

export const {
    useGetMaterialsQuery,
    useLazyGetMaterialsQuery,
    useGetBrandsQuery,
    useAddBrandMutation,
    useEditBrandMutation,
    useAddFinishMutation,
    useGetFinishesQuery,
    useEditFinishMutation,
    useGetFormFactorsQuery,
    useAddFormFactorMutation,
    useEditFormFactorMutation,
    useGetTypesQuery,
    useAddTypeMutation,
    useEditTypeMutation,
    useGetSubstratesQuery,
    useAddSubstrateMutation,
    useEditSubstrateMutation,
    useGetPriceCategoriesQuery,
    useGetPriceCategoriesLinkedToMaterialsQuery,
    useAddPriceCategoryMutation,
    useEditPriceCategoryMutation,
    useEditMaterialMutation,
    useAddMaterialMutation,
    useGetManufacturersQuery,
    useImportMaterialMutation,
    useImportOutsourceMaterialMutation,
    useMassHideShowMutation,
    useResetToDefaultMutation,
    useGetMaterialCountOnResetToDefaultQuery,
    useMassCostEditorMutation,
    useGetOutsourceMaterialThicknessQuery,
    useGetOutsourceMaterialDepthQuery,
    useGetOutsourceMaterialPrefixesQuery,
    useGetOutsourceMaterialsQuery,
    useLazyGetOutsourceMaterialsQuery,
    useEditOutsourceMaterialMutation,
    useDeleteOutsourceMaterialMutation,
    useGetMaterialThicknessesQuery,
} = benchtopApi;
