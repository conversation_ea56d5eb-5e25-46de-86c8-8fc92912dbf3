import React, {useEffect, useState} from 'react';
import {<PERSON>ton, Image} from 'react-bootstrap';
import {AddButton, Spinner} from 'assets';
import DataTable from 'react-data-table-component';
import styled from 'styled-components';
import {
    MaterialGenericInterface,
    useAddFinishMutation,
    useGetFinishesQuery,
    useEditFinishMutation,
} from './store/benchtopModuleApi';
import {Columns} from './shared/Columns';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {AddNewButton} from './Material';
const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
        },
    },
};

const StyledField = styled.input`
    padding: 10px;
    width: 100%;
    border-radius: 5px;
    border: 2px solid;
    border-color: #000 !important;
    box-shadow: none !important;
    font-weight: normal !important;
`;

const Finish = ({setShowPrompt}) => {
    const [showFinishForm, setShowFinishForm] = useState<boolean>(false);
    const [deleteNotice, setDeleteNotice] = useState<string>('');
    const [finish, setFinish] = useState<MaterialGenericInterface>({
        name: '',
        is_deleted: false,
    });
    const cols = Columns(
        [
            {
                name: 'Name',
                selector: (row) => row.name,
                center: true,
            },
        ],
        (row: MaterialGenericInterface) => editButton(row),
        window?.isSuperAdmin
            ? (row: MaterialGenericInterface) => {
                  setDeleteNotice(`Are you sure you want to perform this action on the selected
                  item? This will also delete ${row.material_count} material${
                      parseInt(row.material_count) > 1 ? `s` : ``
                  } linked to this finish.`);
                  setFinish(row);
              }
            : null
    );
    const [finishName, setFinishName] = useState<string>('');

    const dispatch = useAppDispatch();

    const {
        data: finishes = [],
        isLoading: loadAll,
        isFetching: fetchAll,
    } = useGetFinishesQuery({withAllOption: false});

    const [
        addFinish,
        {
            isLoading: loadAdd,
            error: addError,
            isLoading: addHappening,
            reset: addReset,
        },
    ] = useAddFinishMutation();
    const [
        editFinish,
        {
            isLoading: loadEdit,
            error: editError,
            isLoading: editHappening,
            reset: editReset,
        },
    ] = useEditFinishMutation();

    const saveButton = async () => {
        const data = {...finish, ...{name: finishName}};

        if (finish?.id) {
            try {
                await editFinish(data).unwrap();
                dispatch(
                    addNotification(
                        `Finish edited Successfully`,
                        MessageVariant.SUCCESS,
                        `benchtopFinish`
                    )
                );
            } catch (e) {
                throw e;
            }
        } else {
            try {
                await addFinish(data).unwrap();
                dispatch(
                    addNotification(
                        `Finish added Successfully`,
                        MessageVariant.SUCCESS,
                        `benchtopFinish`
                    )
                );
            } catch (e) {
                throw e;
            }
        }

        setFinishName('');
        setShowFinishForm(!showFinishForm);
    };

    // Edit button click handler
    const editButton = (row: MaterialGenericInterface) => {
        setFinish(row);
        setFinishName(row?.name);
        setShowFinishForm(true);
    };

    // Delete click handler
    const deleteButton = async () => {
        const data = {...finish, ...{is_deleted: true}};
        try {
            setDeleteNotice('');
            await editFinish(data).unwrap();
            dispatch(
                addNotification(
                    `Finish deleted Successfully`,
                    MessageVariant.SUCCESS,
                    `benchtopFinish`
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addError)) {
            dispatch(
                addNotification(
                    addError.data.error,
                    MessageVariant.ERROR,
                    `benchtopFinish`
                )
            );
            addReset();
        }
        if (!isEmpty(editError)) {
            dispatch(
                addNotification(
                    editError.data.error,
                    MessageVariant.ERROR,
                    `benchtopFinish`
                )
            );
            editReset();
        }
    }, [addError, editError]);

    if (!isEmpty(deleteNotice)) {
        return (
            <>
                <h2>{deleteNotice}</h2>
                <AddNewButton
                    onClick={deleteButton}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Yes
                </AddNewButton>
                <AddNewButton
                    onClick={() => setDeleteNotice('')}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    No
                </AddNewButton>
            </>
        );
    }

    return (
        <>
            <Notification filterType="benchtopFinish" />
            <Button
                onClick={() => {
                    setShowPrompt(false);
                    // reset states
                    setFinish({
                        name: '',
                        is_deleted: false,
                    });
                    setShowFinishForm(false);
                    // remove all notifications of this type
                    dispatch(deleteAllNotificationsOfType('benchtopFinish'));
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {showFinishForm ? (
                <>
                    <h2>{finish?.id ? 'Edit' : 'New'} Finish</h2>
                    <div>
                        <StyledField
                            type="text"
                            placeholder={`Enter Finish`}
                            onChange={(e) => setFinishName(e.target.value)}
                            value={finishName}
                        />
                        {addHappening || editHappening ? (
                            <Image
                                className="icon_button"
                                style={{
                                    backgroundColor: 'transparent',
                                }}
                                src={`data:image/svg+xml;base64, ${btoa(
                                    Spinner
                                )}`}
                            />
                        ) : (
                            <AddNewButton
                                disabled={addHappening || editHappening}
                                style={{
                                    padding: '0 21px',
                                    margin: '6px 3px',
                                }}
                                onClick={saveButton}>
                                Save
                            </AddNewButton>
                        )}

                        <AddNewButton
                            style={{
                                padding: '0 21px',
                                margin: '6px 3px',
                            }}
                            onClick={() => {
                                setFinishName('');
                                setShowFinishForm(false);
                            }}>
                            Cancel
                        </AddNewButton>
                    </div>
                </>
            ) : (
                <>
                    <AddNewButton
                        onClick={() => {
                            setFinish({
                                name: '',
                                is_deleted: false,
                            });
                            setShowFinishForm(true);
                        }}>
                        <Image
                            className="icon_button"
                            style={{
                                backgroundColor: 'transparent',
                            }}
                            src={`data:image/svg+xml;base64, ${btoa(
                                AddButton
                            )}`}
                        />
                        Add New Finish
                    </AddNewButton>
                    <DataTable
                        columns={cols}
                        data={finishes}
                        striped={true}
                        highlightOnHover={true}
                        dense={true}
                        customStyles={customStyles}
                        progressPending={
                            loadAll || fetchAll || loadAdd || loadEdit
                        }
                    />
                </>
            )}
        </>
    );
};

export default Finish;
