import React, {useEffect, useState} from 'react';
import {Button, Image} from 'react-bootstrap';
import {AddB<PERSON>on, Spinner} from 'assets';
import DataTable from 'react-data-table-component';
import styled from 'styled-components';
import {
    MaterialGenericInterface,
    useAddSubstrateMutation,
    useGetSubstratesQuery,
    useEditSubstrateMutation,
} from './store/benchtopModuleApi';
import {Columns} from './shared/Columns';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {AddNewButton} from './Material';
const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
        },
    },
};

const StyledField = styled.input`
    padding: 10px;
    width: 100%;
    border-radius: 5px;
    border: 2px solid;
    border-color: #000 !important;
    box-shadow: none !important;
    font-weight: normal !important;
`;

const Substrate = ({setShowPrompt}) => {
    const [showSubstrateForm, setShowSubstrateForm] = useState<boolean>(false);
    const [deleteNotice, setDeleteNotice] = useState<string>('');
    const [substrate, setSubstrate] = useState<MaterialGenericInterface>({
        name: '',
        is_deleted: false,
    });
    const cols = Columns(
        [
            {
                name: 'Name',
                selector: (row) => row.name,
                center: true,
            },
        ],
        (row: MaterialGenericInterface) => editButton(row),
        window?.isSuperAdmin
            ? (row: MaterialGenericInterface) => {
                  setDeleteNotice(`Are you sure you want to perform this action on the selected
                  item? This will also delete ${row.material_count} material${
                      parseInt(row.material_count) > 1 ? `s` : ``
                  } linked to this substrate.`);
                  setSubstrate(row);
              }
            : null
    );
    const [substrateName, setSubstrateName] = useState<string>('');

    const dispatch = useAppDispatch();

    const {
        data: substrates = [],
        isLoading: loadAll,
        isFetching: fetchAll,
    } = useGetSubstratesQuery({withAllOption: false});

    const [
        addSubstrate,
        {
            isLoading: loadAdd,
            error: addError,
            isLoading: addHappening,
            reset: addReset,
        },
    ] = useAddSubstrateMutation();
    const [
        editSubstrate,
        {
            isLoading: loadEdit,
            error: editError,
            isLoading: editHappening,
            reset: editReset,
        },
    ] = useEditSubstrateMutation();

    const saveButton = async () => {
        const data = {...substrate, ...{name: substrateName}};

        if (substrate?.id) {
            try {
                await editSubstrate(data).unwrap();
                dispatch(
                    addNotification(
                        `Substrate edited Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopSubstrate'
                    )
                );
            } catch (e) {
                throw e;
            }
        } else {
            try {
                await addSubstrate(data).unwrap();
                dispatch(
                    addNotification(
                        `Substrate added Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopSubstrate'
                    )
                );
            } catch (e) {
                throw e;
            }
        }

        setSubstrateName('');
        setShowSubstrateForm(!showSubstrateForm);
    };

    // Edit button click handler
    const editButton = (row: MaterialGenericInterface) => {
        setSubstrate(row);
        setSubstrateName(row?.name);
        setShowSubstrateForm(true);
    };

    // Delete click handler
    const deleteButton = async () => {
        const data = {...substrate, ...{is_deleted: true}};
        try {
            setDeleteNotice('');
            await editSubstrate(data).unwrap();
            dispatch(
                addNotification(
                    `Substrate deleted Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopSubstrate'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addError)) {
            dispatch(
                addNotification(
                    addError.data.error,
                    MessageVariant.ERROR,
                    'benchtopSubstrate'
                )
            );
            addReset();
        }
        if (!isEmpty(editError)) {
            dispatch(
                addNotification(
                    editError.data.error,
                    MessageVariant.ERROR,
                    'benchtopSubstrate'
                )
            );
            editReset();
        }
    }, [addError, editError]);

    if (!isEmpty(deleteNotice)) {
        return (
            <>
                <h2>{deleteNotice}</h2>
                <AddNewButton
                    onClick={deleteButton}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Yes
                </AddNewButton>
                <AddNewButton
                    onClick={() => setDeleteNotice('')}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    No
                </AddNewButton>
            </>
        );
    }

    return (
        <>
            <Notification filterType="benchtopSubstrate" />
            <Button
                onClick={() => {
                    setShowPrompt(false);
                    // reset states
                    setSubstrate({
                        name: '',
                        is_deleted: false,
                    });
                    setShowSubstrateForm(false);
                    // remove all notifications of this type
                    dispatch(deleteAllNotificationsOfType('benchtopSubstrate'));
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {showSubstrateForm ? (
                <>
                    <h2>{substrate?.id ? 'Edit' : 'New'} Substrate</h2>
                    <div>
                        <StyledField
                            type="text"
                            placeholder={`Enter Substrate`}
                            onChange={(e) => setSubstrateName(e.target.value)}
                            value={substrateName}
                        />
                        {addHappening || editHappening ? (
                            <Image
                                className="icon_button"
                                style={{
                                    backgroundColor: 'transparent',
                                }}
                                src={`data:image/svg+xml;base64, ${btoa(
                                    Spinner
                                )}`}
                            />
                        ) : (
                            <AddNewButton
                                disabled={addHappening || editHappening}
                                style={{
                                    padding: '0 21px',
                                    margin: '6px 3px',
                                }}
                                onClick={saveButton}>
                                Save
                            </AddNewButton>
                        )}

                        <AddNewButton
                            style={{
                                padding: '0 21px',
                                margin: '6px 3px',
                            }}
                            onClick={() => {
                                setSubstrateName('');
                                setShowSubstrateForm(false);
                            }}>
                            Cancel
                        </AddNewButton>
                    </div>
                </>
            ) : (
                <>
                    <AddNewButton
                        onClick={() => {
                            setSubstrate({
                                name: '',
                                is_deleted: false,
                            });
                            setShowSubstrateForm(true);
                        }}>
                        <Image
                            className="icon_button"
                            style={{
                                backgroundColor: 'transparent',
                            }}
                            src={`data:image/svg+xml;base64, ${btoa(
                                AddButton
                            )}`}
                        />
                        Add New Substrate
                    </AddNewButton>
                    <DataTable
                        columns={cols}
                        data={substrates}
                        striped={true}
                        highlightOnHover={true}
                        dense={true}
                        customStyles={customStyles}
                        progressPending={
                            loadAll || fetchAll || loadAdd || loadEdit
                        }
                    />
                </>
            )}
        </>
    );
};

export default Substrate;
