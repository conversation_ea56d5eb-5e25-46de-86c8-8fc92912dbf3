import React, {useCallback} from 'react';
import Notification from 'components/manufacturer/shared/Notification';
import DataTable, {TableColumn} from 'react-data-table-component';
import {
    EndOptionInterface,
    useGetBenchtopEndOptionQuery,
} from 'components/admin/BenchtopModule/store/benchtopModuleEndOptionApi';
import {ConfirmationButton} from 'shared/ConfirmationButton';
import {Edit} from 'assets';
import EditEndOption from 'components/admin/BenchtopModule/EndOption/EditEndOption';

const BenchtopEndOption = () => {
    const {
        data: endOptions,
        isLoading,
        isFetching,
    } = useGetBenchtopEndOptionQuery();

    const emptyFunction = useCallback(() => {
        return;
    }, []);
    const cols: TableColumn<EndOptionInterface>[] = [
        {
            name: 'ID',
            selector: (row) => row.id,
            compact: true,
            wrap: true,
            center: true,
        },

        {
            name: 'Name',
            selector: (row) => row.name,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Radius start',
            selector: (row) => row.default_radius_start,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: '<PERSON>dius End',
            selector: (row) => row.default_radius_end,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Locked',
            selector: (row) => (row.is_locked ? 'Yes' : 'No'),
            center: true,
            compact: true,
            wrap: true,
        },
        {
            name: 'Arc',
            selector: (row) => (row.is_arc ? 'Yes' : 'No'),
            center: true,
            compact: true,
            wrap: true,
        },
        {
            name: 'Action',
            cell: (row) => [
                <ConfirmationButton
                    key="edit"
                    onClick={emptyFunction}
                    classes="x-center small-6 medium-5 large-5"
                    title="Edit Option"
                    message=""
                    displayCloseButton={true}
                    options={{hideFooter: true}}
                    image={Edit as string}>
                    {({setShow}) => (
                        <EditEndOption
                            endOption={row}
                            // this component does some funky stuff
                            // eslint-disable-next-line react/jsx-no-bind
                            hideDialog={() => setShow(false)}
                        />
                    )}
                </ConfirmationButton>,
            ],
            center: true,
        },
    ];
    return (
        <>
            <Notification filterType="benchtopEndoption" />
            <div className="floating_item_body" style={{margin: '10px 0px'}}>
                <h1 className="form_header" style={{display: 'inline'}}>
                    Edge Profiles End Options
                </h1>
                <hr className="form_horizontal_rule" />
                <DataTable
                    columns={cols}
                    data={endOptions}
                    progressPending={isLoading || isFetching}
                    striped={true}
                    highlightOnHover={true}
                />
            </div>
        </>
    );
};

export default BenchtopEndOption;
