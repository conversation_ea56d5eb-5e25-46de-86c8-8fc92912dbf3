import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import store from 'store/dashboard';
import BenchtopEndOption from 'components/admin/BenchtopModule/EndOption';
import ThemeProvider from 'theme/DashboardTheme';

const benchtopEndOptionRenderer = (element: HTMLElement) => {
    const root = createRoot(element);

    root.render(
        <Provider store={store}>
            <ThemeProvider>
                <BenchtopEndOption />
            </ThemeProvider>
        </Provider>
    );
};

export default benchtopEndOptionRenderer;
