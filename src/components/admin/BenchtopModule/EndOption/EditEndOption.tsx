import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
    EndOptionInterface,
    useEditEndOptionMutation,
} from 'components/admin/BenchtopModule/store/benchtopModuleEndOptionApi';
import {isEmpty, cloneDeep} from 'lodash';
import {Form, Formik, FormikHelpers, FormikProps, FormikValues} from 'formik';
import {useAppDispatch} from 'store/customer';

import {
    FormLabel,
    StyledFieldWithFullWidth,
} from 'components/manufacturer/shared/TextField';
import {StyledRow} from 'components/admin/BenchtopModule/EdgeProfiles/AddEditEdgeProfile';
import Slider from 'components/manufacturer/shared/slider/Slider';
import {
    Element,
    SubmitButton,
} from 'components/manufacturer/Coupon/AddEditCoupon';
import {
    MessageVariant,
    addNotification,
} from 'components/manufacturer/shared/NotificationSlice';
import styled from 'styled-components';

const FlexSlider = styled.div`
    display: flex;
    align-items: flex-start;
`;

const InLineDiv = styled.div`
    display: flex;
    justify-content: space-between;

    .textField {
        width: 90%;
    }
`;
const EditEndOption = ({
    hideDialog,
    endOption,
}: {
    hideDialog: () => void;
    endOption?: EndOptionInterface;
}) => {
    const dispatch = useAppDispatch();

    const [editEndOption, {error: endOptionError}] = useEditEndOptionMutation();
    const formikRef = useRef<FormikProps<FormikValues>>(null);
    const [error, setError] = useState('');
    const [initialValues, setInitialValues] = useState<EndOptionInterface>({
        name: '',
        parent_id: null,
        default_radius_start: 0,
        default_radius_end: 0,
        is_locked: false,
        is_arc: false,
    });

    useEffect(() => {
        if (!isEmpty(endOption)) {
            setInitialValues((values) => ({
                ...values,
                ...cloneDeep(endOption),
            }));
        }
    }, [endOption]);

    // cleanup reference
    useEffect(() => {
        return () => {
            formikRef.current = null;
        };
    }, []);

    const setLock = useCallback(() => {
        const {values, setFieldValue} = formikRef.current;

        void (async function () {
            await setFieldValue('is_locked', !values.is_locked);
        })();
    }, []);

    const setArc = useCallback(() => {
        const {values, setFieldValue} = formikRef.current;

        void (async function () {
            await setFieldValue('is_arc', !values.is_arc);
        })();
    }, []);

    const cancelClick = useCallback(() => {
        const {resetForm} = formikRef.current;
        resetForm();
        hideDialog();
    }, []);

    const submitForm = async (values: EndOptionInterface) => {
        await editEndOption({
            ...values,
            default_radius_end: !values.is_arc
                ? values.default_radius_end
                : null,
        }).unwrap();
    };

    const submit = useCallback(
        async (
            values: EndOptionInterface,
            actions: FormikHelpers<EndOptionInterface>
        ) => {
            try {
                await submitForm(values);
                hideDialog();
                actions.setSubmitting(false);
                actions.resetForm();
                dispatch(
                    addNotification(
                        `End option Edited Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopEndoption'
                    )
                );
            } catch (e) {}
        },
        []
    );

    useEffect(() => {
        if (endOptionError) {
            if ('status' in endOptionError) {
                const errMsg: {
                    error?: string;
                } =
                    'error' in endOptionError
                        ? endOptionError
                        : endOptionError.data;
                setError(errMsg.error);
            } else {
                setError(endOptionError.message);
            }
        }
    }, [endOptionError]);

    const removeError = useCallback(() => {
        return setError('');
    }, []);

    return (
        <>
            {error ? (
                <div className="error" onClick={removeError}>
                    {error}
                </div>
            ) : null}
            <Formik
                innerRef={formikRef}
                initialValues={initialValues}
                enableReinitialize={true}
                onSubmit={submit}>
                {(props: FormikProps<EndOptionInterface>) => (
                    <Form>
                        <StyledRow className="row">
                            <InLineDiv className="column small-12">
                                <FormLabel label="Name" />
                                <StyledFieldWithFullWidth
                                    name="name"
                                    className="textField"
                                />
                            </InLineDiv>
                        </StyledRow>
                        <StyledRow className="row">
                            <FlexSlider className="column small-6">
                                <FormLabel label="Lock defaults" />
                                <Slider
                                    value={props.values.is_locked}
                                    handleChange={setLock}
                                    isLoading={false}
                                    AvailableText={``}
                                    HiddenText={``}
                                />
                            </FlexSlider>
                            <FlexSlider className="column small-6">
                                <FormLabel label="Arc defaults" />
                                <Slider
                                    value={props.values.is_arc}
                                    handleChange={setArc}
                                    isLoading={false}
                                    AvailableText={``}
                                    HiddenText={``}
                                />
                            </FlexSlider>
                        </StyledRow>

                        <StyledRow className="row">
                            <InLineDiv className="column small-6">
                                <FormLabel label="Radius Start" />
                                <StyledFieldWithFullWidth
                                    name="default_radius_start"
                                    className="textField"
                                />
                            </InLineDiv>
                            {!props.values.is_arc ? (
                                <InLineDiv className="column small-6">
                                    <FormLabel label="Radius End" />
                                    <StyledFieldWithFullWidth
                                        name="default_radius_end"
                                        className="textField"
                                    />
                                </InLineDiv>
                            ) : null}
                        </StyledRow>
                        <hr />
                        <Element align={'justify-content: space-between;'}>
                            <SubmitButton
                                type="button"
                                className="form_50_button_left "
                                onClick={cancelClick}>
                                Cancel
                            </SubmitButton>
                            <SubmitButton
                                type="submit"
                                disabled={props.isSubmitting}
                                className="form_50_button_right">
                                Save
                            </SubmitButton>
                        </Element>
                    </Form>
                )}
            </Formik>
        </>
    );
};

export default EditEndOption;
