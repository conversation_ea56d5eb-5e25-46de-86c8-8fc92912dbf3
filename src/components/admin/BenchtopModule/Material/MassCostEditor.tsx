import React, {useCallback, useEffect, useState} from 'react';
import {
    Filter,
    MaterialInterface,
    useGetMaterialsQuery,
    useMassCostEditorMutation,
} from '../store/benchtopModuleApi';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {capitalize, eq, identity, isNull, isNumber, pickBy} from 'lodash';
import {Button, Row} from 'react-bootstrap';
import {AddNewButton} from '.';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';

const MassCostEditor = ({
    hideDialog,
    materialFilter,
    onPageMaterials = [],
}: {
    hideDialog: () => void;
    materialFilter: Filter;
    onPageMaterials: MaterialInterface[];
}) => {
    const [filters, setFilters] = useState({
        ...materialFilter,
        ...{
            area_handling_cost: false,
            area_installation_cost: false,
            area_cost: false,
            calcType: {id: 1, name: 'Percent'},
        },
    });
    const [onPageOnly, setOnPageonly] = useState(false);
    const [count, setCount] = useState(onPageMaterials.length);

    const {
        data: {data, pagination} = {
            data: [],
            pagination: {total_count: 0},
        },
    } = useGetMaterialsQuery(materialFilter);

    const [massCostEditor, {isLoading: massCostEditorLoading}] =
        useMassCostEditorMutation();

    useEffect(() => {
        onPageOnly
            ? setCount(onPageMaterials.length)
            : setCount(pagination.total_count);
    }, [onPageMaterials, onPageOnly]);

    const massCost = async () => {
        if (
            !filters?.area_handling_cost &&
            !filters?.area_cost &&
            !filters?.area_installation_cost
        ) {
            dispatch(
                addNotification(
                    `Please check atleast one of the cost checkboxes to run this action`,
                    MessageVariant.ERROR,
                    'benchtopMassCostEditor'
                )
            );
            return;
        }

        if (!isNumber(filters?.adjustValue)) {
            dispatch(
                addNotification(
                    `Value should be a valid number`,
                    MessageVariant.ERROR,
                    'benchtopMassCostEditor'
                )
            );
            return;
        }
        const params = pickBy(
            {
                name: filters?.name,
                type_id: filters?.type?.id,
                brand_id: filters?.brand?.id,
                finish_id: filters?.finish?.id,
                substrate_id: filters?.substrate?.id,
                thickness: filters?.thickness?.id,
                adjust_materials_amount: filters?.adjustValue,
                calc_type: filters?.calcType.id,
            },
            identity
        );

        if (filters?.priceCategory?.id || isNull(filters?.priceCategory?.id)) {
            params.price_cat_id = filters?.priceCategory?.id;
        }

        if (onPageOnly) {
            params.materialIds = onPageMaterials
                .map((material) => material.id)
                .join(',');
        }

        if (!isNaN(filters?.show?.id) && typeof filters?.show?.id == 'number') {
            params.hiddenStatus = filters?.show?.id;
        }

        try {
            await massCostEditor({
                ...params,
                area_handling_cost: filters?.area_handling_cost,
                area_cost: filters?.area_cost,
                area_installation_cost: filters?.area_installation_cost,
                // hiddenStatus: filters?.show?.id,
            }).unwrap();
            // remove all notifications of this type
            dispatch(deleteAllNotificationsOfType('benchtopMassCostEditor'));
            hideDialog();
            dispatch(
                addNotification(
                    `Materials updated Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopMaterial'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    const closePopup = useCallback(() => {
        hideDialog();
        // remove all notifications of this type
        dispatch(deleteAllNotificationsOfType('benchtopMassCostEditor'));
    }, [hideDialog]);

    const dispatch = useAppDispatch();

    return (
        <>
            <Notification filterType="benchtopMassCostEditor" />
            <h2>
                <Row
                    className="row"
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        flexWrap: 'nowrap',
                    }}>
                    <div className="column">
                        <p>
                            You are about to apply mass changes to your
                            materials.
                        </p>
                        <p>The changes being made to:</p>
                    </div>
                    <div className="column" style={{textAlign: 'right'}}>
                        <Button onClick={closePopup}>X</Button>
                    </div>
                </Row>
                <Row className="row">
                    <div className="column  small-6">
                        {filters?.name ? (
                            <p>Name : {capitalize(filters.name)}</p>
                        ) : null}
                        {filters?.type ? (
                            <p>Type : {capitalize(filters.type.name)}</p>
                        ) : null}
                        {filters?.brand ? (
                            <p>Brand : {capitalize(filters.brand.name)}</p>
                        ) : null}
                        {filters?.finish ? (
                            <p>Finish : {capitalize(filters.finish.name)}</p>
                        ) : null}
                        {filters?.substrate ? (
                            <p>
                                Substrate : {capitalize(filters.substrate.name)}
                            </p>
                        ) : null}
                        {filters?.priceCategory ? (
                            <p>
                                Price Category :{' '}
                                {filters.priceCategory.name
                                    ? capitalize(filters.priceCategory.name)
                                    : 'No Price Category'}
                            </p>
                        ) : null}
                        {filters?.thickness ? (
                            <p>Thickness : {filters.thickness.name}</p>
                        ) : null}
                        {filters?.show ? (
                            <p>Show : {capitalize(filters.show.name)}</p>
                        ) : null}
                    </div>
                    <div className="column  small-6">
                        <p
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                            }}>
                            <b>Materials:</b>
                            <select
                                onChange={(e) => {
                                    if (e.target.value == '0') {
                                        setOnPageonly(false);
                                    } else {
                                        setOnPageonly(true);
                                    }
                                }}
                                style={{
                                    padding: '0px 16px 0px 7px',
                                    margin: '5px',
                                    width: '50%',
                                }}
                                name="filter_page"
                                id="filter_page">
                                <option value="0">All</option>
                                <option value="1">On Page Only</option>
                            </select>
                        </p>
                        <p
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                            }}>
                            <b>Adjust:</b>
                            <select
                                onChange={(e) =>
                                    setFilters((state) => ({
                                        ...state,
                                        calcType: {
                                            id: parseInt(e.target.value),
                                            name: e.target.options[
                                                e.target.selectedIndex
                                            ].text,
                                        },
                                    }))
                                }
                                style={{
                                    padding: '0 16px 0 7px',
                                    margin: '5px',
                                    width: '50%',
                                }}
                                name="filter_adjust"
                                id="filter_adjust">
                                <option value="1">Percent</option>
                                <option value="2">Fixed</option>
                            </select>
                        </p>
                        <p
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                            }}>
                            <b>Value:</b>
                            <input
                                type="text"
                                name="filter_adjust_value"
                                style={{
                                    padding: '4px 3px',
                                    boxShadow: 'none',
                                }}
                                onChange={(e) =>
                                    setFilters((state) => ({
                                        ...state,
                                        adjustValue: parseFloat(e.target.value),
                                    }))
                                }
                            />
                        </p>
                        <p
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                            }}>
                            {[
                                {title: 'AHC', name: 'area_handling_cost'},
                                {title: 'AIC', name: 'area_installation_cost'},
                                {title: 'AC', name: 'area_cost'},
                            ].map((cost, i) => {
                                return (
                                    <div key={i} style={{display: 'flex'}}>
                                        <b>{cost.title}</b>
                                        <input
                                            type="checkbox"
                                            style={{margin: '0px 10px'}}
                                            name={cost.name}
                                            onChange={(e) =>
                                                setFilters((state) => ({
                                                    ...state,
                                                    [cost.name]:
                                                        e.target.checked,
                                                }))
                                            }
                                        />
                                    </div>
                                );
                            })}
                        </p>
                    </div>
                </Row>
            </h2>
            <span style={{color: 'red'}}>
                * NB/ When adjusting by a percentage, '20' will raise the prices
                by 20% and '-10' will lower the prices by 10%
            </span>

            <h5>You are about to update {count} materials.</h5>
            {count > 0 ? (
                <AddNewButton
                    disabled={massCostEditorLoading}
                    onClick={massCost}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Adjust
                </AddNewButton>
            ) : (
                <></>
            )}
        </>
    );
};

export default MassCostEditor;
