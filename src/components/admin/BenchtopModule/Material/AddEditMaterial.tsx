import {
    Element,
    SubmitButton,
} from 'components/manufacturer/Coupon/AddEditCoupon';
import Bucket from 'components/manufacturer/shared/Bucket';
import {
    addNotification,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import TextField, {FormLabel} from 'components/manufacturer/shared/TextField';
import {Form, Formik, FormikProps} from 'formik';
import {isEmpty} from 'lodash';
import React, {useEffect, useState} from 'react';
import {Row} from 'react-bootstrap';
import {useAppDispatch} from 'store/dashboard';
import {List} from '../shared/List';
import {useDebounce} from 'components/customer/Materials/helper';
import {
    MaterialInterface,
    useAddMaterialMutation,
    useEditMaterialMutation,
    useGetBrandsQuery,
    useGetFinishesQuery,
    useGetFormFactorsQuery,
    useGetManufacturersQuery,
    useGetPriceCategoriesQuery,
    useGetSubstratesQuery,
    useGetTypesQuery,
} from '../store/benchtopModuleApi';
import SearchBarWithDropDown from 'components/manufacturer/shared/SearchBarWithDropDown';
import ImageComponent from 'components/manufacturer/shared/images/Image';
import {StyledHr, StyledRow} from '../EdgeProfiles/AddEditEdgeProfile';

const AddEditMaterial = ({
    hideDialog,
    material,
}: {
    hideDialog: () => void;
    material?: MaterialInterface;
}) => {
    const {
        data: types = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetTypesQuery({withAllOption: false});
    const {
        data: brands = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetBrandsQuery({withAllOption: false});
    const {
        data: finishes = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetFinishesQuery({withAllOption: false});
    const {
        data: formfactors = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetFormFactorsQuery({withAllOption: false});
    const {
        data: substrates = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetSubstratesQuery({withAllOption: false});
    const {
        data: priceCategories = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetPriceCategoriesQuery({
        withAllOption: false,
        withEmptyOption: true,
    });

    const [addMaterial, {error: addMaterialError}] = useAddMaterialMutation();
    const [editMaterial, {error: editMaterialError}] =
        useEditMaterialMutation();
    const [error, setError] = useState([]);
    const dispatch = useAppDispatch();

    const [keywordsLocal, setKeywordsLocal] = useState<string>('');

    const [isSuccess, setIsSuccess] = useState<boolean>(false);
    const [manufacturersData, setManufacturersData] = useState<
        {id: number; value: string}[]
    >([]);

    const [isManufacturer, setIsManufacturer] = useState<boolean>(false);
    const {
        data: manufacturers = [],
        isLoading: loadingManufacturer,
        isFetching: fetchingManufacturer,
        error: manufacturerLoadingError,
    } = useGetManufacturersQuery({
        withExtraOptions: {
            withAllOption: false,
            WithNoManufacturerOption: true,
        },
    });
    const keywords = useDebounce(keywordsLocal, 1000);

    useEffect(() => {
        if (manufacturerLoadingError) {
            // only admin has access to get manufacturers route
            const errorCode =
                'status' in manufacturerLoadingError &&
                manufacturerLoadingError.status;
            if (errorCode == 401) {
                // unauthorized access
                setIsManufacturer(true);
            }
        }
    }, [manufacturerLoadingError]);

    // find from rtk query data
    useEffect(() => {
        if (!isEmpty(keywords)) {
            setManufacturersData(
                manufacturers.filter((manufacturer) =>
                    manufacturer.value
                        .toLowerCase()
                        .includes(keywords.toLowerCase())
                )
            );
            setIsSuccess(true);
        }
    }, [keywords]);

    let initialValues: MaterialInterface = {
        name: '',
        item_code: null,
        type_id: -1,
        brand_id: -1,
        finish_id: -1,
        substrate_id: -1,
        formfactor_ids: [],
        price_cat_id: -1,
        image_id: null,
        thickness: 0,
        round_up: 0,
        is_grained: 1,
        is_blank: 1,
        is_double_sided: 1,
        allow_butt_join: 0,
        max_butt_join_piece: 2,
        area_handling_cost: 0,
        area_cost: 0,
        area_installation_cost: 0,
        min_job_area: 0,
        measurement: 'M2',
        max_length: 0,
        max_depth: 0,
        is_deleted: 0,
        manufacturers: [],
    };
    // populate data when material provided
    if (!isEmpty(material)) {
        let areaHandlingCost = material?.area_handling_cost
            ? material?.area_handling_cost
            : 0;
        let areaCost = material?.area_cost ? material?.area_cost : 0;
        let areaInstallationCost = material?.area_installation_cost
            ? material?.area_installation_cost
            : 0;
        let minJobArea = material?.min_job_area;
        let roundUp = material?.round_up;
        let measurement = material?.measurement;
        let length = material?.max_length;
        let depth = material?.max_depth;
        let allowButtJoin = material?.allow_butt_join;
        let maxButtJoinPiece = allowButtJoin
            ? material?.max_butt_join_piece
            : initialValues.max_butt_join_piece;

        if (material?.change?.actualChangesHappen) {
            areaHandlingCost = material?.change?.area_handling_cost
                ? material?.change?.area_handling_cost
                : 0;
            areaCost = material?.change?.area_cost
                ? material?.change?.area_cost
                : 0;
            areaInstallationCost = material?.change?.area_installation_cost
                ? material?.change?.area_installation_cost
                : 0;
            minJobArea = material?.change?.min_job_area;
            roundUp = material?.change?.round_up;
            measurement = material?.change?.measurement;
            length = material?.change?.max_length;
            depth = material?.change?.max_depth;
            allowButtJoin = material?.change?.allow_butt_join;
            maxButtJoinPiece = material?.change?.max_butt_join_piece;
        }

        initialValues = {
            ...material,
            formfactor_ids: material.formfactor_ids,
            item_code: material?.change?.item_code
                ? material?.change?.item_code
                : material?.item_code,
            area_cost: areaCost,
            area_installation_cost: areaInstallationCost,
            area_handling_cost: areaHandlingCost,
            min_job_area: minJobArea,
            round_up: roundUp,
            measurement: measurement,
            max_length: length,
            max_depth: depth,
            allow_butt_join: Number(allowButtJoin),
            max_butt_join_piece: maxButtJoinPiece,
            manufacturers:
                manufacturers.length && !isEmpty(material.manufacturers)
                    ? manufacturers.filter((manufacturer) =>
                          material.manufacturers.includes(manufacturer.id)
                      )
                    : [],
        };
    }

    const submitForm = async (values: MaterialInterface) => {
        // sanitize values
        const manufacturerData = JSON.stringify(
            values.manufacturers.map((manufacturer) => manufacturer.id)
        );

        const materialData: MaterialInterface = {
            name: values.name,
            item_code: values.item_code,
            type_id: values.type_id,
            brand_id: values.brand_id,
            finish_id: values.finish_id,
            substrate_id: values.substrate_id,
            thickness: values.thickness,
            formfactor_ids: JSON.stringify(values.formfactor_ids),
            price_cat_id: values.price_cat_id,
            image_id: values.image ? parseInt(values.image[0].id) : null,
            area_cost: values.area_cost,
            area_handling_cost: values.area_handling_cost,
            area_installation_cost: values.area_installation_cost,
            max_depth: values.max_depth,
            max_length: values.max_length,
            min_job_area: values.min_job_area,
            round_up: values.round_up,
            is_blank: values.is_blank,
            is_double_sided: values.is_double_sided,
            is_grained: values.is_grained,
            measurement: values.measurement,
            is_deleted: values?.is_deleted ? values.is_deleted : 0,
            manufacturers: manufacturerData,
            allow_butt_join: Number(values.allow_butt_join),
            max_butt_join_piece: Number(values.allow_butt_join)
                ? values.max_butt_join_piece
                : null,
        };

        if (!isEmpty(material)) {
            materialData.id = material.id;
        }
        try {
            if (isEmpty(material)) {
                await addMaterial(materialData).unwrap();
            } else {
                await editMaterial(materialData).unwrap();
            }
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addMaterialError)) {
            setError(addMaterialError.data.error);
        }
        if (!isEmpty(editMaterialError)) {
            setError(editMaterialError.data.error);
        }
    }, [addMaterialError, editMaterialError]);

    return (
        <div>
            {isEmpty(error) ? null : (
                <div className="error" onClick={() => setError([])}>
                    {error}
                </div>
            )}
            <Formik
                initialValues={initialValues}
                // validationSchema={validationSchema}
                enableReinitialize={true}
                onSubmit={async (values: MaterialInterface, actions) => {
                    try {
                        await submitForm(values);
                        hideDialog();
                        actions.setSubmitting(false);
                        actions.resetForm();
                        setError([]);
                        dispatch(
                            addNotification(
                                `Material ${
                                    isEmpty(material) ? `Added` : `Edited`
                                } Successfully`,
                                MessageVariant.SUCCESS,
                                'benchtopMaterial'
                            )
                        );
                    } catch (e) {
                        setError(e.data.error);
                    }
                }}>
                {(props: FormikProps<MaterialInterface>) => (
                    <Form>
                        <StyledRow className="row">
                            <TextField
                                name="name"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '41%',
                                }}
                                label="Material Name"
                                disabled={isManufacturer}
                                classes="column small-3"
                            />
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Type"
                                        required={true}
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '33%',
                                        }}
                                    />
                                    <List
                                        name="type_id"
                                        options={types}
                                        disabled={isManufacturer}
                                    />
                                </div>
                            </div>
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Brand"
                                        required={true}
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        name="brand_id"
                                        disabled={isManufacturer}
                                        options={brands}
                                    />
                                </div>
                            </div>
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Finish"
                                        required={true}
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '28%',
                                        }}
                                    />
                                    <List
                                        name="finish_id"
                                        disabled={isManufacturer}
                                        options={finishes}
                                    />
                                </div>
                            </div>
                        </StyledRow>
                        <StyledRow className="row">
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Formfactor"
                                        required={true}
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        multiple={true}
                                        name="formfactor_ids"
                                        disabled={isManufacturer}
                                        options={formfactors}
                                    />
                                </div>
                            </div>
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Substrate"
                                        required={true}
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '33%',
                                        }}
                                    />
                                    <List
                                        name="substrate_id"
                                        disabled={isManufacturer}
                                        options={substrates}
                                    />
                                </div>
                            </div>

                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Price Category"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        name="price_cat_id"
                                        disabled={isManufacturer}
                                        options={priceCategories}
                                    />
                                </div>
                            </div>
                            <TextField
                                disabled={isManufacturer}
                                displayFlex={true}
                                required={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '28%',
                                }}
                                name="thickness"
                                label="Thickness"
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledHr />
                        <StyledRow className="row">
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Grained"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        name="is_grained"
                                        disabled={isManufacturer}
                                        options={[
                                            {
                                                name: 'Yes',
                                                id: 1,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                            {
                                                name: 'No',
                                                id: 0,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Double Sided"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '33%',
                                        }}
                                    />
                                    <List
                                        name="is_double_sided"
                                        disabled={isManufacturer}
                                        options={[
                                            {
                                                name: 'Yes',
                                                id: 1,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                            {
                                                name: 'No',
                                                id: 0,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Blank"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        name="is_blank"
                                        disabled={isManufacturer}
                                        options={[
                                            {
                                                name: 'Yes',
                                                id: 1,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                            {
                                                name: 'No',
                                                id: 0,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <TextField
                                name="item_code"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '28%',
                                }}
                                label="Item Code"
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledHr />
                        <StyledRow className="row">
                            <TextField
                                name="max_length"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '41%',
                                }}
                                label="Length"
                                classes="column small-3"
                            />
                            <TextField
                                name="max_depth"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '33%',
                                }}
                                label="Width"
                                classes="column small-3"
                            />
                            <TextField
                                name="min_job_area"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '41%',
                                }}
                                label={`Min Job Area (in ${props.values.measurement})`}
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledRow className="row">
                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Allow Butt Join?"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        name="allow_butt_join"
                                        options={[
                                            {
                                                name: 'Yes',
                                                id: 1,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                            {
                                                name: 'No',
                                                id: 0,
                                                is_deleted: false, // add dummy property for typescript interface
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            {Number(props.values.allow_butt_join) ? (
                                <TextField
                                    name="max_butt_join_piece"
                                    displayFlex={true}
                                    style={{
                                        fontWeight: 'bold',
                                        marginRight: '8px',
                                        alignSelf: 'center',
                                        minWidth: '28%',
                                    }}
                                    label="Max Butt Joins (per piece)"
                                    classes="column small-3"
                                />
                            ) : null}
                        </StyledRow>
                        <StyledHr />
                        <StyledRow className="row">
                            <div className="column small-3">
                                <div style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Round Up (in mm)"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '41%',
                                        }}
                                    />
                                    <List
                                        name="round_up"
                                        options={[
                                            {
                                                name: '0',
                                                id: '0',
                                            },
                                            {
                                                name: '50',
                                                id: '50',
                                            },
                                            {
                                                name: '100',
                                                id: '100',
                                            },
                                            {
                                                name: '200',
                                                id: '200',
                                            },
                                        ]}
                                    />
                                </div>
                            </div>

                            <div className="column small-3">
                                <div
                                    className="form-group"
                                    style={{display: 'flex'}}>
                                    <FormLabel
                                        label="Measurement"
                                        style={{
                                            fontWeight: 'bold',
                                            marginRight: '8px',
                                            alignSelf: 'center',
                                            minWidth: '33%',
                                        }}
                                    />
                                    <List
                                        name="measurement"
                                        options={[
                                            {
                                                name: 'M2',
                                                id: 'M2',
                                            },
                                            {
                                                name: 'L/M',
                                                id: 'LM',
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <TextField
                                name="area_handling_cost"
                                displayFlex={true}
                                dollarSign={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '41%',
                                }}
                                label={`Area Handling Cost (in ${props.values.measurement})`}
                                classes="column small-3"
                            />
                            <TextField
                                name="area_cost"
                                displayFlex={true}
                                dollarSign={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '31%',
                                }}
                                label={`Length/Area Cost (in ${props.values.measurement})`}
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledRow>
                            <TextField
                                name="area_installation_cost"
                                dollarSign={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    width: '41%',
                                }}
                                label={`Area Installation Cost (in M2)`}
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledHr />
                        {window?.isAdmin ? (
                            <StyledRow className="row">
                                <div className="column">
                                    <FormLabel
                                        style={{fontWeight: 'bold'}}
                                        label="Allowed Manufacturers (Leave blank to allow all manufacturers)"
                                    />
                                    <Bucket
                                        name="manufacturers"
                                        placeholder="Manufacturers"
                                        options={props.values.manufacturers}
                                        isLoading={
                                            loadingManufacturer ||
                                            fetchingManufacturer
                                        }
                                    />
                                    <SearchBarWithDropDown
                                        loading={false}
                                        error={false}
                                        success={isSuccess}
                                        searchOnChange={(value: string) => {
                                            setKeywordsLocal(value);
                                        }}
                                        placeholder="Search Manufacturers"
                                        name="manufacturers"
                                        searchItems={manufacturersData}
                                    />
                                </div>
                            </StyledRow>
                        ) : (
                            <></>
                        )}
                        <Row className="row">
                            <div
                                className="column"
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                }}>
                                <FormLabel
                                    style={{fontWeight: 'bold'}}
                                    label="Image"
                                />
                                <div className="form_image">
                                    <ImageComponent
                                        name="image"
                                        imageButtonText={`${
                                            props.values.image
                                                ? `Update`
                                                : `Add`
                                        } Image`}
                                        allowAddEditImage={window?.isAdmin}
                                        showThumbnail={false}
                                        allowMultipleImages={false}
                                    />
                                </div>
                            </div>
                        </Row>
                        <hr />
                        <Element align={`justify-content: space-between;`}>
                            <SubmitButton
                                type="button"
                                className="form_50_button_left "
                                onClick={() => {
                                    props.resetForm();
                                    hideDialog();
                                }}>
                                Cancel
                            </SubmitButton>
                            <SubmitButton
                                type="submit"
                                disabled={props.isSubmitting}
                                className="form_50_button_right">
                                Save
                            </SubmitButton>
                        </Element>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default AddEditMaterial;
