import React from 'react';
import {MaterialGenericInterface} from '../store/benchtopModuleApi';

const Filter = ({
    options = [],
    selectedOption,
    changeFn,
    multipleSelection = false,
    name = '',
    ...props
}: {
    options:
        | MaterialGenericInterface[]
        | {
              id: number;
              name: string;
          }[];
    changeFn: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    name: string;
    selectedOption?: number;
    multipleSelection?: boolean;
}) => {
    return (
        <select
            style={{padding: '0px 15px 0px 5px'}}
            name={name}
            id={name}
            multiple={multipleSelection}
            onChange={changeFn}
            {...props}>
            {options.map((option) => (
                <OptionComponent
                    key={option.id}
                    option={option}
                    selectedOption={selectedOption}
                    multipleSelection={multipleSelection}
                />
            ))}
        </select>
    );
};

const OptionComponent = ({
    option,
    multipleSelection,
    selectedOption,
}: {
    option:
        | MaterialGenericInterface
        | {
              id: number;
              name: string;
          };
    multipleSelection?: boolean;
    selectedOption?: number;
}) => {
    if (
        !option.hasOwnProperty('material_count') ||
        (option?.material_count && parseInt(option.material_count))
    ) {
        return (
            <option
                key={option.id}
                value={option.id}
                selected={multipleSelection || option.id == selectedOption}>
                {option.name}
            </option>
        );
    }
    return <></>;
};

export default Filter;
