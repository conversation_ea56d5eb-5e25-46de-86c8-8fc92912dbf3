import {isEmpty} from 'lodash';
import React from 'react';
import {
    Filter,
    useGetBrandsQuery,
    useGetFinishesQuery,
    useGetMaterialThicknessesQuery,
    useGetPriceCategoriesLinkedToMaterialsQuery,
    useGetSubstratesQuery,
    useGetTypesQuery,
} from '../store/benchtopModuleApi';
import FilterComponent from './Filter';

const Filters = ({
    materialFilter,
    setMaterialFilter,
    showTypeFilter = true,
    showBrandFilter = true,
    showFinishFilter = true,
    showSubstrateFilter = true,
    showPriceCategoryFilter = true,
    showThicknessFilter = true,
}: {
    materialFilter: Filter;
    setMaterialFilter: React.Dispatch<React.SetStateAction<Filter>>;
    showTypeFilter?: boolean;
    showBrandFilter?: boolean;
    showFinishFilter?: boolean;
    showSubstrateFilter?: boolean;
    showPriceCategoryFilter?: boolean;
    showThicknessFilter?: boolean;
}) => {
    const {
        data: types = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetTypesQuery({withAllOption: true, ...materialFilter});

    // condition when show filter updates and there is no result on that then reset filters
    if (types) {
        const stillLoading = types.find((type) => type.id == -1);
        if (typeof stillLoading == 'undefined') {
            const selectedTypeAvailable = types.filter((type) => {
                return type.id == materialFilter.type.id;
            });
            if (isEmpty(selectedTypeAvailable)) {
                setMaterialFilter((state) => ({
                    ...state,
                    currentPage: 1,
                    type: {
                        id: 0,
                        name: 'all',
                    },
                    brand: {id: 0, name: 'all'},
                    finish: {id: 0, name: 'all'},
                    substrate: {id: 0, name: 'all'},
                    priceCategory: {id: 0, name: 'all'},
                    thickness: {id: 0, name: 'all'},
                }));
            }
        }
    }
    const {
        data: brands = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetBrandsQuery(materialFilter);

    const {
        data: finishes = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetFinishesQuery({
        ...materialFilter,
        pageSize: 1000,
    });

    const {
        data: substrates = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetSubstratesQuery(materialFilter);

    const {
        data: priceCategories = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetPriceCategoriesLinkedToMaterialsQuery(materialFilter);

    const {
        data: thicknesses = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetMaterialThicknessesQuery(materialFilter);

    return (
        <>
            {showTypeFilter ? (
                <div>
                    <b style={{margin: '5px 15px'}}>Type</b>
                    <FilterComponent
                        name="filter_type"
                        key="filter_type"
                        selectedOption={materialFilter?.type?.id}
                        changeFn={(e: React.ChangeEvent<HTMLSelectElement>) =>
                            setMaterialFilter((state) => ({
                                ...state,
                                currentPage: 1,
                                type: {
                                    id: parseInt(e.target.value),
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                                // reset other fields
                                brand: {id: 0, name: 'all'},
                                finish: {id: 0, name: 'all'},
                                substrate: {id: 0, name: 'all'},
                                priceCategory: {id: 0, name: 'all'},
                                thickness: {id: 0, name: 'all'},
                            }))
                        }
                        options={types}
                    />
                </div>
            ) : null}
            {showBrandFilter ? (
                <div>
                    <b style={{margin: '5px 15px'}}>Brand</b>
                    <FilterComponent
                        name="filter_brand"
                        key="filter_brand"
                        selectedOption={materialFilter?.brand?.id}
                        changeFn={(e: React.ChangeEvent<HTMLSelectElement>) =>
                            setMaterialFilter((state) => ({
                                ...state,
                                currentPage: 1,
                                type: materialFilter?.type,
                                brand: {
                                    id: parseInt(e.target.value),
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                                // reset other fields
                                finish: {id: 0, name: 'all'},
                                substrate: {id: 0, name: 'all'},
                                priceCategory: {id: 0, name: 'all'},
                                thickness: {id: 0, name: 'all'},
                            }))
                        }
                        options={brands}
                    />
                </div>
            ) : null}
            {showFinishFilter ? (
                <div>
                    <b style={{margin: '5px 15px'}}>Finish</b>
                    <FilterComponent
                        name="filter_finish"
                        key="filter_finish"
                        selectedOption={materialFilter?.finish?.id}
                        changeFn={(e: React.ChangeEvent<HTMLSelectElement>) =>
                            setMaterialFilter((state) => ({
                                ...state,
                                currentPage: 1,
                                type: materialFilter?.type,
                                brand: materialFilter?.brand,
                                finish: {
                                    id: parseInt(e.target.value),
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                                // reset other fields
                                substrate: {id: 0, name: 'all'},
                                priceCategory: {id: 0, name: 'all'},
                                thickness: {id: 0, name: 'all'},
                            }))
                        }
                        options={finishes}
                    />
                </div>
            ) : null}
            {showSubstrateFilter ? (
                <div>
                    <b style={{margin: '5px 15px'}}>Substrate</b>
                    <FilterComponent
                        name="filter_substrate"
                        key="filter_substrate"
                        selectedOption={materialFilter?.substrate?.id}
                        changeFn={(e: React.ChangeEvent<HTMLSelectElement>) =>
                            setMaterialFilter((state) => ({
                                ...state,
                                currentPage: 1,
                                type: materialFilter?.type,
                                brand: materialFilter?.brand,
                                finish: materialFilter?.finish,
                                substrate: {
                                    id: parseInt(e.target.value),
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                                // reset other fields
                                priceCategory: {id: 0, name: 'all'},
                                thickness: {id: 0, name: 'all'},
                            }))
                        }
                        options={substrates}
                    />
                </div>
            ) : null}
            {showPriceCategoryFilter ? (
                <div>
                    <b style={{margin: '5px 15px'}}>Price Category</b>
                    <FilterComponent
                        name="filter_price_cat"
                        key="filter_price_cat"
                        selectedOption={materialFilter?.priceCategory?.id}
                        changeFn={(e: React.ChangeEvent<HTMLSelectElement>) =>
                            setMaterialFilter((state) => ({
                                ...state,
                                currentPage: 1,
                                type: materialFilter?.type,
                                brand: materialFilter?.brand,
                                finish: materialFilter?.finish,
                                substrate: materialFilter?.substrate,
                                priceCategory: {
                                    id: e.target.value
                                        ? parseInt(e.target.value)
                                        : null,
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                                // reset other fields
                                thickness: {id: 0, name: 'all'},
                            }))
                        }
                        options={priceCategories}
                    />
                </div>
            ) : null}
            {showThicknessFilter ? (
                <div>
                    <b style={{margin: '5px 15px'}}>Thickness</b>
                    <FilterComponent
                        name="filter_thickness"
                        key="filter_thickness"
                        selectedOption={materialFilter?.thickness?.id}
                        changeFn={(e: React.ChangeEvent<HTMLSelectElement>) =>
                            setMaterialFilter((state) => ({
                                ...state,
                                currentPage: 1,
                                type: materialFilter?.type,
                                brand: materialFilter?.brand,
                                finish: materialFilter?.finish,
                                substrate: materialFilter?.substrate,
                                priceCategory: materialFilter?.priceCategory,
                                thickness: {
                                    id: parseInt(e.target.value),
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                            }))
                        }
                        options={thicknesses}
                    />
                </div>
            ) : null}
        </>
    );
};

export default Filters;
