import {Spinner} from 'assets';
import {
    addNotification,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import {capitalize, identity, isEmpty, isNull, pickBy} from 'lodash';
import React, {useEffect, useState} from 'react';
import {Button, Image} from 'react-bootstrap';
import {useAppDispatch} from 'store/dashboard';
import {AddNewButton} from '.';
import {
    Filter,
    useGetMaterialsQuery,
    useMassHideShowMutation,
} from '../store/benchtopModuleApi';

const MassHideShow = ({
    hideDialog,
    materialFilter,
}: {
    hideDialog: () => void;
    materialFilter: Filter;
}) => {
    const showStatus = materialFilter.show.id
        ? materialFilter.show
        : {
              id: 0,
              name: 'Visible',
          };
    const [filters, setFilters] = useState({
        ...materialFilter,
        show: {...showStatus},
    });
    const [
        massHideShowMaterials,
        {error: massHideShowMaterialsError, isLoading: isMassHideShowLoading},
    ] = useMassHideShowMutation();
    const dispatch = useAppDispatch();

    const {
        data: {data, pagination} = {
            data: [],
            pagination: {total_count: 0},
        },
        isLoading,
        isFetching,
    } = useGetMaterialsQuery(filters);

    const applyMassHide = async () => {
        let params = pickBy(
            {
                name: filters?.name,
                type_id: filters?.type.id,
                brand_id: filters?.brand.id,
                finish_id: filters?.finish.id,
                substrate_id: filters?.substrate.id,
                thickness: filters?.thickness.id,
            },
            identity
        );

        if (filters?.priceCategory?.id || isNull(filters?.priceCategory?.id)) {
            params = {
                ...params,
                price_cat_id: filters?.priceCategory?.id,
            };
        }

        try {
            await massHideShowMaterials({
                ...params,
                hiddenStatus: filters?.show.id ? 0 : 1, // inverse the inversion to send correct status
            }).unwrap();
            hideDialog();
            dispatch(
                addNotification(
                    `Materials updated Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopMaterial'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(massHideShowMaterialsError)) {
            hideDialog();
            dispatch(
                addNotification(
                    `Error: Something went wrong when running mass hide show action on materials. Contact admin for more information.
                    Error Type : ${massHideShowMaterialsError.data.error}`,
                    MessageVariant.ERROR,
                    'benchtopMaterial'
                )
            );
        }
    }, [massHideShowMaterialsError]);

    return (
        <>
            <Button
                onClick={hideDialog}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            <h2>
                <p>You are about to apply mass changes to your materials.</p>
                <p>The changes being made to:</p>
                <br />
                {filters?.name ? (
                    <p>Name : {capitalize(filters.name)}</p>
                ) : null}
                {filters?.type ? (
                    <p>Type : {capitalize(filters.type.name)}</p>
                ) : null}
                {filters?.brand ? (
                    <p>Brand : {capitalize(filters.brand.name)}</p>
                ) : null}
                {filters?.finish ? (
                    <p>Finish : {capitalize(filters.finish.name)}</p>
                ) : null}
                {filters?.substrate ? (
                    <p>Substrate : {capitalize(filters.substrate.name)}</p>
                ) : null}
                {filters?.priceCategory ? (
                    <p>
                        Price Category :{' '}
                        {filters.priceCategory.name
                            ? capitalize(filters.priceCategory.name)
                            : 'No Price Category'}
                    </p>
                ) : null}
                {filters?.thickness ? (
                    <p>Thickness : {filters.thickness.name}</p>
                ) : null}
                <span>
                    <b>Function</b>
                    <select
                        onChange={(e) =>
                            setFilters((state) => ({
                                ...state,
                                type: filters?.type,
                                brand: filters?.brand,
                                finish: filters?.finish,
                                substrate: filters?.substrate,
                                priceCategory: filters?.priceCategory,
                                show: {
                                    id: parseInt(e.target.value) ? 0 : 1, // invert it to get count
                                    name: e.target.options[
                                        e.target.selectedIndex
                                    ].text,
                                },
                            }))
                        }
                        style={{padding: '0 16px 0 4px', margin: '5px 15px'}}
                        name="filter_show"
                        id="filter_show">
                        <option value="0" selected={filters?.show.id == 1}>
                            Show
                        </option>
                        <option value="1" selected={filters?.show.id == 0}>
                            Hide
                        </option>
                    </select>
                </span>
            </h2>
            {isLoading || isFetching ? (
                <Image
                    className="icon_button"
                    style={{
                        backgroundColor: 'transparent',
                    }}
                    src={`data:image/svg+xml;base64, ${btoa(Spinner)}`}
                />
            ) : (
                <>
                    <h5>
                        {' '}
                        You are about to update {pagination?.total_count}{' '}
                        materials.
                    </h5>
                    <AddNewButton
                        onClick={applyMassHide}
                        style={{
                            padding: '0 21px',
                            margin: '6px 3px',
                        }}>
                        Yes
                    </AddNewButton>
                </>
            )}
        </>
    );
};

export default MassHideShow;
