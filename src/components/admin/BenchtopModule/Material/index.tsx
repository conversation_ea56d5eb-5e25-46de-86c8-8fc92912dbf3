import React, {useCallback, useEffect, useState} from 'react';
import DataTable, {TableColumn} from 'react-data-table-component';
import {ConfirmationButton} from 'shared/ConfirmationButton';
import {
    Edit,
    Delete,
    AddButton,
    Upload,
    EditButton,
    ButtonPassword,
    ButtonRotateAntiClockWise,
    EyeVisible,
    Currency,
} from 'assets';
import styled from 'styled-components';
import {
    Filter,
    MaterialInterface,
    useEditMaterialMutation,
    useGetManufacturersQuery,
    useGetMaterialsQuery,
    useLazyGetMaterialsQuery,
    useMassHideShowMutation,
    useResetToDefaultMutation,
} from '../store/benchtopModuleApi';
import Brand from '../Brand';
import Finish from '../Finish';
import FormFactor from '../FormFactor';
import Type from '../Type';
import Substrate from '../Substrate';
import PriceCategory from '../PriceCategory';
import {
    addNotification,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import AddEditMaterial from './AddEditMaterial';
import {Import} from './Import';
import Filters from './Filters';
import MassHideShow from './MassHideShow';
import {isEmpty} from 'lodash';
import {formatPrice} from 'shared/helpers';
import {useDebounce} from 'components/customer/Materials/helper';
import ResetToDefault from './ResetToDefault';
import MassCostEditor from './MassCostEditor';
import {downloadToCSV} from '../shared/DownloadToCSV';

const TOGGLE = -1;
export const AddNewButton = styled.button`
    background: #00a8e0;
    min-width: max-content;
    color: #fff;
    text-decoration: none;
    vertical-align: top;
    font-size: 19px;
    border: 0;
    margin-top: 2px;
    text-shadow: 0 0 black;
    &:hover {
        background: #00a8e0;
        color: #fff;
        opacity: 0.5;
    }
    &:focus {
        background: #00a8e0;
    }
    &[disabled] {
        opacity: 0.5;
        color: #fff;
    }
`;

const MassButton = styled.button`
    background: #00a8e0;
    min-width: max-content;
    color: #fff;
    text-decoration: none;
    margin-top: 2px;
    border: 0;
    text-shadow: 0 0 black;
    &:hover {
        background: #00a8e0;
        color: #fff;
        opacity: 0.5;
    }
    &:focus {
        background: #00a8e0;
    }
`;

const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
            textAlign: 'center',
            color: '#0079A5',
            '>div>div': {
                whiteSpace: 'unset',
            },
        },
    },
    pagination: {
        pageButtonsStyle: {
            background: 'transparent',
        },
    },
    header: {
        style: {
            fontSize: '22px',
            minHeight: '56px',
            paddingLeft: '16px',
            paddingRight: '8px',
            whiteSpace: 'unset',
        },
    },
};

const BenchtopMaterial = () => {
    const dispatch = useAppDispatch();
    const [selectedMaterials, setSelectedMaterials] = useState<
        MaterialInterface[]
    >([]);
    const [toggledClearRows, setToggleClearRows] = useState(false);
    const [nameLocal, setNameLocal] = useState<string>('');
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(25);

    let initialFilter: Filter = {
        withAllOption: true,
        currentPage: 1,
        pageSize: perPage,
        name: '',
        type: {id: 0, name: 'all'},
        brand: {id: 0, name: 'all'},
        finish: {id: 0, name: 'all'},
        substrate: {id: 0, name: 'all'},
        priceCategory: {id: 0, name: 'all'},
        thickness: {id: 0, name: 'all'},
    };
    window?.isManufacturer
        ? (initialFilter = {
              ...initialFilter,
              show: {id: 0, name: 'visible'},
          })
        : null;

    const [
        toggleHideShowMaterials,
        {error: toggleHideShowMaterialsError, isSuccess: toggleHideShowSuccess},
    ] = useMassHideShowMutation();
    const [
        resetToDefaultMaterials,
        {error: resetToDefaultMaterialsError, isSuccess: resetToDefaultSuccess},
    ] = useResetToDefaultMutation();
    const [materialFilter, setMaterialFilter] = useState<Filter>(initialFilter);
    const {
        data: {data: materials, pagination} = {
            data: [],
            pagination: {total_count: 0},
        },
        isLoading,
        isFetching,
    } = useGetMaterialsQuery(materialFilter);

    const [fetchExportMaterials] = useLazyGetMaterialsQuery();
    const [editMaterial] = useEditMaterialMutation();

    const {
        data: manufacturers = [
            {
                id: -1,
                name: 'Loading...',
            },
        ],
    } = useGetManufacturersQuery({withExtraOptions: {withAllOption: true}});

    const conditionalRowStyles = [
        {
            when: (row: MaterialInterface) => parseInt(row?.change?.is_hidden),
            style: {
                backgroundColor: 'lightgray',
            },
        },
        {
            when: (row: MaterialInterface) =>
                row?.change?.actualChangesHappen &&
                parseInt(row?.change?.is_hidden) != 1,
            style: {
                backgroundColor: '#99c9da',
            },
        },
    ];

    useEffect(() => {
        if (pagination.total_count) {
            setTotalRows(pagination.total_count);
        }
    }, [pagination]);

    const handlePageChange = (page: number) => {
        setMaterialFilter((state) => ({
            ...state,
            currentPage: page,
        }));
    };

    const resetToDefaultAction = async (
        selectedMaterials: MaterialInterface[]
    ) => {
        const requestData: {
            materialIds: string;
            resetToDefault: boolean;
        } = {
            materialIds: selectedMaterials
                .map((material) => material.id)
                .join(','),
            resetToDefault: true,
        };
        try {
            await resetToDefaultMaterials(requestData).unwrap();
        } catch (e) {
            throw e;
        }
    };

    // function to toggle selected materials
    const toggleHideShow = useCallback(() => {
        setToggleClearRows(!toggledClearRows);
        const requestData: {
            materialIds: string;
            hiddenStatus: number;
        } = {
            materialIds: selectedMaterials
                .map((material) => material.id)
                .join(','),
            hiddenStatus: TOGGLE,
        };
        void toggleHideShowMaterials(requestData)
            .unwrap()
            .then(() => {
                setSelectedMaterials([]);
            });
    }, [selectedMaterials, toggledClearRows]);

    useEffect(() => {
        if (!isEmpty(toggleHideShowMaterialsError)) {
            dispatch(
                addNotification(
                    `Error: Something went wrong when running toggle hide show action on materials. Contact admin for more information.
                    Error Type : ${toggleHideShowMaterialsError.data.error}`,
                    MessageVariant.ERROR,
                    'benchtopMaterial'
                )
            );
        }
        if (!isEmpty(resetToDefaultMaterialsError)) {
            dispatch(
                addNotification(
                    `Error: Something went wrong when running reset to default action on materials. Contact admin for more information.
                    Error Type : ${resetToDefaultMaterialsError.data.error}`,
                    MessageVariant.ERROR,
                    'benchtopMaterial'
                )
            );
        }
    }, [toggleHideShowMaterialsError, resetToDefaultMaterialsError]);

    useEffect(() => {
        if (toggleHideShowSuccess || resetToDefaultSuccess) {
            dispatch(
                addNotification(
                    `Materials updating is successful`,
                    MessageVariant.SUCCESS,
                    'benchtopMaterial'
                )
            );
        }
    }, [toggleHideShowSuccess, resetToDefaultSuccess]);

    const downloadMaterials = async () => {
        dispatch(
            addNotification(
                `Downloading materials list, please wait`,
                MessageVariant.INFO,
                'benchtopMaterial'
            )
        );
        // only 1000 records are requested in single request
        const totalRequestedRecords = 1000;
        let exportMaterials: MaterialInterface[] = [];
        if (pagination.total_count <= totalRequestedRecords) {
            const fetchMaterialRequest = await fetchExportMaterials(
                {
                    ...materialFilter,
                    pageSize: totalRequestedRecords,
                },
                true
            );

            if (fetchMaterialRequest.isSuccess) {
                exportMaterials = [...fetchMaterialRequest.data.data];
            }
            if (fetchMaterialRequest.isError) {
                dispatch(
                    addNotification(
                        `Error: Something went wrong when exporting materials. Contact admin for more information.
                        Error Type : ${fetchMaterialRequest.error.data.error}`,
                        MessageVariant.ERROR,
                        'benchtopMaterial'
                    )
                );
                return;
            }
        } else {
            const totalRequests = Math.ceil(
                pagination.total_count / totalRequestedRecords
            );

            for (let request = 0; request < totalRequests; request++) {
                const fetchMaterialRequest = await fetchExportMaterials(
                    {
                        ...materialFilter,
                        pageSize: totalRequestedRecords,
                        currentPage: request + 1,
                    },
                    true
                );

                if (fetchMaterialRequest.isSuccess) {
                    exportMaterials = [
                        ...exportMaterials,
                        ...fetchMaterialRequest.data.data,
                    ];
                }

                if (fetchMaterialRequest.isError) {
                    dispatch(
                        addNotification(
                            `Error: Something went wrong when exporting materials. Contact admin for more information.
                            Error Type : ${fetchMaterialRequest.error.data.error}`,
                            MessageVariant.ERROR,
                            'benchtopMaterial'
                        )
                    );
                    return;
                }
            }
        }

        const header = [
            'Item Code',
            'Name',
            'Type',
            'Brand',
            'Finish',
            'Substrate',
            'Thickness',
            'Formfactors',
            'Price Cat',
            'Roundup (in mm)',
            'Blanks',
            'Grained',
            'Double Sided',
            'Area Handling Cost',
            'M2 OR L/M',
            'Area Cost',
            'Area Installation Cost',
            'Minimum Job Area',
            'Max Length',
            'Max Width',
        ];

        const data = exportMaterials.map(function (row: MaterialInterface) {
            // area handling cost from manufacturer if changed
            const areaHandlingCost = row?.change?.actualChangesHappen
                ? row?.change?.area_handling_cost
                    ? row?.change?.area_handling_cost
                    : 0
                : row.area_handling_cost
                ? row.area_handling_cost
                : 0;

            // measurement from manufacturer if changed
            const measurement = row?.change?.actualChangesHappen
                ? row?.change?.measurement
                    ? row?.change?.measurement
                    : row.measurement
                : row.measurement;

            // area cost from manufacturer if changed
            const areaCost = row?.change?.actualChangesHappen
                ? row?.change?.area_cost
                    ? row?.change?.area_cost
                    : 0
                : row.area_cost
                ? row.area_cost
                : 0;

            // area installation cost from manufacturer if changed
            const areaInstallationCost = row?.change?.actualChangesHappen
                ? row?.change?.area_installation_cost
                    ? row?.change?.area_installation_cost
                    : 0
                : row.area_installation_cost
                ? row.area_installation_cost
                : 0;

            // min_job_area from manufacturer if changed
            const minJobArea = row?.change?.actualChangesHappen
                ? row?.change?.min_job_area
                    ? row?.change?.min_job_area
                    : 0
                : row.min_job_area
                ? row.min_job_area
                : 0;

            // max_length from manufacturer if changed
            const maxLength = row?.change?.actualChangesHappen
                ? row?.change?.max_length
                    ? row?.change?.max_length
                    : 0
                : row.max_length
                ? row.max_length
                : 0;
            // max_depth from manufacturer if changed
            const maxDepth = row?.change?.actualChangesHappen
                ? row?.change?.max_depth
                    ? row?.change?.max_depth
                    : 0
                : row.max_depth
                ? row.max_depth
                : 0;
            return [
                row?.change?.item_code ? row?.change?.item_code : row.item_code,
                row.name,
                row.type_name,
                row.brand_name,
                row.finish_name,
                row.substrate_name,
                row.thickness,
                `"${row.form_factor_name}"`,
                row.price_category_name,
                row?.change?.round_up ? row?.change?.round_up : row.round_up,
                row.is_blank ? 'Yes' : 'No',
                row.is_grained ? 'Yes' : 'No',
                row.is_double_sided ? 'Yes' : 'No',
                areaHandlingCost,
                measurement,
                areaCost,
                areaInstallationCost,
                minJobArea,
                maxLength,
                maxDepth,
            ];
        });
        downloadToCSV(header, data, 'benchtop_materials');
        dispatch(
            addNotification(
                `Download complete`,
                MessageVariant.SUCCESS,
                'benchtopMaterial'
            )
        );
    };

    const cols: TableColumn<MaterialInterface>[] = [
        {
            name: 'ID',
            selector: (row) => row.id,
            compact: true,
            wrap: true,
            sortable: true,
            sortField: 'id',
            width: '2%',
        },
        {
            name: 'Item Code',
            selector: (row) =>
                row?.change?.item_code ? row?.change?.item_code : row.item_code,
            compact: true,
            wrap: true,
            width: '3%',
        },
        {
            name: 'Name',
            selector: (row) => row.name,
            compact: true,
            wrap: true,
            sortable: true,
            sortField: 'name',
            width: '5%',
        },
        {
            name: 'Type',
            selector: (row) => row.type_name,
            compact: true,
            wrap: true,
            width: '4%',
        },
        {
            name: 'Brand',
            selector: (row) => row.brand_name,
            compact: true,
            wrap: true,
            width: '4%',
        },
        {
            name: 'Finish',
            selector: (row) => row.finish_name,
            wrap: true,
            compact: true,
            width: '4%',
        },
        {
            name: 'Substrate',
            compact: true,
            selector: (row) => row.substrate_name,
            wrap: true,
            width: '4%',
        },
        {
            name: 'Form Factor',
            selector: (row) => row.form_factor_name,
            compact: true,
            id: 'material_form_factor',
            wrap: true,
            width: '5%',
        },
        {
            name: 'Price Category',
            selector: (row) => row.price_category_name,
            compact: true,
            wrap: true,
            width: '8%',
        },
        {
            name: 'Thickness',
            selector: (row) => row.thickness,
            compact: true,
            center: true,
            wrap: true,
            sortable: true,
            sortField: 'thickness',
            width: '4.5%',
        },
        {
            name: 'Roundup (in mm)',
            selector: (row) =>
                row?.change?.round_up ? row?.change?.round_up : row.round_up,
            sortable: true,
            sortField: 'round_up',
            width: '5%',
        },
        {
            name: 'Blanks',
            selector: (row) => (row.is_blank ? 'Yes' : 'No'),
            compact: true,
            sortable: true,
            sortField: 'is_blank',
            width: '4%',
        },
        {
            name: 'Grained',
            selector: (row) => (row.is_grained ? 'Yes' : 'No'),
            compact: true,
            sortable: true,
            sortField: 'is_grained',
            width: '4%',
        },
        {
            name: 'DS',
            selector: (row) => (row.is_double_sided ? 'Yes' : 'No'),
            compact: true,
            sortable: true,
            sortField: 'is_double_sided',
            width: '2%',
        },
        {
            name: 'Area Handling Cost',
            selector: (row) => {
                let [areaHandlingCost, parentAreaHandlingCost]: number[] = [
                    row.area_handling_cost ? row.area_handling_cost : 0,
                    row.area_handling_cost ? row.area_handling_cost : 0,
                ];
                if (row?.change?.actualChangesHappen) {
                    areaHandlingCost = row?.change?.area_handling_cost
                        ? row?.change?.area_handling_cost
                        : 0;
                    return `${formatPrice(areaHandlingCost, {})} (${formatPrice(
                        parentAreaHandlingCost,
                        {}
                    )})`;
                }
                return `${formatPrice(parentAreaHandlingCost, {})}`;
            },
            sortable: true,
            compact: true,
            sortField: 'area_handling_cost',
            width: '5%',
            center: true,
        },
        {
            name: 'Area Cost',
            compact: true,
            selector: (row) => {
                let [areaCost, parentAreaCost]: number[] = [
                    row.area_cost ? row.area_cost : 0,
                    row.area_cost ? row.area_cost : 0,
                ];
                if (row?.change?.actualChangesHappen) {
                    areaCost = row?.change?.area_cost
                        ? row?.change?.area_cost
                        : 0;
                    return `${formatPrice(areaCost, {})} (${formatPrice(
                        parentAreaCost,
                        {}
                    )})`;
                }
                return `${formatPrice(parentAreaCost, {})}`;
            },
            sortable: true,
            sortField: 'area_cost',
            width: '6%',
            center: true,
        },
        {
            name: 'Area Installation Cost',
            compact: true,
            selector: (row) => {
                let [
                    areaInstallationCost,
                    parentAreaInstallationCost,
                ]: number[] = [
                    row.area_installation_cost ? row.area_installation_cost : 0,
                    row.area_installation_cost ? row.area_installation_cost : 0,
                ];
                if (row?.change?.actualChangesHappen) {
                    areaInstallationCost = row?.change?.area_installation_cost
                        ? row?.change?.area_installation_cost
                        : 0;
                    return `${formatPrice(
                        areaInstallationCost,
                        {}
                    )} (${formatPrice(parentAreaInstallationCost, {})})`;
                }
                return `${formatPrice(parentAreaInstallationCost, {})}`;
            },
            sortable: true,
            sortField: 'area_installation_cost',
            width: '5%',
            center: true,
        },
        {
            name: 'Min Job Area',
            compact: true,
            selector: (row) => {
                let [minJobArea, parentMinJobArea]: number[] = [
                    row.min_job_area ? row.min_job_area : 0,
                    row.min_job_area ? row.min_job_area : 0,
                ];
                if (row?.change?.actualChangesHappen) {
                    minJobArea = row?.change?.min_job_area
                        ? row?.change?.min_job_area
                        : 0;
                    return `${minJobArea} (${parentMinJobArea})`;
                }
                return parentMinJobArea;
            },
            sortable: true,
            sortField: 'min_job_area',
            width: '4%',
            center: true,
        },
        {
            name: 'UOM',
            compact: true,
            selector: (row) => {
                let [measurement, parentMeasurement]: string[] = [
                    row.measurement,
                    row.measurement,
                ];
                if (row?.change?.actualChangesHappen) {
                    measurement = row?.change?.measurement;
                    return `${measurement} (${parentMeasurement})`;
                }
                return parentMeasurement;
            },
            width: '3%',
            center: true,
        },
        {
            name: 'Length',
            selector: (row) => {
                let [length, parentLength]: number[] = [
                    row.max_length ? row.max_length : 0,
                    row.max_length ? row.max_length : 0,
                ];
                if (row?.change?.actualChangesHappen) {
                    length = row?.change?.max_length
                        ? row?.change?.max_length
                        : 0;
                    return `${length} (${parentLength})`;
                }
                return parentLength;
            },
            sortable: true,
            sortField: 'max_length',
            width: '4%',
            compact: true,
        },
        {
            name: 'Width',
            selector: (row) => {
                let [depth, parentDepth]: number[] = [
                    row.max_depth ? row.max_depth : 0,
                    row.max_depth ? row.max_depth : 0,
                ];
                if (row?.change?.actualChangesHappen) {
                    depth = row?.change?.max_depth ? row?.change?.max_depth : 0;
                    return `${depth} (${parentDepth})`;
                }
                return parentDepth;
            },
            sortable: true,
            sortField: 'max_depth',
            width: '4%',
            compact: true,
        },
        {
            name: 'Action',
            cell: (row) => [
                <React.Fragment key={`action_buttons_${row.id}`}>
                    <ConfirmationButton
                        onClick={() => {}}
                        classes="x-center small-11 medium-8 large-8"
                        title="Edit Material"
                        message=""
                        options={{hideFooter: true}}
                        image={Edit}>
                        {({setShow}) => (
                            <AddEditMaterial
                                hideDialog={() => setShow(false)}
                                material={row}
                            />
                        )}
                    </ConfirmationButton>
                    {row?.change?.actualChangesHappen ? (
                        <ConfirmationButton
                            onClick={() => {
                                resetToDefaultAction([row]);
                            }}
                            title="Reset to Default"
                            message="Are you sure you want to perform this action on the selected item?"
                            classes="x-center small-11 medium-5 large-5"
                            image={ButtonRotateAntiClockWise}
                        />
                    ) : null}
                    {window?.isAdmin && !row.is_deleted ? (
                        <ConfirmationButton
                            onClick={() => {
                                deleteMaterial(row);
                            }}
                            title="Delete Material"
                            message="Are you sure you want to perform this action on the selected item?"
                            classes="x-center small-11 medium-5 large-5"
                            image={Delete}
                        />
                    ) : null}
                </React.Fragment>,
            ],
            center: true,
            omit: materialFilter?.manufacturer?.id ? true : false,
        },
    ];

    const deleteMaterial = async (row: MaterialInterface) => {
        const data = {
            ...row,
            ...{
                is_deleted: true,
                formfactor_ids: JSON.stringify(row.formfactor_ids),
                manufacturers: JSON.stringify(row.manufacturers),
            },
        };
        try {
            await editMaterial(data).unwrap();
            dispatch(
                addNotification(
                    `Material deleted Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopMaterial'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    const nameFilter = useDebounce(nameLocal, 1000);

    const handleSort = async (column, sortDirection) => {
        setMaterialFilter((state) => ({
            ...state,
            sortBy: column.sortField,
            orderBy: sortDirection,
        }));
    };

    useEffect(() => {
        setMaterialFilter((state) => ({
            ...state,
            type: materialFilter?.type,
            brand: materialFilter?.brand,
            finish: materialFilter?.finish,
            substrate: materialFilter?.substrate,
            priceCategory: materialFilter?.priceCategory,
            // thickness: materialFilter?.thickness,
            manufacturer: materialFilter?.manufacturer,
            show: materialFilter?.show,
            name: nameFilter,
            currentPage: 1,
        }));
    }, [nameFilter]);

    const paginationComponentOptions = {
        noRowsPerPage: true,
    };

    return (
        <>
            <Notification filterType="benchtopMaterial" />

            <div className="floating_item_body" style={{margin: '10px 0px'}}>
                <h1 className="form_header" style={{display: 'inline'}}>
                    Materials
                </h1>
                <div className="floatR pageButtons">
                    {window?.isAdmin ? (
                        <>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Types"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={EditButton}
                                buttonText="Types">
                                {({setShow}) => {
                                    return <Type setShowPrompt={setShow} />;
                                }}
                            </ConfirmationButton>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Brands"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={EditButton}
                                buttonText="Brands">
                                {({setShow}) => {
                                    return <Brand setShowPrompt={setShow} />;
                                }}
                            </ConfirmationButton>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Finishes"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={EditButton}
                                buttonText="Finish">
                                {({setShow}) => {
                                    return <Finish setShowPrompt={setShow} />;
                                }}
                            </ConfirmationButton>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Form Factor"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={EditButton}
                                buttonText="Form Factor">
                                {({setShow}) => {
                                    return (
                                        <FormFactor setShowPrompt={setShow} />
                                    );
                                }}
                            </ConfirmationButton>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Substrate"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={EditButton}
                                buttonText="Substrate">
                                {({setShow}) => {
                                    return (
                                        <Substrate setShowPrompt={setShow} />
                                    );
                                }}
                            </ConfirmationButton>
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-11 medium-8 large-8"
                                title="Price Categories"
                                message=""
                                options={{hideFooter: true}}
                                component={AddNewButton}
                                image={EditButton}
                                buttonText="Price Categories">
                                {({setShow}) => {
                                    return (
                                        <PriceCategory
                                            setShowPrompt={setShow}
                                        />
                                    );
                                }}
                            </ConfirmationButton>
                        </>
                    ) : (
                        <></>
                    )}
                    <a href="benchtopModule_outsourcematerials.php">
                        <AddNewButton>Pricing Matrix</AddNewButton>
                    </a>
                </div>

                <hr className="form_horizontal_rule" />
                <div className="pageButtons">
                    <span>
                        <ConfirmationButton
                            onClick={downloadMaterials}
                            component={AddNewButton}
                            title="Download Benchtop Materials"
                            message="Are you sure you want to download?"
                            classes="x-center small-11 medium-5 large-5"
                            image={Upload}
                            options={{}}
                            buttonText="Export"
                        />
                    </span>
                    {window?.isManufacturer && !isEmpty(materials) ? (
                        <ConfirmationButton
                            onClick={toggleHideShow}
                            classes="x-center small-8 medium-6 large-6"
                            title="Toggle Materials"
                            message={`${
                                isEmpty(selectedMaterials)
                                    ? `No Material selected. Please select materials to run this action.`
                                    : `Are you sure you want to perform this action on the selected materials?`
                            }`}
                            options={
                                isEmpty(selectedMaterials)
                                    ? {hideFooter: true}
                                    : {}
                            }
                            component={AddNewButton}
                            image={EyeVisible as string}
                            displayCloseButton={true}
                            buttonText="Hide/Show Items"
                        />
                    ) : null}

                    {window?.isAdmin ? (
                        <>
                            <span>
                                <ConfirmationButton
                                    onClick={() => {}}
                                    classes="x-center small-11 medium-8 large-8"
                                    title="Material"
                                    message=""
                                    options={{hideFooter: true}}
                                    component={AddNewButton}
                                    image={AddButton}
                                    buttonText="Add New Material">
                                    {({setShow}) => {
                                        return (
                                            <AddEditMaterial
                                                hideDialog={() =>
                                                    setShow(false)
                                                }
                                            />
                                        );
                                    }}
                                </ConfirmationButton>
                            </span>
                            <span>
                                <ConfirmationButton
                                    onClick={() => {}}
                                    classes="x-center small-11 medium-8 large-8"
                                    title="Import materials"
                                    message=""
                                    options={{hideFooter: true}}
                                    component={AddNewButton}
                                    image={Upload}
                                    buttonText="Import">
                                    {({setShow}) => {
                                        return (
                                            <Import
                                                hideDialog={() =>
                                                    setShow(false)
                                                }
                                            />
                                        );
                                    }}
                                </ConfirmationButton>
                            </span>
                        </>
                    ) : (
                        <></>
                    )}
                </div>

                <div
                    style={{
                        display: 'flex',
                        marginTop: '25px',
                        justifyContent: 'right',
                        flexWrap: 'wrap',
                    }}>
                    <div>
                        <b style={{margin: '5px 15px'}}>Name</b>
                        <input
                            type="text"
                            name="filter_name"
                            style={{
                                padding: '4px 3px',
                                boxShadow: 'none',
                            }}
                            onChange={(e) => setNameLocal(e.target.value)}
                        />
                    </div>
                    <Filters
                        materialFilter={materialFilter}
                        setMaterialFilter={setMaterialFilter}
                    />
                    {window?.isAdmin ? (
                        <div style={{marginRight: '5px'}}>
                            <b style={{margin: '5px 15px'}}>Manufacturers</b>
                            <select
                                key="filter_manufacturer"
                                style={{padding: '0 3px'}}
                                name="filter_manufacturer"
                                id="filter_manufacturer"
                                onChange={(e) =>
                                    setMaterialFilter((state) => ({
                                        ...state,
                                        type: materialFilter?.type,
                                        brand: materialFilter?.brand,
                                        finish: materialFilter?.finish,
                                        substrate: materialFilter?.substrate,
                                        priceCategory:
                                            materialFilter?.priceCategory,
                                        thickness: materialFilter?.thickness,
                                        currentPage: 1,
                                        manufacturer: {
                                            id: parseInt(e.target.value),
                                            name: e.target.options[
                                                e.target.selectedIndex
                                            ].text,
                                        },
                                        show: {
                                            id: parseInt(e.target.value)
                                                ? 0
                                                : NaN,
                                            name: parseInt(e.target.value)
                                                ? 'visible'
                                                : '',
                                        }, // set show to visible initially when manufacturer selected
                                    }))
                                }>
                                {manufacturers.map((manufacturer) => (
                                    <option
                                        key={manufacturer.id}
                                        value={manufacturer.id}>
                                        {manufacturer.value}
                                    </option>
                                ))}
                            </select>
                        </div>
                    ) : null}

                    {window?.isManufacturer ||
                    materialFilter?.manufacturer?.id ? (
                        <div style={{marginRight: '5px'}}>
                            <b style={{margin: '5px 15px'}}>Show</b>
                            <select
                                key="filter_show"
                                style={{padding: '0 16px 0 4px'}}
                                name="filter_show"
                                id="filter_show"
                                onChange={(e) =>
                                    setMaterialFilter((state) => ({
                                        ...state,
                                        type: materialFilter?.type,
                                        brand: materialFilter?.brand,
                                        finish: materialFilter?.finish,
                                        substrate: materialFilter?.substrate,
                                        priceCategory:
                                            materialFilter?.priceCategory,
                                        thickness: materialFilter?.thickness,
                                        currentPage: 1,
                                        manufacturer:
                                            materialFilter?.manufacturer,
                                        show: {
                                            id: parseInt(e.target.value),
                                            name: e.target.options[
                                                e.target.selectedIndex
                                            ].text,
                                        },
                                    }))
                                }>
                                <option value="">All</option>
                                <option
                                    value="0"
                                    selected={materialFilter?.show.id == 0}>
                                    Visible
                                </option>
                                <option
                                    value="1"
                                    selected={materialFilter?.show.id == 1}>
                                    Hidden
                                </option>
                            </select>
                        </div>
                    ) : (
                        <></>
                    )}
                    <div>
                        {window?.isManufacturer ? (
                            <>
                                <ConfirmationButton
                                    onClick={() => {}}
                                    classes="x-center small-8 medium-6 large-6"
                                    title="Mass Hide/show"
                                    message=""
                                    options={{hideFooter: true}}
                                    component={MassButton}
                                    image={EyeVisible}
                                    buttonText="Mass Hide/show">
                                    {({setShow}) => (
                                        <MassHideShow
                                            materialFilter={materialFilter}
                                            hideDialog={() => setShow(false)}
                                        />
                                    )}
                                </ConfirmationButton>
                                <ConfirmationButton
                                    onClick={() => {}}
                                    classes="x-center small-8 medium-6 large-6"
                                    title="Reset To Default"
                                    message=""
                                    options={{hideFooter: true}}
                                    component={MassButton}
                                    image={ButtonRotateAntiClockWise}
                                    buttonText="Reset To Default">
                                    {({setShow}) => (
                                        <ResetToDefault
                                            onPageMaterials={materials}
                                            action={resetToDefaultAction}
                                            materialFilter={materialFilter}
                                            hideDialog={() => setShow(false)}
                                        />
                                    )}
                                </ConfirmationButton>
                            </>
                        ) : (
                            <></>
                        )}

                        {!materialFilter?.manufacturer?.id ? (
                            <ConfirmationButton
                                onClick={() => {}}
                                classes="x-center small-8 medium-6 large-6"
                                title="Mass Cost Editing"
                                message=""
                                options={{hideFooter: true}}
                                component={MassButton}
                                image={Currency}
                                buttonText="Mass Cost Editing">
                                {({setShow}) => (
                                    <MassCostEditor
                                        onPageMaterials={materials}
                                        action={resetToDefaultAction}
                                        materialFilter={materialFilter}
                                        hideDialog={() => setShow(false)}
                                    />
                                )}
                            </ConfirmationButton>
                        ) : (
                            <></>
                        )}
                    </div>
                </div>
                <DataTable
                    columns={cols}
                    data={materials}
                    striped={true}
                    highlightOnHover={true}
                    customStyles={customStyles}
                    progressPending={isLoading || isFetching}
                    conditionalRowStyles={conditionalRowStyles}
                    selectableRows={window?.isManufacturer ? true : false}
                    selectableRowsVisibleOnly={true}
                    selectableRowsHighlight={true}
                    onSelectedRowsChange={(state) =>
                        setSelectedMaterials(state.selectedRows)
                    }
                    clearSelectedRows={toggledClearRows}
                    pagination
                    paginationServer
                    paginationPerPage={perPage}
                    paginationTotalRows={totalRows}
                    paginationComponentOptions={paginationComponentOptions}
                    onChangePage={handlePageChange}
                    onSort={handleSort}
                    sortServer
                />
            </div>
        </>
    );
};

export default BenchtopMaterial;
