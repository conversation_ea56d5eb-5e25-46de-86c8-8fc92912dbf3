import {SubmitButton} from 'components/manufacturer/Coupon/AddEditCoupon';
import Notification from 'components/manufacturer/shared/Notification';
import {FormLabel} from 'components/manufacturer/shared/TextField';
import React, {useCallback, useEffect, useState} from 'react';
import {Button} from 'react-bootstrap';
import styled from 'styled-components';
import {
    MaterialGenericInterface,
    useGetBrandsQuery,
    useGetFinishesQuery,
    useGetFormFactorsQuery,
    useGetPriceCategoriesQuery,
    useGetSubstratesQuery,
    useGetTypesQuery,
    useImportMaterialMutation,
} from '../store/benchtopModuleApi';

import {useAppDispatch} from 'store/dashboard';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';

import {series} from 'async';
import {capitalize, isEmpty, uniq, without, zip, zipObject} from 'lodash';

const FileField = styled.input`
    padding: 10px;
`;

export const Import = ({hideDialog}: {hideDialog: () => void}) => {
    const {data: types = [], isSuccess: typeSuccess} = useGetTypesQuery({
        withAllOption: false,
    });
    const {data: brands = [], isSuccess: brandSuccess} = useGetBrandsQuery({
        withAllOption: false,
    });
    const {data: finishes = [], isSuccess: finishSuccess} = useGetFinishesQuery(
        {withAllOption: false, pageSize: 1000}
    );
    const {data: formfactors = [], isSuccess: formFactorSuccess} =
        useGetFormFactorsQuery({
            withAllOption: false,
        });
    const {data: substrates = [], isSuccess: substrateSuccess} =
        useGetSubstratesQuery({
            withAllOption: false,
        });
    const {data: priceCategories = [], isSuccess: priceCateSuccess} =
        useGetPriceCategoriesQuery({
            withAllOption: false,
        });

    const [importMaterial] = useImportMaterialMutation();

    const [notInDB, setNotInDB] = useState([]);
    const [newFormFactors, setNewFormFactors] = useState<boolean>(false);
    const [requiredComponentsFetched, setRequiredComponentsFetched] =
        useState<boolean>(false);

    const [fileupload, setFileUpload] = useState<File>();
    const [importMaterialData, setImportMaterialData] = useState();
    const [showForm, setShowForm] = useState<boolean>(true);

    // method for actual import process
    const performImport = async () => {
        const materialWithKeys = importMaterialData.map((single) =>
            zipObject(
                [
                    'itemCode',
                    'name',
                    'type',
                    'brand',
                    'finish',
                    'substrate',
                    'thickness',
                    'formfactors',
                    'priceCat',
                    'roundup',
                    'blanks',
                    'grained',
                    'doubleSided',
                    'areaHandlingCost',
                    'measurement',
                    'areaCost',
                    'areaInstallationCost',
                    'minimumJobArea',
                    'length',
                    'width',
                ],
                single
            )
        );

        const mappedTypes = types.map((type) => ({
            [type.name.toLowerCase()]: type.id,
        }));
        const mappedBrands = brands.map((brand) => ({
            [brand.name.toLowerCase()]: brand.id,
        }));
        const mappedFinish = finishes.map((finish) => ({
            [finish.name.toLowerCase()]: finish.id,
        }));
        const mappedFormFactors = formfactors.map((ff) => ({
            [ff.name.toLowerCase()]: ff.id,
        }));
        const mappedSubstrates = substrates.map((sub) => ({
            [sub.name.toLowerCase()]: sub.id,
        }));
        const mappedPriceCategories = priceCategories.map((cat) => ({
            [cat.name.toLowerCase()]: cat.id,
        }));
        const {data} = await importMaterial({
            materials: materialWithKeys,
            newBenchtopComponents: {
                types: notInDB.find((r) => r.component == 'Type')?.name,
                brands: notInDB.find((r) => r.component == 'Brand')?.name,
                finishes: notInDB.find((r) => r.component == 'Finish')?.name,
                substrates: notInDB.find((r) => r.component == 'Substrate')
                    ?.name,
                priceCategories: notInDB.find(
                    (r) => r.component == 'Price Category'
                )?.name,
            },
            oldBenchtopComponents: {
                types: mappedTypes,
                brands: mappedBrands,
                finishes: mappedFinish,
                formfactors: mappedFormFactors,
                substrates: mappedSubstrates,
                priceCategories: mappedPriceCategories,
            },
        });

        const message = `${data.message} ${
            data?.errors && Object.keys(data.errors).length > 0
                ? `${Object.keys(data.errors).length} error(s),`
                : ''
        }`;

        const errorFileUrl =
            data?.errors && Object.keys(data.errors).length > 0
                ? generateErrorFile(data.errors)
                : null;

        const finalMessage = errorFileUrl
            ? `${message} <a href="${errorFileUrl}" download="errors.txt">Download Errors</a>`
            : message;

        hideDialog();
        dispatch(deleteAllNotificationsOfType('benchtopMaterial'));
        dispatch(deleteAllNotificationsOfType('benchtopUploads'));
        dispatch(
            addNotification(
                finalMessage,
                MessageVariant.SUCCESS,
                `benchtopMaterial`
            )
        );
    };

    useEffect(() => {
        if (
            typeSuccess &&
            brandSuccess &&
            finishSuccess &&
            formFactorSuccess &&
            substrateSuccess &&
            priceCateSuccess
        ) {
            setRequiredComponentsFetched(true);
        }
    }, [
        typeSuccess,
        brandSuccess,
        finishSuccess,
        formFactorSuccess,
        substrateSuccess,
        priceCateSuccess,
    ]);

    const csvToArray = function (text: string) {
        let p = '';
        let row = [''];
        let ret = [row];
        let i = 0;
        let r = 0;
        let s = !0;
        let l;
        for (l of text) {
            if ('"' === l) {
                if (s && l === p) row[i] += l;
                s = !s;
            } else if (',' === l && s) l = row[++i] = '';
            else if ('\n' === l && s) {
                if ('\r' === p) row[i] = row[i].slice(0, -1);
                row = ret[++r] = [(l = '')];
                i = 0;
            } else row[i] += l;
            p = l;
        }
        return ret;
    };

    const dispatch = useAppDispatch();
    const generateErrorFile = (errors: Record<string, string[]>) => {
        let errorText = 'Row Errors:\n';

        for (const [row, messages] of Object.entries(errors)) {
            errorText += `${row}:\n`;
            messages.forEach((msg) => {
                errorText += `  - ${msg}\n`;
            });
        }

        const blob = new Blob([errorText], {type: 'text/plain'});
        const url = URL.createObjectURL(blob);
        return url; // Return the URL for the Blob
    };
    const uploader = async () => {
        if (
            fileupload instanceof File &&
            (fileupload.type == 'text/csv' ||
                fileupload.type == 'application/vnd.ms-excel')
        ) {
            setShowForm(false);
            // remove all notifications of this type
            dispatch(deleteAllNotificationsOfType('benchtopUploads'));
            const results = await readDataFromFile();
            const filteredData = results[0]
                .split('\n') // split with new lines
                .splice(1) // remove header line
                .filter((r) => {
                    return r; // remove empty lines
                })
                .map(
                    (row) =>
                        csvToArray(row)
                            .flat()
                            .map((val) => val.trim()) // trim spaces
                );
            setImportMaterialData(filteredData);
            const [
                ,
                ,
                materialTypes,
                materialBrands,
                materialFinishes,
                materialSubstrates,
                ,
                materialFormFactors,
                materialPriceCats,
            ] = zip(...filteredData);

            const typesNotInDB = [
                ...new Set(
                    materialTypes
                        .filter((notEmpty) => notEmpty)
                        .map((item: string) => capitalize(item))
                ), // remove case insensitive duplicates
            ].filter((type: string) => {
                return !types
                    .map((type) => type.name.toLowerCase())
                    .includes(type.toLowerCase());
            });

            const brandsNotInDB = [
                ...new Set(
                    materialBrands
                        .filter((notEmpty) => notEmpty)
                        .map((item: string) => capitalize(item))
                ), // remove case insensitive duplicates
            ].filter((brand: string) => {
                return !brands
                    .map((brand) => brand.name.toLowerCase())
                    .includes(brand.toLowerCase());
            });

            const finishesNotInDB = [
                ...new Set(
                    materialFinishes
                        .filter((notEmpty) => notEmpty)
                        .map((item: string) => capitalize(item))
                ), // remove case insensitive duplicates
            ].filter((finish: string) => {
                return !finishes
                    .map((finish) => finish.name.toLowerCase())
                    .includes(finish.toLowerCase());
            });

            const formFactorsNotInDB = [
                ...new Set(
                    materialFormFactors
                        .filter((notEmpty) => notEmpty)
                        .map((item: string) => item.toUpperCase())
                ), // remove case insensitive duplicates
            ].filter((ff: string) => {
                const newFormFactors = ff.split(',');

                const ffInDBs = formfactors.map((dbFormFactor) =>
                    dbFormFactor.name.toUpperCase()
                );
                return !newFormFactors.every((ff) =>
                    ffInDBs.includes(ff.trim())
                );
            });

            const substratesNotInDB = [
                ...new Set(
                    materialSubstrates
                        .filter((notEmpty) => notEmpty)
                        .map((item: string) => item.toUpperCase())
                ), // remove case insensitive duplicates
            ].filter((substrate: string) => {
                return !substrates
                    .map((substrate) => substrate.name.toLowerCase())
                    .includes(substrate.toLowerCase());
            });

            const priceCatsNotInDB = [
                ...new Set(
                    materialPriceCats
                        .filter((notEmpty) => notEmpty)
                        .map((item: string) => item.toUpperCase())
                ), // remove case insensitive duplicates
            ].filter((cat: string) => {
                return !priceCategories
                    .map((cat) => cat.name.toLowerCase())
                    .includes(cat.toLowerCase());
            });

            const showNotInDB = [];
            if (!isEmpty(uniq(typesNotInDB))) {
                showNotInDB.push({component: 'Type', name: uniq(typesNotInDB)});
            }
            if (!isEmpty(uniq(brandsNotInDB))) {
                showNotInDB.push({
                    component: 'Brand',
                    name: uniq(brandsNotInDB),
                });
            }
            if (!isEmpty(uniq(finishesNotInDB))) {
                showNotInDB.push({
                    component: 'Finish',
                    name: uniq(finishesNotInDB),
                });
            }
            if (!isEmpty(uniq(formFactorsNotInDB))) {
                setNewFormFactors(true);
                showNotInDB.push({
                    component: 'Form Factor',
                    name: uniq(formFactorsNotInDB),
                });
            }
            if (!isEmpty(uniq(substratesNotInDB))) {
                showNotInDB.push({
                    component: 'Substrate',
                    name: uniq(substratesNotInDB),
                });
            }
            if (!isEmpty(uniq(priceCatsNotInDB))) {
                showNotInDB.push({
                    component: 'Price Category',
                    name: uniq(priceCatsNotInDB),
                });
            }
            if (!isEmpty(showNotInDB)) {
                setNotInDB(showNotInDB);
            } else {
                // All components like brands, finishes, types etc. are in DB. Just import
                const materialsWithKeys = filteredData.map((single) =>
                    zipObject(
                        [
                            'itemCode',
                            'name',
                            'type',
                            'brand',
                            'finish',
                            'substrate',
                            'thickness',
                            'formfactors',
                            'priceCat',
                            'roundup',
                            'blanks',
                            'grained',
                            'doubleSided',
                            'areaHandlingCost',
                            'measurement',
                            'areaCost',
                            'areaInstallationCost',
                            'minimumJobArea',
                            'length',
                            'width',
                        ],
                        single
                    )
                );
                const mappedTypes: MaterialGenericInterface[] = types.map(
                    (type) => ({
                        [type.name.toLowerCase()]: type.id,
                    })
                );
                const mappedBrands: MaterialGenericInterface[] = brands.map(
                    (brand) => ({
                        [brand.name.toLowerCase()]: brand.id,
                    })
                );
                const mappedFinish: MaterialGenericInterface[] = finishes.map(
                    (finish) => ({
                        [finish.name.toLowerCase()]: finish.id,
                    })
                );
                const mappedFormFactors: MaterialGenericInterface[] =
                    formfactors.map((ff) => ({
                        [ff.name.toLowerCase()]: ff.id,
                    }));
                const mappedSubstrates: MaterialGenericInterface[] =
                    substrates.map((sub) => ({
                        [sub.name.toLowerCase()]: sub.id,
                    }));
                const mappedPriceCategories: MaterialGenericInterface[] =
                    priceCategories.map((cat) => ({
                        [cat.name.toLowerCase()]: cat.id,
                    }));

                const {data} = await importMaterial({
                    materials: materialsWithKeys as string[],
                    newBenchtopComponents: [],
                    oldBenchtopComponents: {
                        types: mappedTypes,
                        brands: mappedBrands,
                        finishes: mappedFinish,
                        formfactors: mappedFormFactors,
                        substrates: mappedSubstrates,
                        priceCategories: mappedPriceCategories,
                    },
                });

                const message = `${data.message} ${
                    data?.errors && Object.keys(data.errors).length > 0
                        ? `${Object.keys(data.errors).length} error(s),`
                        : ''
                }`;

                const errorFileUrl =
                    data?.errors && Object.keys(data.errors).length > 0
                        ? generateErrorFile(data.errors)
                        : null;

                const finalMessage = errorFileUrl
                    ? `${message} <a href="${errorFileUrl}" download="errors.txt">Download Errors</a>`
                    : message;

                hideDialog();
                dispatch(deleteAllNotificationsOfType('benchtopMaterial'));
                dispatch(deleteAllNotificationsOfType('benchtopUploads'));
                dispatch(
                    addNotification(
                        finalMessage,
                        MessageVariant.SUCCESS,
                        `benchtopMaterial`
                    )
                );
            }
        } else {
            dispatch(
                addNotification(
                    `Incorrect file format`,
                    MessageVariant.ERROR,
                    `benchtopUploads`
                )
            );
        }
    };

    const readDataFromFile = async () => {
        const contents = [];

        contents.push((cb) => {
            const reader = new FileReader();

            reader.readAsText(fileupload);
            reader.onload = () => {
                cb(null, reader.result);
            };

            reader.onerror = () => {
                cb(reader.error.message);
            };
        });

        try {
            return await series(contents);
        } catch (error) {
            dispatch(
                addNotification(
                    `Error reading file`,
                    MessageVariant.ERROR,
                    `benchtopUploads`
                )
            );
        }
    };

    const closePopup = useCallback(() => {
        hideDialog();
        // remove all notifications of this type
        dispatch(deleteAllNotificationsOfType('benchtopUploads'));
    }, [hideDialog]);

    if (!requiredComponentsFetched) {
        return <h1>Loading...</h1>;
    }

    if (!showForm && newFormFactors) {
        return (
            <div>
                <h4>
                    New formfactors are in CSV sheet. Please remove those
                    formfactors and try again.
                </h4>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                    }}>
                    <SubmitButton
                        type="button"
                        className="form_50_button_right"
                        onClick={() => setShowForm(true)}
                        style={{margin: '10px 0 0 0;'}}>
                        Cancel
                    </SubmitButton>
                </div>
            </div>
        );
    }

    return (
        <>
            <Notification filterType="benchtopUploads" />
            <div style={{textAlign: 'right'}}>
                <Button onClick={closePopup}>X</Button>
            </div>
            {!showForm && !isEmpty(notInDB) ? (
                <div>
                    <h4>Following components are not persisted in database.</h4>
                    <ul>
                        {notInDB.map((item, i) => {
                            return (
                                <React.Fragment key={i}>
                                    <li>
                                        <b>{item.component} </b> -{' '}
                                        {item.name.join(', ')}
                                    </li>
                                </React.Fragment>
                            );
                        })}
                    </ul>

                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                        }}>
                        <SubmitButton
                            type="button"
                            className="form_50_button_left"
                            onClick={performImport}
                            style={{margin: '10px 0 0 0;'}}>
                            Start importing
                        </SubmitButton>
                        <SubmitButton
                            type="button"
                            className="form_50_button_right"
                            onClick={() => setShowForm(true)}
                            style={{margin: '10px 0 0 0;'}}>
                            Cancel
                        </SubmitButton>
                    </div>
                </div>
            ) : (
                <></>
            )}
            {showForm && (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                    }}>
                    <FormLabel
                        label="Upload File"
                        required={true}
                        style={{fontWeight: 'bold'}}
                    />
                    <FileField
                        type="file"
                        onChange={(e) => {
                            setFileUpload(e.target.files[0]);
                        }}
                    />
                    <SubmitButton
                        type="button"
                        className="form_50_button_left"
                        onClick={uploader}
                        style={{margin: '10px 0 0 0;'}}>
                        Upload
                    </SubmitButton>
                </div>
            )}
        </>
    );
};
