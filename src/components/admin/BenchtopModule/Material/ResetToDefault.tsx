import {Spinner} from 'assets';
import {
    addNotification,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import {capitalize, identity, isEmpty, isNull, pickBy} from 'lodash';
import React, {useEffect, useMemo, useState} from 'react';
import {Button, Image} from 'react-bootstrap';
import {useAppDispatch} from 'store/dashboard';
import {AddNewButton} from '.';
import {
    useGetMaterialCountOnResetToDefaultQuery,
    useResetToDefaultMutation,
} from '../store/benchtopModuleApi';
import {Filter, MaterialInterface} from '../store/benchtopModuleApi';

const ResetToDefault = ({
    hideDialog,
    action,
    materialFilter,
    onPageMaterials = [],
}: {
    hideDialog: () => void;
    materialFilter: Filter;
    action: () => void;
    onPageMaterials: MaterialInterface[];
}) => {
    const [filters, setFilters] = useState(materialFilter);
    const [onPageOnly, setOnPageonly] = useState(false);
    const [count, setCount] = useState(0);
    const {
        data: materialCount,
        isLoading,
        isFetching,
        isSuccess,
    } = useGetMaterialCountOnResetToDefaultQuery(filters, {
        skip: onPageOnly,
    });

    const [
        resetToDefaultMaterials,
        {
            error: resetToDefaultMaterialsError,
            isSuccess: resetToDefaultSuccess,
            isLoading: resetToDefaultLoading,
        },
    ] = useResetToDefaultMutation();

    const resetToDefaults = async () => {
        let params = pickBy(
            {
                name: filters?.name,
                type_id: filters?.type.id,
                brand_id: filters?.brand.id,
                finish_id: filters?.finish.id,
                substrate_id: filters?.substrate.id,
                thickness: filters?.thickness.id,
                resetToDefault: true,
            },
            identity
        );

        if (filters?.priceCategory?.id || isNull(filters?.priceCategory?.id)) {
            params = {
                ...params,
                price_cat_id: filters?.priceCategory?.id,
            };
        }
        try {
            await resetToDefaultMaterials({
                ...params,
                hiddenStatus: filters?.show?.id,
            }).unwrap();
            hideDialog();
            dispatch(
                addNotification(
                    `Materials updated Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopMaterial'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        setCount(materialCount);
    }, [materialCount]);

    const dispatch = useAppDispatch();
    return (
        <>
            <Button
                onClick={hideDialog}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            <h2>
                <p>You are about to apply mass changes to your materials.</p>
                <p>The changes being made to:</p>
                <br />
                {filters?.name ? (
                    <p>Name : {capitalize(filters.name)}</p>
                ) : null}
                {filters?.type ? (
                    <p>Type : {capitalize(filters.type.name)}</p>
                ) : null}
                {filters?.brand ? (
                    <p>Brand : {capitalize(filters.brand.name)}</p>
                ) : null}
                {filters?.finish ? (
                    <p>Finish : {capitalize(filters.finish.name)}</p>
                ) : null}
                {filters?.substrate ? (
                    <p>Substrate : {capitalize(filters.substrate.name)}</p>
                ) : null}
                {filters?.priceCategory ? (
                    <p>
                        Price Category :{' '}
                        {filters.priceCategory.name
                            ? capitalize(filters.priceCategory.name)
                            : 'No Price Category'}
                    </p>
                ) : null}
                {filters?.thickness ? (
                    <p>Thickness : {filters.thickness.name}</p>
                ) : null}
                {filters?.show ? (
                    <p>Show : {capitalize(filters.show.name)}</p>
                ) : null}
                <span>
                    <b>Materials:</b>
                    <select
                        onChange={(e) => {
                            if (e.target.value == '0') {
                                setOnPageonly(false);
                            } else {
                                setOnPageonly(true);
                            }
                        }}
                        style={{padding: '0 16px 0 4px', margin: '5px 15px'}}
                        name="filter_page"
                        id="filter_page">
                        <option value="0">All</option>
                        <option value="1">On Page Only</option>
                    </select>
                </span>
            </h2>
            {isLoading || isFetching ? (
                <Image
                    className="icon_button"
                    style={{
                        backgroundColor: 'transparent',
                    }}
                    src={`data:image/svg+xml;base64, ${btoa(Spinner)}`}
                />
            ) : (
                <>
                    <h5>
                        You are about to update{' '}
                        {onPageOnly
                            ? onPageMaterials.filter(
                                  (material) =>
                                      material.change.actualChangesHappen
                              ).length
                            : count}{' '}
                        materials.
                    </h5>
                    {count ||
                    onPageMaterials.filter(
                        (material) => material.change.actualChangesHappen
                    ).length ? (
                        <AddNewButton
                            disabled={resetToDefaultLoading}
                            onClick={
                                onPageOnly
                                    ? () => {
                                          hideDialog();
                                          action(onPageMaterials);
                                      }
                                    : resetToDefaults
                            }
                            style={{
                                padding: '0 21px',
                                margin: '6px 3px',
                            }}>
                            Yes
                        </AddNewButton>
                    ) : (
                        <></>
                    )}
                </>
            )}
        </>
    );
};

export default ResetToDefault;
