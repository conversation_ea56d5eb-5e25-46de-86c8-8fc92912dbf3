import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import store from 'store/dashboard';
import BenchtopMaterial from './index';
import ThemeProvider from 'theme/DashboardTheme';

const benchtopMaterialRenderer = (element: HTMLElement) => {
    const root = createRoot(element);

    root.render(
        <Provider store={store}>
            <ThemeProvider>
                <BenchtopMaterial />
            </ThemeProvider>
        </Provider>
    );
};

export default benchtopMaterialRenderer;
