import React, {useCallback, useEffect, useState} from 'react';
import DataTable, {TableColumn} from 'react-data-table-component';
import {
    EdgeProfileInterface,
    EdgeProfileFilterInterface,
    useGetEdgeProfilesQuery,
    useEditEdgeProfileMutation,
    useMassHideShowMutation,
} from '../store/benchtopModuleEdgeProfileApi';
import {AddButton, ButtonPassword, Delete, Edit} from 'assets';
import {ConfirmationButton} from 'shared/ConfirmationButton';
import AddEditEdgeProfile from './AddEditEdgeProfile';
import {AddNewButton} from '../Material';
import {
    MessageVariant,
    addNotification,
} from 'components/manufacturer/shared/NotificationSlice';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {formatPrice} from 'shared/helpers';
import Notification from 'components/manufacturer/shared/Notification';
const PER_PAGE = 25;
const BenchtopEdgeFinish = () => {
    const dispatch = useAppDispatch();
    let initialFilter: EdgeProfileFilterInterface = {
        currentPage: 1,
        pageSize: PER_PAGE,
    };
    window?.isManufacturer
        ? (initialFilter = {
              ...initialFilter,
              show: {id: 0, name: 'visible'},
          })
        : null;
    const [totalRows, setTotalRows] = useState(0);
    const [edgeProfileFilter, setEdgeProfileFilter] = useState(initialFilter);
    const [toggledClearRows, setToggledClearRows] = useState(false);
    const {
        data: {data: edgeProfiles = [], pagination} = {
            data: [],
            pagination: {total_count: 0},
        },
        isLoading,
        isFetching,
    } = useGetEdgeProfilesQuery(edgeProfileFilter);
    const [editEdgeProfile, {error: editEdgeProfileError}] =
        useEditEdgeProfileMutation();
    const [selectedEdgeProfiles, setSelectedEdgeProfiles] = useState<
        EdgeProfileInterface[]
    >([]);
    const [toggleHideShowEdgeProfiles] = useMassHideShowMutation();

    useEffect(() => {
        if (editEdgeProfileError) {
            if ('status' in editEdgeProfileError) {
                const errMsg: {
                    error?: string;
                } =
                    'error' in editEdgeProfileError
                        ? editEdgeProfileError
                        : editEdgeProfileError.data;
                dispatch(
                    addNotification(
                        errMsg.error,
                        MessageVariant.ERROR,
                        'BenchtopEdgeProfiles'
                    )
                );
            } else {
                dispatch(
                    addNotification(
                        editEdgeProfileError.message,
                        MessageVariant.ERROR,
                        'BenchtopEdgeProfiles'
                    )
                );
            }
        }
    }, [editEdgeProfileError]);

    const emptyFunction = useCallback(() => {
        return;
    }, []);

    const deleteEdgeProfile = useCallback(
        (row: EdgeProfileInterface) => () => {
            const data = {...row, ...{is_deleted: true, parent_id: null}};
            const setValue = (async function () {
                await editEdgeProfile(data);
                dispatch(
                    addNotification(
                        `Edge Profile deleted Successfully`,
                        MessageVariant.SUCCESS,
                        'BenchtopEdgeProfiles'
                    )
                );
            })();
        },
        []
    );

    const cols: TableColumn<EdgeProfileInterface>[] = [
        {
            name: 'ID',
            selector: (row) => row.id,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Item Code',
            selector: (row) => row.code,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Prefix',
            selector: (row) => row.prefix,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Name',
            selector: (row) => row.name,
            compact: true,
            wrap: true,
            center: true,
        },
        {
            name: 'Application Cost',
            selector: (row) => {
                return `${formatPrice(row.application_cost, {})}`;
            },
            center: true,
            compact: true,
            wrap: true,
        },
        {
            name: 'Area Handling Cost',
            selector: (row) => {
                return `${formatPrice(row.area_handling_cost, {})}`;
            },
            center: true,
            compact: true,
            wrap: true,
        },
        {
            name: 'Handling Cost',
            selector: (row) => {
                return `${formatPrice(row.handling_cost, {})}`;
            },
            center: true,
            wrap: true,
            compact: true,
        },
        {
            name: 'Length Cost',
            center: true,
            compact: true,
            selector: (row) => {
                return `${formatPrice(row.length_cost, {})}`;
            },
            wrap: true,
        },
        {
            name: 'Cost Multiplier(%)',
            selector: (row) => row.cost_multiplier * 100,
            compact: true,
            center: true,
            wrap: true,
        },
        {
            name: 'Action',
            cell: (row) => [
                <React.Fragment key={row.id}>
                    <ConfirmationButton
                        onClick={emptyFunction}
                        title="Edit Edge Profile"
                        classes="x-center small-11 medium-8 large-8"
                        message=""
                        options={{hideFooter: true}}
                        image={Edit}>
                        {({setShow}) => (
                            <AddEditEdgeProfile
                                hideDialog={() => setShow(false)}
                                edgeProfile={row}
                            />
                        )}
                    </ConfirmationButton>
                    {!row.is_hidden && row.is_hidden != null ? (
                        <ConfirmationButton
                            onClick={deleteEdgeProfile(row)}
                            title="Delete Edge Profile"
                            message="Are you sure you want to perform this action on the selected item?"
                            classes="x-center small-11 medium-5 large-5"
                            image={Delete}
                        />
                    ) : null}
                </React.Fragment>,
            ],
            center: true,
        },
    ];
    const conditionalRowStyles = [
        {
            when: (row: EdgeProfileInterface) =>
                window?.isManufacturer &&
                (row.is_hidden == null || row.is_hidden),
            style: {
                backgroundColor: 'lightgray',
            },
        },
    ];

    // function to toggle selected egde profiles
    const toggleHideShow = useCallback(
        (selectedEdgeProfiles: EdgeProfileInterface[]) => () => {
            const setValue = (async function () {
                setToggledClearRows(!toggledClearRows);

                const requestData: {
                    edgeProfileIds: string;
                } = {
                    edgeProfileIds: selectedEdgeProfiles
                        .map((edgeProfile) => edgeProfile.id)
                        .join(','),
                };
                await toggleHideShowEdgeProfiles(requestData);
                setSelectedEdgeProfiles([]);
            })();
        },
        []
    );
    const handlePageChange = useCallback((page: number) => {
        return setEdgeProfileFilter((state) => ({
            ...state,
            currentPage: page,
        }));
    }, []);
    useEffect(() => {
        if (pagination.total_count) {
            setTotalRows(pagination.total_count);
        }
    }, [pagination]);

    const showHideFilter = useCallback(
        (e: React.ChangeEvent<HTMLSelectElement>) => {
            return setEdgeProfileFilter((state) => ({
                ...state,
                currentPage: 1,
                manufacturer: edgeProfileFilter?.manufacturer,
                show: {
                    id: parseInt(e.target.value),
                    name: e.target.options[e.target.selectedIndex].text,
                },
            }));
        },
        []
    );

    const rowSelectedChange = useCallback(
        (state: {
            allSelected: boolean;
            selectedCount: number;
            selectedRows: EdgeProfileInterface[];
        }) => {
            return setSelectedEdgeProfiles(state.selectedRows);
        },
        []
    );
    return (
        <>
            <Notification filterType="BenchtopEdgeProfiles" />

            <div className="floating_item_body" style={{margin: '10px 0px'}}>
                <h1 className="form_header" style={{display: 'inline'}}>
                    Edge Profiles
                </h1>
                <div className="floatR pageButtons" style={{display: 'flex'}}>
                    <span>
                        {window?.isManufacturer ? (
                            <div>
                                <strong style={{margin: '5px 5px'}}>
                                    Show
                                </strong>
                                <select
                                    style={{padding: '0 16px 0 4px'}}
                                    name="filter_show"
                                    id="filter_show"
                                    onChange={showHideFilter}>
                                    <option value="">All</option>
                                    <option
                                        value="0"
                                        selected={
                                            edgeProfileFilter?.show.id == 0
                                        }>
                                        Visible
                                    </option>
                                    <option
                                        value="1"
                                        selected={
                                            edgeProfileFilter?.show.id == 1
                                        }>
                                        Hidden
                                    </option>
                                </select>
                            </div>
                        ) : null}
                    </span>
                    <span>
                        <ConfirmationButton
                            onClick={emptyFunction}
                            classes="x-center small-11 medium-8 large-8"
                            title="Add Edge Profile"
                            message=""
                            options={{hideFooter: true}}
                            component={AddNewButton}
                            image={AddButton}
                            buttonText="Add New Edge Profile">
                            {({setShow}) => {
                                return (
                                    <AddEditEdgeProfile
                                        hideDialog={() => setShow(false)}
                                    />
                                );
                            }}
                        </ConfirmationButton>
                        <a href="benchtopModule_endoptions.php">
                            <AddNewButton>Arc End Options</AddNewButton>
                        </a>
                    </span>
                </div>
                <DataTable
                    columns={cols}
                    data={edgeProfiles}
                    conditionalRowStyles={conditionalRowStyles}
                    onSelectedRowsChange={rowSelectedChange}
                    striped={true}
                    highlightOnHover={true}
                    progressPending={isLoading || isFetching}
                    clearSelectedRows={toggledClearRows}
                    selectableRowsVisibleOnly={true}
                    selectableRowsHighlight={true}
                    paginationPerPage={PER_PAGE}
                    paginationTotalRows={totalRows}
                    paginationComponentOptions={{
                        noRowsPerPage: true,
                    }}
                    onChangePage={handlePageChange}
                    pagination
                    paginationServer
                    selectableRows={window?.isManufacturer ? true : false}
                />
                <div style={{marginTop: '10px'}}>
                    {window?.isManufacturer && !isEmpty(edgeProfiles) ? (
                        <ConfirmationButton
                            onClick={toggleHideShow(selectedEdgeProfiles)}
                            classes="x-center small-8 medium-6 large-6"
                            title="Toggle Edge Profiles"
                            message={`${
                                isEmpty(selectedEdgeProfiles)
                                    ? `No Edge Profiles selected. Please select Edge Profiles to run this action.`
                                    : `Are you sure you want to perform this action on the selected edge profiles?`
                            }`}
                            options={
                                isEmpty(selectedEdgeProfiles)
                                    ? {hideFooter: true}
                                    : {}
                            }
                            component={AddNewButton}
                            image={ButtonPassword}
                            displayCloseButton={true}
                            buttonText="Hide/Show Items"
                        />
                    ) : null}
                </div>
            </div>
        </>
    );
};

export default BenchtopEdgeFinish;
