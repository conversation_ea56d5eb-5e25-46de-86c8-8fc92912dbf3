import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import store from 'store/dashboard';
import BenchtopEdgeFinish from './index';
import ThemeProvider from 'theme/DashboardTheme';

const benchtopEdgeFinishRenderer = (element: HTMLElement) => {
    const root = createRoot(element);

    root.render(
        <Provider store={store}>
            <ThemeProvider>
                <BenchtopEdgeFinish />
            </ThemeProvider>
        </Provider>
    );
};

export default benchtopEdgeFinishRenderer;
