import React, {
    ChangeEvent,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import {
    Element,
    SubmitButton,
} from 'components/manufacturer/Coupon/AddEditCoupon';
import styled from 'styled-components';
import {
    EdgeProfileInterface,
    useAddEdgeProfileMutation,
    useEditEdgeProfileMutation,
} from '../store/benchtopModuleEdgeProfileApi';
import {
    Field,
    Form,
    Formik,
    FormikHelpers,
    FormikProps,
    FormikValues,
    useFormikContext,
} from 'formik';
import {Row} from 'react-bootstrap';
import TextField, {FormLabel} from 'components/manufacturer/shared/TextField';
import {
    Filter,
    useGetBrandsQuery,
    useGetFinishesQuery,
    useGetMaterialThicknessesQuery,
    useGetSubstratesQuery,
    useGetTypesQuery,
} from '../store/benchtopModuleApi';
import {
    MessageVariant,
    addNotification,
} from 'components/manufacturer/shared/NotificationSlice';
import {useAppDispatch} from 'store/customer';
import {cloneDeep, isEmpty} from 'lodash';
import RestrictionsTable from 'shared/components/Table/RestrictionsTable';
import ImageComponent from 'components/manufacturer/shared/images/Image';
import symbols from './EdgeProfileSymbol';
import Select, {SelectOptions} from 'shared/components/Forms/CustomSelect';
import {NoImage} from 'assets';
import {List} from 'components/admin/BenchtopModule/shared/List';
import {useGetBenchtopEndOptionQuery} from '../store/benchtopModuleEndOptionApi';

const LabelCheck = styled.label`
    color: #4d4d4d;
    font-weight: 600;
    display: inline;
`;

const OuterDiv = styled.div`
    margin-left: 15px;
    display: flex;
`;

const InnerDiv = styled.div`
    border: 2px solid;
    border-radius: 5px;
    margin: 15px;
    margin-top: 15px;
`;

const CheckBoxDiv = styled.div`
    padding: 0px;
    margin: 0 15px;
`;

const FlexImages = styled(CheckBoxDiv)`
    display: flex;
    align-items: center;
    .symbols {
        margin: 0 0 0 16px;
        padding: 10px;
    }
    img {
        width: 50px;
        height: 50px;
    }
`;

export const StyledRow = styled(Row)`
    margin-top: 25px;
    margin-bottom: 25px;
`;

export const StyledHr = styled.hr`
    border: 1px dashed solid;
`;

const StyledHeader = styled.h2`
    color: #4d4d4d;
    font-weight: 700;
    margin-left: 12px;
    margin-top: 25px;
`;

const NO_SYMBOL_SELECTED = 'NoImage';

const AddEditEdgeProfile = ({
    hideDialog,
    edgeProfile,
}: {
    hideDialog: () => void;
    edgeProfile?: EdgeProfileInterface;
}) => {
    const dispatch = useAppDispatch();
    const [error, setError] = useState('');
    const [addEdgeProfile, {error: addEdgeProfileError}] =
        useAddEdgeProfileMutation();
    const [editEdgeProfile, {error: editEdgeProfileError}] =
        useEditEdgeProfileMutation();
    const {data: endOptions, isSuccess} = useGetBenchtopEndOptionQuery();
    const formikRef = useRef<FormikProps<FormikValues>>(null);

    const [initialValues, setInitialValues] = useState<EdgeProfileInterface>({
        id: 0,
        code: null,
        prefix: null,
        name: '',
        symbol: NO_SYMBOL_SELECTED,
        edge_highlight: 1,
        image_id: null,
        application_cost: 0,
        area_handling_cost: 0,
        handling_cost: 0,
        length_cost: 0,
        cost_multiplier: 0,
        restrict_adjacent: false,
        restrict_corner_clip: false,
        restrict_corner_notch: false,
        is_end_only: false,
        end_id: null,
        is_postformed_profile: false,
        is_end_roll_available: true,
        is_default_filter_by_type: true,
        is_default_filter_by_brand: true,
        is_default_filter_by_finish: true,
        is_default_filter_by_substrate: true,
        is_default_filter_by_thickness: true,
        typeRestrictions: [],
        brandRestrictions: [],
        finishRestrictions: [],
        substrateRestrictions: [],
        thicknessRestrictions: [],
    });
    let filter: Filter = {
        withAllOption: false,
    };
    if (window?.isManufacturer) {
        filter = {
            ...filter,
            show: {id: 0, name: 'visible'},
        };
    }
    const {data: types} = useGetTypesQuery(filter);
    const {data: brands} = useGetBrandsQuery(filter);
    const {data: finishes} = useGetFinishesQuery(filter);
    const {data: substrates} = useGetSubstratesQuery(filter);
    const {data: thickness} = useGetMaterialThicknessesQuery({
        withAllOption: false,
    });

    // cleanup reference
    useEffect(() => {
        return () => {
            formikRef.current = null;
        };
    }, []);

    useEffect(() => {
        if (addEdgeProfileError) {
            if ('status' in addEdgeProfileError) {
                const errMsg: {
                    error?: string;
                } =
                    'error' in addEdgeProfileError
                        ? addEdgeProfileError
                        : addEdgeProfileError.data;
                setError(errMsg.error);
            } else {
                setError(addEdgeProfileError.message);
            }
        }
    }, [addEdgeProfileError]);

    useEffect(() => {
        if (editEdgeProfileError) {
            if ('status' in editEdgeProfileError) {
                const errMsg: {
                    error?: string;
                } =
                    'error' in editEdgeProfileError
                        ? editEdgeProfileError
                        : editEdgeProfileError.data;
                setError(errMsg.error);
            } else {
                setError(editEdgeProfileError.message);
            }
        }
    }, [editEdgeProfileError]);

    useEffect(() => {
        if (!isEmpty(edgeProfile)) {
            setInitialValues((values) => ({
                ...values,
                ...cloneDeep(edgeProfile),
                symbol: isEmpty(edgeProfile.symbol)
                    ? NO_SYMBOL_SELECTED
                    : edgeProfile.symbol,
                cost_multiplier: edgeProfile.cost_multiplier * 100,
                edge_highlight: edgeProfile.edge_highlight ? 1 : 0,
            }));
        }
    }, [edgeProfile]);

    const submitForm = async (values: EdgeProfileInterface) => {
        // sanitize values
        const edgeProfileData: EdgeProfileInterface = {
            name: values.name,
            code: values.code,
            prefix: values.prefix,
            edge_highlight: values.edge_highlight,
            symbol:
                String(values.symbol) == NO_SYMBOL_SELECTED
                    ? ''
                    : String(values.symbol),
            image_id: values.image ? parseInt(values.image[0].id) : null,
            application_cost: values.application_cost,
            area_handling_cost: values.area_handling_cost,
            handling_cost: values.handling_cost,
            length_cost: values.length_cost,
            cost_multiplier: values.cost_multiplier
                ? values.cost_multiplier / 100
                : 0,
            restrict_adjacent: values.is_postformed_profile
                ? true
                : values.restrict_adjacent,
            restrict_corner_clip: values.is_postformed_profile
                ? false
                : values.restrict_corner_clip,
            restrict_corner_notch: values.is_postformed_profile
                ? false
                : values.restrict_corner_notch,
            is_postformed_profile: values.is_postformed_profile,
            is_end_only: values.is_end_only,
            end_id: values.is_end_only ? Number(values.end_id) || null : null,
            is_end_roll_available: values.is_end_roll_available,
            is_default_filter_by_type: values.is_default_filter_by_type,
            is_default_filter_by_brand: values.is_default_filter_by_brand,
            is_default_filter_by_finish: values.is_default_filter_by_finish,
            is_default_filter_by_substrate:
                values.is_default_filter_by_substrate,
            is_default_filter_by_thickness:
                values.is_default_filter_by_thickness,
            typeRestrictions: values.typeRestrictions,
            brandRestrictions: values.brandRestrictions,
            finishRestrictions: values.finishRestrictions,
            substrateRestrictions: values.substrateRestrictions,
            thicknessRestrictions: values.thicknessRestrictions,
        };

        if (!isEmpty(edgeProfile)) {
            edgeProfileData.id = edgeProfile.id;
        }
        try {
            if (isEmpty(edgeProfile)) {
                await addEdgeProfile(edgeProfileData).unwrap();
            } else {
                await editEdgeProfile(edgeProfileData).unwrap();
            }
        } catch (e) {
            throw e;
        }
    };

    const submit = useCallback(
        async (
            values: EdgeProfileInterface,
            actions: FormikHelpers<EdgeProfileInterface>
        ) => {
            try {
                await submitForm(values);
                hideDialog();
                actions.setSubmitting(false);
                actions.resetForm();
                setError('');
                dispatch(
                    addNotification(
                        `Edge Profile ${
                            isEmpty(edgeProfile) ? `Added` : `Edited`
                        } Successfully`,
                        MessageVariant.SUCCESS,
                        'BenchtopEdgeProfiles'
                    )
                );
            } catch (e) {}
        },
        []
    );

    const symbolOptions = useMemo(() => {
        const options = symbols?.map((symbol) => ({
            label: symbol.id,
            value: symbol.id.toString(),
            image: `data:image/svg+xml;base64, ${btoa(symbol.name)}`,
        }));
        return [
            {
                label: 'No Image',
                value: NO_SYMBOL_SELECTED,
                image: `data:image/svg+xml;base64, ${btoa(NoImage)}`,
            },
            ...options,
        ];
    }, [symbols]);

    const removeError = useCallback(() => {
        return setError('');
    }, []);

    // update arc end options based on manufacturer options if there are any manufacturer records so option doesn't fall on NONE option
    useEffect(() => {
        if (!isEmpty(endOptions)) {
            const endOptionId = initialValues.end_id;
            const manufacturerEndOption =
                endOptionId &&
                endOptions.find(
                    (endOption) => endOption.parent_id == endOptionId
                );

            if (manufacturerEndOption) {
                setInitialValues((values) => ({
                    ...values,
                    end_id: manufacturerEndOption.id,
                }));
            }
        }
    }, [endOptions]);

    const endOptionsDropDown = useMemo(() => {
        if (endOptions) {
            return endOptions.map((option) => {
                return {id: option.id, name: option.name};
            });
        }
    }, [endOptions]);

    // update corner options when aarc end option is selected
    const setRestrictionOnChange = useCallback(
        (e: ChangeEvent<HTMLSelectElement>) => {
            const {setFieldValue} = formikRef.current;
            const onChangeValue = parseInt(e.target.value);
            void (async () => {
                await setFieldValue('end_id', onChangeValue);
                await setFieldValue('restrict_corner_clip', onChangeValue > 0);
                await setFieldValue('restrict_corner_notch', onChangeValue > 0);
            })();
        },
        []
    );

    return (
        <div>
            {error ? (
                <div className="error" onClick={removeError}>
                    {error}
                </div>
            ) : null}
            <Formik
                initialValues={initialValues}
                enableReinitialize={true}
                innerRef={formikRef}
                onSubmit={submit}>
                {(props: FormikProps<EdgeProfileInterface>) => (
                    <Form>
                        <StyledRow>
                            <CheckBoxDiv>
                                <LabelCheck style={{marginRight: '24px'}}>
                                    End Profile Only
                                </LabelCheck>
                                <Field
                                    type="checkbox"
                                    style={{fontWeight: 'bold'}}
                                    name="is_end_only"
                                    classes="column small-12"
                                />
                            </CheckBoxDiv>

                            <CheckBoxDiv>
                                <LabelCheck style={{marginRight: '24px'}}>
                                    Is PostFormed Profile
                                </LabelCheck>
                                <Field
                                    type="checkbox"
                                    style={{fontWeight: 'bold'}}
                                    name="is_postformed_profile"
                                    classes="column small-12"
                                />
                            </CheckBoxDiv>
                            {props.values.is_postformed_profile ? (
                                <CheckBoxDiv>
                                    <LabelCheck style={{marginRight: '24px'}}>
                                        Is End Roll Available
                                    </LabelCheck>
                                    <Field
                                        type="checkbox"
                                        style={{fontWeight: 'bold'}}
                                        name="is_end_roll_available"
                                        classes="column small-12"
                                    />
                                </CheckBoxDiv>
                            ) : null}
                        </StyledRow>
                        {props.values.is_end_only && isSuccess ? (
                            <StyledRow>
                                <FlexImages>
                                    <div>
                                        <LabelCheck>Arc End option</LabelCheck>
                                    </div>
                                    <div className="symbols">
                                        <List
                                            name="end_id"
                                            options={[
                                                {id: 0, name: 'None'},
                                                ...endOptionsDropDown,
                                            ]}
                                            onChange={setRestrictionOnChange}
                                        />
                                    </div>
                                </FlexImages>
                            </StyledRow>
                        ) : null}

                        <StyledRow className="row">
                            <TextField
                                name="name"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '37%',
                                }}
                                label="Name"
                                classes="column small-3"
                            />
                            <TextField
                                name="code"
                                required={true}
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '30%',
                                }}
                                label="Code"
                                classes="column small-3"
                            />
                            <TextField
                                name="prefix"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '30%',
                                }}
                                label="Matrix Prefix"
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledRow className="row">
                            <TextField
                                name="application_cost"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '40%',
                                }}
                                label="Application Cost (LM)"
                                classes="column small-3"
                            />
                            <TextField
                                name="area_handling_cost"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '3px',
                                    alignSelf: 'center',
                                    minWidth: '35%',
                                }}
                                label="Area Handling Cost (M2)"
                                classes="column small-3"
                            />
                            <TextField
                                name="handling_cost"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '40%',
                                }}
                                label="Handling Cost"
                                classes="column small-3"
                            />
                            <TextField
                                name="length_cost"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '8px',
                                    alignSelf: 'center',
                                    minWidth: '45%',
                                }}
                                label="Length Cost (LM)"
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledRow className="row">
                            <TextField
                                name="cost_multiplier"
                                displayFlex={true}
                                style={{
                                    fontWeight: 'bold',
                                    marginRight: '4px',
                                    alignSelf: 'center',
                                    minWidth: '45%',
                                }}
                                label="Cost Multiplier (%)"
                                classes="column small-3"
                            />
                        </StyledRow>
                        <StyledRow>
                            <FlexImages>
                                <div>
                                    <LabelCheck>Symbol</LabelCheck>
                                </div>
                                <div className="symbols">
                                    <Select
                                        title="symbol"
                                        isInvalid={false}
                                        backgroundImageUrl=""
                                        value={
                                            props.values.symbol ||
                                            NO_SYMBOL_SELECTED
                                        }
                                        options={symbolOptions}
                                        showOptionLabel={false}
                                        onSelect={(option) => {
                                            props.setFieldValue(
                                                'symbol',
                                                option.value
                                            );
                                            // if no symbol is selected the edge weights should be heavy else light
                                            props.setFieldValue(
                                                'edge_highlight',
                                                String(option.value) ==
                                                    NO_SYMBOL_SELECTED
                                                    ? 1
                                                    : 0
                                            );
                                        }}
                                    />
                                </div>
                            </FlexImages>
                            <FlexImages>
                                <div>
                                    <LabelCheck>Edge line weighting</LabelCheck>
                                </div>
                                <div className="symbols">
                                    <List
                                        name="edge_highlight"
                                        options={[
                                            {
                                                name: 'Light',
                                                id: 0,
                                            },
                                            {
                                                name: 'Heavy',
                                                id: 1,
                                            },
                                        ]}
                                    />
                                </div>
                            </FlexImages>
                        </StyledRow>
                        <StyledRow className="row">
                            <CheckBoxDiv>
                                <LabelCheck style={{marginRight: '24px'}}>
                                    Restrict Adjacent Edge
                                </LabelCheck>
                                {props.values.is_postformed_profile ? (
                                    <Field
                                        type="checkbox"
                                        style={{fontWeight: 'bold'}}
                                        name="restrict_adjacent"
                                        classes="column small-12"
                                        checked={true}
                                        disabled={true}
                                    />
                                ) : (
                                    <Field
                                        type="checkbox"
                                        style={{fontWeight: 'bold'}}
                                        name="restrict_adjacent"
                                        classes="column small-12"
                                    />
                                )}
                            </CheckBoxDiv>
                            <CheckBoxDiv>
                                <LabelCheck style={{marginRight: '24px'}}>
                                    Restrict Corner Clip
                                </LabelCheck>
                                <Field
                                    type="checkbox"
                                    style={{fontWeight: 'bold'}}
                                    name="restrict_corner_clip"
                                    classes="column small-12"
                                    disabled={
                                        props.values.is_postformed_profile ||
                                        (props.values.is_end_only &&
                                            props.values.end_id > 0)
                                    }
                                />
                            </CheckBoxDiv>
                            <CheckBoxDiv>
                                <LabelCheck style={{marginRight: '24px'}}>
                                    Restrict Corner Notch
                                </LabelCheck>
                                <Field
                                    type="checkbox"
                                    style={{fontWeight: 'bold'}}
                                    name="restrict_corner_notch"
                                    classes="column small-12"
                                    disabled={
                                        props.values.is_postformed_profile ||
                                        (props.values.is_end_only &&
                                            props.values.end_id > 0)
                                    }
                                />
                            </CheckBoxDiv>
                        </StyledRow>
                        <StyledHeader> Restrictions </StyledHeader>
                        <OuterDiv>
                            <div>
                                {types ? (
                                    <RestrictionsTable
                                        data={types}
                                        defaultName="is_default_filter_by_type"
                                        name="typeRestrictions"
                                        tableHeading="Material Type"
                                    />
                                ) : null}
                            </div>
                            <div>
                                {brands ? (
                                    <RestrictionsTable
                                        data={brands}
                                        name="brandRestrictions"
                                        defaultName="is_default_filter_by_brand"
                                        tableHeading="Material Brand"
                                    />
                                ) : null}
                            </div>
                            <div>
                                {finishes ? (
                                    <RestrictionsTable
                                        data={finishes}
                                        defaultName="is_default_filter_by_finish"
                                        name="finishRestrictions"
                                        tableHeading="Material Finish"
                                    />
                                ) : null}
                            </div>
                            <div>
                                {substrates ? (
                                    <RestrictionsTable
                                        defaultName="is_default_filter_by_substrate"
                                        name="substrateRestrictions"
                                        data={substrates}
                                        tableHeading="Material Substrate"
                                    />
                                ) : null}
                            </div>
                            <div>
                                {thickness ? (
                                    <RestrictionsTable
                                        defaultName="is_default_filter_by_thickness"
                                        name="thicknessRestrictions"
                                        data={thickness}
                                        tableHeading="Material Thickness"
                                    />
                                ) : null}
                            </div>
                        </OuterDiv>
                        <Row className="row">
                            <div
                                className="column"
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                }}>
                                <FormLabel
                                    style={{fontWeight: 'bold'}}
                                    label="Image"
                                />
                                <div className="form_image">
                                    <ImageComponent
                                        name="image"
                                        imageButtonText={`${
                                            props.values.image
                                                ? `Update`
                                                : `Add`
                                        } Image`}
                                        showThumbnail={false}
                                        allowAddEditImage={
                                            window?.isAdmin ||
                                            window?.isManufacturer
                                        }
                                        allowMultipleImages={false}
                                    />
                                </div>
                            </div>
                        </Row>

                        <Element align={`justify-content: space-between;`}>
                            <SubmitButton
                                type="button"
                                className="form_50_button_left "
                                onClick={() => {
                                    props.resetForm();
                                    hideDialog();
                                }}>
                                Cancel
                            </SubmitButton>
                            <SubmitButton
                                type="submit"
                                disabled={props.isSubmitting}
                                className="form_50_button_right">
                                Save
                            </SubmitButton>
                        </Element>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default AddEditEdgeProfile;
