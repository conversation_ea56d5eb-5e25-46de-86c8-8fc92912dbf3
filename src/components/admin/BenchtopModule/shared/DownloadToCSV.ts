/**
 * Download to CSV.
 * @param {Array} header Header for the CSV.
 * @param {Array} data Data for CSV.
 * @param {String} fileName Name of the file.
 */
export function downloadToCSV(
    header: string[] = [],
    data: (string | number)[][] = [],
    fileName = 'file'
) {
    const rows = [];
    // add header
    rows.push(header);
    // add data
    Array.prototype.push.apply(rows, data);

    let csvContent = 'data:text/csv;charset=utf-8,';

    rows.forEach(function (rowArray) {
        const row = rowArray.join(',');
        csvContent += row + '\r\n';
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `${fileName}.csv`);
    document.body.appendChild(link); // Required for FF

    link.click(); // download the data file
}
