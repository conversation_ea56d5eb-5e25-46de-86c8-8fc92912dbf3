import React, {useMemo} from 'react';
import {MaterialGenericInterface} from '../store/benchtopModuleApi';
import {Edit, Delete} from 'assets';
import {TableColumn} from 'react-data-table-component';
import {Image} from 'react-bootstrap';

interface Action {
    callback: () => void;
    icon: string;
    iconTitle: string;
    key: number;
    showDialog: boolean;
}
const ActionComponent = ({callback, icon, iconTitle = ''}: Action) => {
    return (
        <Image
            onClick={callback}
            className="icon_button"
            style={{
                backgroundColor: 'transparent',
                marginLeft: '10px',
            }}
            src={`data:image/svg+xml;base64, ${btoa(icon)}`}
            title={iconTitle}
        />
    );
};

const Columns = (
    cols: TableColumn<MaterialGenericInterface>,
    edit: () => void,
    delete_: () => void
) => {
    return useMemo(
        () => [
            ...cols,
            {
                name: 'Actions',
                cell: (row) => [
                    edit ? (
                        <ActionComponent
                            key={row.id}
                            icon={Edit}
                            iconTitle="Edit"
                            showDialog={false}
                            callback={() => edit(row)}
                            // {...props}
                        />
                    ) : (
                        <></>
                    ),
                    delete_ ? (
                        <ActionComponent
                            key={row.id}
                            icon={Delete}
                            iconTitle="Delete"
                            showDialog={false}
                            callback={() => delete_(row)}
                            // {...props}
                        />
                    ) : (
                        <></>
                    ),
                ],
                center: true,
            },
        ],
        [edit, delete_]
    );
};

export {Columns};
