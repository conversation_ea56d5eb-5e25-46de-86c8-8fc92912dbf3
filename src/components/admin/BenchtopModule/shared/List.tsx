import {useField, useFormikContext} from 'formik';
import React, {useEffect} from 'react';
import styled from 'styled-components';
import {MaterialGenericInterface} from '../store/benchtopModuleApi';

const StyledField = styled.select`
    padding: 10px;
    width: 100%;
    height: auto;
    border-radius: 5px;
    border: 2px solid;
    border-color: #000 !important;
    box-shadow: none !important;
    font-weight: normal !important;
`;
export const List = ({
    name,
    options,
    setDefault = false,
    defaultName,
    defaultId = '',
    ...props
}: {
    name: string;
    options:
        | MaterialGenericInterface[]
        | {
              id: number;
              name: string;
          }[];
    setDefault: boolean;
    defaultName: string;
    defaultId: string;
}) => {
    const [field, , {setValue}] = useField({name});
    const optionList = options.map((option) => {
        return (
            <option key={option.id} value={option.id}>
                {option.name}
            </option>
        );
    });

    useEffect(() => {
        if (field.value == -1) {
            // set to first value
            setValue(options[0]?.id);
        }
    }, [options]);

    return (
        <StyledField as="select" {...field} {...props}>
            {setDefault ? (
                <option key={defaultId} value={defaultId}>
                    {defaultName}
                </option>
            ) : null}
            {optionList}
        </StyledField>
    );
};
