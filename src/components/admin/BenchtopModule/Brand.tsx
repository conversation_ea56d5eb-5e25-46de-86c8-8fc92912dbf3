import React, {useEffect, useState} from 'react';
import {<PERSON>ton, Image} from 'react-bootstrap';
import {AddButton, Spinner} from 'assets';
import DataTable from 'react-data-table-component';
import styled from 'styled-components';
import {
    MaterialGenericInterface,
    useAddBrandMutation,
    useEditBrandMutation,
    useGetBrandsQuery,
} from './store/benchtopModuleApi';
import {Columns} from './shared/Columns';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {AddNewButton} from './Material';
const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
        },
    },
};

const StyledField = styled.input`
    padding: 10px;
    width: 100%;
    border-radius: 5px;
    border: 2px solid;
    border-color: #000 !important;
    box-shadow: none !important;
    font-weight: normal !important;
`;

const Brand = ({setShowPrompt}) => {
    const [showBrandForm, setShowBrandForm] = useState<boolean>(false);
    const [deleteNotice, setDeleteNotice] = useState<string>('');
    const [brand, setBrand] = useState<MaterialGenericInterface>({
        name: '',
        is_deleted: false,
    });
    const cols = Columns(
        [
            {
                name: 'Name',
                selector: (row) => row.name,
                center: true,
            },
        ],
        (row: MaterialGenericInterface) => editButton(row),
        window?.isSuperAdmin
            ? (row: MaterialGenericInterface) => {
                  setDeleteNotice(`Are you sure you want to perform this action on the selected
                  item? This will also delete ${row.material_count} material${
                      parseInt(row.material_count) > 1 ? `s` : ``
                  } linked to this brand.`);
                  setBrand(row);
              }
            : null
    );
    const [brandName, setBrandName] = useState<string>('');

    const dispatch = useAppDispatch();

    const {
        data: brands = [],
        isLoading: loadBrands,
        isFetching: fetchBrands,
    } = useGetBrandsQuery({withAllOption: false});

    const [
        addBrand,
        {
            isLoading: loadAddNewBrand,
            error: addBrandError,
            isLoading: addbrandHappening,
            reset: addBrandReset,
        },
    ] = useAddBrandMutation();
    const [
        editBrand,
        {
            isLoading: loadEditBrand,
            error: editBrandError,
            isLoading: editbrandHappening,
            reset: editBrandReset,
        },
    ] = useEditBrandMutation();

    const saveButton = async () => {
        const data = {...brand, ...{name: brandName}};

        if (brand?.id) {
            try {
                await editBrand(data).unwrap();
                dispatch(
                    addNotification(
                        `Brand edited Successfully`,
                        MessageVariant.SUCCESS,
                        `benchtopBrand`
                    )
                );
            } catch (e) {
                throw e;
            }
        } else {
            try {
                await addBrand(data).unwrap();
                dispatch(
                    addNotification(
                        `Brand added Successfully`,
                        MessageVariant.SUCCESS,
                        `benchtopBrand`
                    )
                );
            } catch (e) {
                throw e;
            }
        }

        setBrandName('');
        setShowBrandForm(!showBrandForm);
    };

    // Edit button click handler
    const editButton = (row: MaterialGenericInterface) => {
        setBrand(row);
        setBrandName(row?.name);
        setShowBrandForm(true);
    };

    // Delete click handler
    const deleteButton = async () => {
        const data = {...brand, ...{is_deleted: true}};
        try {
            setDeleteNotice('');
            await editBrand(data).unwrap();
            dispatch(
                addNotification(
                    `Brand deleted Successfully`,
                    MessageVariant.SUCCESS,
                    `benchtopBrand`
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addBrandError)) {
            dispatch(
                addNotification(
                    addBrandError.data.error,
                    MessageVariant.ERROR,
                    `benchtopBrand`
                )
            );
            addBrandReset();
        }
        if (!isEmpty(editBrandError)) {
            dispatch(
                addNotification(
                    editBrandError.data.error,
                    MessageVariant.ERROR,
                    `benchtopBrand`
                )
            );
            editBrandReset();
        }
    }, [addBrandError, editBrandError]);

    if (!isEmpty(deleteNotice)) {
        return (
            <>
                <h2>{deleteNotice}</h2>
                <AddNewButton
                    onClick={deleteButton}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Yes
                </AddNewButton>
                <AddNewButton
                    onClick={() => setDeleteNotice('')}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    No
                </AddNewButton>
            </>
        );
    }

    return (
        <>
            <Notification filterType="benchtopBrand" />
            <Button
                onClick={() => {
                    setShowPrompt(false);
                    // reset states
                    setBrand({
                        name: '',
                        is_deleted: false,
                    });
                    setShowBrandForm(false);
                    // remove all notifications of this type
                    dispatch(deleteAllNotificationsOfType('benchtopBrand'));
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {showBrandForm ? (
                <>
                    <h2>{brand?.id ? 'Edit' : 'New'} Brand</h2>
                    <div>
                        <StyledField
                            type="text"
                            placeholder={`Enter Brand`}
                            onChange={(e) => setBrandName(e.target.value)}
                            value={brandName}
                        />
                        {addbrandHappening || editbrandHappening ? (
                            <Image
                                className="icon_button"
                                style={{
                                    backgroundColor: 'transparent',
                                }}
                                src={`data:image/svg+xml;base64, ${btoa(
                                    Spinner
                                )}`}
                            />
                        ) : (
                            <AddNewButton
                                disabled={
                                    addbrandHappening || editbrandHappening
                                }
                                style={{
                                    padding: '0 21px',
                                    margin: '6px 3px',
                                }}
                                onClick={saveButton}>
                                Save
                            </AddNewButton>
                        )}

                        <AddNewButton
                            style={{
                                padding: '0 21px',
                                margin: '6px 3px',
                            }}
                            onClick={() => {
                                setBrandName('');
                                setShowBrandForm(false);
                            }}>
                            Cancel
                        </AddNewButton>
                    </div>
                </>
            ) : (
                <>
                    <AddNewButton
                        onClick={() => {
                            setBrand({
                                name: '',
                                is_deleted: false,
                            });
                            setShowBrandForm(true);
                        }}>
                        <Image
                            className="icon_button"
                            style={{
                                backgroundColor: 'transparent',
                            }}
                            src={`data:image/svg+xml;base64, ${btoa(
                                AddButton
                            )}`}
                        />
                        Add New Brand
                    </AddNewButton>
                    <DataTable
                        columns={cols}
                        data={brands}
                        striped={true}
                        highlightOnHover={true}
                        dense={true}
                        customStyles={customStyles}
                        progressPending={
                            loadBrands ||
                            fetchBrands ||
                            loadAddNewBrand ||
                            loadEditBrand
                        }
                    />
                </>
            )}
        </>
    );
};

export default Brand;
