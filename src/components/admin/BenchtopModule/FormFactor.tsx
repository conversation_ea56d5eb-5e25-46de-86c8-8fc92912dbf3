import React, {useEffect, useState} from 'react';
import {Button, Image} from 'react-bootstrap';
import {Spinner} from 'assets';
import DataTable from 'react-data-table-component';
import styled from 'styled-components';
import {
    MaterialGenericInterface,
    useAddFormFactorMutation,
    useGetFormFactorsQuery,
    useEditFormFactorMutation,
} from './store/benchtopModuleApi';
import {
    addNotification,
    deleteAllNotificationsOfType,
    MessageVariant,
} from 'components/manufacturer/shared/NotificationSlice';
import Notification from 'components/manufacturer/shared/Notification';
import {useAppDispatch} from 'store/dashboard';
import {isEmpty} from 'lodash';
import {AddNewButton} from './Material';
const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
        },
    },
};

const StyledField = styled.input`
    padding: 10px;
    width: 100%;
    border-radius: 5px;
    border: 2px solid;
    border-color: #000 !important;
    box-shadow: none !important;
    font-weight: normal !important;
`;

const FormFactor = ({setShowPrompt}) => {
    const [showFormFactorForm, setShowFormFactorForm] =
        useState<boolean>(false);
    const [deleteNotice, setDeleteNotice] = useState<string>('');
    const [formFactor, setFormFactor] = useState<MaterialGenericInterface>({
        name: '',
        is_deleted: false,
    });
    const cols = [
        {
            name: 'Name',
            selector: (row) => row.name,
            center: true,
        },
        {
            name: 'Identifiers',
            selector: (row) => row.identifier,
            center: true,
        },
    ];
    // (row: MaterialGenericInterface) => editButton(row),
    // window?.isSuperAdmin
    //     ? (row: MaterialGenericInterface) => {
    //           setDeleteNotice(`Are you sure you want to perform this action on the selected
    //           item? This will also delete ${row.material_count} material${
    //               parseInt(row.material_count) > 1 ? `s` : ``
    //           } linked to this form factor.`);
    //           setFormFactor(row);
    //       }
    //     : null

    const [formFactorName, setFormFactorName] = useState<string>('');

    const dispatch = useAppDispatch();

    const {
        data: formFactors = [],
        isLoading: loadAll,
        isFetching: fetchAll,
    } = useGetFormFactorsQuery({withAllOption: false});

    const [
        addFormFactor,
        {
            isLoading: loadAdd,
            error: addError,
            isLoading: addHappening,
            reset: addReset,
        },
    ] = useAddFormFactorMutation();
    const [
        editFormFactor,
        {
            isLoading: loadEdit,
            error: editError,
            isLoading: editHappening,
            reset: editReset,
        },
    ] = useEditFormFactorMutation();

    const saveButton = async () => {
        const data = {...formFactor, ...{name: formFactorName}};

        if (formFactor?.id) {
            try {
                await editFormFactor(data).unwrap();
                dispatch(
                    addNotification(
                        `FormFactor edited Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopFormFactor'
                    )
                );
            } catch (e) {
                throw e;
            }
        } else {
            try {
                await addFormFactor(data).unwrap();
                dispatch(
                    addNotification(
                        `FormFactor added Successfully`,
                        MessageVariant.SUCCESS,
                        'benchtopFormFactor'
                    )
                );
            } catch (e) {
                throw e;
            }
        }

        setFormFactorName('');
        setShowFormFactorForm(!showFormFactorForm);
    };

    // Edit button click handler
    const editButton = (row: MaterialGenericInterface) => {
        setFormFactor(row);
        setFormFactorName(row?.name);
        setShowFormFactorForm(true);
    };

    // Delete click handler
    const deleteButton = async () => {
        const data = {...formFactor, ...{is_deleted: true}};
        try {
            setDeleteNotice('');
            await editFormFactor(data).unwrap();
            dispatch(
                addNotification(
                    `FormFactor deleted Successfully`,
                    MessageVariant.SUCCESS,
                    'benchtopFormFactor'
                )
            );
        } catch (e) {
            throw e;
        }
    };

    useEffect(() => {
        if (!isEmpty(addError)) {
            dispatch(
                addNotification(
                    addError.data.error,
                    MessageVariant.ERROR,
                    'benchtopFormFactor'
                )
            );
            addReset();
        }
        if (!isEmpty(editError)) {
            dispatch(
                addNotification(
                    editError.data.error,
                    MessageVariant.ERROR,
                    'benchtopFormFactor'
                )
            );
            editReset();
        }
    }, [addError, editError]);

    if (!isEmpty(deleteNotice)) {
        return (
            <>
                <h2>{deleteNotice}</h2>
                <AddNewButton
                    onClick={deleteButton}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    Yes
                </AddNewButton>
                <AddNewButton
                    onClick={() => setDeleteNotice('')}
                    style={{
                        padding: '0 21px',
                        margin: '6px 3px',
                    }}>
                    No
                </AddNewButton>
            </>
        );
    }

    return (
        <>
            <Notification filterType="benchtopFormFactor" />
            <Button
                onClick={() => {
                    setShowPrompt(false);
                    // reset states
                    setFormFactor({
                        name: '',
                        is_deleted: false,
                    });
                    setShowFormFactorForm(false);
                    // remove all notifications of this type
                    dispatch(
                        deleteAllNotificationsOfType('benchtopFormFactor')
                    );
                }}
                style={{
                    float: 'right',
                    position: 'absolute',
                    top: '10px',
                    right: '12px',
                }}>
                X
            </Button>
            {showFormFactorForm ? (
                <>
                    <h2>{formFactor?.id ? 'Edit' : 'New'} FormFactor</h2>
                    <div>
                        <StyledField
                            type="text"
                            placeholder={`Enter FormFactor`}
                            onChange={(e) => setFormFactorName(e.target.value)}
                            value={formFactorName}
                        />
                        {addHappening || editHappening ? (
                            <Image
                                className="icon_button"
                                style={{
                                    backgroundColor: 'transparent',
                                }}
                                src={`data:image/svg+xml;base64, ${btoa(
                                    Spinner
                                )}`}
                            />
                        ) : (
                            <AddNewButton
                                disabled={addHappening || editHappening}
                                style={{
                                    padding: '0 21px',
                                    margin: '6px 3px',
                                }}
                                onClick={saveButton}>
                                Save
                            </AddNewButton>
                        )}

                        <AddNewButton
                            style={{
                                padding: '0 21px',
                                margin: '6px 3px',
                            }}
                            onClick={() => {
                                setFormFactorName('');
                                setShowFormFactorForm(false);
                            }}>
                            Cancel
                        </AddNewButton>
                    </div>
                </>
            ) : (
                <>
                    {/* <AddNewButton
                        onClick={() => {
                            setFormFactor({
                                name: '',
                                is_deleted: false,
                            });
                            setShowFormFactorForm(true);
                        }}>
                        <Image
                            className="icon_button"
                            style={{
                                backgroundColor: 'transparent',
                            }}
                            src={`data:image/svg+xml;base64, ${btoa(
                                AddButton
                            )}`}
                        />
                        Add New FormFactor
                    </AddNewButton> */}
                    <div style={{paddingTop: '35px'}} />
                    <DataTable
                        columns={cols}
                        data={formFactors}
                        striped={true}
                        highlightOnHover={true}
                        dense={true}
                        customStyles={customStyles}
                        progressPending={
                            loadAll || fetchAll || loadAdd || loadEdit
                        }
                    />
                </>
            )}
        </>
    );
};

export default FormFactor;
