import React from 'react';
import styled from 'styled-components';

// Styled components
const EditorContent = styled.div`
    display: flex;
    flex-direction: column;
    gap: 4px;
    position: relative;
    padding-right: 8px;
`;

const InputWithDragHandle = styled.div`
    display: flex;
    align-items: center;
    gap: 4px;
`;

const PositionRow = styled.div`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
`;

const Input = styled.input`
    background: #333;
    border: 1px solid #444;
    border-radius: 3px;
    color: #f1f1f1;
    padding: 4px 8px;
    width: 80px;
    font-size: 13px;
    &:focus {
        outline: none;
        border-color: #666;
    }
`;

const MeasurementDisplay = styled.div`
    font-size: 11px;
    color: #888;
    margin-left: 5px;
    margin-right: auto;
`;

const WallDistanceLabel = styled.div`
    font-size: 12px;
    color: #aaa;
    margin-bottom: 4px;
`;

interface DistanceEditorProps {
    activeDirection: 'right' | 'left' | 'back' | 'front' | 'up' | 'down' | null;
    wallDistanceInputs: {
        left: string;
        right: string;
        back: string;
        front: string;
        top: string;
        bottom: string;
    };
    inputRefs: {
        right: React.RefObject<HTMLInputElement>;
        left: React.RefObject<HTMLInputElement>;
        back: React.RefObject<HTMLInputElement>;
        front: React.RefObject<HTMLInputElement>;
        up: React.RefObject<HTMLInputElement>;
        down: React.RefObject<HTMLInputElement>;
    };
    handleRightWallChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleLeftWallChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleBackWallChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleFrontWallChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleTopWallChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleBottomWallChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const DistanceEditor: React.FC<DistanceEditorProps> = ({
    activeDirection,
    wallDistanceInputs,
    inputRefs,
    handleRightWallChange,
    handleLeftWallChange,
    handleBackWallChange,
    handleFrontWallChange,
    handleTopWallChange,
    handleBottomWallChange,
}) => {
    if (!activeDirection) return null;

    return (
        <EditorContent>
            {activeDirection === 'right' && (
                <>
                    <WallDistanceLabel>Distance from wall:</WallDistanceLabel>
                    <PositionRow>
                        <InputWithDragHandle>
                            <Input
                                ref={inputRefs.right}
                                type="text"
                                value={wallDistanceInputs.right}
                                onChange={handleRightWallChange}
                            />
                        </InputWithDragHandle>
                        <MeasurementDisplay>mm</MeasurementDisplay>
                    </PositionRow>
                </>
            )}

            {activeDirection === 'left' && (
                <>
                    <WallDistanceLabel>Distance from wall:</WallDistanceLabel>
                    <PositionRow>
                        <InputWithDragHandle>
                            <Input
                                ref={inputRefs.left}
                                type="text"
                                value={wallDistanceInputs.left}
                                onChange={handleLeftWallChange}
                            />
                        </InputWithDragHandle>
                        <MeasurementDisplay>mm</MeasurementDisplay>
                    </PositionRow>
                </>
            )}

            {activeDirection === 'back' && (
                <>
                    <WallDistanceLabel>Distance from wall:</WallDistanceLabel>
                    <PositionRow>
                        <InputWithDragHandle>
                            <Input
                                ref={inputRefs.back}
                                type="text"
                                value={wallDistanceInputs.back}
                                onChange={handleBackWallChange}
                            />
                        </InputWithDragHandle>
                        <MeasurementDisplay>mm</MeasurementDisplay>
                    </PositionRow>
                </>
            )}

            {activeDirection === 'front' && (
                <>
                    <WallDistanceLabel>Distance from wall:</WallDistanceLabel>
                    <PositionRow>
                        <InputWithDragHandle>
                            <Input
                                ref={inputRefs.front}
                                type="text"
                                value={wallDistanceInputs.front}
                                onChange={handleFrontWallChange}
                            />
                        </InputWithDragHandle>
                        <MeasurementDisplay>mm</MeasurementDisplay>
                    </PositionRow>
                </>
            )}

            {activeDirection === 'up' && (
                <>
                    <WallDistanceLabel>
                        Distance from ceiling:
                    </WallDistanceLabel>
                    <PositionRow>
                        <InputWithDragHandle>
                            <Input
                                ref={inputRefs.up}
                                type="text"
                                value={wallDistanceInputs.top}
                                onChange={handleTopWallChange}
                            />
                        </InputWithDragHandle>
                        <MeasurementDisplay>mm</MeasurementDisplay>
                    </PositionRow>
                </>
            )}

            {activeDirection === 'down' && (
                <>
                    <WallDistanceLabel>Distance from floor:</WallDistanceLabel>
                    <PositionRow>
                        <InputWithDragHandle>
                            <Input
                                ref={inputRefs.down}
                                type="text"
                                value={wallDistanceInputs.bottom}
                                onChange={handleBottomWallChange}
                            />
                        </InputWithDragHandle>
                        <MeasurementDisplay>mm</MeasurementDisplay>
                    </PositionRow>
                </>
            )}
        </EditorContent>
    );
};

export default DistanceEditor;
