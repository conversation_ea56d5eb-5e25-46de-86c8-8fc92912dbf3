import React, {useCallback, useState, useEffect} from 'react';
import * as THREE from 'three';
import {Icon} from 'shared/helpers';
import {useAppSelector} from 'store/customer';
import {getActiveGroup} from 'components/customer/RoomPlanner/store/roomPlannerSlice';
import {OverlayTrigger} from 'shared';
import FloatingMenuButton from 'components/customer/RoomPlanner/components/common/FloatingMenuButton';

const Movement = () => {
    const activeGroup = useAppSelector(getActiveGroup);
    const [isMovementMode, setIsMovementMode] = useState(false);

    // Sync movement mode state with the active cabinet when it changes
    useEffect(() => {
        if (activeGroup) {
            // Get the current movement mode state from the cabinet
            const currentMovementMode = !!activeGroup.userData.isMovementMode;

            // Update local state if it doesn't match the cabinet's state
            if (isMovementMode !== currentMovementMode) {
                setIsMovementMode(currentMovementMode);

                // Make sure arrows are shown/hidden appropriately
                const userData = activeGroup.userData as {
                    setActiveState?: (isActive: boolean) => void;
                };

                if (typeof userData?.setActiveState === 'function') {
                    userData.setActiveState(currentMovementMode);
                }
            }
        } else {
            // Reset state when no cabinet is selected
            setIsMovementMode(false);
        }
    }, [activeGroup, isMovementMode]);

    const handleMovementClick = useCallback(() => {
        setIsMovementMode((prev) => !prev);

        if (activeGroup) {
            // Set movement mode on the cabinet
            activeGroup.userData.isMovementMode = !isMovementMode;

            // Reset hover states for all arrows to prevent stale hover states
            if (activeGroup.userData.arrowGroup) {
                const arrowGroup = activeGroup.userData
                    .arrowGroup as THREE.Group;

                // Reset hover state for each arrow
                if (arrowGroup.children) {
                    arrowGroup.children.forEach((arrow: THREE.Object3D) => {
                        if (
                            arrow.userData &&
                            arrow.userData.isArrow &&
                            arrow.userData.isHovered
                        ) {
                            if (
                                typeof arrow.userData.onHoverEnd === 'function'
                            ) {
                                (
                                    arrow.userData as {
                                        onHoverEnd: () => void;
                                    }
                                ).onHoverEnd();
                            }
                            arrow.userData.isHovered = false;
                        }
                    });
                }
            }

            // Safe typing of userData
            const userData = activeGroup.userData as {
                setActiveState?: (isActive: boolean) => void;
            };

            if (typeof userData?.setActiveState === 'function') {
                userData.setActiveState(!isMovementMode);
            }
        }
    }, [activeGroup, isMovementMode]);

    return (
        <OverlayTrigger placement="top" overlay={<strong>Reposition</strong>}>
            <FloatingMenuButton
                onClick={handleMovementClick}
                className={isMovementMode ? 'active' : ''}>
                <Icon
                    className="icon_button"
                    color={isMovementMode ? '#29b6f6' : '#b2bec3'}
                    iconName="Button-Drag.svg"
                />
            </FloatingMenuButton>
        </OverlayTrigger>
    );
};

export default Movement;
