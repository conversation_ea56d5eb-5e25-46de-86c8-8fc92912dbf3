import React, {useRef, useState, useEffect, useCallback} from 'react';
import {useAppSelector} from 'store/customer';
import styled from 'styled-components';
import {
    getActiveGroup,
    getRoomDimensions,
    getRoomType,
} from '../../store/roomPlannerSlice';
import useAutoSave from '../../lib/useAutoSave';
import * as THREE from 'three';
import {shallowEqual} from 'react-redux';
import DistanceEditor from './DistanceEditor';

// Define custom userData interface for the THREE.Group
interface CustomUserData {
    move?: (position: THREE.Vector3) => void;
    resetArrowVisibility?: () => void;
    onArrowClick?: (params: {
        direction: 'right' | 'left' | 'back' | 'front' | 'up' | 'down';
        screenX: number;
        screenY: number;
        arrowMesh: THREE.Mesh;
    }) => void;
    currentDirection?: string;
    movementArrows?: THREE.Mesh[];
    [key: string]: unknown; // Allow for other properties
}

// Styled components
const EditorContainer = styled.div`
    position: fixed;
    background: #1e1e1e;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 8px 12px;
    width: 200px;
    z-index: 2000;
    color: #f1f1f1;
`;

const CloseButton = styled.button`
    position: absolute;
    top: -4px;
    right: -8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #444;
    border: none;
    color: #fff;
    font-size: 14px;
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;

    &:hover {
        background: #555;
    }
`;

const MeasurementDisplay = styled.div`
    font-size: 11px;
    color: #888;
    margin-left: 5px;
    margin-right: auto;
`;

const InputGroup = styled.div`
    display: flex;
    align-items: center;
`;

const StepButton = styled.button`
    background: #444;
    border: none;
    border-radius: 3px;
    color: #f1f1f1;
    width: 55px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    margin-left: 4px;
    font-size: 12px;
    &:hover {
        background: #555;
    }
`;

const MovementControlContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
`;

const MovementRow = styled.div`
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 6px;
`;

const MovementLabel = styled.div`
    font-size: 12px;
    color: #aaa;
`;

const MovementInput = styled.input`
    background: #333;
    border: 1px solid #444;
    border-radius: 3px;
    color: #f1f1f1;
    padding: 4px 8px;
    width: 80px;
    font-size: 13px;
    text-align: center;
    &:focus {
        outline: none;
        border-color: #666;
    }
`;

type ComputedDimension = {
    computedWidth: number;
    roomWidth: number;
    roomDepth: number;
    roomDepthComputed: number;
    wallHeight: number;
    scale: number;
    roomLeftDepth: number;
    roomRightDepth: number;
    roomLeftWidth: number;
    roomRightWidth: number;
    positionX?: number;
    defaultPositionZ?: number;
};

const PositionEditor = () => {
    const activeGroup = useAppSelector(getActiveGroup);
    const {debouncedSave} = useAutoSave();
    const {rectangularDimension, lShapedDimension} = useAppSelector(
        getRoomDimensions,
        shallowEqual
    );
    const roomType = useAppSelector(getRoomType);

    const editorRef = useRef<HTMLDivElement>(null);
    const prevActiveGroupRef = useRef<THREE.Group | null>(null);

    // Initial position in the center of the screen
    // const [position, setPosition] = useState(() => {
    //     const initialX = window.innerWidth / 2 - 150; // 150 is half the width of the editor
    //     const initialY = window.innerHeight / 2 - 250; // Approximate half height
    //     return {x: initialX, y: initialY};
    // });

    // Dragging state
    // const isDraggingRef = useRef(false);
    // const startPosRef = useRef({x: 0, y: 0});
    // const editorPosRef = useRef({x: 0, y: 0});

    // // Window bounds
    // const [windowSize, setWindowSize] = useState({
    //     width: window.innerWidth,
    //     height: window.innerHeight,
    // });

    // Use the room dimensions with scaling factor
    const roomWidth = rectangularDimension.width * 0.2 || 0;

    // Store cabinet dimensions and position
    const [cabinetDimensions, setCabinetDimensions] = useState({
        width: 0,
        height: 0,
        depth: 0,
    });

    // Store distance from walls as editable strings
    const [wallDistanceInputs, setWallDistanceInputs] = useState({
        left: '0',
        right: '0',
        back: '0',
        front: '0',
        top: '0',
        bottom: '0',
    });

    // Add state for active direction and position
    type Direction = 'right' | 'left' | 'back' | 'front' | 'up' | 'down';
    const [activeDirection, setActiveDirection] = useState<Direction | null>(
        null
    );
    const [editorPosition, setEditorPosition] = useState({x: 0, y: 0});

    // Add refs for input fields
    const inputRefs: Record<Direction, React.RefObject<HTMLInputElement>> = {
        right: useRef<HTMLInputElement>(null),
        left: useRef<HTMLInputElement>(null),
        back: useRef<HTMLInputElement>(null),
        front: useRef<HTMLInputElement>(null),
        up: useRef<HTMLInputElement>(null),
        down: useRef<HTMLInputElement>(null),
    };

    // New state for movement amount
    const [movementAmount, setMovementAmount] = useState('100');

    // Add a new state to track if currently dragging

    // Update window size on resize
    // useEffect(() => {
    //     const handleResize = () => {
    //         setWindowSize({
    //             width: window.innerWidth,
    //             height: window.innerHeight,
    //         });
    //     };

    //     window.addEventListener('resize', handleResize);
    //     return () => window.removeEventListener('resize', handleResize);
    // }, []);

    // const handleMouseMove = useCallback(
    //     (e: MouseEvent) => {
    //         if (!isDraggingRef.current) return;

    //         // Calculate the new position
    //         const deltaX = e.clientX - startPosRef.current.x;
    //         const deltaY = e.clientY - startPosRef.current.y;

    //         const newX = editorPosRef.current.x + deltaX;
    //         const newY = editorPosRef.current.y + deltaY;

    //         // Keep editor within window bounds
    //         const editorWidth = editorRef.current?.offsetWidth || 300;
    //         const editorHeight = editorRef.current?.offsetHeight || 500;

    //         const boundedX = Math.max(
    //             0,
    //             Math.min(newX, windowSize.width - editorWidth)
    //         );
    //         const boundedY = Math.max(
    //             0,
    //             Math.min(newY, windowSize.height - editorHeight)
    //         );

    //         // setPosition({x: boundedX, y: boundedY});
    //     },
    //     [windowSize]
    // );

    // const handleMouseUp = useCallback(() => {
    //     isDraggingRef.current = false;
    //     document.removeEventListener('mousemove', handleMouseMove);
    //     document.removeEventListener('mouseup', handleMouseUp);
    // }, [handleMouseMove]);

    // Get cabinet dimensions
    const getCabinetDimensions = useCallback(() => {
        if (!activeGroup) return {width: 0, height: 0, depth: 0};

        let width = 0;
        let height = 0;
        let depth = 0;

        // If userData doesn't have dimensions, try to compute from bounding box
        if (width === 0 && height === 0 && depth === 0) {
            // Create a bounding box for the entire group
            const bbox = new THREE.Box3().setFromObject(activeGroup);
            width = bbox.max.x - bbox.min.x;
            height = bbox.max.y - bbox.min.y;
            depth = bbox.max.z - bbox.min.z;
        }

        return {width, height, depth};
    }, [activeGroup]);

    const getRoomDimensionsComputed = useCallback((): ComputedDimension => {
        const scale = 0.2;
        return {
            computedWidth:
                ((roomType === 'RECTANGULAR'
                    ? rectangularDimension.width
                    : lShapedDimension.rightWidth) /
                    2) *
                    -scale +
                26.1,
            roomWidth:
                (roomType === 'RECTANGULAR'
                    ? rectangularDimension.width
                    : lShapedDimension.rightWidth) * scale,
            roomDepth:
                roomType === 'RECTANGULAR'
                    ? rectangularDimension.length
                    : lShapedDimension.leftWidth,
            roomDepthComputed:
                ((roomType === 'RECTANGULAR'
                    ? rectangularDimension.length
                    : lShapedDimension.leftWidth) /
                    2) *
                    -scale -
                25,
            wallHeight:
                (roomType === 'RECTANGULAR'
                    ? rectangularDimension.height
                    : lShapedDimension.height) * scale,
            scale,
            roomLeftDepth: lShapedDimension.leftDepth || 0,
            roomRightDepth: lShapedDimension.rightDepth || 0,
            roomLeftWidth: lShapedDimension.leftWidth || 0,
            roomRightWidth: lShapedDimension.rightWidth || 0,
        };
    }, [roomType, rectangularDimension, lShapedDimension]);

    // Calculate distances from walls based on cabinet position and dimensions
    const calculateWallDistances = useCallback(() => {
        if (!activeGroup || !rectangularDimension) return;

        const dimensions = getRoomDimensionsComputed();

        const {width, depth, height} = getCabinetDimensions();

        const halfWidth = width / 2;
        const halfHeight = height / 2;

        // Get cabinet position
        const position = activeGroup.position;
        const roomHalfWidth = roomWidth / 2;
        // Calculate distance from each wall
        const rightDistance =
            (roomHalfWidth - position.x - halfWidth) * 5 - 260;
        const leftDistance = (-dimensions.computedWidth + position.x) * 5 - 25;
        const backDistance =
            (-dimensions.roomDepthComputed + position.z - depth) * 5;
        const frontDistance = (-dimensions.roomDepthComputed - position.z) * 5;

        // Calculate top distance (from ceiling)
        const topDistance =
            (dimensions.wallHeight / 2 - (position.y + halfHeight)) * 5;

        // Calculate bottom distance (from floor)
        const bottomDistance =
            (position.y - halfHeight - -dimensions.wallHeight / 2) * 5;

        // Use functional update to prevent dependency issues
        setWallDistanceInputs((prevInputs) => {
            // Only update if values have changed significantly
            const newLeft = Math.round(leftDistance).toString();
            const newRight = Math.max(0, Math.round(rightDistance)).toString();
            const newBack = Math.round(
                Math.round(backDistance - 50)
            ).toString();
            const newFront = Math.max(0, Math.round(frontDistance)).toString();
            const newTop = Math.round(topDistance + 50).toString();
            const newBottom = Math.round(bottomDistance + 49).toString();

            if (
                prevInputs.left !== newLeft ||
                prevInputs.right !== newRight ||
                prevInputs.back !== newBack ||
                prevInputs.front !== newFront ||
                prevInputs.top !== newTop ||
                prevInputs.bottom !== newBottom
            ) {
                return {
                    left: newLeft,
                    right: newRight,
                    back: newBack,
                    front: newFront,
                    top: newTop,
                    bottom: newBottom,
                };
            }
            return prevInputs;
        });
    }, [
        activeGroup,
        rectangularDimension,
        roomWidth,
        getRoomDimensionsComputed,
        getCabinetDimensions,
    ]);

    // First useEffect - Initialize cabinet dimensions when activeGroup changes
    useEffect(() => {
        // Only update if the activeGroup actually changed (not just a re-render)
        if (activeGroup && activeGroup !== prevActiveGroupRef.current) {
            prevActiveGroupRef.current = activeGroup;
            const dimensions = getCabinetDimensions();
            setCabinetDimensions(dimensions);
        }
    }, [activeGroup, getCabinetDimensions]);

    // Third useEffect - Calculate wall distances when needed
    useEffect(() => {
        if (activeGroup && activeGroup.position) {
            // Use a timeout to prevent too frequent updates
            const timeoutId = setTimeout(() => {
                calculateWallDistances();
            }, 50);

            return () => clearTimeout(timeoutId);
        }
    }, [activeGroup, calculateWallDistances]);

    // Function to update arrow positions
    const updateArrowPositions = useCallback(() => {
        if (!activeGroup) return;

        // Get the scene from the activeGroup
        const scene = activeGroup.parent;
        if (!scene) return;

        // Find and update the movement arrows
        scene.children.forEach((child: THREE.Object3D) => {
            if (child.name === 'cabinetMovementArrows') {
                // Update arrow positions based on new cabinet position
                const arrowGroup = child;
                arrowGroup.position.copy(activeGroup.position);

                // Reset hover states on all arrows when repositioning
                arrowGroup.children.forEach((arrow) => {
                    if (arrow.userData && arrow.userData.isArrow) {
                        // If arrow is in a hovered state, remove the hover effect
                        if (arrow.userData.isHovered) {
                            if (
                                typeof arrow.userData.onHoverEnd === 'function'
                            ) {
                                (
                                    arrow.userData as {
                                        onHoverEnd: () => void;
                                    }
                                ).onHoverEnd();
                            }
                            arrow.userData.isHovered = false;
                        }
                    }
                });
            }
        });
    }, [activeGroup]);

    // Function to apply position changes based on wall distance
    const applyWallDistanceChange = useCallback(
        (
            wall: 'left' | 'right' | 'back' | 'front' | 'top' | 'bottom',
            value: string
        ) => {
            if (!activeGroup) return;

            // Get the numeric value
            const numValue = value === '' ? 0 : parseFloat(value);
            if (isNaN(numValue)) return;

            const {width, depth, height} = cabinetDimensions;
            const halfWidth = width / 2;
            const halfHeight = height / 2;
            const roomHalfWidth = roomWidth / 2;
            const dimensions = getRoomDimensionsComputed();

            let newX = activeGroup.position.x;
            let newY = activeGroup.position.y;
            let newZ = activeGroup.position.z;

            // Calculate new position based on which wall was changed
            switch (wall) {
                case 'right':
                    newX = roomHalfWidth - halfWidth - (numValue + 260) / 5;
                    break;
                case 'left':
                    newX = (numValue + 25) / 5 + dimensions.computedWidth;
                    break;
                case 'back':
                    newZ =
                        dimensions.roomDepthComputed +
                        depth +
                        50 +
                        numValue / 5;
                    break;
                case 'front':
                    newZ = -dimensions.roomDepthComputed - numValue / 5;
                    break;
                case 'top':
                    newY =
                        dimensions.wallHeight / 2 -
                        numValue / 5 -
                        halfHeight +
                        10;
                    break;
                case 'bottom':
                    newY =
                        numValue / 5 -
                        dimensions.wallHeight / 2 +
                        halfHeight -
                        10;
                    break;
            }

            // Create a new position vector
            const newPosition = new THREE.Vector3(newX, newY, newZ);

            // Move the cabinet
            const userData = activeGroup.userData as CustomUserData;
            if (userData.move) {
                userData.move(newPosition);
            } else {
                activeGroup.position.copy(newPosition);
            }

            // Update arrow positions after cabinet moves
            updateArrowPositions();

            // Update position inputs
            calculateWallDistances();

            // Save the changes
            debouncedSave(activeGroup);
        },
        [
            activeGroup,
            cabinetDimensions,
            roomWidth,
            getRoomDimensionsComputed,
            debouncedSave,
            updateArrowPositions,
            calculateWallDistances,
        ]
    );

    // Function to handle changes to wall distance inputs with immediate application
    const handleWallDistanceChange = (
        wall: 'left' | 'right' | 'back' | 'front' | 'top' | 'bottom',
        value: string
    ) => {
        // Update the input value
        setWallDistanceInputs({
            ...wallDistanceInputs,
            [wall]: value,
        });

        // Apply the changes immediately
        applyWallDistanceChange(wall, value);
    };

    // Updated Move functions to use the movement amount
    const handleMoveWithAmount = useCallback(
        (direction: string) => {
            if (!activeGroup) return;

            // Get the movement amount value (default to 0 if empty or invalid)
            const amount =
                movementAmount === '' ? 0 : parseFloat(movementAmount);
            if (isNaN(amount)) return;

            // Get the current position vector
            const currentPos = activeGroup.position.clone();

            // Create movement vector based on the arrow direction
            // Always move in the direction of the arrow regardless of cabinet orientation
            const movementVector = new THREE.Vector3();
            const adjustedStep = amount * 0.2; // Using the movement amount instead of stepSize

            // Move based on the direction of the arrow that was clicked
            switch (direction) {
                case 'left':
                    movementVector.set(-adjustedStep, 0, 0); // Left in world space
                    break;
                case 'right':
                    movementVector.set(adjustedStep, 0, 0); // Right in world space
                    break;
                case 'back':
                    movementVector.set(0, 0, -adjustedStep); // Back in world space
                    break;
                case 'front':
                    movementVector.set(0, 0, adjustedStep); // Front in world space
                    break;
                case 'up':
                    movementVector.set(0, adjustedStep, 0); // Up in world space
                    break;
                case 'down':
                    movementVector.set(0, -adjustedStep, 0); // Down in world space
                    break;
            }

            // Add the movement to current position
            const newPosition = currentPos.add(movementVector);

            // Update the model through the userData.move function if available
            if (
                activeGroup.userData &&
                typeof activeGroup.userData.move === 'function'
            ) {
                (
                    activeGroup.userData as {
                        move: (position: THREE.Vector3) => void;
                    }
                ).move(newPosition);
            } else {
                // Otherwise update position directly
                activeGroup.position.copy(newPosition);
            }

            // Update arrow positions after cabinet moves
            updateArrowPositions();

            // Recalculate wall distances after moving
            calculateWallDistances();

            debouncedSave(activeGroup);
        },
        [
            activeGroup,
            movementAmount,
            debouncedSave,
            updateArrowPositions,
            calculateWallDistances,
        ]
    );

    // Then create specific direction handlers using the new function
    const handleMoveAmountLeft = useCallback(() => {
        handleMoveWithAmount('left');
        handleClose();
    }, [handleMoveWithAmount]);

    const handleMoveAmountRight = useCallback(() => {
        handleMoveWithAmount('right');
        handleClose();
    }, [handleMoveWithAmount]);

    const handleMoveAmountBack = useCallback(() => {
        handleMoveWithAmount('back');
        handleClose();
    }, [handleMoveWithAmount]);

    const handleMoveAmountFront = useCallback(() => {
        handleMoveWithAmount('front');
        handleClose();
    }, [handleMoveWithAmount]);

    const handleMoveAmountUp = useCallback(() => {
        handleMoveWithAmount('up');
        handleClose();
    }, [handleMoveWithAmount]);

    const handleMoveAmountDown = useCallback(() => {
        handleMoveWithAmount('down');
        handleClose();
    }, [handleMoveWithAmount]);

    // Handler for movement amount change
    const handleMovementAmountChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setMovementAmount(e.target.value);
        },
        []
    );

    // Factory function to create handlers for each wall direction
    const createWallChangeHandler = (
        wall: 'left' | 'right' | 'back' | 'front' | 'top' | 'bottom'
    ) => {
        // Return a memoized handler for the specific wall
        return useCallback(
            (e: React.ChangeEvent<HTMLInputElement>) => {
                handleWallDistanceChange(wall, e.target.value);
            },
            [handleWallDistanceChange]
        );
    };

    // Create handlers for each direction
    const handleRightWallChange = createWallChangeHandler('right');
    const handleLeftWallChange = createWallChangeHandler('left');
    const handleBackWallChange = createWallChangeHandler('back');
    const handleFrontWallChange = createWallChangeHandler('front');
    const handleTopWallChange = createWallChangeHandler('top');
    const handleBottomWallChange = createWallChangeHandler('bottom');

    // Function to handle arrow click from the 3D scene
    useEffect(() => {
        if (activeGroup) {
            // Reset arrow visibility when active group changes
            const userData = activeGroup.userData as CustomUserData;
            if (userData.resetArrowVisibility) {
                userData.resetArrowVisibility();
            }

            // Add the onArrowClick handler to the activeGroup
            userData.onArrowClick = ({
                direction,
                screenX,
                screenY,
                arrowMesh,
            }: {
                direction: 'right' | 'left' | 'back' | 'front' | 'up' | 'down';
                screenX: number;
                screenY: number;
                arrowMesh: THREE.Mesh;
            }) => {
                if (direction === activeDirection) {
                    return;
                }
                // Map direction based on cabinet's current orientation
                const currentDirection = userData.currentDirection;
                let mappedDirection = direction;

                // Map direction based on cabinet orientation
                if (currentDirection === 'left') {
                    // When cabinet faces left
                    if (direction === 'front') mappedDirection = 'right';
                    else if (direction === 'right') mappedDirection = 'back';
                    else if (direction === 'back') mappedDirection = 'left';
                    else if (direction === 'left') mappedDirection = 'front';
                } else if (currentDirection === 'right') {
                    // When cabinet faces right
                    if (direction === 'front') mappedDirection = 'left';
                    else if (direction === 'left') mappedDirection = 'back';
                    else if (direction === 'back') mappedDirection = 'right';
                    else if (direction === 'right') mappedDirection = 'front';
                } else if (currentDirection === 'back') {
                    // When cabinet faces back
                    if (direction === 'front') mappedDirection = 'back';
                    else if (direction === 'back') mappedDirection = 'front';
                    else if (direction === 'left') mappedDirection = 'right';
                    else if (direction === 'right') mappedDirection = 'left';
                }
                // Otherwise use the original direction (when currentDirection is 'front')

                // Set new active direction with the mapped value
                setActiveDirection(mappedDirection);

                // Hide all other arrows except the clicked one
                if (userData.movementArrows) {
                    userData.movementArrows.forEach((arrow: THREE.Mesh) => {
                        if (arrow !== arrowMesh) {
                            arrow.visible = false;
                        }
                    });
                }

                // Adjust position based on direction
                let adjustedX = screenX;
                let adjustedY = screenY;

                switch (direction) {
                    case 'right':
                        adjustedX += 50; // Move input field to the right of the arrow
                        break;
                    case 'left':
                        adjustedX -= 150; // Move input field to the left of the arrow
                        break;
                    case 'front':
                        adjustedY += 30; // Move input field below the arrow
                        adjustedX -= 75; // Center horizontally
                        break;
                    case 'back':
                        adjustedY -= 50; // Move input field above the arrow
                        adjustedX -= 75; // Center horizontally
                        break;
                    case 'up':
                        adjustedX += 50; // Move input field to the right of the arrow
                        adjustedY -= 10; // Slight vertical adjustment
                        break;
                    case 'down':
                        adjustedX += 50; // Move input field to the right of the arrow
                        adjustedY -= 10; // Slight vertical adjustment
                        break;
                }

                setEditorPosition({
                    x: adjustedX,
                    y: adjustedY,
                });

                // Focus the corresponding input field after a short delay
                setTimeout(() => {
                    // Validate that direction is a valid key
                    const validDirections: Direction[] = [
                        'right',
                        'left',
                        'back',
                        'front',
                        'up',
                        'down',
                    ];
                    if (validDirections.includes(direction)) {
                        let inputRef;
                        switch (direction) {
                            case 'right':
                                inputRef = inputRefs.right;
                                break;
                            case 'left':
                                inputRef = inputRefs.left;
                                break;
                            case 'back':
                                inputRef = inputRefs.back;
                                break;
                            case 'front':
                                inputRef = inputRefs.front;
                                break;
                            case 'up':
                                inputRef = inputRefs.up;
                                break;
                            case 'down':
                                inputRef = inputRefs.down;
                                break;
                        }
                        if (inputRef?.current) {
                            inputRef.current.focus();
                            inputRef.current.select();
                        }
                    }
                }, 0);
            };

            return () => {
                // Cleanup when unmounting or when active group changes
                if (activeGroup?.userData) {
                    const userData = activeGroup.userData as CustomUserData;
                    if (userData.resetArrowVisibility) {
                        userData.resetArrowVisibility();
                    }
                }
                setActiveDirection(null);
            };
        }
    }, [activeGroup]); // Keep only activeGroup as dependency

    // Function to handle closing the editor
    const handleClose = useCallback(() => {
        if (activeGroup) {
            const userData = activeGroup.userData as CustomUserData;
            if (userData.resetArrowVisibility) {
                userData.resetArrowVisibility();
            }
        }
        setActiveDirection(null);
    }, [activeGroup]);

    // Don't show editor if no direction is active
    if (!activeGroup || !activeDirection) return null;

    return (
        <EditorContainer
            ref={editorRef}
            style={{
                top: `${editorPosition.y}px`,
                left: `${editorPosition.x}px`,
                display: activeDirection ? 'block' : 'none',
            }}>
            {/* Movement controls */}
            <MovementControlContainer>
                <CloseButton onClick={handleClose}>×</CloseButton>

                <MovementLabel>Reposition by: </MovementLabel>
                <MovementRow>
                    {activeDirection === 'right' && (
                        <>
                            <InputGroup>
                                <MovementInput
                                    type="text"
                                    value={movementAmount}
                                    onChange={handleMovementAmountChange}
                                />
                                <MeasurementDisplay>mm</MeasurementDisplay>
                            </InputGroup>
                            <StepButton onClick={handleMoveAmountRight}>
                                Move
                            </StepButton>
                        </>
                    )}

                    {activeDirection === 'left' && (
                        <>
                            <InputGroup>
                                <MovementInput
                                    type="text"
                                    value={movementAmount}
                                    onChange={handleMovementAmountChange}
                                />
                                <MeasurementDisplay>mm</MeasurementDisplay>
                            </InputGroup>
                            <StepButton onClick={handleMoveAmountLeft}>
                                Move
                            </StepButton>
                        </>
                    )}

                    {activeDirection === 'back' && (
                        <>
                            <InputGroup>
                                <MovementInput
                                    type="text"
                                    value={movementAmount}
                                    onChange={handleMovementAmountChange}
                                />
                                <MeasurementDisplay>mm</MeasurementDisplay>
                            </InputGroup>
                            <StepButton onClick={handleMoveAmountBack}>
                                Move
                            </StepButton>
                        </>
                    )}

                    {activeDirection === 'front' && (
                        <>
                            <InputGroup>
                                <MovementInput
                                    type="text"
                                    value={movementAmount}
                                    onChange={handleMovementAmountChange}
                                />
                                <MeasurementDisplay>mm</MeasurementDisplay>
                            </InputGroup>
                            <StepButton onClick={handleMoveAmountFront}>
                                Move
                            </StepButton>
                        </>
                    )}

                    {activeDirection === 'up' && (
                        <>
                            <InputGroup>
                                <MovementInput
                                    type="text"
                                    value={movementAmount}
                                    onChange={handleMovementAmountChange}
                                />
                                <MeasurementDisplay>mm</MeasurementDisplay>
                            </InputGroup>
                            <StepButton onClick={handleMoveAmountUp}>
                                Move
                            </StepButton>
                        </>
                    )}

                    {activeDirection === 'down' && (
                        <>
                            <InputGroup>
                                <MovementInput
                                    type="text"
                                    value={movementAmount}
                                    onChange={handleMovementAmountChange}
                                />
                                <MeasurementDisplay>mm</MeasurementDisplay>
                            </InputGroup>
                            <StepButton onClick={handleMoveAmountDown}>
                                Move
                            </StepButton>
                        </>
                    )}
                </MovementRow>
            </MovementControlContainer>
            <DistanceEditor
                activeDirection={activeDirection}
                wallDistanceInputs={wallDistanceInputs}
                inputRefs={inputRefs}
                handleRightWallChange={handleRightWallChange}
                handleLeftWallChange={handleLeftWallChange}
                handleBackWallChange={handleBackWallChange}
                handleFrontWallChange={handleFrontWallChange}
                handleTopWallChange={handleTopWallChange}
                handleBottomWallChange={handleBottomWallChange}
            />
        </EditorContainer>
    );
};

export default PositionEditor;
