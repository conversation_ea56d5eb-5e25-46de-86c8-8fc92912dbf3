import {TemplateVariable} from 'components/customer/Preview3D/usePreview3DData';
import {generateCabinetMesh} from 'components/customer/RoomPlanner/helpers/generateCabinetMesh';
import {
    getGroupWidth,
    autoRotateClamping,
} from 'components/customer/RoomPlanner/helpers/cabinetPosition';
import {RoomPlannerProductWithTemplate} from 'components/customer/RoomPlanner/types';
import {ComputedDimension} from 'components/customer/RoomPlanner/lib/useRoomPlanner';
import {debouncedAutoSave} from 'components/customer/RoomPlanner/lib/useAutoSave';
import {store} from 'store/customer';
import * as THREE from 'three';
import productEvents from 'components/customer/RoomPlanner/lib/useProductEvents';
import {
    cabinetGroupListAdd,
    rotatingSet,
} from 'components/customer/RoomPlanner/store/roomPlannerSlice';
import {RECENTLY_ADDED_KEYS, addRecentItem} from 'hooks/RecentlyAdded';
import {invalidateTotalProductCount} from 'components/customer/Job/store/jobApi';
import {invalidateParticularRoomAndCost} from 'components/customer/Product/store/productApi';
import Camera from 'components/customer/RoomPlanner/lib/useCameraSetup';
import {roomPlannerApi} from 'components/customer/RoomPlanner/store/roomPlannerApi';
import {deleteCabinetMesh} from 'components/customer/RoomPlanner/helpers/deleteCabinetMesh';

const setRotating = () => {
    const state = store.getState();
    const isRotating = state.roomPlanner.isRotating;

    store.dispatch(rotatingSet(!isRotating));
};

const getCabinetState = (cabinetGroup: THREE.Group<THREE.Object3DEventMap>) => {
    const cabinetPositions =
        store.getState().roomPlanner.layoutData?.layout_room_products;

    if (!cabinetPositions) return {position: null, rotation: null};

    const cabinetPosition = cabinetPositions?.find(
        (cabinet) => cabinet.job_cabinet_id == cabinetGroup.userData.id
    );

    if (!cabinetPosition) return {position: null, rotation: null};

    return {
        position: cabinetPosition.position,
        rotation: cabinetPosition.rotation,
    };
};

const determineSpawnLocation = (
    cabinetGroup: THREE.Group<THREE.Object3DEventMap>,
    existingCabinets: THREE.Group<THREE.Object3DEventMap>[],
    initialPosition: THREE.Vector3,
    dimensions: ComputedDimension
): THREE.Vector3 => {
    const {width} = getGroupWidth(cabinetGroup);
    const {scale, roomWidth} = dimensions;
    const newCabinetWidth = width * scale;

    // Calculate room boundaries with margins
    const roomLeftEdge = -roomWidth / 2 + 30; // Left wall with margin
    const roomRightEdge = roomWidth / 2 - 30; // Right wall with margin

    // Step 1: Calculate the preferred spawn position (left side of room)
    const preferredSpawnPosition = new THREE.Vector3(
        roomLeftEdge + newCabinetWidth / 2, // Position cabinet center at left side
        initialPosition.y, // Keep the same Y (height)
        initialPosition.z // Keep the same Z (depth)
    );

    // Step 2: Check if there are existing cabinets at the same level (base vs upper)
    const sameLevelCabinets = existingCabinets.filter((cabinet) => {
        const yDifference = Math.abs(cabinet.position.y - initialPosition.y);
        return yDifference < 50; // Tolerance for same level
    });

    // Step 3: Check if the preferred spawn location is occupied
    let isSpawnLocationOccupied = false;

    for (const existingCabinet of sameLevelCabinets) {
        const existingCabinetBox = new THREE.Box3().setFromObject(
            existingCabinet
        );

        // Calculate if the new cabinet would overlap with this existing cabinet
        const newCabinetLeftEdge =
            preferredSpawnPosition.x - newCabinetWidth / 2;
        const newCabinetRightEdge =
            preferredSpawnPosition.x + newCabinetWidth / 2;

        const existingCabinetLeftEdge = existingCabinetBox.min.x;
        const existingCabinetRightEdge = existingCabinetBox.max.x;

        // Check for overlap
        const hasOverlap = !(
            newCabinetRightEdge <= existingCabinetLeftEdge ||
            newCabinetLeftEdge >= existingCabinetRightEdge
        );

        if (hasOverlap) {
            isSpawnLocationOccupied = true;
            break; // Found overlap, no need to check further
        }
    }

    if (isSpawnLocationOccupied) {
        // Step 4: Iteratively find the next available position
        let currentTestPosition = preferredSpawnPosition.x;
        let iteration = 0;
        const maxIterations = 20; // Safety limit to prevent infinite loops

        while (iteration < maxIterations) {
            iteration++;

            // Check if current test position overlaps with any existing cabinet
            let currentBlockingCabinet = null;
            const testCabinetLeftEdge =
                currentTestPosition - newCabinetWidth / 2;
            const testCabinetRightEdge =
                currentTestPosition + newCabinetWidth / 2;

            for (const existingCabinet of sameLevelCabinets) {
                const existingCabinetBox = new THREE.Box3().setFromObject(
                    existingCabinet
                );
                const existingCabinetLeftEdge = existingCabinetBox.min.x;
                const existingCabinetRightEdge = existingCabinetBox.max.x;

                // Check for overlap
                const hasOverlap = !(
                    testCabinetRightEdge <= existingCabinetLeftEdge ||
                    testCabinetLeftEdge >= existingCabinetRightEdge
                );

                if (hasOverlap) {
                    currentBlockingCabinet = {
                        id:
                            (existingCabinet.userData.id as string | number) ||
                            'unknown',
                        leftEdge: existingCabinetLeftEdge,
                        rightEdge: existingCabinetRightEdge,
                    };
                    break;
                }
            }

            if (!currentBlockingCabinet) {
                // Found a clear position! Check if it fits within room bounds
                const finalCabinetRightEdge = testCabinetRightEdge; // No additional margin needed

                if (finalCabinetRightEdge <= roomRightEdge) {
                    const finalPosition = new THREE.Vector3(
                        currentTestPosition, // No additional margin - spawn exactly beside
                        preferredSpawnPosition.y,
                        preferredSpawnPosition.z
                    );

                    return finalPosition;
                } else {
                    return preferredSpawnPosition;
                }
            } else {
                // Move to the right of the blocking cabinet and try again
                currentTestPosition =
                    currentBlockingCabinet.rightEdge + newCabinetWidth / 2;
            }
        }

        // If we've exhausted iterations, fall back to preferred position
        return preferredSpawnPosition;
    } else {
        return preferredSpawnPosition;
    }
};

export const renderCabinetGroup = (
    product: RoomPlannerProductWithTemplate,
    dimensions: ComputedDimension,
    room: string,
    row?: number
) => {
    const state = store.getState();
    const roomType = state.roomPlanner.roomType;
    const {
        scale,
        wallHeight,
        roomDepthComputed,
        positionX,
        roomLeftDepth,
        roomRightDepth,
        roomLeftWidth,
        roomRightWidth,
        defaultPositionZ,
    } = dimensions;
    const variables = JSON.parse(
        product.template_3d[0].attributes.variables
    ) as unknown as TemplateVariable;
    const cabinetWidth =
        Number(product.cabinet_width) -
        (product.cabinet_carc_thickness || product.cabinet_ext_thickness);
    const lShapedValues = variables?.hasLShapedShelves
        ? {
              cabinet_left_width: product.width2,
              cabinet_right_depth: product.length_depth,
              cabinet_right_width:
                  Number(product.width) -
                  (product.cabinet_carc_thickness ||
                      product.cabinet_ext_thickness),
              cabinet_left_depth: product.width_depth,
          }
        : {};

    const cabinetGroup = generateCabinetMesh({
        ...product,
        ...lShapedValues,
        cabinet_width: cabinetWidth,
    } as unknown as RoomPlannerProductWithTemplate);

    const templateName = product.template_3d[0].attributes.template;
    const isFloorProduct = templateName?.includes('KickerFramed');
    const isPanelProduct = templateName?.includes('AppliedPanel');
    const isLShapedProduct = variables?.hasLShapedShelves;
    const sitOnFloor =
        isFloorProduct ||
        cabinetGroup.userData.isRobeProduct ||
        cabinetGroup.userData.noBottom;

    const {width, height} = getGroupWidth(cabinetGroup);
    const positionY =
        -(wallHeight / 2) +
        (variables?.isUpperProduct ? 300 : sitOnFloor ? 51 : 73);
    const positionZ =
        defaultPositionZ ?? roomDepthComputed + height * scale + 10;

    cabinetGroup.userData.id = product.duplicate_id
        ? product.duplicate_id
        : product.job_cabinet_id;
    cabinetGroup.userData.values = product;
    cabinetGroup.userData.onHide = () => {
        cabinetGroup.visible = false;
    };

    const directions = ['front', 'left', 'back', 'right'];

    cabinetGroup.userData.ignoreSnapping = isPanelProduct;
    cabinetGroup.userData.isFloorProduct = isFloorProduct;
    cabinetGroup.userData.isLShapedProduct = isLShapedProduct;

    cabinetGroup.userData.currentDirection = 'front'; // Default facing direction

    const rotateAroundCenter = (group: THREE.Group, angle: number) => {
        const variables = JSON.parse(
            cabinetGroup.userData.values.template_3d[0].attributes.variables
        ) as TemplateVariable;

        const box = new THREE.Box3().setFromObject(group);
        const oldCenter = box.getCenter(new THREE.Vector3());

        group.rotation.z += angle;

        group.updateMatrixWorld();

        const newBox = new THREE.Box3().setFromObject(group);
        const newCenter = newBox.getCenter(new THREE.Vector3());

        const offset = newCenter.clone().sub(oldCenter);
        group.position.sub(offset);
        if (variables?.isUpperProduct) {
            const clamped = autoRotateClamping(
                group,
                store.getState().roomPlanner.upperProductCurrentWall,
                group.position
            );
            if (clamped) {
                group.position.copy(clamped);
                group.updateMatrixWorld();
            }
        }
    };

    cabinetGroup.userData.rotateLeft = () => {
        setRotating();

        // Store the current direction before rotation
        const originalDirection = cabinetGroup.userData.currentDirection;
        cabinetGroup.userData.currentRotation = cabinetGroup.rotation;
        // Update direction
        const currentIndex = directions.indexOf(originalDirection);
        cabinetGroup.userData.currentDirection =
            directions[(currentIndex + 1) % 4];
        rotateAroundCenter(cabinetGroup, Math.PI / 2);

        debouncedAutoSave(cabinetGroup);
    };

    cabinetGroup.userData.rotateRight = () => {
        setRotating();

        // Store the current direction before rotation
        const originalDirection = cabinetGroup.userData.currentDirection;
        cabinetGroup.userData.currentRotation = cabinetGroup.rotation;
        // Update direction
        const currentIndex = directions.indexOf(originalDirection);
        cabinetGroup.userData.currentDirection =
            directions[(currentIndex + 3) % 4]; // +3 is the same as -1
        rotateAroundCenter(cabinetGroup, -Math.PI / 2);

        debouncedAutoSave(cabinetGroup);
    };

    cabinetGroup.userData.duplicateCabinet = async () => {
        try {
            const roomId = cabinetGroup.userData.values.room_id;
            let jobCabinetId = cabinetGroup.userData.id as number;

            if (typeof cabinetGroup.userData.id === 'string') {
                const jobIdParts = cabinetGroup.userData.id.split('-');
                jobCabinetId = Number(jobIdParts[0]);
            }

            const updatedState = store.getState();
            const cabinetGroupList = updatedState.roomPlanner.cabinetGroupList;

            const roomCabinetNumber = cabinetGroupList.length + 1;
            const newCabinetGroup = cabinetGroup.clone();
            newCabinetGroup.userData.id = roomCabinetNumber;
            newCabinetGroup.userData.quantity = 1;

            newCabinetGroup.userData.values.job_cabinet_id = roomCabinetNumber;
            newCabinetGroup.userData.values.id = roomCabinetNumber;
            newCabinetGroup.userData.values.duplicate_id = undefined;
            newCabinetGroup.userData.values.room_cab_number = roomCabinetNumber;
            newCabinetGroup.userData.values.room_id = roomId;

            // Position the new cabinet next to the original
            const existingPositions = cabinetGroupList.map(
                (cabinet) => cabinet.position
            );

            const maxX = Math.max(
                ...existingPositions.map((pos) => pos.x),
                newCabinetGroup.position.x
            );
            newCabinetGroup.position.set(
                maxX + 150,
                newCabinetGroup.position.y,
                newCabinetGroup.position.z
            );
            const {cabinetGroup: upatedCabinetGroup} = renderCabinetGroup(
                newCabinetGroup.userData
                    .values as RoomPlannerProductWithTemplate,
                {
                    ...dimensions,
                    positionX: newCabinetGroup.position.x + 40,
                    defaultPositionZ: newCabinetGroup.position.z,
                },
                room
            );

            const response = await store.dispatch(
                roomPlannerApi.endpoints.duplicateProduct.initiate({
                    roomId,
                    jobCabinetId,
                })
            );

            if (response && response.data) {
                const cabinet = response.data;

                upatedCabinetGroup.userData.id = cabinet.job_cabinet_id;
                upatedCabinetGroup.userData.values.job_cabinet_id =
                    cabinet.job_cabinet_id;
                upatedCabinetGroup.userData.values.id = cabinet.job_cabinet_id;

                // Update recent items and invalidate counts
                store.dispatch(invalidateTotalProductCount());
                store.dispatch(invalidateParticularRoomAndCost(Number(roomId)));
                addRecentItem(cabinet.type, RECENTLY_ADDED_KEYS.PRODUCT);
            } else {
                if (upatedCabinetGroup) {
                    // delete added product if there was a problem with the API
                    Camera.scene.remove(upatedCabinetGroup);
                }
            }
        } catch {}
    };

    cabinetGroup.userData.moveUp = () => {
        try {
            setRotating();

            // Get cabinet dimensions
            const {height} = getGroupWidth(cabinetGroup);

            // Calculate the upper boundary limit (top of the wall - half the cabinet height)
            const upperLimit = wallHeight / 2 - (height * scale) / 2 - 25;

            // Calculate the new position
            const newY = cabinetGroup.position.y + 10;

            // Only move if it won't exceed the wall boundary
            if (newY <= upperLimit) {
                cabinetGroup.position.y = newY;
                // Save the updated position
                debouncedAutoSave(cabinetGroup);
            } else {
                // Optionally move to the exact upper limit if we're trying to go beyond
                cabinetGroup.position.y = upperLimit;
                debouncedAutoSave(cabinetGroup);
            }
        } catch (error) {
            // console.error('Error moving cabinet up:', error);
        }
    };

    cabinetGroup.userData.moveDown = () => {
        try {
            setRotating();

            // Get cabinet dimensions
            const {height} = getGroupWidth(cabinetGroup);

            // Calculate the lower boundary limit (bottom of the wall + half the cabinet height)
            const lowerLimit = -(wallHeight / 2) + (height * scale) / 2 + 64;

            // Calculate the new position
            const newY = cabinetGroup.position.y - 10;

            // Only move if it won't exceed the wall boundary
            if (newY >= lowerLimit) {
                cabinetGroup.position.y = newY;

                // Save the updated position
                debouncedAutoSave(cabinetGroup);
            } else {
                // Optionally move to the exact lower limit if we're trying to go beyond
                cabinetGroup.position.y = lowerLimit;
                debouncedAutoSave(cabinetGroup);
            }
        } catch {}
    };

    cabinetGroup.userData.move = (postion: THREE.Vector3) => {
        try {
            setRotating();

            cabinetGroup.position.x = postion.x;
            cabinetGroup.position.y = postion.y;
            cabinetGroup.position.z = postion.z;
            debouncedAutoSave(cabinetGroup);
        } catch (error) {
            // console.error('Error moving cabinet up:', error);
        }
    };

    cabinetGroup.userData.deleteCabinet = () => deleteCabinetMesh(cabinetGroup);

    const {position: storedPosition, rotation: storedRotation} =
        getCabinetState(cabinetGroup);

    const storedCurrentDirection = localStorage.getItem(
        `cabinet-currentDirection:${cabinetGroup.userData.id}`
    );

    if (storedCurrentDirection) {
        cabinetGroup.userData.currentDirection = JSON.parse(
            storedCurrentDirection
        ) as string;
    }

    if (storedRotation) {
        cabinetGroup.rotation.copy(storedRotation);
    }

    if (storedPosition) {
        cabinetGroup.position.copy(storedPosition);
    } else {
        // Check the cabinet if add on the room is over lapping

        // Calculate initial position based on cabinet type
        let initialPosition: THREE.Vector3;

        if (!variables?.isUpperProduct) {
            const initialZ = positionZ;
            const adjustedZ =
                row > 1 ? initialZ + 30 + height * scale : initialZ;
            initialPosition = new THREE.Vector3(
                positionX,
                positionY,
                adjustedZ
            );
        } else {
            initialPosition = new THREE.Vector3(
                positionX,
                positionY,
                positionZ
            );
        }

        // Get existing cabinets
        const state = store.getState();
        const existingCabinets = state.roomPlanner.cabinetGroupList;

        // Use our spawn location determination function
        const spawnPosition = determineSpawnLocation(
            cabinetGroup,
            existingCabinets,
            initialPosition,
            dimensions
        );

        cabinetGroup.position.copy(spawnPosition);
    }

    cabinetGroup.rotation.x = -(90 * Math.PI) / 180;
    cabinetGroup.scale.set(scale, scale, scale);

    productEvents.attachEvents({
        scene: Camera.scene,
        cabinetGroup,
        scale,
        camera: Camera.camera,
        controls: Camera.controls,
        product,
        computedWidth: positionX + width * scale,
        positionY,
        roomDepth: dimensions.roomDepth * scale,
        roomWidth: dimensions.roomWidth,
        roomType,
        roomLeftDepth,
        roomRightDepth,
        roomLeftWidth,
        roomRightWidth,
    });

    Camera.scene.add(cabinetGroup);

    store.dispatch(cabinetGroupListAdd(cabinetGroup));

    const updatedState = store.getState();
    const cabinetGroupList = updatedState.roomPlanner.cabinetGroupList;

    productEvents.setProducts(cabinetGroupList);

    return {widthIncrement: width * scale, cabinetGroup};
};
