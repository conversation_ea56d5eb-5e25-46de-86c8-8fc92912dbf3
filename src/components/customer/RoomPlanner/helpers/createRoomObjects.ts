import * as THREE from 'three';
import {FontLoader} from 'three/examples/jsm/loaders/FontLoader';
import {TextGeometry} from 'three/examples/jsm/geometries/TextGeometry';
import {getGroupWidth} from 'components/customer/RoomPlanner/helpers/cabinetPosition';
// import {getRoomDimensionsComputed} from 'components/customer/RoomPlanner/helpers/getComputedDimension';

interface GroupWithUpdatePosition extends THREE.Group {
    userData: {
        updatePosition: (width: number, depth: number, height: number) => void;
    };
}

type MeasurementObject = {
    leftArrow: THREE.ArrowHelper;
    rightArrow: THREE.ArrowHelper;
    sprite: THREE.Sprite;
    leftGuideline?: THREE.Line;
    rightGuideline?: THREE.Line;
};

export const addDashedLine = (
    parentMesh: THREE.Mesh,
    handleLines: (lines: THREE.Line[]) => void,
    floorWidth: number,
    floorLength: number
) => {
    const dashedMaterial = new THREE.LineDashedMaterial({
        color: '#333',
        gapSize: 3,
    });

    // Geometry for the lines
    const leftLineGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(floorWidth / 2 - 50, 300, 0),
        new THREE.Vector3(-floorWidth / 2 - 50, 300, 0),
    ]);
    const rightLineGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(floorLength / 2 + 50, 300, 0),
        new THREE.Vector3(-floorLength / 2, 300, 0),
    ]);

    const leftLine = new THREE.Line(leftLineGeometry, dashedMaterial);
    const rightLine = new THREE.Line(rightLineGeometry, dashedMaterial);

    // Compute line distances for dashes
    leftLine.computeLineDistances();
    rightLine.computeLineDistances();

    leftLine.position.z = 27;
    rightLine.position.z = 27;
    rightLine.rotation.z = (90 * Math.PI) / 180;

    parentMesh.add(leftLine);
    parentMesh.add(rightLine);

    const leftLine2 = leftLine.clone();
    const rightLine2 = rightLine.clone();

    leftLine2.position.y = 200;
    rightLine2.position.x = 200;

    rightLine.position.y = floorLength / 2 - 25;
    rightLine2.position.y = floorLength / 2 - 25;
    leftLine.position.x = floorWidth / 2 + 25;
    leftLine2.position.x = floorWidth / 2 + 25;

    parentMesh.add(leftLine2);
    parentMesh.add(rightLine2);

    leftLine.userData.id = 'leftLine';
    leftLine2.userData.id = 'leftLine2';
    rightLine.userData.id = 'rightLine';
    rightLine2.userData.id = 'rightLine2';

    handleLines([leftLine, leftLine2, rightLine, rightLine2]);

    leftLine.visible = false;
    leftLine2.visible = false;
    rightLine.visible = false;
    rightLine2.visible = false;

    return {
        leftLine,
        leftLine2,
        rightLine,
        rightLine2,
    };
};

export const addArrowsAndText = (
    wall: THREE.Mesh,
    width: number,
    handleArrows: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[]
    ) => void,
    isSideWall: boolean, // Changed to boolean with default value
    measurement = 0,
    wallHeight = 0,
    id = ''
) => {
    // Arrow Helper
    const arrowDir = new THREE.Vector3(1, 0, 0).normalize();
    const arrowLength = width - 50 - (isSideWall ? 25 : 0);
    const arrowColor = '#333';

    // Create left arrow
    const leftArrow = new THREE.ArrowHelper(
        arrowDir.clone().negate(), // Direction
        new THREE.Vector3(width - 75 + (isSideWall ? 25 : 0), wallHeight, 12.5), // Start point
        arrowLength,
        arrowColor,
        8,
        8
    );
    wall.add(leftArrow);

    // Create right arrow
    const rightArrow = new THREE.ArrowHelper(
        arrowDir,
        new THREE.Vector3(width + 75, wallHeight, 12.5),
        arrowLength,
        arrowColor,
        8,
        8
    );
    wall.add(rightArrow);

    // Text for width
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 128;

    context.font = '50px Arial';
    context.fillStyle = '#000';
    context.textAlign = 'center';
    context.fillText(`${measurement} mm`, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    const textMaterial = new THREE.SpriteMaterial({map: texture});
    const textSprite = new THREE.Sprite(textMaterial);
    textSprite.position.set(width + (isSideWall ? 25 : 0), wallHeight, 20);
    textSprite.scale.set(100, 50, 1);

    wall.add(textSprite);

    rightArrow.userData.id = id;
    leftArrow.userData.id = id;
    textMaterial.userData.id = id;

    handleArrows([rightArrow, leftArrow, textMaterial]);

    leftArrow.visible = false;
    rightArrow.visible = false;
    textMaterial.visible = false;
};

export const addArrowsAndTextForCabinetDimension = (
    wall: THREE.Mesh,
    width: number,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[],
        arrowGroup?: THREE.Group<THREE.Object3DEventMap>
    ) => void,
    isSideWall = false,
    isLshapedRoom = false,
    isTopWall = false,
    id = ''
) => {
    const arrowDir = new THREE.Vector3(1, 0, 0).normalize();
    const reverseArrowDir = arrowDir.clone().negate();
    const arrowLength = width - 10;
    const arrowColor = 0x333333;
    const arrowHeadLength = 0;
    const arrowHeadWidth = 0;

    const createArrow = (
        origin: THREE.Vector3,
        direction: THREE.Vector3,
        arrowLength: number
    ) => {
        const arrow = new THREE.ArrowHelper(
            direction,
            origin,
            arrowLength,
            arrowColor,
            arrowHeadLength,
            arrowHeadWidth
        );

        return arrow;
    };

    // Create left and right arrows
    const leftArrowOrigin = new THREE.Vector3(width - 15, 25, 40);
    const rightArrowOrigin = new THREE.Vector3(15, 25, 40);

    const leftArrow = createArrow(
        leftArrowOrigin,
        reverseArrowDir,
        arrowLength
    );
    const rightArrow = createArrow(rightArrowOrigin, arrowDir, arrowLength);

    const arrowGroup = new THREE.Group();
    arrowGroup.userData.id =
        id ||
        (isSideWall && isTopWall
            ? 'arrowGroupLeftTopWall'
            : isSideWall
            ? 'arrowGroupLeft'
            : isTopWall
            ? 'arrowGroupTopTopWall'
            : 'arrowGroupTop');

    arrowGroup.add(rightArrow);
    arrowGroup.add(leftArrow);

    wall.add(arrowGroup);

    // Load font and create text
    const loader = new FontLoader();
    loader.load('/templates/3D/fonts/Poppins SemiBold_Regular.json', (font) => {
        const createTextMesh = (text: string, position: THREE.Vector3) => {
            const textGeometry = new TextGeometry(text, {
                font: font,
                size: 12,
                depth: 1,
            });
            const textMaterial = new THREE.MeshBasicMaterial({color: 0x000000});
            const textMesh = new THREE.Mesh(textGeometry, textMaterial);
            textMesh.position.copy(position);
            return textMesh;
        };

        let textMesh = createTextMesh(``, new THREE.Vector3(width, 30, 41));

        arrowGroup.userData.onDrag = (
            width: number,
            depth: number,
            cabinetGroup?: THREE.Group<THREE.Object3DEventMap>,
            height?: number
        ) => {
            // Set the entire group visible
            arrowGroup.visible = true;

            // Ensure all children are visible
            arrowGroup.traverse((child) => {
                child.visible = true;
            });

            const computedWidth = isSideWall ? depth - 8 : width - 8;

            let cabinetLabel;

            // Determine which dimension to show based on the arrow group ID and cabinet direction
            if (arrowGroup.userData.id === 'arrowGroupFront') {
                // For front arrow group: show width for front/back direction, depth for left/right direction
                cabinetLabel =
                    cabinetGroup.userData.currentDirection === 'front' ||
                    cabinetGroup.userData.currentDirection === 'back'
                        ? (
                              cabinetGroup.userData.values as {
                                  cabinet_width?: string;
                              }
                          )?.cabinet_width
                        : (
                              cabinetGroup.userData.values as {
                                  cabinet_depth?: number;
                              }
                          )?.cabinet_depth;
            } else if (arrowGroup.userData.id === 'arrowGroupRight') {
                // For right arrow group: show depth for front/back direction, width for left/right direction
                cabinetLabel =
                    cabinetGroup.userData.currentDirection === 'front' ||
                    cabinetGroup.userData.currentDirection === 'back'
                        ? (
                              cabinetGroup.userData.values as {
                                  cabinet_depth?: number;
                              }
                          )?.cabinet_depth
                        : (
                              cabinetGroup.userData.values as {
                                  cabinet_width?: string;
                              }
                          )?.cabinet_width;
            } else if (arrowGroup.userData.id === 'arrowGroupLeft') {
                // For left arrow group: always show cabinet height regardless of direction
                cabinetLabel = (
                    cabinetGroup.userData.values as {
                        cabinet_height?: number;
                    }
                )?.cabinet_height;
            } else {
                // Default behavior for other arrow groups
                cabinetLabel =
                    cabinetGroup.userData.currentDirection === 'front' ||
                    cabinetGroup.userData.currentDirection === 'back'
                        ? (
                              cabinetGroup.userData.values as {
                                  cabinet_depth?: number;
                              }
                          )?.cabinet_depth
                        : (
                              cabinetGroup.userData.values as {
                                  cabinet_width?: string;
                              }
                          )?.cabinet_width;
            }

            const offset = isSideWall ? 0 : 13;

            // Calculate new arrow length based on width parameter
            const newWidth = height ?? computedWidth;
            const newArrowLength = newWidth;
            const newarrowHeadLength = 10;
            const newarrowHeadWidth = 8;

            // Update arrows by adjusting their length
            leftArrow.setLength(
                newArrowLength - offset,
                newarrowHeadLength,
                newarrowHeadWidth
            );
            rightArrow.setLength(
                newArrowLength + offset,
                newarrowHeadLength,
                newarrowHeadWidth
            );

            // Update arrow positions based on new width
            leftArrow.position.set(newWidth, 25, 40);
            rightArrow.position.set(offset, 25, 40);

            // Update text position & content
            arrowGroup.remove(textMesh);
            textMesh.geometry.dispose();

            const cabinetHeight = height
                ? (cabinetGroup.userData.values as {cabinet_height?: number})
                      ?.cabinet_height
                : cabinetLabel;
            // Create the new text mesh
            const textContent = `${cabinetHeight} mm`;
            textMesh = createTextMesh(
                textContent,
                new THREE.Vector3(0, 0, 0) // We'll position it after centering
            );

            // Center the text geometry
            textMesh.geometry.computeBoundingBox();
            const textWidth =
                textMesh.geometry.boundingBox.max.x -
                textMesh.geometry.boundingBox.min.x;

            // Calculate the TRUE center position between the arrows
            // Right arrow is at 'offset', left arrow is at 'newWidth'
            const leftArrowPos = newWidth;
            const rightArrowPos = offset;
            const arrowSpaceWidth = leftArrowPos - rightArrowPos;
            const trueMidpoint = rightArrowPos + arrowSpaceWidth / 2;

            // Position text at the true midpoint, adjusting for text width
            let textMeshPositionX = trueMidpoint + textWidth / 2 + 5;

            // Special cases as before
            if (isSideWall) {
                // For side walls, we need a different calculation
                textMeshPositionX = newWidth / 2 - textWidth / 2;
            }
            if (!isSideWall && isLshapedRoom) {
                textMeshPositionX = newWidth - 15 - textWidth / 2;
            }

            // Apply the position
            textMesh.position.set(textMeshPositionX, 30, isSideWall ? 41 : 38);

            // Apply rotations
            if (isSideWall) textMesh.rotateX(-Math.PI / 2);
            else {
                textMesh.rotateX(-Math.PI / 2);
                if (!isLshapedRoom) {
                    textMesh.rotateZ(Math.PI);
                }
            }

            arrowGroup.add(textMesh);
            arrowGroup.userData.textMesh = textMesh;
        };

        arrowGroup.userData.onDragEnd = () => {
            // Hide the entire group and all its children
            arrowGroup.visible = false;
            arrowGroup.traverse((child) => {
                child.visible = false;
            });
        };
    });

    handleArrowsAndGroup([rightArrow, leftArrow], arrowGroup);

    // Hide the entire arrow group and all its children
    arrowGroup.visible = false;
};

export const createRoomDimensionArrow = ({
    arrowDir,
    reverseArrowDir,
    arrowOrigin,
    arrowLength,
    onArrowsCreated,
    isVertical = false,
}: {
    arrowDir: THREE.Vector3;
    reverseArrowDir: THREE.Vector3;
    arrowOrigin: THREE.Vector3;
    arrowLength: number;
    isVertical?: boolean;
    onArrowsCreated?: (
        arrow: THREE.ArrowHelper,
        reverseArrow: THREE.ArrowHelper
    ) => void;
}): THREE.Group<THREE.Object3DEventMap> => {
    const arrowHeadLength = 20;
    const arrowHeadWidth = 16;
    let reverserArrowOrigin = new THREE.Vector3(
        arrowOrigin.x + arrowLength,
        arrowOrigin.y,
        arrowOrigin.z
    );

    if (isVertical) {
        reverserArrowOrigin = new THREE.Vector3(
            arrowOrigin.x,
            arrowOrigin.y + arrowLength,
            arrowOrigin.z
        );
    }

    const arrow = new THREE.ArrowHelper(
        arrowDir,
        arrowOrigin,
        arrowLength,
        0x000000,
        arrowHeadLength,
        arrowHeadWidth
    );

    // Create reverse arrow for double-header effect (top)
    const reverseArrow = new THREE.ArrowHelper(
        reverseArrowDir,
        reverserArrowOrigin,
        arrowLength,
        0x000000,
        arrowHeadLength,
        arrowHeadWidth
    );

    onArrowsCreated && onArrowsCreated(arrow, reverseArrow);

    const arrowGroup = new THREE.Group();
    arrowGroup.add(arrow, reverseArrow);

    return arrowGroup;
};

// Measurement Room Object still on progress on re factor

export const addDashedLineWall = (
    parentMesh: THREE.Mesh,
    handleLines: (lines: THREE.Line[]) => void,
    floorWidth: number,
    floorLength: number
) => {
    const dashedMaterial = new THREE.LineDashedMaterial({
        color: '#333',
        gapSize: 3,
    });

    // Geometry for the lines
    const leftLineGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(floorWidth / 2 - 50, 300, 0),
        new THREE.Vector3(-floorWidth / 2 - 50, 300, 0),
    ]);
    const rightLineGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(floorLength / 2 + 60, 300, 0),
        new THREE.Vector3(-floorLength / 2, 300, 0),
    ]);

    const leftLine = new THREE.Line(leftLineGeometry, dashedMaterial);
    const rightLine = new THREE.Line(rightLineGeometry, dashedMaterial);

    // Compute line distances for dashes
    leftLine.computeLineDistances();
    rightLine.computeLineDistances();

    leftLine.position.z = 27;
    rightLine.position.z = 27;
    rightLine.rotation.z = (90 * Math.PI) / 180;

    parentMesh.add(leftLine);
    parentMesh.add(rightLine);

    const leftLine2 = leftLine.clone();
    const rightLine2 = rightLine.clone();

    leftLine2.position.y = 200;
    rightLine2.position.x = 0;

    rightLine.position.y = floorLength / 2 - 0;
    rightLine2.position.y = floorLength / 2 - 0;

    leftLine.position.x = floorWidth / 2 + 90;
    leftLine2.position.x = floorWidth / 2 + 25;

    parentMesh.add(leftLine2);
    parentMesh.add(rightLine2);

    leftLine.userData.id = 'leftLineTop-wall';
    leftLine2.userData.id = 'leftLine2';

    rightLine.userData.id = 'rightLine-wall';
    rightLine2.userData.id = 'rightLine2-wall';

    handleLines([leftLine]);

    leftLine.visible = false;
    leftLine2.visible = false;
    rightLine.visible = false;
    rightLine2.visible = false;

    return {
        leftLine,
        leftLine2,
        rightLine,
        rightLine2,
    };
};

export const addCabinetDimensionArrows = (
    cabinetGroup: THREE.Group,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial | THREE.Mesh)[],
        arrowGroup?: THREE.Group<THREE.Object3DEventMap>
    ) => void
) => {
    // Get cabinet dimensions
    const box = new THREE.Box3().setFromObject(cabinetGroup);
    const size = new THREE.Vector3();
    box.getSize(size);
    const width = size.x;
    const height = size.y;
    const depth = size.z;

    // Create a new group for the dimension indicators
    const dimensionGroup = new THREE.Group();
    dimensionGroup.name = 'cabinetDimensionArrows';

    // Add a unique ID to identify this arrow group
    dimensionGroup.userData.id =
        (cabinetGroup.userData.id as string) || 'cabinetDimensionArrows';

    // Store references to the width, depth, and height groups for updates
    dimensionGroup.userData.widthGroup = null;
    dimensionGroup.userData.depthGroup = null;
    dimensionGroup.userData.heightGroup = null;

    // Store all arrow objects and text materials to pass to the handler
    const arrowObjects: (
        | THREE.ArrowHelper
        | THREE.SpriteMaterial
        | THREE.Mesh
    )[] = [];

    // Distance from cabinet surface
    const offset = 0;
    const createWidthDimension = (
        width: number,
        depth: number,
        height: number
    ) => {
        // Remove existing width group if any
        if (dimensionGroup.userData.widthGroup) {
            dimensionGroup.remove(
                dimensionGroup.userData.widthGroup as THREE.Object3D
            );
        }

        // Width direction
        // Calculate positions
        const centerPos = new THREE.Vector3(
            width / 2,
            depth / 2,
            height + offset
        );

        // Create width group
        const widthGroup = new THREE.Group();
        widthGroup.name = 'width-dimension';

        // Create text for the dimension
        const displayWidth =
            (cabinetGroup.userData.values as {cabinet_width?: number})
                ?.cabinet_width || 0;
        const displayText = `${Math.round(displayWidth)} mm`;

        // Create sprite for text
        const createTextSprite = (text: string) => {
            // Create canvas - larger canvas to prevent text cropping
            const canvas = document.createElement('canvas');
            canvas.width = 384; // Increased from 256
            canvas.height = 192; // Increased from 128
            canvas.style.backgroundColor = 'transparent';

            const context = canvas.getContext('2d');
            if (!context) return null;

            // Clear the canvas to make it transparent
            context.clearRect(0, 0, canvas.width, canvas.height);

            // Text - larger and bolder for better visibility
            context.font = 'bold 100px Arial'; // Increased from 40px
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillStyle = 'black';

            // Add padding around text to prevent cropping
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            // Create texture
            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            texture.premultiplyAlpha = true;

            // Create sprite material
            const spriteMaterial = new THREE.SpriteMaterial({
                map: texture,
                transparent: true,
                depthTest: false,
                depthWrite: false,
                alphaTest: 0.01,
                sizeAttenuation: false,
            });

            // Create sprite - larger scale
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(60, 30, 1); // Increased from 40, 20, 1

            // Ensure sprite always renders on top
            sprite.renderOrder = 999;

            return sprite;
        };

        // Create the text sprite
        const textSprite = createTextSprite(displayText);
        if (textSprite) {
            textSprite.position.copy(centerPos);
            widthGroup.add(textSprite);
            widthGroup.userData.textSprite = textSprite;
        }

        // Create a position update method
        widthGroup.userData.updatePosition = (
            newWidth: number,
            newDepth: number,
            newHeight: number
        ) => {
            // Calculate new position
            const newCenterPos = new THREE.Vector3(
                newWidth / 2,
                newDepth,
                newHeight + offset
            );

            // Update sprite position
            if (widthGroup.userData.textSprite) {
                const textSprite = widthGroup.userData
                    .textSprite as THREE.Sprite;
                textSprite.position.copy(newCenterPos);
            }
        };

        // Add a method to update the text value
        widthGroup.userData.updateText = (newWidth: number) => {
            const displayText = `${Math.round(newWidth)} mm`;
            const textSprite = createTextSprite(displayText);

            if (textSprite && widthGroup.userData.textSprite) {
                // Replace old sprite with new one
                const oldSprite = widthGroup.userData
                    .textSprite as THREE.Sprite;
                textSprite.position.copy(oldSprite.position);
                widthGroup.remove(oldSprite);
                widthGroup.add(textSprite);
                widthGroup.userData.textSprite = textSprite;
            }
        };

        // Add width group to dimension group
        dimensionGroup.add(widthGroup);

        // Store reference to width group for updates
        dimensionGroup.userData.widthGroup = widthGroup;

        return widthGroup;
    };

    // Create depth dimension arrows and update function
    const createDepthDimension = (
        width: number,
        depth: number,
        height: number
    ) => {
        // Remove existing depth group if any
        if (dimensionGroup.userData.depthGroup) {
            dimensionGroup.remove(
                dimensionGroup.userData.depthGroup as THREE.Object3D
            );
        }

        // Depth direction (back to front)
        const centerPos = new THREE.Vector3(100, depth / 2, height + offset);

        // Create depth group
        const depthGroup = new THREE.Group();
        depthGroup.name = 'depth-dimension';

        // Create text for the dimension
        const displayDepth =
            (cabinetGroup.userData.values as {cabinet_depth?: number})
                ?.cabinet_depth || 0;
        const displayText = `${Math.round(displayDepth)} mm`;

        // Create sprite for text
        const createTextSprite = (text: string) => {
            // Create canvas - larger canvas to prevent text cropping
            const canvas = document.createElement('canvas');
            canvas.width = 384; // Increased from 256
            canvas.height = 192; // Increased from 128
            canvas.style.backgroundColor = 'transparent';

            const context = canvas.getContext('2d');
            if (!context) return null;

            // Clear the canvas to make it transparent
            context.clearRect(0, 0, canvas.width, canvas.height);

            // Text - larger and bolder for better visibility
            context.font = 'bold 100px Arial'; // Increased from 40px
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillStyle = 'black';

            // Add padding around text to prevent cropping
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            // Create texture
            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            texture.premultiplyAlpha = true;

            // Create sprite material
            const spriteMaterial = new THREE.SpriteMaterial({
                map: texture,
                transparent: true,
                depthTest: false,
                depthWrite: false,
                alphaTest: 0.01,
                sizeAttenuation: false,
            });

            // Create sprite - larger scale
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(60, 30, 1); // Increased from 40, 20, 1

            // Ensure sprite always renders on top
            sprite.renderOrder = 999;

            return sprite;
        };

        // Create the text sprite
        const textSprite = createTextSprite(displayText);
        if (textSprite) {
            textSprite.position.copy(centerPos);
            depthGroup.add(textSprite);
            depthGroup.userData.textSprite = textSprite;
        }

        // Create a position update method
        depthGroup.userData.updatePosition = (
            newWidth: number,
            newDepth: number,
            newHeight: number
        ) => {
            // Calculate new position
            const newCenterPos = new THREE.Vector3(
                -5,
                newDepth / 2,
                newHeight - offset
            );

            // Update sprite position
            if (depthGroup.userData.textSprite) {
                const textSprite = depthGroup.userData
                    .textSprite as THREE.Sprite;
                textSprite.position.copy(newCenterPos);
            }
        };

        // Add a method to update the text value
        depthGroup.userData.updateText = (newDepth: number) => {
            const displayText = `${Math.round(newDepth)} mm`;
            const textSprite = createTextSprite(displayText);

            if (textSprite && depthGroup.userData.textSprite) {
                // Replace old sprite with new one
                const oldSprite = depthGroup.userData
                    .textSprite as THREE.Sprite;
                textSprite.position.copy(oldSprite.position);
                depthGroup.remove(oldSprite);
                depthGroup.add(textSprite);
                depthGroup.userData.textSprite = textSprite;
            }
        };

        // Add depth group to dimension group
        dimensionGroup.add(depthGroup);

        // Store reference to depth group for updates
        dimensionGroup.userData.depthGroup = depthGroup;

        return depthGroup;
    };

    // Create height dimension arrows and update function
    const createHeightDimension = (
        width: number,
        depth: number,
        height: number
    ) => {
        // Remove existing height group if any
        if (dimensionGroup.userData.heightGroup) {
            dimensionGroup.remove(
                dimensionGroup.userData.heightGroup as THREE.Object3D
            );
        }

        // Height direction (bottom to top)
        const centerPos = new THREE.Vector3(100, -20, height / 2);

        // Create height group
        const heightGroup = new THREE.Group();
        heightGroup.name = 'height-dimension';

        // Create text for the dimension
        const displayHeight =
            (cabinetGroup.userData.values as {cabinet_height?: number})
                ?.cabinet_height || 0;
        const displayText = `${Math.round(displayHeight)} mm`;

        // Create sprite for text
        const createTextSprite = (text: string) => {
            // Create canvas - larger canvas to prevent text cropping
            const canvas = document.createElement('canvas');
            canvas.width = 384; // Increased from 256
            canvas.height = 192; // Increased from 128
            canvas.style.backgroundColor = 'transparent';

            const context = canvas.getContext('2d');
            if (!context) return null;

            // Clear the canvas to make it transparent
            context.clearRect(0, 0, canvas.width, canvas.height);

            // Text - larger and bolder for better visibility
            context.font = 'bold 100px Arial'; // Increased from 40px
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillStyle = 'black';

            // Add padding around text to prevent cropping
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            // Create texture
            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            texture.premultiplyAlpha = true;

            // Create sprite material
            const spriteMaterial = new THREE.SpriteMaterial({
                map: texture,
                transparent: true,
                depthTest: false,
                depthWrite: false,
                alphaTest: 0.01,
                sizeAttenuation: false,
            });

            // Create sprite - larger scale
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(60, 30, 1); // Increased from 40, 20, 1

            // Ensure sprite always renders on top
            sprite.renderOrder = 999;

            return sprite;
        };

        // Create the text sprite
        const textSprite = createTextSprite(displayText);
        if (textSprite) {
            textSprite.position.copy(centerPos);
            heightGroup.add(textSprite);
            heightGroup.userData.textSprite = textSprite;
        }

        // Create a position update method
        heightGroup.userData.updatePosition = (
            newWidth: number,
            newDepth: number,
            newHeight: number
        ) => {
            // Calculate new position
            const newCenterPos = new THREE.Vector3(100, -20, newHeight / 2);

            // Update sprite position
            if (heightGroup.userData.textSprite) {
                const textSprite = heightGroup.userData
                    .textSprite as THREE.Sprite;
                textSprite.position.copy(newCenterPos);
            }
        };

        // Add a method to update the text value
        heightGroup.userData.updateText = (newHeight: number) => {
            const displayText = `${Math.round(newHeight)} mm`;
            const textSprite = createTextSprite(displayText);

            if (textSprite && heightGroup.userData.textSprite) {
                // Replace old sprite with new one
                const oldSprite = heightGroup.userData
                    .textSprite as THREE.Sprite;
                textSprite.position.copy(oldSprite.position);
                heightGroup.remove(oldSprite);
                heightGroup.add(textSprite);
                heightGroup.userData.textSprite = textSprite;
            }
        };

        // Add height group to dimension group
        dimensionGroup.add(heightGroup);
        // Store reference to height group for updates
        dimensionGroup.userData.heightGroup = heightGroup;

        return heightGroup;
    };

    // Create a function to update all dimensions
    dimensionGroup.userData.updateDimensions = (
        newWidth: number,
        newDepth: number,
        newHeight: number
    ) => {
        // First, clear the arrowObjects array to avoid duplication
        arrowObjects.length = 0;
        // Then use the interface in your conditionals
        if (dimensionGroup.userData.widthGroup) {
            (
                dimensionGroup.userData.widthGroup as GroupWithUpdatePosition
            ).userData.updatePosition(newWidth, newDepth, newHeight);
        }

        if (dimensionGroup.userData.heightGroup) {
            (
                dimensionGroup.userData.heightGroup as GroupWithUpdatePosition
            ).userData.updatePosition(newWidth, newDepth, newHeight);
        }

        if (dimensionGroup.userData.depthGroup) {
            (
                dimensionGroup.userData.depthGroup as GroupWithUpdatePosition
            ).userData.updatePosition(newWidth, newDepth, newHeight);
        } else {
            // Clear the arrowObjects array to avoid duplication
            arrowObjects.length = 0;
            createWidthDimension(newWidth, newDepth, newHeight);
        }

        // Re-register with arrow handler
        handleArrowsAndGroup(arrowObjects, dimensionGroup);
    };

    dimensionGroup.userData.dispose = () => {
        // Remove from parent if added to parent instead of cabinet
        if (dimensionGroup.parent) {
            dimensionGroup.parent.remove(dimensionGroup);
        }
    };

    cabinetGroup.userData.disposeDimensionArrows = () => {
        (
            dimensionGroup.userData as {
                dispose: () => void;
            }
        ).dispose();
    };

    // Normal orientation for other directions
    createWidthDimension(width, depth, height);
    createDepthDimension(width, depth, height);
    createHeightDimension(width, depth, height);

    // Add dimension group to the scene at the cabinet's position
    if (cabinetGroup.parent) {
        cabinetGroup.parent.add(dimensionGroup);
    } else {
        return null;
    }
    // Position dimension group to match cabinet position
    dimensionGroup.position.copy(cabinetGroup.position);
    dimensionGroup.rotation.copy(cabinetGroup.rotation);
    // Set initial visibility to false
    dimensionGroup.visible = false;
    // Add onDrag and onDragEnd handlers
    dimensionGroup.userData.onDrag = (
        width: number,
        depth: number,
        height: number
    ) => {
        dimensionGroup.visible = true;
        // Update arrow dimensions if width and depth are provided
        if (width && depth && height) {
            const isHorizontal =
                cabinetGroup.userData.currentDirection === 'left' ||
                cabinetGroup.userData.currentDirection === 'right';

            if (!isHorizontal)
                (
                    dimensionGroup.userData as {
                        updateDimensions: (
                            width: number,
                            depth: number,
                            height: number
                        ) => void;
                    }
                ).updateDimensions(width, depth, height);
            else
                (
                    dimensionGroup.userData as {
                        updateDimensions: (
                            depth: number,
                            width: number,
                            height: number
                        ) => void;
                    }
                ).updateDimensions(depth, width, height);
        }
    };

    dimensionGroup.userData.onDragEnd = () => {
        dimensionGroup.visible = false;
    };
    // Register with arrow handler
    handleArrowsAndGroup(arrowObjects, dimensionGroup);
    return dimensionGroup;
};

export const addHeightArrowsAndText = (
    wall: THREE.Mesh,
    handleArrows: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[]
    ) => void,
    wallHeight: number,
    scale = 1,
    id = ''
) => {
    const arrowsAndText = [];
    const arrowColor = '#333';

    // Height Arrow Helper
    const heightArrowDir = new THREE.Vector3(0, 1, 0).normalize();
    const heightArrowLength = wallHeight - 50;

    // Create bottom arrow (height)
    const bottomArrow = new THREE.ArrowHelper(
        heightArrowDir.clone().negate(),
        new THREE.Vector3(-25, wallHeight - 25, 12.5),
        heightArrowLength,
        arrowColor,
        8,
        8
    );
    wall.add(bottomArrow);

    // Create top arrow (height)
    const topArrow = new THREE.ArrowHelper(
        heightArrowDir,
        new THREE.Vector3(-25, 25, 12.5),
        heightArrowLength,
        arrowColor,
        8,
        8
    );
    wall.add(topArrow);

    // Text for height
    const heightCanvas = document.createElement('canvas');
    const heightContext = heightCanvas.getContext('2d');
    heightCanvas.width = 256;
    heightCanvas.height = 128;

    heightContext.font = '50px Arial';
    heightContext.fillStyle = '#000';
    heightContext.textAlign = 'center';

    // Calculate actual height in mm based on scale
    const heightInMm = Math.round(wallHeight / scale - 100);
    heightContext.fillText(
        `${heightInMm} mm`,
        heightCanvas.width / 2,
        heightCanvas.height / 2
    );

    const heightTexture = new THREE.CanvasTexture(heightCanvas);
    const heightTextMaterial = new THREE.SpriteMaterial({map: heightTexture});
    const heightTextSprite = new THREE.Sprite(heightTextMaterial);

    // Position the height text on the left side of the wall
    heightTextSprite.position.set(-75, wallHeight / 2, 20);
    heightTextSprite.scale.set(100, 50, 1);

    wall.add(heightTextSprite);

    // Set user data for all elements
    topArrow.userData.id = id;
    bottomArrow.userData.id = id;
    heightTextMaterial.userData.id = id;

    // Add all elements to the arrows array for handling
    arrowsAndText.push(topArrow, bottomArrow, heightTextMaterial);

    // Handle visibility through the callback
    handleArrows(arrowsAndText);

    // Hide by default
    topArrow.visible = false;
    bottomArrow.visible = false;
    heightTextMaterial.visible = false;

    return arrowsAndText;
};

export const addAllCabinetDimensionsToBackWall = (
    cabinetGroupList: THREE.Group<THREE.Object3DEventMap>[],
    backWall: THREE.Mesh,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[],
        arrowGroup?: THREE.Group<THREE.Object3DEventMap>
    ) => void,
    getGroupWidth: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    }
): THREE.Group<THREE.Object3DEventMap> | null => {
    if (!cabinetGroupList || !cabinetGroupList.length || !backWall) return null;

    // Create a new group for the dimension arrows
    const dimensionGroup = new THREE.Group();
    dimensionGroup.name = 'cabinetBackWallDimensions';
    dimensionGroup.userData.id = 'cabinetBackWallDimensions';

    // Position the group near the top of the back wall
    const backWallPosition = backWall.position.clone();
    const backWallBoundingBox = new THREE.Box3().setFromObject(backWall);
    const backWallHeight =
        backWallBoundingBox.max.y - backWallBoundingBox.min.y;

    dimensionGroup.position.copy(backWallPosition);
    dimensionGroup.position.y += backWallHeight / 2 + 20; // Position above the back wall

    // Add text displaying cabinet dimensions
    const createDimensionText = (
        text: string,
        position: THREE.Vector3
    ): THREE.Sprite => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (!context) return;
        canvas.width = 512; // Wider canvas for more text
        canvas.height = 64;

        context.fillStyle = '#ffffff';
        context.fillRect(0, 0, canvas.width, canvas.height);

        context.font = 'Bold 18px Arial';
        context.fillStyle = '#000000';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(text, canvas.width / 2, canvas.height / 2);

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({map: texture});
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(80, 10, 1);
        sprite.position.copy(position);

        return sprite;
    };

    // Function to update dimensions for all cabinets
    dimensionGroup.userData.updateAllDimensions = () => {
        // Clear existing children
        while (dimensionGroup.children.length > 0) {
            dimensionGroup.remove(dimensionGroup.children[0]);
        }

        if (!cabinetGroupList || cabinetGroupList.length === 0) return;

        // Create combined text for all cabinets
        let allCabinetsText = 'Cabinet Dimensions:';

        cabinetGroupList.forEach((cabinet, index) => {
            if (!cabinet) return;

            const cabinetSize = getGroupWidth(cabinet);
            const formattedWidth = cabinetSize.width.toFixed(1);
            const formattedDepth = cabinetSize.depth.toFixed(1);
            const formattedHeight = cabinetSize.height.toFixed(1);

            // Add cabinet name and dimensions
            const cabinetName = cabinet.name || `Cabinet ${index + 1}`;
            allCabinetsText += ` ${cabinetName}: ${formattedWidth}x${formattedDepth}x${formattedHeight}`;

            // Add separator between cabinets
            if (index < cabinetGroupList.length - 1) {
                allCabinetsText += ' | ';
            }
        });

        // Create the text sprite and add to dimensionGroup
        const textSprite = createDimensionText(
            allCabinetsText,
            new THREE.Vector3(0, 0, 0)
        );
        dimensionGroup.add(textSprite);
    };

    (
        dimensionGroup.userData as {updateAllDimensions: () => void}
    ).updateAllDimensions();
    // Initial update with current dimensions

    // Add the dimension group to the scene
    backWall.parent?.add(dimensionGroup);

    if (handleArrowsAndGroup) {
        handleArrowsAndGroup([], dimensionGroup);
    }

    return dimensionGroup;
};

export const addMultipleMeasurementsToWall = (
    backWall: THREE.Mesh,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial | THREE.Line)[],
        arrowGroup?: THREE.Group
    ) => void,
    wallHeight: number,
    cabinetGroupList?: THREE.Group<THREE.Object3DEventMap>[],
    getGroupWidth?: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    },
    roomWidth?: number,
    roomLength?: number,
    scale?: number,
    wallRefId?: string
) => {
    // Check if there's an existing measurement group on the wall
    let arrowsGroup = backWall.children.find(
        (child) => child.name === 'multipleMeasurementsGroup'
    ) as THREE.Group;

    // Create a new group only if one doesn't exist
    if (!arrowsGroup) {
        arrowsGroup = new THREE.Group();
        arrowsGroup.name = 'multipleMeasurementsGroup';
        arrowsGroup.userData.type = 'multipleMeasurements';
        arrowsGroup.userData.updateVisibility = (show: boolean) => {
            arrowsGroup.visible = show;
        };

        // Add the new group to the wall
        backWall.add(arrowsGroup);
    }

    // Keep track of all arrow objects for updates
    let arrowObjects: (
        | THREE.ArrowHelper
        | THREE.SpriteMaterial
        | THREE.Line
    )[] = [];

    // Use existing measurement map or create a new one
    let measurementMap = new Map<string, MeasurementObject>();

    // If the measurement map already exists in userData, use it
    if (arrowsGroup.userData.measurementMap) {
        measurementMap = arrowsGroup.userData.measurementMap as Map<
            string,
            MeasurementObject
        >;
    } else {
        // Store the map in userData for future reference
        arrowsGroup.userData.measurementMap = measurementMap;
    }

    // Define arrow properties outside the loop
    const arrowColor = '#333';
    const arrowHeadLength = 8;
    const arrowHeadWidth = 8;

    // Generate measurements based on cabinet dimensions if cabinetGroupList is provided
    let finalMeasurements: {
        start: number;
        end: number;
        width: number;
        height: number;
        depth: number;
        position: number;
        cabinetId: string;
        label: string;
        dimensions: {
            width: number;
            height: number;
            depth: number;
        };
        currentDirection: string;
    }[] = [];

    if (cabinetGroupList && cabinetGroupList.length > 0 && getGroupWidth) {
        finalMeasurements = generateMeasurementsFromCabinets(
            cabinetGroupList,
            getGroupWidth
        );
    }

    // Updated collision handling function with wall proximity prioritization
    const handleMeasurementCollisions = (
        measurementsToCheck: {
            start: number;
            end: number;
            label: string;
            cabinetId?: string;
            width: number;
            currentDirection: string;
        }[],
        positionY: number,
        spacing = 50 // Vertical spacing between colliding measurements
    ) => {
        // First, sort measurements by position for easier collision detection
        const sortedMeasurements = [...measurementsToCheck].sort(
            (a, b) => a.start - b.start
        );

        // Map to store adjusted Y positions for each measurement
        const adjustedPositions = new Map<string, number>();

        // Apply size difference adjustments to measurement extents
        sortedMeasurements.forEach((measurement, index) => {
            // Check if we have a next measurement to compare with
            const nextMeasurement =
                index < sortedMeasurements.length - 1
                    ? sortedMeasurements[index + 1]
                    : null;

            if (nextMeasurement && measurement.width && nextMeasurement.width) {
                const leftCabinetWidth = measurement.width || 0;
                const rightCabinetWidth = nextMeasurement.width || 0;
                const roundedLeftWidth = Math.round(leftCabinetWidth * 10) / 10;
                const roundedRightWidth =
                    Math.round(rightCabinetWidth * 10) / 10;
                const isSameSize = roundedLeftWidth === roundedRightWidth;
                const sizeDifference =
                    Math.abs(rightCabinetWidth - leftCabinetWidth) / 2;
                const adjustmentFactor = sizeDifference * 0.45;
                const rightCabinetIsBigger =
                    rightCabinetWidth > leftCabinetWidth;

                if (!isSameSize) {
                    if (rightCabinetIsBigger) {
                        measurement.end -=
                            Math.min(adjustmentFactor * 2, 50) + 15;
                    } else {
                        measurement.start +=
                            Math.min(adjustmentFactor * 2, 50) - 4;
                    }
                } else {
                    measurement.end -= Math.min(adjustmentFactor * 2, 50) + 4;
                    measurement.start += Math.min(adjustmentFactor * 2, 50) + 4;
                }
            }

            // Apply adjustment for left and back directions
            if (measurement.currentDirection === 'left') {
                measurement.start -= measurement.width;
            }
            // Apply adjustment for left and back directions
            if (measurement.currentDirection === 'back') {
                measurement.start -= measurement.width;
            }
        });

        // Initialize with base position
        sortedMeasurements.forEach((measurement) => {
            const id =
                measurement.cabinetId ||
                `measurement-${sortedMeasurements.indexOf(measurement)}`;
            adjustedPositions.set(id, positionY);
        });

        // Check for collisions and adjust positions
        sortedMeasurements.forEach((current, i) => {
            const currentId =
                current.cabinetId ||
                `measurement-${sortedMeasurements.indexOf(current)}`;

            // Keep track of highest collision position for this measurement
            let highestCollisionPosition =
                adjustedPositions.get(currentId) || positionY;

            // Check against all other measurements for collisions
            sortedMeasurements.forEach((other, j) => {
                // Skip self-comparison
                if (i === j) return;

                const otherId =
                    other.cabinetId ||
                    `measurement-${sortedMeasurements.indexOf(other)}`;

                // Calculate centers and extents for both measurements
                const currentCenter = (current.start + current.end) / 2;
                const otherCenter = (other.start + other.end) / 2;

                // Calculate actual half-widths for each measurement label
                const labelMinWidth = 10; // Minimum width for a label in scene units

                // Calculate label widths based on actual widths, not the cabinet widths
                const currentLabelWidth = Math.max(
                    current.width * 1,
                    labelMinWidth
                );
                const otherLabelWidth = Math.max(
                    other.width * 1,
                    labelMinWidth
                );

                // Calculate half-widths for collision detection
                const currentHalfWidth = currentLabelWidth / 2;
                const otherHalfWidth = otherLabelWidth / 2;

                // Calculate the extents of each label based on center and half-width
                const currentLeft = currentCenter - currentHalfWidth;
                const currentRight = currentCenter + currentHalfWidth;
                const otherLeft = otherCenter - otherHalfWidth;
                const otherRight = otherCenter + otherHalfWidth;

                // Check for actual overlap between the label extents
                const overlap = !(
                    currentRight < otherLeft || currentLeft > otherRight
                );

                if (overlap) {
                    // Find the cabinet groups for both measurements
                    const currentCabinet = cabinetGroupList?.find(
                        (cabinet) => cabinet?.userData?.id === current.cabinetId
                    );

                    const otherCabinet = cabinetGroupList?.find(
                        (cabinet) => cabinet?.userData?.id === other.cabinetId
                    );

                    // Get Z positions (depth) of both cabinets if available
                    const currentZ = currentCabinet?.position.z || 0;
                    const otherZ = otherCabinet?.position.z || 0;

                    // Get the other measurement's current position
                    const otherPosition =
                        adjustedPositions.get(otherId) || positionY;

                    // Determine which one is closer to the wall
                    const zDifference = Math.abs(currentZ - otherZ);
                    const zThreshold = 50; // Threshold to determine significant difference

                    // Only adjust position if Z positions are significantly different
                    if (zDifference > zThreshold) {
                        // Prioritize the cabinet closer to the wall (smaller Z value)
                        if (currentZ > otherZ) {
                            // Current cabinet is further from wall, it should go above the other
                            const newPosition = otherPosition + spacing + 50;
                            if (newPosition > highestCollisionPosition) {
                                highestCollisionPosition = newPosition;
                            }
                        }
                        // If current is closer to the wall, no adjustment needed
                    }
                }
            });
            adjustedPositions.set(currentId, highestCollisionPosition);
        });

        return adjustedPositions;
    };

    const createOrUpdateMeasurements = (
        measurementsToCreate: {
            start: number;
            end: number;
            width: number;
            height: number;
            depth: number;
            position: number;
            cabinetId: string;
            label: string;
            dimensions: {
                width: number;
                height: number;
                depth: number;
            };
            currentDirection: string;
            isFrontWall?: boolean;
        }[]
    ) => {
        // Track which cabinet IDs are still present
        const currentCabinetIds = new Set(
            measurementsToCreate.map((m) => m.cabinetId)
        );

        // Remove measurements for cabinets that no longer exist
        const existingMeasurementIds = Array.from(measurementMap.keys());
        for (const id of existingMeasurementIds) {
            if (!currentCabinetIds.has(id)) {
                // Get the components for this cabinet
                const components = measurementMap.get(id);
                if (components) {
                    // Remove all components from the group
                    arrowsGroup.remove(components.leftArrow);
                    arrowsGroup.remove(components.rightArrow);
                    arrowsGroup.remove(components.sprite);
                    if (components.leftGuideline)
                        arrowsGroup.remove(components.leftGuideline);
                    if (components.rightGuideline)
                        arrowsGroup.remove(components.rightGuideline);

                    // Remove from the map
                    measurementMap.delete(id);
                }
            }
        }

        // Calculate adjusted positions based on collisions
        const basePositionY = wallHeight + 10; // Base position
        const adjustedPositions = handleMeasurementCollisions(
            measurementsToCreate,
            basePositionY
        );

        // Clear the arrowObjects array for new/updated components
        arrowObjects = [];

        measurementsToCreate.forEach((measurement, index) => {
            const {label, cabinetId, width} = measurement;

            // Use cabinetId if available, otherwise fall back to index-based identification
            const measurementId = cabinetId || `measurement-${index}`;

            // Find the corresponding cabinet group
            let cabinetGroup: THREE.Group<THREE.Object3DEventMap> | undefined;

            if (cabinetId && cabinetGroupList) {
                cabinetGroup = cabinetGroupList.find(
                    (cabinet) => cabinet?.userData?.id === cabinetId
                );
            }

            // Get accurate width for arrow length
            // Calculation for positioning when Rotate Right
            const getRotationValueFromDirection = (
                cabinetGroup: THREE.Group<THREE.Object3DEventMap> | undefined,
                width: number
            ) => {
                // Get current direction
                const rotatedCabinet = cabinetGroup.userData
                    .currentDirection as string;

                // Default rotation value is 0
                let rotationValue = 0;

                // Set rotation value based on direction
                if (rotatedCabinet === 'left') {
                    rotationValue = width; // or any value you want for 'left'
                } else if (rotatedCabinet === 'back') {
                    rotationValue = width; // or any value you want for 'back'
                }
                // front and right will keep the default value of 0

                return rotationValue;
            };

            const rotatedCabinet = getRotationValueFromDirection(
                cabinetGroup,
                width
            );

            // Get the collision-adjusted Y position for this measurement
            const positionY =
                adjustedPositions.get(measurementId) || basePositionY;
            const positionZ = wallRefId === 'backwall' ? 0 : 30;

            // Calculate positions based on cabinet position if available
            let xPosition = 0;
            if (roomWidth && scale && cabinetGroup) {
                xPosition =
                    -cabinetGroup?.position.x +
                    (roomWidth * scale) / 2 -
                    (width / 2 - 42) +
                    rotatedCabinet;
            }

            // Calculate left and right arrow positions
            const leftPos = xPosition - width / 2;
            const rightPos = xPosition + width / 2;

            // Check if this measurement already exists
            const components = measurementMap.get(measurementId);

            if (components) {
                // UPDATE EXISTING COMPONENTS

                // Update sprite texture (label)
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                if (context) {
                    canvas.width = 156;
                    canvas.height = 64;
                    context.fillStyle = 'black';
                    context.font = 'bold 27px Arial';
                    context.textAlign = 'center';
                    context.textBaseline = 'middle';

                    // Display the actual width in the label
                    const displayLabel = `${label} mm`;
                    context.fillText(
                        displayLabel,
                        canvas.width / 2,
                        canvas.height / 2
                    );

                    const texture = new THREE.CanvasTexture(canvas);
                    // Replace the existing material's map with the new texture
                    components.sprite.material.map = texture;
                    components.sprite.material.needsUpdate = true;
                }

                // Update positions of existing components
                components.sprite.position.x = xPosition;
                components.sprite.position.y = positionY + 50;
                components.sprite.position.z = 25;

                // Update left arrow
                components.leftArrow.position.set(
                    xPosition,
                    positionY,
                    positionZ
                );
                components.leftArrow.setLength(
                    width / 2,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Update right arrow
                components.rightArrow.position.set(
                    xPosition,
                    positionY,
                    positionZ
                );
                components.rightArrow.setLength(
                    width / 2,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Update guidelines
                if (components.leftGuideline) {
                    arrowsGroup.remove(components.leftGuideline);
                }
                if (components.rightGuideline) {
                    arrowsGroup.remove(components.rightGuideline);
                }

                // Create new guidelines with updated positions
                // Create vertical guidelines (dotted lines) running from top to bottom
                const createVerticalGuideline = (
                    zpos: number,
                    xPos: number,
                    yPos: number,
                    height: number
                ) => {
                    // Define points for the line - from top to bottom
                    const points = [];
                    points.push(new THREE.Vector3(xPos, -height, zpos)); // Top point
                    points.push(new THREE.Vector3(xPos, yPos, zpos)); // Bottom point at arrow level

                    // Create line geometry
                    const lineGeometry =
                        new THREE.BufferGeometry().setFromPoints(points);

                    // Create dotted line material
                    const lineMaterial = new THREE.LineDashedMaterial({
                        color: 0x333333, // Dark gray color
                        dashSize: 5, // Size of the dash
                        gapSize: 5, // Size of the gap
                        opacity: 0.7, // Slight transparency
                        transparent: true,
                    });

                    // Create the line
                    const line = new THREE.Line(lineGeometry, lineMaterial);

                    // You must call this for the dashed line to work
                    line.computeLineDistances();

                    return line;
                };

                // Create new guidelines
                const leftGuideline = createVerticalGuideline(
                    positionZ,
                    leftPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                const rightGuideline = createVerticalGuideline(
                    positionZ,
                    rightPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                // Add new guidelines
                arrowsGroup.add(leftGuideline);
                arrowsGroup.add(rightGuideline);

                // Update component references
                components.leftGuideline = leftGuideline;
                components.rightGuideline = rightGuideline;

                // Add to arrowObjects for the handler
                arrowObjects.push(components.sprite.material);
                arrowObjects.push(components.leftArrow);
                arrowObjects.push(components.rightArrow);
                arrowObjects.push(leftGuideline);
                arrowObjects.push(rightGuideline);
            } else {
                // CREATE NEW COMPONENTS FOR THIS MEASUREMENT

                // Create label first so we can position arrows relative to it
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                if (!context) return;

                canvas.width = 156;
                canvas.height = 64;

                context.fillStyle = 'black';
                context.font = 'bold 27px Arial';
                context.textAlign = 'center';
                context.textBaseline = 'middle';

                // Display the actual width in the label
                const displayLabel = `${label} mm`;
                context.fillText(
                    displayLabel,
                    canvas.width / 2,
                    canvas.height / 2
                );

                const texture = new THREE.CanvasTexture(canvas);
                const spriteMaterial = new THREE.SpriteMaterial({
                    map: texture,
                    transparent: true,
                });
                const sprite = new THREE.Sprite(spriteMaterial);
                sprite.scale.set(100, 50, 1); // Size of the label

                // Position the label using the collision-adjusted Y position
                sprite.position.x = xPosition;
                sprite.position.y = positionY + 50;
                sprite.position.z = 25; // Ensure it's in front of arrows

                // Create left arrow (pointing right/inward)
                const leftArrow = new THREE.ArrowHelper(
                    new THREE.Vector3(1, 0, 0).normalize(), // Direction vector (pointing right)
                    new THREE.Vector3(xPosition, positionY, positionZ), // Start position
                    width / 2, // Length of arrow - now based on cabinet width
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Store the measurement ID in the arrow's userData
                leftArrow.userData.measurementId = measurementId;
                leftArrow.userData.arrowType = 'left';

                // Create right arrow (pointing left/inward)
                const rightArrow = new THREE.ArrowHelper(
                    new THREE.Vector3(-1, 0, 0).normalize(), // Direction vector (pointing left)
                    new THREE.Vector3(xPosition, positionY, positionZ), // End position
                    width / 2, // Length of arrow - now based on cabinet width
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Store the measurement ID in the arrow's userData
                rightArrow.userData.measurementId = measurementId;
                rightArrow.userData.arrowType = 'right';

                // Create vertical guidelines (dotted lines) running from top to bottom
                const createVerticalGuideline = (
                    zpos: number,
                    xPos: number,
                    yPos: number,
                    height: number
                ) => {
                    // Define points for the line - from top to bottom
                    const points = [];
                    points.push(new THREE.Vector3(xPos, -height, zpos)); // Top point
                    points.push(new THREE.Vector3(xPos, yPos, zpos)); // Bottom point at arrow level

                    // Create line geometry
                    const lineGeometry =
                        new THREE.BufferGeometry().setFromPoints(points);

                    // Create dotted line material
                    const lineMaterial = new THREE.LineDashedMaterial({
                        color: 0x333333, // Dark gray color
                        dashSize: 5, // Size of the dash
                        gapSize: 5, // Size of the gap
                        opacity: 0.7, // Slight transparency
                        transparent: true,
                    });

                    // Create the line
                    const line = new THREE.Line(lineGeometry, lineMaterial);

                    // You must call this for the dashed line to work
                    line.computeLineDistances();

                    return line;
                };

                // Create vertical guidelines at each arrow position
                const leftGuideline = createVerticalGuideline(
                    positionZ,
                    leftPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                const rightGuideline = createVerticalGuideline(
                    positionZ,
                    rightPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                // Add everything to the group
                arrowsGroup.add(sprite);
                arrowsGroup.add(leftArrow);
                arrowsGroup.add(rightArrow);
                arrowsGroup.add(leftGuideline);
                arrowsGroup.add(rightGuideline);

                // Add to arrowObjects array
                arrowObjects.push(spriteMaterial);
                arrowObjects.push(leftArrow);
                arrowObjects.push(rightArrow);
                arrowObjects.push(leftGuideline);
                arrowObjects.push(rightGuideline);

                // Store the measurement components in the map
                measurementMap.set(measurementId, {
                    leftArrow,
                    rightArrow,
                    sprite,
                    leftGuideline,
                    rightGuideline,
                });
            }
        });

        // Call the handler function with arrow objects
        handleArrowsAndGroup(arrowObjects, arrowsGroup);
    };

    // Create or update the measurements
    createOrUpdateMeasurements(finalMeasurements);

    // Add update method to the group's userData
    arrowsGroup.userData.updateMeasurements = (
        cabinetGroupList?: THREE.Group<THREE.Object3DEventMap>[]
    ) => {
        let updatedMeasurements = finalMeasurements;

        if (cabinetGroupList && cabinetGroupList.length > 0 && getGroupWidth) {
            updatedMeasurements = generateMeasurementsFromCabinets(
                cabinetGroupList,
                getGroupWidth
            );
        }

        // This will update existing measurements and create new ones as needed
        createOrUpdateMeasurements(updatedMeasurements);
    };

    // Add method to update positions when cabinets move
    arrowsGroup.userData.updatePositionsForCabinet = (cabinetId: string) => {
        if (!cabinetGroupList || !roomWidth || !scale) return;

        // Find the cabinet with the given ID
        const cabinetGroup = cabinetGroupList.find(
            (cabinet) => cabinet.userData.id === cabinetId
        );
        if (!cabinetGroup) return;
        const dimensions = getGroupWidth(cabinetGroup);
        // Get the measurement components associated with this cabinet
        const measurement = measurementMap.get(cabinetId);
        if (!measurement) return;

        const {leftArrow, rightArrow, sprite, leftGuideline, rightGuideline} =
            measurement;

        // Update positions
        const newX =
            -cabinetGroup.position.x +
            (roomWidth * scale) / 2 -
            (dimensions.width / 2 - 42);

        // Calculate left and right arrow positions
        const leftPos = newX - dimensions.width / 2;
        const rightPos = newX + dimensions.width / 2;

        // Update arrow positions
        leftArrow.position.x = newX;
        rightArrow.position.x = newX;
        sprite.position.x = newX;

        // Update guidelines if they exist
        if (leftGuideline && rightGuideline) {
            // Remove existing guidelines
            arrowsGroup.remove(leftGuideline);
            arrowsGroup.remove(rightGuideline);

            // Create new guidelines
            const positionY = leftArrow.position.y;
            const positionZ = leftArrow.position.z;

            // Create vertical guidelines function
            const createVerticalGuideline = (
                zpos: number,
                xPos: number,
                yPos: number,
                height: number
            ) => {
                const points = [];
                points.push(new THREE.Vector3(xPos, -height, zpos));
                points.push(new THREE.Vector3(xPos, yPos, zpos));
                const lineGeometry = new THREE.BufferGeometry().setFromPoints(
                    points
                );
                const lineMaterial = new THREE.LineDashedMaterial({
                    color: 0x333333,
                    dashSize: 5,
                    gapSize: 5,
                    opacity: 0.7,
                    transparent: true,
                });
                const line = new THREE.Line(lineGeometry, lineMaterial);
                line.computeLineDistances();
                return line;
            };

            // Create new guidelines with updated positions
            const newLeftGuideline = createVerticalGuideline(
                positionZ,
                leftPos,
                positionY,
                wallHeight / 2 - 290
            );

            const newRightGuideline = createVerticalGuideline(
                positionZ,
                rightPos,
                positionY,
                wallHeight / 2 - 290
            );

            // Add new guidelines to the group
            arrowsGroup.add(newLeftGuideline);
            arrowsGroup.add(newRightGuideline);

            // Update the references in the measurement map
            measurement.leftGuideline = newLeftGuideline;
            measurement.rightGuideline = newRightGuideline;
        }

        // After updating positions, we should check for collisions and adjust if needed
        if (cabinetGroupList) {
            // Get current measurements
            const currentMeasurements = cabinetGroupList.map(
                (cabinet, index) => {
                    const dimensions = getGroupWidth(cabinet);
                    const cabinetId =
                        (cabinet.userData.id as string) || `cabinet-${index}`;
                    // You'll need to extract start/end from cabinet position/dimensions
                    // This is a simplified approximation
                    let start = 0;
                    let end = 0;

                    if (getGroupWidth) {
                        try {
                            const width = dimensions.width;
                            const centerX = cabinet.position.x;
                            start = centerX - width / 2;
                            end = centerX + width / 2;
                        } catch (error) {}
                    }

                    return {
                        start,
                        end,
                        label: `${end - start} `,
                        cabinetId,
                        width: dimensions.width,
                        currentDirection: cabinet?.userData
                            ?.currentDirection as string,
                    };
                }
            );

            // Calculate new Y positions
            const basePositionY = wallHeight + 50;
            const adjustedPositions = handleMeasurementCollisions(
                currentMeasurements,
                basePositionY
            );
            // Apply new Y positions to all measurements
            for (const [id, components] of Array.from(
                measurementMap.entries()
            )) {
                const newY = adjustedPositions.get(id);
                if (newY !== undefined) {
                    components.leftArrow.position.y = newY;
                    components.rightArrow.position.y = newY;
                    components.sprite.position.y = newY + 50;

                    // Update guidelines if they exist
                    if (components.leftGuideline && components.rightGuideline) {
                        // Remove existing guidelines
                        arrowsGroup.remove(components.leftGuideline);
                        arrowsGroup.remove(components.rightGuideline);

                        // Create vertical guidelines function
                        const createVerticalGuideline = (
                            zpos: number,
                            xPos: number,
                            yPos: number,
                            height: number
                        ) => {
                            const points = [];
                            points.push(new THREE.Vector3(xPos, -height, zpos));
                            points.push(new THREE.Vector3(xPos, yPos, zpos));
                            const lineGeometry =
                                new THREE.BufferGeometry().setFromPoints(
                                    points
                                );
                            const lineMaterial = new THREE.LineDashedMaterial({
                                color: 0x333333,
                                dashSize: 5,
                                gapSize: 5,
                                opacity: 0.7,
                                transparent: true,
                            });
                            const line = new THREE.Line(
                                lineGeometry,
                                lineMaterial
                            );
                            line.computeLineDistances();
                            return line;
                        };

                        // Get cabinet for this measurement
                        const cabinet = cabinetGroupList.find(
                            (cabinet) => cabinet?.userData?.id === id
                        );

                        if (cabinet) {
                            const dimensions = getGroupWidth(cabinet);
                            const positionZ = components.leftArrow.position.z;

                            // Calculate X positions
                            const xPosition = components.leftArrow.position.x;
                            const leftPos = xPosition - dimensions.width / 2;
                            const rightPos = xPosition + dimensions.width / 2;

                            // Create new guidelines
                            const newLeftGuideline = createVerticalGuideline(
                                positionZ,
                                leftPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            const newRightGuideline = createVerticalGuideline(
                                positionZ,
                                rightPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            // Add to the group
                            arrowsGroup.add(newLeftGuideline);
                            arrowsGroup.add(newRightGuideline);

                            // Update references
                            components.leftGuideline = newLeftGuideline;
                            components.rightGuideline = newRightGuideline;
                        }
                    }
                }
            }
        }
    };

    arrowsGroup.userData.updateAllPositions = () => {
        if (!cabinetGroupList || !roomWidth || !scale) return;

        // Update X positions first
        cabinetGroupList.forEach((cabinet) => {
            const cabinetId = cabinet.userData.id as string;
            const dimensions = getGroupWidth(cabinet);

            if (cabinetId) {
                // Get the measurement components associated with this cabinet
                const measurement = measurementMap.get(cabinetId);
                if (measurement) {
                    const {
                        leftArrow,
                        rightArrow,
                        sprite,
                        leftGuideline,
                        rightGuideline,
                    } = measurement;

                    // Update X positions
                    const newX =
                        -cabinet.position.x +
                        (roomWidth * scale) / 2 -
                        (dimensions.width / 2 - 42);
                    leftArrow.position.x = newX;
                    rightArrow.position.x = newX;
                    sprite.position.x = newX;

                    // Update guidelines if they exist
                    if (leftGuideline && rightGuideline) {
                        // Remove existing guidelines
                        arrowsGroup.remove(leftGuideline);
                        arrowsGroup.remove(rightGuideline);

                        // Create new guidelines
                        const positionY = leftArrow.position.y;
                        const positionZ = leftArrow.position.z;

                        // Calculate left and right positions
                        const leftPos = newX - dimensions.width / 2;
                        const rightPos = newX + dimensions.width / 2;

                        // Create vertical guidelines function
                        const createVerticalGuideline = (
                            zpos: number,
                            xPos: number,
                            yPos: number,
                            height: number
                        ) => {
                            const points = [];
                            points.push(new THREE.Vector3(xPos, -height, zpos));
                            points.push(new THREE.Vector3(xPos, yPos, zpos));
                            const lineGeometry =
                                new THREE.BufferGeometry().setFromPoints(
                                    points
                                );
                            const lineMaterial = new THREE.LineDashedMaterial({
                                color: 0x333333,
                                dashSize: 5,
                                gapSize: 5,
                                opacity: 0.7,
                                transparent: true,
                            });
                            const line = new THREE.Line(
                                lineGeometry,
                                lineMaterial
                            );
                            line.computeLineDistances();
                            return line;
                        };

                        // Create new guidelines
                        const newLeftGuideline = createVerticalGuideline(
                            positionZ,
                            leftPos,
                            positionY,
                            wallHeight / 2 - 290
                        );

                        const newRightGuideline = createVerticalGuideline(
                            positionZ,
                            rightPos,
                            positionY,
                            wallHeight / 2 - 290
                        );

                        // Add to the group
                        arrowsGroup.add(newLeftGuideline);
                        arrowsGroup.add(newRightGuideline);

                        // Update references
                        measurement.leftGuideline = newLeftGuideline;
                        measurement.rightGuideline = newRightGuideline;
                    }
                }
            }
        });

        // After updating all X positions, check for collisions and adjust Y positions
        if (cabinetGroupList) {
            // Get current measurements
            const currentMeasurements = cabinetGroupList.map(
                (cabinet, index) => {
                    const dimensions = getGroupWidth(cabinet);

                    const cabinetId =
                        (cabinet.userData.id as string) || `cabinet-${index}`;
                    // Extract start/end from cabinet position/dimensions
                    let start = 0;
                    let end = 0;

                    if (getGroupWidth) {
                        try {
                            const width = dimensions.width;
                            const centerX = cabinet.position.x;
                            start = centerX - width / 2;
                            end = centerX + width / 2;
                        } catch (error) {}
                    }

                    return {
                        start,
                        end,
                        label: `${end - start} `,
                        cabinetId,
                        width: dimensions.width,
                        currentDirection: cabinet?.userData
                            ?.currentDirection as string,
                    };
                }
            );

            // Calculate new Y positions
            const basePositionY = wallHeight + 50;
            const adjustedPositions = handleMeasurementCollisions(
                currentMeasurements,
                basePositionY
            );

            // Apply new Y positions to all measurements
            for (const [id, components] of Array.from(
                measurementMap.entries()
            )) {
                const newY = adjustedPositions.get(id);
                if (newY !== undefined) {
                    components.leftArrow.position.y = newY;
                    components.rightArrow.position.y = newY;
                    components.sprite.position.y = newY + 50;

                    // Update guidelines if they exist
                    if (components.leftGuideline && components.rightGuideline) {
                        // Remove existing guidelines
                        arrowsGroup.remove(components.leftGuideline);
                        arrowsGroup.remove(components.rightGuideline);

                        // Create vertical guidelines function
                        const createVerticalGuideline = (
                            zpos: number,
                            xPos: number,
                            yPos: number,
                            height: number
                        ) => {
                            const points = [];
                            points.push(new THREE.Vector3(xPos, -height, zpos));
                            points.push(new THREE.Vector3(xPos, yPos, zpos));
                            const lineGeometry =
                                new THREE.BufferGeometry().setFromPoints(
                                    points
                                );
                            const lineMaterial = new THREE.LineDashedMaterial({
                                color: 0x333333,
                                dashSize: 5,
                                gapSize: 5,
                                opacity: 0.7,
                                transparent: true,
                            });
                            const line = new THREE.Line(
                                lineGeometry,
                                lineMaterial
                            );
                            line.computeLineDistances();
                            return line;
                        };

                        // Get cabinet for this measurement
                        const cabinet = cabinetGroupList.find(
                            (cabinet) => cabinet?.userData?.id === id
                        );

                        if (cabinet) {
                            const dimensions = getGroupWidth(cabinet);
                            const positionZ = components.leftArrow.position.z;

                            // Calculate X positions
                            const xPosition = components.leftArrow.position.x;
                            const leftPos = xPosition - dimensions.width / 2;
                            const rightPos = xPosition + dimensions.width / 2;

                            // Create new guidelines
                            const newLeftGuideline = createVerticalGuideline(
                                positionZ,
                                leftPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            const newRightGuideline = createVerticalGuideline(
                                positionZ,
                                rightPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            // Add to the group
                            arrowsGroup.add(newLeftGuideline);
                            arrowsGroup.add(newRightGuideline);

                            // Update references
                            components.leftGuideline = newLeftGuideline;
                            components.rightGuideline = newRightGuideline;
                        }
                    }
                }
            }
        }
    };

    return arrowsGroup;
};

export const addMultipleMeasurementsToWallLeftRight = (
    backWall: THREE.Mesh,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial | THREE.Line)[],
        arrowGroup?: THREE.Group
    ) => void,
    wallHeight: number,
    cabinetGroupList?: THREE.Group<THREE.Object3DEventMap>[],
    getGroupWidth?: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    },
    roomWidth?: number,
    roomLength?: number,
    scale?: number,
    wallRefId?: string
) => {
    // Check if there's an existing measurement group on the wall
    let arrowsGroup = backWall.children.find(
        (child) => child.name === 'multipleMeasurementsGroup'
    ) as THREE.Group;

    // Create a new group only if one doesn't exist
    if (!arrowsGroup) {
        arrowsGroup = new THREE.Group();
        arrowsGroup.name = 'multipleMeasurementsGroup';
        arrowsGroup.userData.type = 'multipleMeasurements';
        arrowsGroup.userData.updateVisibility = (show: boolean) => {
            arrowsGroup.visible = show;
        };

        // Add the new group to the wall
        backWall.add(arrowsGroup);
    }

    // Keep track of all arrow objects for updates
    let arrowObjects: (
        | THREE.ArrowHelper
        | THREE.SpriteMaterial
        | THREE.Line
    )[] = [];

    // Use existing measurement map or create a new one

    let measurementMap = new Map<string, MeasurementObject>();

    // If the measurement map already exists in userData, use it
    if (arrowsGroup.userData.measurementMap) {
        measurementMap = arrowsGroup.userData.measurementMap as Map<
            string,
            MeasurementObject
        >;
    } else {
        // Store the map in userData for future reference
        arrowsGroup.userData.measurementMap = measurementMap;
    }

    // Define arrow properties outside the loop
    const arrowColor = '#333';
    const arrowHeadLength = 8;
    const arrowHeadWidth = 8;

    // Generate measurements based on cabinet dimensions if cabinetGroupList is provided
    let finalMeasurements: {
        start: number;
        end: number;
        width: number;
        height: number;
        depth: number;
        position: number;
        cabinetId: string;
        label: string;
        dimensions: {
            width: number;
            height: number;
            depth: number;
        };
        currentDirection: string;
        positionZ: number;
    }[] = [];

    if (cabinetGroupList && cabinetGroupList.length > 0 && getGroupWidth) {
        finalMeasurements = generateMeasurementsFromCabinets(
            cabinetGroupList,
            getGroupWidth
        );
    }

    // Updated collision handling function with wall proximity prioritization
    const handleMeasurementCollisions = (
        measurementsToCheck: {
            start: number;
            end: number;
            label: string;
            cabinetId?: string;
            width: number;
            currentDirection: string;
            positionZ?: number;
            position?: number;
            depth?: number;
        }[],
        positionY: number,
        spacing = 50 // Vertical spacing between colliding measurements
    ) => {
        // First, sort measurements by position for easier collision detection
        const sortedMeasurements = [...measurementsToCheck].sort(
            (a, b) => a.start - b.start
        );

        // Map to store adjusted Y positions for each measurement
        const adjustedPositions = new Map<string, number>();

        // Apply size difference adjustments to measurement extents
        sortedMeasurements.forEach((measurement, index) => {
            // Check if we have a next measurement to compare with
            const nextMeasurement =
                index < sortedMeasurements.length - 1
                    ? sortedMeasurements[index + 1]
                    : null;

            if (nextMeasurement && measurement.width && nextMeasurement.width) {
                const leftCabinetWidth = measurement.width || 0;
                const rightCabinetWidth = nextMeasurement.width || 0;
                const roundedLeftWidth = Math.round(leftCabinetWidth * 10) / 10;
                const roundedRightWidth =
                    Math.round(rightCabinetWidth * 10) / 10;
                const isSameSize = roundedLeftWidth === roundedRightWidth;
                const sizeDifference =
                    Math.abs(rightCabinetWidth - leftCabinetWidth) / 2;
                const adjustmentFactor = sizeDifference * 0.45;
                const rightCabinetIsBigger =
                    rightCabinetWidth > leftCabinetWidth;

                if (!isSameSize) {
                    if (rightCabinetIsBigger) {
                        measurement.end -=
                            Math.min(adjustmentFactor * 2, 50) + 15;
                    } else {
                        measurement.start +=
                            Math.min(adjustmentFactor * 2, 50) - 4;
                    }
                } else {
                    measurement.end -= Math.min(adjustmentFactor * 2, 50) + 4;
                    measurement.start += Math.min(adjustmentFactor * 2, 50) + 4;
                }
            }

            // Apply adjustment for left and back directions
            if (measurement.currentDirection === 'left') {
                measurement.start -= measurement.width;
            }
            // Apply adjustment for left and back directions
            if (measurement.currentDirection === 'back') {
                measurement.start -= measurement.width;
            }
        });

        // Initialize with base position
        sortedMeasurements.forEach((measurement) => {
            const id =
                measurement.cabinetId ||
                `measurement-${sortedMeasurements.indexOf(measurement)}`;
            adjustedPositions.set(id, positionY);
        });
        // Check for collisions and adjust positions
        sortedMeasurements.forEach((current, i) => {
            const currentId =
                current.cabinetId ||
                `measurement-${sortedMeasurements.indexOf(current)}`;

            // Keep track of highest collision position for this measurement
            let highestCollisionPosition =
                adjustedPositions.get(currentId) || positionY;

            // Check against all other measurements for collisions
            sortedMeasurements.forEach((other, j) => {
                // Skip self-comparison
                if (i === j) return;

                const otherId =
                    other.cabinetId ||
                    `measurement-${sortedMeasurements.indexOf(other)}`;

                // Calculate centers and extents for both measurements
                const currentCenter = (current.start + current.end) / 2;
                const otherCenter = (other.start + other.end) / 2;

                // Calculate actual half-widths for each measurement label
                const labelMinWidth = 10; // Minimum width for a label in scene units

                // Calculate label widths based on actual widths, not the cabinet widths
                const currentLabelWidth = Math.max(
                    current.width * 1,
                    labelMinWidth
                );
                const otherLabelWidth = Math.max(
                    other.width * 1,
                    labelMinWidth
                );

                // Calculate half-widths for collision detection
                const currentHalfWidth = currentLabelWidth / 2;
                const otherHalfWidth = otherLabelWidth / 2;

                // Calculate the extents of each label based on center and half-width
                const currentLeft = currentCenter - currentHalfWidth;
                const currentRight = currentCenter + currentHalfWidth;
                const otherLeft = otherCenter - otherHalfWidth;
                const otherRight = otherCenter + otherHalfWidth;

                const currentZStart = current.positionZ;
                const currentZEnd = current.positionZ + current.depth;
                const otherZStart = other.positionZ;
                const otherZEnd = other.positionZ + other.depth;

                // Check if z extents overlap
                const zOverlap = !(
                    currentZEnd < otherZStart || currentZStart > otherZEnd
                );

                // Check if the measurements overlap on the x-axis
                const xOverlap = !(
                    currentRight < otherLeft || currentLeft > otherRight
                );

                // Stack vertically if EITHER condition is true
                if (xOverlap || zOverlap) {
                    const otherPosition =
                        adjustedPositions.get(otherId) || positionY;
                    const newPosition = otherPosition + spacing + 50;
                    if (newPosition > highestCollisionPosition) {
                        highestCollisionPosition = newPosition;
                    }
                }
            });
            adjustedPositions.set(currentId, highestCollisionPosition);
        });

        return adjustedPositions;
    };

    const createOrUpdateMeasurements = (
        measurementsToCreate: {
            start: number;
            end: number;
            width: number;
            height: number;
            depth: number;
            position: number;
            cabinetId: string;
            label: string;
            dimensions: {
                width: number;
                height: number;
                depth: number;
            };
            currentDirection: string;
            isFrontWall?: boolean;
            positionZ: number;
        }[]
    ) => {
        // Track which cabinet IDs are still present
        const currentCabinetIds = new Set(
            measurementsToCreate.map((m) => m.cabinetId)
        );

        // Remove measurements for cabinets that no longer exist
        const existingMeasurementIds = Array.from(measurementMap.keys());
        for (const id of existingMeasurementIds) {
            if (!currentCabinetIds.has(id)) {
                // Get the components for this cabinet
                const components = measurementMap.get(id);
                if (components) {
                    // Remove all components from the group
                    arrowsGroup.remove(components.leftArrow);
                    arrowsGroup.remove(components.rightArrow);
                    arrowsGroup.remove(components.sprite);
                    if (components.leftGuideline)
                        arrowsGroup.remove(components.leftGuideline);
                    if (components.rightGuideline)
                        arrowsGroup.remove(components.rightGuideline);

                    // Remove from the map
                    measurementMap.delete(id);
                }
            }
        }

        // Clear the arrowObjects array for new/updated components
        arrowObjects = [];

        // Step 1: Group measurements by Z position (within a tolerance)
        const zPositionTolerance = 100; // Tolerance value for grouping by Z
        const zGroups = new Map<number, typeof measurementsToCreate>();

        // Create Z position groups
        measurementsToCreate.forEach((measurement) => {
            const zKey =
                Math.round(measurement.positionZ / zPositionTolerance) *
                zPositionTolerance;

            // Get the existing array or create a new one if it doesn't exist
            const existingGroup = zGroups.get(zKey) || [];

            // Push the measurement to the group
            existingGroup.push(measurement);

            // Set the updated group back to the map
            zGroups.set(zKey, existingGroup);
        });

        // Helper function to create or update arrow components
        const createOrUpdateArrowComponents = (
            measurementId: string,
            label: string,
            xPosition: number,
            positionY: number,
            positionZ: number,
            leftPos: number,
            rightPos: number,
            depth: number
        ) => {
            // Check if this measurement already exists
            const components = measurementMap.get(measurementId);

            if (components) {
                // UPDATE EXISTING COMPONENTS

                // Update sprite texture (label)
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                if (context) {
                    canvas.width = 156;
                    canvas.height = 64;
                    context.fillStyle = 'black';
                    context.font = 'bold 27px Arial';
                    context.textAlign = 'center';
                    context.textBaseline = 'middle';

                    // Display the actual width in the label
                    const displayLabel = `${label} mm`;
                    context.fillText(
                        displayLabel,
                        canvas.width / 2,
                        canvas.height / 2
                    );

                    const texture = new THREE.CanvasTexture(canvas);
                    // Replace the existing material's map with the new texture
                    components.sprite.material.map = texture;
                    components.sprite.material.needsUpdate = true;
                }

                // Update positions of existing components
                components.sprite.position.x = xPosition;
                components.sprite.position.y = positionY + 50;
                components.sprite.position.z = 25;

                // Update left arrow
                components.leftArrow.position.set(
                    xPosition,
                    positionY,
                    positionZ
                );
                components.leftArrow.setLength(
                    depth / 2,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Update right arrow
                components.rightArrow.position.set(
                    xPosition,
                    positionY,
                    positionZ
                );
                components.rightArrow.setLength(
                    depth / 2,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Update guidelines
                if (components.leftGuideline) {
                    arrowsGroup.remove(components.leftGuideline);
                }
                if (components.rightGuideline) {
                    arrowsGroup.remove(components.rightGuideline);
                }

                // Create new guidelines with updated positions
                const leftGuideline = createVerticalGuideline(
                    positionZ,
                    leftPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                const rightGuideline = createVerticalGuideline(
                    positionZ,
                    rightPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                // Add new guidelines
                arrowsGroup.add(leftGuideline);
                arrowsGroup.add(rightGuideline);

                // Update component references
                components.leftGuideline = leftGuideline;
                components.rightGuideline = rightGuideline;

                // Add to arrowObjects for the handler
                arrowObjects.push(components.sprite.material);
                arrowObjects.push(components.leftArrow);
                arrowObjects.push(components.rightArrow);
                arrowObjects.push(leftGuideline);
                arrowObjects.push(rightGuideline);
            } else {
                // CREATE NEW COMPONENTS FOR THIS MEASUREMENT

                // Create label first so we can position arrows relative to it
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                if (!context) return;

                canvas.width = 156;
                canvas.height = 64;

                context.fillStyle = 'black';
                context.font = 'bold 27px Arial';
                context.textAlign = 'center';
                context.textBaseline = 'middle';

                // Display the actual width in the label
                const displayLabel = `${label} mm`;
                context.fillText(
                    displayLabel,
                    canvas.width / 2,
                    canvas.height / 2
                );

                const texture = new THREE.CanvasTexture(canvas);
                const spriteMaterial = new THREE.SpriteMaterial({
                    map: texture,
                    transparent: true,
                });
                const sprite = new THREE.Sprite(spriteMaterial);
                sprite.scale.set(100, 50, 1); // Size of the label

                // Position the label using the collision-adjusted Y position
                sprite.position.x = xPosition;
                sprite.position.y = positionY + 50;
                sprite.position.z = 25; // Ensure it's in front of arrows

                // Create left arrow (pointing right/inward)
                const leftArrow = new THREE.ArrowHelper(
                    new THREE.Vector3(1, 0, 0).normalize(), // Direction vector (pointing right)
                    new THREE.Vector3(xPosition, positionY, positionZ), // Start position
                    depth / 2, // Length of arrow - now based on cabinet width
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Store the measurement ID in the arrow's userData
                leftArrow.userData.measurementId = measurementId;
                leftArrow.userData.arrowType = 'left';

                // Create right arrow (pointing left/inward)
                const rightArrow = new THREE.ArrowHelper(
                    new THREE.Vector3(-1, 0, 0).normalize(), // Direction vector (pointing left)
                    new THREE.Vector3(xPosition, positionY, positionZ), // End position
                    depth / 2, // Length of arrow - now based on cabinet width
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Store the measurement ID in the arrow's userData
                rightArrow.userData.measurementId = measurementId;
                rightArrow.userData.arrowType = 'right';

                // Create vertical guidelines at each arrow position
                const leftGuideline = createVerticalGuideline(
                    positionZ,
                    leftPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                const rightGuideline = createVerticalGuideline(
                    positionZ,
                    rightPos,
                    positionY,
                    wallHeight / 2 - 290
                );

                // Add everything to the group
                arrowsGroup.add(sprite);
                arrowsGroup.add(leftArrow);
                arrowsGroup.add(rightArrow);
                arrowsGroup.add(leftGuideline);
                arrowsGroup.add(rightGuideline);

                // Add to arrowObjects array
                arrowObjects.push(spriteMaterial);
                arrowObjects.push(leftArrow);
                arrowObjects.push(rightArrow);
                arrowObjects.push(leftGuideline);
                arrowObjects.push(rightGuideline);

                // Store the measurement components in the map
                measurementMap.set(measurementId, {
                    leftArrow,
                    rightArrow,
                    sprite,
                    leftGuideline,
                    rightGuideline,
                });
            }
        };

        // Helper function to create vertical guidelines
        const createVerticalGuideline = (
            zpos: number,
            xPos: number,
            yPos: number,
            height: number
        ) => {
            // Define points for the line - from top to bottom
            const points = [];
            points.push(new THREE.Vector3(xPos, -height, zpos)); // Top point
            points.push(new THREE.Vector3(xPos, yPos, zpos)); // Bottom point at arrow level

            // Create line geometry
            const lineGeometry = new THREE.BufferGeometry().setFromPoints(
                points
            );

            // Create dotted line material
            const lineMaterial = new THREE.LineDashedMaterial({
                color: 0x333333, // Dark gray color
                dashSize: 5, // Size of the dash
                gapSize: 5, // Size of the gap
                opacity: 0.7, // Slight transparency
                transparent: true,
            });

            // Create the line
            const line = new THREE.Line(lineGeometry, lineMaterial);

            // You must call this for the dashed line to work
            line.computeLineDistances();

            return line;
        };
        // Sort Z groups from front to back
        const sortedZKeys = Array.from(zGroups.keys()).sort((a, b) => a - b);

        // Base position Y for the first row
        const basePositionY = wallHeight + 10;
        // Current position Y that will be incremented for each row
        let currentRowPositionY = basePositionY;

        // Process each Z group (row) sequentially
        sortedZKeys.forEach((zKey) => {
            const rowMeasurements = zGroups.get(zKey) || [];

            // Sort measurements in this row by X position
            rowMeasurements.sort((a, b) => a.start - b.start);

            // For each row, create "lanes" for measurements that don't overlap
            const lanes: {
                yPosition: number;
                measurements: {
                    id: string;
                    xStart: number;
                    xEnd: number;
                }[];
            }[] = [{yPosition: currentRowPositionY, measurements: []}];

            // Process each measurement in this row
            rowMeasurements.forEach((measurement, index) => {
                const {label, cabinetId, width, depth} = measurement;

                // Use cabinetId if available, otherwise fall back to index-based identification
                const measurementId = cabinetId || `measurement-${index}`;

                // Find the corresponding cabinet group
                let cabinetGroup:
                    | THREE.Group<THREE.Object3DEventMap>
                    | undefined;

                if (cabinetId && cabinetGroupList) {
                    cabinetGroup = cabinetGroupList.find(
                        (cabinet) => cabinet?.userData?.id === cabinetId
                    );
                }

                // Get accurate width for arrow length
                // Calculation for positioning when Rotate Right
                const getRotationValueFromDirection = (
                    cabinetGroup:
                        | THREE.Group<THREE.Object3DEventMap>
                        | undefined,
                    width: number,
                    depth: number
                ) => {
                    // Get current direction
                    const rotatedCabinet = cabinetGroup?.userData
                        ?.currentDirection as string;

                    // Default rotation value is 0
                    let rotationValue = 0;

                    // Set rotation value based on direction
                    if (rotatedCabinet === 'left') {
                        rotationValue = depth; // or any value you want for 'left'
                    } else if (rotatedCabinet === 'back') {
                        rotationValue = 0; // or any value you want for 'back'
                    } else if (rotatedCabinet === 'front') {
                        rotationValue = depth; // or any value you want for 'back'
                    }
                    // front and right will keep the default value of 0

                    return rotationValue;
                };

                const rotatedCabinet = getRotationValueFromDirection(
                    cabinetGroup,
                    width,
                    depth
                );

                // Calculate positions based on cabinet position if available
                let xPosition = 0;
                if (roomLength && scale && cabinetGroup) {
                    xPosition =
                        -cabinetGroup?.position.z +
                        (roomLength * scale) / 2 -
                        (depth / 2 + 12) +
                        rotatedCabinet;
                }

                // Calculate left and right arrow positions
                const leftPos = xPosition - depth / 2;
                const rightPos = xPosition + depth / 2;

                // Calculate label width for collision detection
                const labelWidth = Math.max(depth, 100); // Minimum label width to ensure visibility
                const xStart = xPosition - labelWidth / 2;
                const xEnd = xPosition + labelWidth / 2;

                // Special handling for first cabinet (always place in first lane)
                if (index === 0 && zKey === sortedZKeys[0]) {
                    // First cabinet in first row always goes in the first lane
                    lanes[0].measurements.push({
                        id: measurementId,
                        xStart,
                        xEnd,
                    });

                    // Set position variables for arrow creation
                    const positionY = lanes[0].yPosition;
                    const positionZ = wallRefId == 'frontwall' ? 30 : 30;

                    // Create/update arrows for this measurement with these positions
                    createOrUpdateArrowComponents(
                        measurementId,
                        label,
                        xPosition,
                        positionY,
                        positionZ,
                        leftPos,
                        rightPos,
                        depth
                    );

                    return; // Skip the rest of processing for first cabinet
                }

                // For other cabinets, find a lane that can accommodate this measurement
                let laneFound = false;

                // Try to find a lane where this measurement doesn't overlap with any existing ones
                for (const lane of lanes) {
                    let overlapsInLane = false;

                    // Check if this measurement overlaps with any in this lane
                    for (const existing of lane.measurements) {
                        // Check for overlap
                        if (
                            !(xEnd < existing.xStart || xStart > existing.xEnd)
                        ) {
                            overlapsInLane = true;
                            break;
                        }
                    }

                    // If no overlaps in this lane, use it
                    if (!overlapsInLane) {
                        lane.measurements.push({
                            id: measurementId,
                            xStart,
                            xEnd,
                        });

                        // Set position variables for arrow creation
                        const positionY = lane.yPosition;
                        const positionZ = wallRefId == 'frontwall' ? 30 : 30;

                        // Create/update arrows for this measurement with these positions
                        createOrUpdateArrowComponents(
                            measurementId,
                            label,
                            xPosition,
                            positionY,
                            positionZ,
                            leftPos,
                            rightPos,
                            depth
                        );

                        laneFound = true;
                        break;
                    }
                }

                // If no existing lane works, create a new one
                if (!laneFound) {
                    const newLaneY = lanes[lanes.length - 1].yPosition + 100; // Space between lanes

                    lanes.push({
                        yPosition: newLaneY,
                        measurements: [
                            {
                                id: measurementId,
                                xStart,
                                xEnd,
                            },
                        ],
                    });

                    // Set position variables for arrow creation
                    const positionY = newLaneY;
                    const positionZ = wallRefId == 'frontwall' ? 30 : 30;

                    // Create/update arrows for this measurement with these positions
                    createOrUpdateArrowComponents(
                        measurementId,
                        label,
                        xPosition,
                        positionY,
                        positionZ,
                        leftPos,
                        rightPos,
                        depth
                    );
                }
            });

            // Find the lowest lane Y position to set as the start for the next row
            if (lanes.length > 0) {
                const maxLaneY = Math.max(
                    ...lanes.map((lane) => lane.yPosition)
                );
                currentRowPositionY = maxLaneY + 150; // Space between rows
            }
        });

        // Call the handler function with arrow objects
        handleArrowsAndGroup(arrowObjects, arrowsGroup);
    };

    // Create or update the measurements
    createOrUpdateMeasurements(finalMeasurements);

    // Add update method to the group's userData
    arrowsGroup.userData.updateMeasurements = (
        cabinetGroupList?: THREE.Group<THREE.Object3DEventMap>[]
    ) => {
        let updatedMeasurements = finalMeasurements;

        if (cabinetGroupList && cabinetGroupList.length > 0 && getGroupWidth) {
            updatedMeasurements = generateMeasurementsFromCabinets(
                cabinetGroupList,
                getGroupWidth
            );
        }

        // This will update existing measurements and create new ones as needed
        createOrUpdateMeasurements(updatedMeasurements);
    };

    // Add method to update positions when cabinets move
    arrowsGroup.userData.updatePositionsForCabinet = (cabinetId: string) => {
        if (!cabinetGroupList || !roomWidth || !scale) return;

        // Find the cabinet with the given ID
        const cabinetGroup = cabinetGroupList.find(
            (cabinet) => cabinet.userData.id === cabinetId
        );
        if (!cabinetGroup) return;
        const dimensions = getGroupWidth(cabinetGroup);
        // Get the measurement components associated with this cabinet
        const measurement = measurementMap.get(cabinetId);
        if (!measurement) return;

        const {leftArrow, rightArrow, sprite, leftGuideline, rightGuideline} =
            measurement;

        // Update positions
        const newX =
            -cabinetGroup.position.x +
            (roomLength * scale) / 2 -
            (dimensions.depth / 2 - 42);

        // Calculate left and right arrow positions
        const leftPos = newX - dimensions.width / 2;
        const rightPos = newX + dimensions.width / 2;

        // Update arrow positions
        leftArrow.position.x = newX;
        rightArrow.position.x = newX;
        sprite.position.x = newX;

        // Update guidelines if they exist
        if (leftGuideline && rightGuideline) {
            // Remove existing guidelines
            arrowsGroup.remove(leftGuideline);
            arrowsGroup.remove(rightGuideline);

            // Create new guidelines
            const positionY = leftArrow.position.y;
            const positionZ = leftArrow.position.z;

            // Create vertical guidelines function
            const createVerticalGuideline = (
                zpos: number,
                xPos: number,
                yPos: number,
                height: number
            ) => {
                const points = [];
                points.push(new THREE.Vector3(xPos, -height, zpos));
                points.push(new THREE.Vector3(xPos, yPos, zpos));
                const lineGeometry = new THREE.BufferGeometry().setFromPoints(
                    points
                );
                const lineMaterial = new THREE.LineDashedMaterial({
                    color: 0x333333,
                    dashSize: 5,
                    gapSize: 5,
                    opacity: 0.7,
                    transparent: true,
                });
                const line = new THREE.Line(lineGeometry, lineMaterial);
                line.computeLineDistances();
                return line;
            };

            // Create new guidelines with updated positions
            const newLeftGuideline = createVerticalGuideline(
                positionZ,
                leftPos,
                positionY,
                wallHeight / 2 - 290
            );

            const newRightGuideline = createVerticalGuideline(
                positionZ,
                rightPos,
                positionY,
                wallHeight / 2 - 290
            );

            // Add new guidelines to the group
            arrowsGroup.add(newLeftGuideline);
            arrowsGroup.add(newRightGuideline);

            // Update the references in the measurement map
            measurement.leftGuideline = newLeftGuideline;
            measurement.rightGuideline = newRightGuideline;
        }

        // After updating positions, we should check for collisions and adjust if needed
        if (cabinetGroupList) {
            // Get current measurements
            const currentMeasurements = cabinetGroupList.map(
                (cabinet, index) => {
                    const dimensions = getGroupWidth(cabinet);
                    const cabinetId =
                        (cabinet.userData.id as string) || `cabinet-${index}`;
                    // You'll need to extract start/end from cabinet position/dimensions
                    // This is a simplified approximation
                    let start = 0;
                    let end = 0;

                    if (getGroupWidth) {
                        try {
                            const width = dimensions.width;
                            const centerX = cabinet.position.x;
                            start = centerX - width / 2;
                            end = centerX + width / 2;
                        } catch (error) {}
                    }

                    return {
                        start,
                        end,
                        label: `${end - start} `,
                        cabinetId,
                        width: dimensions.width,
                        currentDirection: cabinet?.userData
                            ?.currentDirection as string,
                    };
                }
            );

            // Calculate new Y positions
            const basePositionY = wallHeight + 50;
            const adjustedPositions = handleMeasurementCollisions(
                currentMeasurements,
                basePositionY
            );
            // Apply new Y positions to all measurements
            for (const [id, components] of Array.from(
                measurementMap.entries()
            )) {
                const newY = adjustedPositions.get(id);
                if (newY !== undefined) {
                    components.leftArrow.position.y = newY;
                    components.rightArrow.position.y = newY;
                    components.sprite.position.y = newY + 50;

                    // Update guidelines if they exist
                    if (components.leftGuideline && components.rightGuideline) {
                        // Remove existing guidelines
                        arrowsGroup.remove(components.leftGuideline);
                        arrowsGroup.remove(components.rightGuideline);

                        // Create vertical guidelines function
                        const createVerticalGuideline = (
                            zpos: number,
                            xPos: number,
                            yPos: number,
                            height: number
                        ) => {
                            const points = [];
                            points.push(new THREE.Vector3(xPos, -height, zpos));
                            points.push(new THREE.Vector3(xPos, yPos, zpos));
                            const lineGeometry =
                                new THREE.BufferGeometry().setFromPoints(
                                    points
                                );
                            const lineMaterial = new THREE.LineDashedMaterial({
                                color: 0x333333,
                                dashSize: 5,
                                gapSize: 5,
                                opacity: 0.7,
                                transparent: true,
                            });
                            const line = new THREE.Line(
                                lineGeometry,
                                lineMaterial
                            );
                            line.computeLineDistances();
                            return line;
                        };

                        // Get cabinet for this measurement
                        const cabinet = cabinetGroupList.find(
                            (cabinet) => cabinet?.userData?.id === id
                        );

                        if (cabinet) {
                            const dimensions = getGroupWidth(cabinet);
                            const positionZ = components.leftArrow.position.z;

                            // Calculate X positions
                            const xPosition = components.leftArrow.position.x;
                            const leftPos = xPosition - dimensions.width / 2;
                            const rightPos = xPosition + dimensions.width / 2;

                            // Create new guidelines
                            const newLeftGuideline = createVerticalGuideline(
                                positionZ,
                                leftPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            const newRightGuideline = createVerticalGuideline(
                                positionZ,
                                rightPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            // Add to the group
                            arrowsGroup.add(newLeftGuideline);
                            arrowsGroup.add(newRightGuideline);

                            // Update references
                            components.leftGuideline = newLeftGuideline;
                            components.rightGuideline = newRightGuideline;
                        }
                    }
                }
            }
        }
    };

    arrowsGroup.userData.updateAllPositions = () => {
        if (!cabinetGroupList || !roomWidth || !scale) return;

        // Update X positions first
        cabinetGroupList.forEach((cabinet) => {
            const cabinetId = cabinet.userData.id as string;
            const dimensions = getGroupWidth(cabinet);

            if (cabinetId) {
                // Get the measurement components associated with this cabinet
                const measurement = measurementMap.get(cabinetId);
                if (measurement) {
                    const {
                        leftArrow,
                        rightArrow,
                        sprite,
                        leftGuideline,
                        rightGuideline,
                    } = measurement;

                    // Update X positions
                    const newX =
                        -cabinet.position.x +
                        (roomWidth * scale) / 2 -
                        (dimensions.width / 2 - 42);
                    leftArrow.position.x = newX;
                    rightArrow.position.x = newX;
                    sprite.position.x = newX;

                    // Update guidelines if they exist
                    if (leftGuideline && rightGuideline) {
                        // Remove existing guidelines
                        arrowsGroup.remove(leftGuideline);
                        arrowsGroup.remove(rightGuideline);

                        // Create new guidelines
                        const positionY = leftArrow.position.y;
                        const positionZ = leftArrow.position.z;

                        // Calculate left and right positions
                        const leftPos = newX - dimensions.width / 2;
                        const rightPos = newX + dimensions.width / 2;

                        // Create vertical guidelines function
                        const createVerticalGuideline = (
                            zpos: number,
                            xPos: number,
                            yPos: number,
                            height: number
                        ) => {
                            const points = [];
                            points.push(new THREE.Vector3(xPos, -height, zpos));
                            points.push(new THREE.Vector3(xPos, yPos, zpos));
                            const lineGeometry =
                                new THREE.BufferGeometry().setFromPoints(
                                    points
                                );
                            const lineMaterial = new THREE.LineDashedMaterial({
                                color: 0x333333,
                                dashSize: 5,
                                gapSize: 5,
                                opacity: 0.7,
                                transparent: true,
                            });
                            const line = new THREE.Line(
                                lineGeometry,
                                lineMaterial
                            );
                            line.computeLineDistances();
                            return line;
                        };

                        // Create new guidelines
                        const newLeftGuideline = createVerticalGuideline(
                            positionZ,
                            leftPos,
                            positionY,
                            wallHeight / 2 - 290
                        );

                        const newRightGuideline = createVerticalGuideline(
                            positionZ,
                            rightPos,
                            positionY,
                            wallHeight / 2 - 290
                        );

                        // Add to the group
                        arrowsGroup.add(newLeftGuideline);
                        arrowsGroup.add(newRightGuideline);

                        // Update references
                        measurement.leftGuideline = newLeftGuideline;
                        measurement.rightGuideline = newRightGuideline;
                    }
                }
            }
        });

        // After updating all X positions, check for collisions and adjust Y positions
        if (cabinetGroupList) {
            // Get current measurements
            const currentMeasurements = cabinetGroupList.map(
                (cabinet, index) => {
                    const dimensions = getGroupWidth(cabinet);

                    const cabinetId =
                        (cabinet.userData.id as string) || `cabinet-${index}`;
                    // Extract start/end from cabinet position/dimensions
                    let start = 0;
                    let end = 0;

                    if (getGroupWidth) {
                        try {
                            const width = dimensions.width;
                            const centerX = cabinet.position.x;
                            start = centerX - width / 2;
                            end = centerX + width / 2;
                        } catch (error) {}
                    }

                    return {
                        start,
                        end,
                        label: `${end - start} `,
                        cabinetId,
                        width: dimensions.width,
                        currentDirection: cabinet?.userData
                            ?.currentDirection as string,
                    };
                }
            );

            // Calculate new Y positions
            const basePositionY = wallHeight + 50;
            const adjustedPositions = handleMeasurementCollisions(
                currentMeasurements,
                basePositionY
            );

            // Apply new Y positions to all measurements
            for (const [id, components] of Array.from(
                measurementMap.entries()
            )) {
                const newY = adjustedPositions.get(id);
                if (newY !== undefined) {
                    components.leftArrow.position.y = newY;
                    components.rightArrow.position.y = newY;
                    components.sprite.position.y = newY + 50;

                    // Update guidelines if they exist
                    if (components.leftGuideline && components.rightGuideline) {
                        // Remove existing guidelines
                        arrowsGroup.remove(components.leftGuideline);
                        arrowsGroup.remove(components.rightGuideline);

                        // Create vertical guidelines function
                        const createVerticalGuideline = (
                            zpos: number,
                            xPos: number,
                            yPos: number,
                            height: number
                        ) => {
                            const points = [];
                            points.push(new THREE.Vector3(xPos, -height, zpos));
                            points.push(new THREE.Vector3(xPos, yPos, zpos));
                            const lineGeometry =
                                new THREE.BufferGeometry().setFromPoints(
                                    points
                                );
                            const lineMaterial = new THREE.LineDashedMaterial({
                                color: 0x333333,
                                dashSize: 5,
                                gapSize: 5,
                                opacity: 0.7,
                                transparent: true,
                            });
                            const line = new THREE.Line(
                                lineGeometry,
                                lineMaterial
                            );
                            line.computeLineDistances();
                            return line;
                        };

                        // Get cabinet for this measurement
                        const cabinet = cabinetGroupList.find(
                            (cabinet) => cabinet?.userData?.id === id
                        );

                        if (cabinet) {
                            const dimensions = getGroupWidth(cabinet);
                            const positionZ = components.leftArrow.position.z;

                            // Calculate X positions
                            const xPosition = components.leftArrow.position.x;
                            const leftPos = xPosition - dimensions.width / 2;
                            const rightPos = xPosition + dimensions.width / 2;

                            // Create new guidelines
                            const newLeftGuideline = createVerticalGuideline(
                                positionZ,
                                leftPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            const newRightGuideline = createVerticalGuideline(
                                positionZ,
                                rightPos,
                                newY,
                                wallHeight / 2 - 290
                            );

                            // Add to the group
                            arrowsGroup.add(newLeftGuideline);
                            arrowsGroup.add(newRightGuideline);

                            // Update references
                            components.leftGuideline = newLeftGuideline;
                            components.rightGuideline = newRightGuideline;
                        }
                    }
                }
            }
        }
    };

    return arrowsGroup;
};

export const addEmptySpaceMeasurements = (
    backWall: THREE.Mesh,
    cabinetGroupList: THREE.Group<THREE.Object3DEventMap>[],
    getGroupWidth: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    },
    wallHeight: number,
    roomWidth?: number,
    roomLength?: number,
    scale?: number,
    handleArrowsAndGroup?: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[],
        arrowGroup?: THREE.Group
    ) => void,
    wallRefId?: string
) => {
    // Create a group for the empty space arrows
    const emptySpaceGroup = new THREE.Group();
    emptySpaceGroup.name = 'emptySpaceMeasurementsGroup';
    emptySpaceGroup.userData.type = 'emptySpaceMeasurements';
    emptySpaceGroup.userData.updateVisibility = (show: boolean) => {
        emptySpaceGroup.visible = show;
    };

    // Keep track of arrow objects
    const arrowObjects: (THREE.ArrowHelper | THREE.SpriteMaterial)[] = [];

    // If no cabinets and no room width, no empty spaces to measure
    if ((!cabinetGroupList || cabinetGroupList.length === 0) && !roomWidth) {
        backWall.add(emptySpaceGroup);
        return emptySpaceGroup;
    }

    // Special case: If there are no cabinets but we know the room width
    if ((!cabinetGroupList || cabinetGroupList.length === 0) && roomWidth) {
        // Measure the entire wall as empty space
        const gaps = [
            {
                start: 0,
                end: roomWidth,
                width: roomWidth,
                leftCabinetId: 'wallStart',
                rightCabinetId: 'wallEnd',
                xPosition: 0,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
            },
        ];

        createEmptySpaceMeasurements(
            gaps,
            wallHeight,
            roomWidth,
            roomLength,
            scale,
            arrowObjects,
            emptySpaceGroup,
            wallRefId
        );

        if (handleArrowsAndGroup) {
            handleArrowsAndGroup(arrowObjects, emptySpaceGroup);
        }

        backWall.add(emptySpaceGroup);
        return emptySpaceGroup;
    }

    // Extract cabinet positions and dimensions
    const cabinetPositions = cabinetGroupList
        .map((cabinet) => {
            try {
                const dimensions = getGroupWidth(cabinet);
                // For BoxGeometry, make sure width is accurate - no scaling factor needed
                const width = dimensions.width;
                const centerX = cabinet.position.x;
                const currentDirection = cabinet?.userData
                    ?.currentDirection as string;
                // Adjust position based on direction
                let adjustedCenterX = centerX;
                const adjustedLeft = centerX - width / 2;
                let adjustedRight = centerX + width / 2;
                const zPosition = cabinet.position.z; // Get z-position for filtering

                // Apply adjustment for left and back directions
                if (currentDirection === 'left') {
                    adjustedCenterX = centerX - width;
                    adjustedRight -= width;
                }
                // Apply adjustment for left and back directions
                if (currentDirection === 'back') {
                    adjustedCenterX = centerX - width;
                    adjustedRight -= width;
                }

                return {
                    id: (cabinet.userData.id as string) || undefined,
                    left: adjustedLeft,
                    right: adjustedRight,
                    width: width,
                    xPosition: -adjustedCenterX,
                    dimensions,
                    cabinet,
                    direction: currentDirection,
                    zPosition,
                };
            } catch (error) {
                return null;
            }
        })
        .filter(Boolean); // Remove any nulls

    // Sort cabinets by left position
    cabinetPositions.sort((a, b) => a.left - b.left);

    const gaps = [];

    // Special case: Only one cabinet
    if (cabinetPositions.length === 1 && roomWidth) {
        gaps.push({
            start: 0,
            end: cabinetPositions[0].left,
            width: cabinetPositions[0].left,
            leftCabinetId: 'wallStart',
            rightCabinetId: cabinetPositions[0].id,
            xPosition: cabinetPositions[0].xPosition,
            dimensions: cabinetPositions[0].dimensions,
            currentCabinetWidth: 0,
            nextCabinetWidth: 0,
            cabinet: cabinetPositions[0].cabinet,
        });

        // Last cabinet to right wall
        const lastCabinet = cabinetPositions[cabinetPositions.length - 1];
        if (lastCabinet.right < roomWidth) {
            gaps.push({
                start: cabinetPositions[cabinetPositions.length - 1].right,
                end: cabinetPositions[cabinetPositions.length - 1].left,
                width: roomWidth * scale,
                leftCabinetId: lastCabinet.id,
                rightCabinetId: 'wallEnd',
                xPosition:
                    cabinetPositions[cabinetPositions.length - 1].xPosition,
                dimensions:
                    cabinetPositions[cabinetPositions.length - 1].dimensions,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
                cabinet: cabinetPositions[cabinetPositions.length - 1].cabinet,
            });
        }
    } else if (cabinetPositions.length > 0 && roomWidth) {
        // Multiple cabinets or we need room width

        // First cabinet to left wall
        gaps.push({
            start: 0,
            end: cabinetPositions[0].left,
            width: cabinetPositions[0].left,
            leftCabinetId: 'wallStart',
            rightCabinetId: cabinetPositions[0].id,
            xPosition: cabinetPositions[0].xPosition,
            dimensions: cabinetPositions[0].dimensions,
            currentCabinetWidth: 0,
            nextCabinetWidth: 0,
            cabinet: cabinetPositions[0].cabinet,
        });

        // Last cabinet to right wall
        const lastCabinet = cabinetPositions[cabinetPositions.length - 1];
        if (lastCabinet.right < roomWidth) {
            let lastCabinetRight = lastCabinet.right;
            if (
                cabinetPositions[cabinetPositions.length - 1].direction ===
                    'left' ||
                cabinetPositions[cabinetPositions.length - 1].direction ===
                    'back'
            ) {
                // Minus the width of the next cabinet from both start and end points
                lastCabinetRight += lastCabinet.width;
            }
            gaps.push({
                start: lastCabinetRight,
                end: cabinetPositions[cabinetPositions.length - 1].left,
                width: roomWidth * scale,
                leftCabinetId: lastCabinet.id,
                rightCabinetId: 'wallEnd',
                xPosition:
                    cabinetPositions[cabinetPositions.length - 1].xPosition,
                dimensions:
                    cabinetPositions[cabinetPositions.length - 1].dimensions,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
                cabinet: cabinetPositions[cabinetPositions.length - 1].cabinet,
            });
        }

        // Calculate gaps between cabinets
        cabinetPositions.slice(0, -1).forEach((currentCabinet, index) => {
            const nextCabinet = cabinetPositions[index + 1];
            if (!currentCabinet || !nextCabinet) return; // Skip if either cabinet is undefined
            // Get directions as a combined key for faster lookup
            const currentDirection =
                (currentCabinet.cabinet?.userData
                    ?.currentDirection as string) || 'front';
            const nextDirection =
                (nextCabinet.cabinet?.userData?.currentDirection as string) ||
                'front';
            const directionPair = `${currentDirection}-${nextDirection}`;
            // Initial measurements
            let adjustedStart = currentCabinet.right;
            let adjustedEnd = nextCabinet.left;
            let xPositionAdjust = 0;
            let gapWidth = nextCabinet.left - currentCabinet.right;

            // Apply direction-based adjustments in one go
            switch (directionPair) {
                case 'left-front':
                    adjustedStart += currentCabinet.width;
                    xPositionAdjust += 10;
                    // xPositionAdjust -= 25;
                    // xPositionAdjust -= 20;
                    break;
                case 'left-back':
                    gapWidth -= nextCabinet.width;
                    xPositionAdjust += 10;
                    break;
                case 'left-left':
                    gapWidth -= currentCabinet.width;
                    break;
                case 'back-front':
                    adjustedStart += currentCabinet.width;
                    gapWidth -= nextCabinet.width;
                    break;
                case 'front-left':
                    adjustedEnd -= nextCabinet.width;
                    gapWidth -= nextCabinet.width;
                    // xPositionAdjust += 20;
                    break;
                case 'front-back':
                    adjustedEnd -= nextCabinet.width;
                    gapWidth -= nextCabinet.width;

                    break;
                case 'back-back':
                    gapWidth -= nextCabinet.width;
                    break;
                case 'back-left':
                    gapWidth -= nextCabinet.width;
                    xPositionAdjust += 10;
                    break;

                case 'right-left':
                    gapWidth -= nextCabinet.width;
                    xPositionAdjust += 10;
                    break;
                //
                case 'right-back':
                    gapWidth -= nextCabinet.width;
                    xPositionAdjust += 10;
                    break;
                // Add other combinations as needed
            }

            // Apply the position adjustment
            currentCabinet.xPosition += xPositionAdjust;
            // Calculate the gap width based on adjusted positions
            // Size adjustments logic
            const leftCabinetWidth = currentCabinet.width || 0;
            const rightCabinetWidth = nextCabinet.width || 0;
            const roundedLeftWidth = Math.floor(leftCabinetWidth);
            const roundedRightWidth = Math.floor(rightCabinetWidth);
            const isSameSize = roundedLeftWidth === roundedRightWidth;

            if (!isSameSize) {
                const sizeDifference =
                    Math.abs(rightCabinetWidth - leftCabinetWidth) / 2;
                const adjustmentFactor = sizeDifference * 0.45;
                const rightCabinetIsBigger =
                    roundedRightWidth > roundedLeftWidth;

                if (currentDirection == 'front' && nextDirection == 'front') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 20;
                        gapWidth += Math.min(adjustmentFactor * 2, 45) + 5;
                    } else {
                        currentCabinet.xPosition -= adjustmentFactor - 20;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 5;
                        adjustedStart -= adjustmentFactor - 25;
                        adjustedEnd -= adjustmentFactor - 25;
                    }
                }

                if (currentDirection == 'back' && nextDirection == 'back') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                    } else {
                        currentCabinet.xPosition -= adjustmentFactor - 15;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 20;
                        adjustedStart -= adjustmentFactor - 25;
                        adjustedEnd -= adjustmentFactor - 25;
                    }
                }

                if (currentDirection === 'front' && nextDirection === 'back') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                        adjustedEnd -= adjustmentFactor - 25;
                        adjustedStart -= adjustmentFactor - 25;
                    } else {
                        currentCabinet.xPosition -= adjustmentFactor - 15;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 10;
                        adjustedStart -= adjustmentFactor - 25;
                        adjustedEnd -= adjustmentFactor - 25;
                    }
                }

                if (currentDirection === 'back' && nextDirection === 'front') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                        adjustedEnd -= adjustmentFactor - 25;
                        adjustedStart -= adjustmentFactor - 25;
                    } else {
                        currentCabinet.xPosition += adjustmentFactor - 20;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 10;
                        adjustedStart -= adjustmentFactor - 40;
                        adjustedEnd -= adjustmentFactor - 40;
                    }
                }

                if (currentDirection === 'left' && nextDirection === 'front') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                        adjustedEnd -= adjustmentFactor - 25;
                        adjustedStart -= adjustmentFactor - 25;
                    } else {
                        currentCabinet.xPosition += adjustmentFactor;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45);
                        adjustedStart -= adjustmentFactor - 25;
                    }
                }
            }

            // Only include significant gaps
            if (gapWidth > 2) {
                gaps.push({
                    start: adjustedStart,
                    end: adjustedEnd,
                    width: gapWidth,
                    leftCabinetId: currentCabinet.id,
                    rightCabinetId: nextCabinet.id,
                    xPosition: currentCabinet.xPosition,
                    dimensions: currentCabinet.dimensions,
                    cabinetWidth: currentCabinet.width,
                    currentCabinetWidth: currentCabinet.width,
                    nextCabinetWidth: nextCabinet.width,
                    cabinet: currentCabinet.cabinet,
                    nextCabinet: nextCabinet.cabinet,
                    nextCabinetDimension: nextCabinet.dimensions,
                    currentDirection,
                    nextDirection,
                });
            }
        });
    }

    // Create measurements for each gap
    createEmptySpaceMeasurements(
        gaps,
        wallHeight,
        roomWidth,
        roomLength,
        scale,
        arrowObjects,
        emptySpaceGroup,
        wallRefId
    );

    // Add the handler function if provided
    if (handleArrowsAndGroup) {
        handleArrowsAndGroup(arrowObjects, emptySpaceGroup);
    }

    // Add update method
    emptySpaceGroup.userData.updateEmptySpaces = () => {
        // Remove all existing measurements
        while (emptySpaceGroup.children.length > 0) {
            emptySpaceGroup.remove(emptySpaceGroup.children[0]);
        }

        // Clear the arrowObjects array
        arrowObjects.length = 0;

        // Recreate empty space measurements with updated cabinet positions
        const updatedGroup = addEmptySpaceMeasurements(
            backWall,
            cabinetGroupList,
            getGroupWidth,
            wallHeight,
            roomWidth,
            roomLength,
            scale,
            handleArrowsAndGroup
        );

        // Find cabinet measurements group to check for collisions
        const cabinetMeasurements = backWall.children.find(
            (child) => child.name === 'multipleMeasurementsGroup'
        ) as THREE.Group;

        // Handle collisions between cabinet and empty space labels
        if (cabinetMeasurements) {
            handleCabinetEmptySpaceCollisions(
                cabinetMeasurements,
                updatedGroup
            );
        }

        return updatedGroup;
    };

    // Add to back wall
    backWall.add(emptySpaceGroup);

    // Find cabinet measurements group to check for collisions
    const cabinetMeasurements = backWall.children.find(
        (child) => child.name === 'multipleMeasurementsGroup'
    ) as THREE.Group;

    // Handle collisions between cabinet and empty space labels
    if (cabinetMeasurements) {
        handleCabinetEmptySpaceCollisions(cabinetMeasurements, emptySpaceGroup);
    }

    return emptySpaceGroup;
};

export const addEmptySpaceMeasurementsLeftRight = (
    backWall: THREE.Mesh,
    cabinetGroupList: THREE.Group<THREE.Object3DEventMap>[],
    getGroupWidth: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    },
    wallHeight: number,
    roomWidth?: number,
    roomLength?: number,
    scale?: number,
    handleArrowsAndGroup?: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[],
        arrowGroup?: THREE.Group
    ) => void,
    wallRefId?: string
) => {
    // Create a group for the empty space arrows
    const emptySpaceGroup = new THREE.Group();
    emptySpaceGroup.name = 'emptySpaceMeasurementsGroup';
    emptySpaceGroup.userData.type = 'emptySpaceMeasurements';
    emptySpaceGroup.userData.updateVisibility = (show: boolean) => {
        emptySpaceGroup.visible = show;
    };

    // Keep track of arrow objects
    const arrowObjects: (THREE.ArrowHelper | THREE.SpriteMaterial)[] = [];

    // If no cabinets and no room width, no empty spaces to measure
    if ((!cabinetGroupList || cabinetGroupList.length === 0) && !roomWidth) {
        backWall.add(emptySpaceGroup);
        return emptySpaceGroup;
    }

    // Special case: If there are no cabinets but we know the room width
    if ((!cabinetGroupList || cabinetGroupList.length === 0) && roomWidth) {
        // Measure the entire wall as empty space
        const gaps = [
            {
                start: 0,
                end: roomWidth,
                width: roomWidth,
                leftCabinetId: 'wallStart',
                rightCabinetId: 'wallEnd',
                xPosition: 0,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
            },
        ];

        createEmptySpaceMeasurementsLeftRight(
            gaps,
            wallHeight,
            roomWidth,
            roomLength,
            scale,
            arrowObjects,
            emptySpaceGroup,
            wallRefId
        );

        if (handleArrowsAndGroup) {
            handleArrowsAndGroup(arrowObjects, emptySpaceGroup);
        }

        backWall.add(emptySpaceGroup);
        return emptySpaceGroup;
    }

    // Extract cabinet positions and dimensions
    const cabinetPositions = cabinetGroupList
        .map((cabinet) => {
            try {
                const dimensions = getGroupWidth(cabinet);
                // For BoxGeometry, make sure width is accurate - no scaling factor needed
                const width = dimensions.depth;
                const centerX = cabinet.position.z;
                const currentDirection = cabinet?.userData
                    ?.currentDirection as string;
                // Adjust position based on direction
                let adjustedCenterX = centerX;
                const adjustedLeft = centerX - width / 2;
                const adjustedRight = centerX + width / 2;
                const zPosition = cabinet.position.z; // Get z-position for filtering

                // Apply adjustment for left and back directions
                if (currentDirection === 'left') {
                    adjustedCenterX = centerX - width / 2 - 35;
                    // adjustedRight -= width;
                }
                // Apply adjustment for left and back directions
                if (currentDirection === 'back') {
                    adjustedCenterX = centerX + width * 0.75 - 34;
                    // adjustedRight -= width;
                }
                if (currentDirection === 'front') {
                    adjustedCenterX = centerX - width / 2;
                }
                if (currentDirection === 'right') {
                    adjustedCenterX = centerX + width / 2 - 35;
                }

                return {
                    id: (cabinet.userData.id as string) || undefined,
                    left: adjustedLeft,
                    right: adjustedRight,
                    width: width,
                    xPosition: -adjustedCenterX,
                    dimensions,
                    cabinet,
                    direction: currentDirection,
                    zPosition,
                };
            } catch (error) {
                return null;
            }
        })
        .filter(Boolean); // Remove any nulls

    // Sort cabinets by left position
    cabinetPositions.sort((a, b) => a.left - b.left);

    const gaps = [];

    // Special case: Only one cabinet
    if (cabinetPositions.length === 1 && roomLength) {
        gaps.push({
            start: 0,
            end: cabinetPositions[0].left,
            width: cabinetPositions[0].left,
            leftCabinetId: 'wallStart',
            rightCabinetId: cabinetPositions[0].id,
            xPosition: cabinetPositions[0].xPosition,
            dimensions: cabinetPositions[0].dimensions,
            currentCabinetWidth: 0,
            nextCabinetWidth: 0,
            cabinet: cabinetPositions[0].cabinet,
        });

        // Last cabinet to right wall
        const lastCabinet = cabinetPositions[cabinetPositions.length - 1];
        if (lastCabinet.right < roomLength) {
            gaps.push({
                start: cabinetPositions[cabinetPositions.length - 1].right,
                end: cabinetPositions[cabinetPositions.length - 1].left,
                width: roomLength * scale,
                leftCabinetId: lastCabinet.id,
                rightCabinetId: 'wallEnd',
                xPosition:
                    cabinetPositions[cabinetPositions.length - 1].xPosition,
                dimensions:
                    cabinetPositions[cabinetPositions.length - 1].dimensions,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
                cabinet: cabinetPositions[cabinetPositions.length - 1].cabinet,
            });
        }
    } else if (cabinetPositions.length > 0 && roomWidth) {
        // Multiple cabinets or we need room width
        // First cabinet to left wall
        gaps.push({
            start: 0,
            end: cabinetPositions[0].left,
            width: cabinetPositions[0].left,
            leftCabinetId: 'wallStart',
            rightCabinetId: cabinetPositions[0].id,
            xPosition: cabinetPositions[0].xPosition,
            dimensions: cabinetPositions[0].dimensions,
            currentCabinetWidth: 0,
            nextCabinetWidth: 0,
            cabinet: cabinetPositions[0].cabinet,
        });

        // Last cabinet to right wall
        const lastCabinet = cabinetPositions[cabinetPositions.length - 1];
        if (lastCabinet.right < roomLength) {
            let lastCabinetRight = lastCabinet.right;
            // if (
            //     cabinetPositions[cabinetPositions.length - 1].direction ===
            //         'left' ||
            //     cabinetPositions[cabinetPositions.length - 1].direction ===
            //         'back'
            // ) {
            //     // Minus the width of the next cabinet from both start and end points
            //     lastCabinetRight += lastCabinet.width;
            // }

            lastCabinetRight += lastCabinet.width;

            gaps.push({
                start: lastCabinetRight,
                end: cabinetPositions[cabinetPositions.length - 1].left,
                width: roomLength * scale,
                leftCabinetId: lastCabinet.id,
                rightCabinetId: 'wallEnd',
                xPosition:
                    cabinetPositions[cabinetPositions.length - 1].xPosition,
                dimensions:
                    cabinetPositions[cabinetPositions.length - 1].dimensions,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
                cabinet: cabinetPositions[cabinetPositions.length - 1].cabinet,
            });
        }

        // Calculate gaps between cabinets
        cabinetPositions.slice(0, -1).forEach((currentCabinet, index) => {
            const nextCabinet = cabinetPositions[index + 1];
            if (!currentCabinet || !nextCabinet) return; // Skip if either cabinet is undefined
            // Get directions as a combined key for faster lookup
            const currentDirection =
                (currentCabinet.cabinet?.userData
                    ?.currentDirection as string) || 'front';
            const nextDirection =
                (nextCabinet.cabinet?.userData?.currentDirection as string) ||
                'front';
            const directionPair = `${currentDirection}-${nextDirection}`;
            // Initial measurements
            let adjustedStart = currentCabinet.right;
            let adjustedEnd = nextCabinet.left;
            let xPositionAdjust = 0;
            let gapWidth = nextCabinet.left - currentCabinet.right;

            // Apply direction-based adjustments in one go
            switch (directionPair) {
                case 'left-front':
                    adjustedEnd += currentCabinet.width;
                    gapWidth += currentCabinet.width / 2 - 20;

                    // gapWidth += currentCabinet.width;
                    // xPositionAdjust -= 25;
                    // xPositionAdjust -= 20;
                    break;
                case 'left-back':
                    gapWidth += nextCabinet.width + 45;
                    xPositionAdjust += 10;
                    break;
                case 'left-left':
                    adjustedStart += currentCabinet.width;
                    break;
                case 'left-right':
                    adjustedStart += currentCabinet.width;
                    // adjustedEnd -= nextCabinet.width;
                    gapWidth += currentCabinet.width;

                    break;
                case 'back-front':
                    adjustedStart += currentCabinet.width;
                    gapWidth -= nextCabinet.width;
                    break;
                case 'front-left':
                    adjustedEnd -= nextCabinet.width;
                    gapWidth -= nextCabinet.width / 2 - 45;
                    // xPositionAdjust += 20;
                    break;
                case 'front-right':
                    // adjustedStart += currentCabinet.width + 100;
                    // adjustedEnd += currentCabinet.width + 100;
                    // gapWidth += currentCabinet.width / 2 - 45;
                    // adjustedStart += currentCabinet.width;
                    // xPositionAdjust += 10;
                    gapWidth += nextCabinet.width - 45;

                    break;
                case 'front-back':
                    // adjustedEnd -= nextCabinet.width;
                    gapWidth += nextCabinet.width;

                    break;
                case 'back-back':
                    gapWidth -= currentCabinet.width / 2 - 55;
                    break;
                case 'back-left':
                    gapWidth -= nextCabinet.width - 30;
                    // xPositionAdjust += 10;
                    break;
                case 'back-right':
                    gapWidth += 40;
                    // xPositionAdjust += 10;
                    break;

                case 'right-left':
                    gapWidth -= nextCabinet.width;
                    xPositionAdjust += 10;
                    break;
                //
                case 'right-back':
                    gapWidth -= 40;

                    // gapWidth -= nextCabinet.width;
                    // xPositionAdjust += 10;
                    break;

                case 'right-front':
                    gapWidth -= nextCabinet.width + 35;
                    break;
                // Add other combinations as needed
            }

            // Apply the position adjustment
            currentCabinet.xPosition += xPositionAdjust;
            // Calculate the gap width based on adjusted positions
            // Size adjustments logic
            const leftCabinetWidth = currentCabinet.width || 0;
            const rightCabinetWidth = nextCabinet.width || 0;
            const roundedLeftWidth = Math.round(leftCabinetWidth * 10) / 10;
            const roundedRightWidth = Math.round(rightCabinetWidth * 10) / 10;
            const isSameSize = roundedLeftWidth === roundedRightWidth;

            if (!isSameSize) {
                const sizeDifference =
                    Math.abs(rightCabinetWidth - leftCabinetWidth) / 2;
                const adjustmentFactor = sizeDifference * 0.45;
                const rightCabinetIsBigger =
                    rightCabinetWidth > leftCabinetWidth;

                if (currentDirection == 'front' && nextDirection == 'front') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                    } else {
                        currentCabinet.xPosition -= adjustmentFactor - 15;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 20;
                        adjustedStart -= adjustmentFactor - 25;
                        adjustedEnd -= adjustmentFactor - 25;
                    }
                }

                if (currentDirection == 'back' && nextDirection == 'back') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                    } else {
                        currentCabinet.xPosition -= adjustmentFactor - 15;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 20;
                        adjustedStart -= adjustmentFactor - 25;
                        adjustedEnd -= adjustmentFactor - 25;
                    }
                }

                if (currentDirection === 'front' && nextDirection === 'back') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                        adjustedEnd -= adjustmentFactor - 25;
                        adjustedStart -= adjustmentFactor - 25;
                    } else {
                        currentCabinet.xPosition -= adjustmentFactor - 15;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 10;
                        adjustedStart -= adjustmentFactor - 25;
                        adjustedEnd -= adjustmentFactor - 25;
                    }
                }

                if (currentDirection === 'back' && nextDirection === 'front') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                        adjustedEnd -= adjustmentFactor - 25;
                        adjustedStart -= adjustmentFactor - 25;
                    } else {
                        currentCabinet.xPosition += adjustmentFactor - 20;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45) + 10;
                        adjustedStart -= adjustmentFactor - 40;
                        adjustedEnd -= adjustmentFactor - 40;
                    }
                }

                if (currentDirection === 'left' && nextDirection === 'front') {
                    if (rightCabinetIsBigger) {
                        currentCabinet.xPosition += adjustmentFactor - 25;
                        gapWidth += Math.min(adjustmentFactor * 2, 45);
                        adjustedEnd -= adjustmentFactor - 25;
                        adjustedStart -= adjustmentFactor - 25;
                    } else {
                        currentCabinet.xPosition += adjustmentFactor;
                        gapWidth -= Math.min(adjustmentFactor * 2, 45);
                        adjustedStart -= adjustmentFactor - 25;
                    }
                }
            }

            // Only include significant gaps
            if (gapWidth > 2) {
                gaps.push({
                    start: adjustedStart,
                    end: adjustedEnd,
                    width: gapWidth,
                    leftCabinetId: currentCabinet.id,
                    rightCabinetId: nextCabinet.id,
                    xPosition: currentCabinet.xPosition,
                    dimensions: currentCabinet.dimensions,
                    cabinetWidth: currentCabinet.width,
                    currentCabinetWidth: currentCabinet.width,
                    nextCabinetWidth: nextCabinet.width,
                    cabinet: currentCabinet.cabinet,
                    nextCabinet: nextCabinet.cabinet,
                    nextCabinetDimension: nextCabinet.dimensions,
                    currentDirection,
                    nextDirection,
                });
            }
        });
    }

    // Create measurements for each gap
    createEmptySpaceMeasurementsLeftRight(
        gaps,
        wallHeight,
        roomWidth,
        roomLength,
        scale,
        arrowObjects,
        emptySpaceGroup,
        wallRefId
    );

    // Add the handler function if provided
    if (handleArrowsAndGroup) {
        handleArrowsAndGroup(arrowObjects, emptySpaceGroup);
    }

    // Add update method
    emptySpaceGroup.userData.updateEmptySpaces = () => {
        // Remove all existing measurements
        while (emptySpaceGroup.children.length > 0) {
            emptySpaceGroup.remove(emptySpaceGroup.children[0]);
        }

        // Clear the arrowObjects array
        arrowObjects.length = 0;

        // Recreate empty space measurements with updated cabinet positions
        const updatedGroup = addEmptySpaceMeasurements(
            backWall,
            cabinetGroupList,
            getGroupWidth,
            wallHeight,
            roomWidth,
            roomLength,
            scale,
            handleArrowsAndGroup
        );

        // Find cabinet measurements group to check for collisions
        const cabinetMeasurements = backWall.children.find(
            (child) => child.name === 'multipleMeasurementsGroup'
        ) as THREE.Group;

        // Handle collisions between cabinet and empty space labels
        if (cabinetMeasurements) {
            handleCabinetEmptySpaceCollisions(
                cabinetMeasurements,
                updatedGroup
            );
        }

        return updatedGroup;
    };

    // Add to back wall
    backWall.add(emptySpaceGroup);

    // Find cabinet measurements group to check for collisions
    const cabinetMeasurements = backWall.children.find(
        (child) => child.name === 'multipleMeasurementsGroup'
    ) as THREE.Group;

    // Handle collisions between cabinet and empty space labels
    if (cabinetMeasurements) {
        handleCabinetEmptySpaceCollisions(cabinetMeasurements, emptySpaceGroup);
    }

    return emptySpaceGroup;
};

const createEmptySpaceMeasurementsLeftRight = (
    gaps: {
        start: number;
        end: number;
        width: number;
        leftCabinetId: string | undefined;
        rightCabinetId: string | undefined;
        xPosition: number;
        cabinetWidth?: number;
        dimensions?: {
            width: number;
            height: number;
            depth: number;
        };
        currentCabinetWidth?: number;
        nextCabinetWidth?: number;
        cabinet?: THREE.Group<THREE.Object3DEventMap>;
        nextCabinet?: THREE.Group<THREE.Object3DEventMap>;
        nextCabinetDimension?: {
            width: number;
            height: number;
            depth: number;
        };
    }[],
    wallHeight: number,
    roomWidth?: number,
    roomLength?: number,
    scale?: number,
    arrowObjects: (THREE.ArrowHelper | THREE.SpriteMaterial)[] = [],
    emptySpaceGroup: THREE.Group = new THREE.Group(),
    wallRefId?: string
) => {
    const arrowColor = '#333';
    const basePositionY = wallHeight + 10;
    const arrowHeadLength = 8;
    const arrowHeadWidth = 8;

    const filteredEdgeGaps = gaps;
    // If all gaps were filtered out, return early
    if (filteredEdgeGaps.length === 0) {
        return emptySpaceGroup;
    }
    // Step 3: Create measurements for edge gaps
    filteredEdgeGaps.forEach((gap, index) => {
        // Calculate positions based on gap edges - keep original computation
        let actualArrowLength;
        const offset = Math.abs(gap.start - gap.end) - 40;
        // Calculation for positining when Rotate Right

        // Handle wall start case separately
        if (gap.leftCabinetId === 'wallStart') {
            actualArrowLength = (roomWidth * scale) / 2 - (gap.xPosition + 40);
        }
        if (gap.rightCabinetId === 'wallEnd') {
            actualArrowLength =
                gap.width / 2 + (gap.xPosition - offset / 2) + 30;
        } else {
            // Apply rotation adjustment to the arrow length calculation
        }

        // Create label with matching style to createMeasurements
        const canvas = document.createElement('canvas');
        canvas.width = 156;
        canvas.height = 64;

        const context = canvas.getContext('2d');
        if (!context) return;

        // Draw text
        context.fillStyle = 'black';
        context.font = 'bold 27px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';

        // Display the gap width in mm - no extra multiplier
        const displayWidth = Math.round(actualArrowLength);

        // Convert to original units using scale and ensure we're not displaying zero
        const originalWidth =
            displayWidth !== null && scale
                ? displayWidth / scale
                : displayWidth;

        // Only create a label if we have a non-zero, non-null value
        let displayLabel = '';
        if (originalWidth !== null && originalWidth > 0) {
            displayLabel = `${Math.round(originalWidth)} mm`;
        }

        context.fillText(displayLabel, canvas.width / 2, canvas.height / 2);

        const texture = new THREE.CanvasTexture(canvas);
        const textMaterial = new THREE.SpriteMaterial({map: texture});
        const textSprite = new THREE.Sprite(textMaterial);

        // Position sprite at center, but adjust for wide gaps
        textSprite.position.set(actualArrowLength / 2, basePositionY + 50, 25);

        // Scale consistently with other dimension labels
        textSprite.scale.set(100, 50, 1);

        // Minimum arrow length threshold - don't show arrows smaller than this
        const minArrowLength = 10;

        // Only create and add arrows if the length is sufficient

        // Width direction vectors
        const leftToRightDir = new THREE.Vector3(1, 0, 0).normalize();
        const rightToLeftDir = new THREE.Vector3(-1, 0, 0).normalize();
        const positionZ =
            wallRefId == 'frontwall' || wallRefId == 'leftwall' ? 30 : 0; // Use proper Z offset

        if (gap.leftCabinetId === 'wallStart') {
            if (Math.abs(actualArrowLength) > minArrowLength) {
                const spritePosition = roomWidth * scale;
                textSprite.position.set(
                    spritePosition / 2 +
                        spritePosition / 2 -
                        Math.abs(actualArrowLength) / 2,
                    basePositionY + 50,
                    25
                );

                emptySpaceGroup.add(textSprite);
                arrowObjects.push(textMaterial);

                // Create left arrow (pointing right/inward from wall start)
                const wallStartLeftArrow = new THREE.ArrowHelper(
                    leftToRightDir,
                    new THREE.Vector3(
                        roomWidth * scale,
                        basePositionY,
                        positionZ
                    ),
                    45, // Use the full gap width for wall start
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                wallStartLeftArrow.userData.gapId = index;
                emptySpaceGroup.add(wallStartLeftArrow);
                arrowObjects.push(wallStartLeftArrow);

                // // Create right arrow (pointing left/inward)
                const wallStartRightArrow = new THREE.ArrowHelper(
                    rightToLeftDir,
                    new THREE.Vector3(
                        roomWidth * scale,
                        basePositionY,
                        positionZ
                    ),
                    Math.abs(actualArrowLength),
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                wallStartRightArrow.userData.gapId = index;
                emptySpaceGroup.add(wallStartRightArrow);
                arrowObjects.push(wallStartRightArrow);
            }
        }
        if (gap.rightCabinetId === 'wallEnd') {
            emptySpaceGroup.add(textSprite);
            arrowObjects.push(textMaterial);
            // Create left arrow (pointing right/inward)
            if (Math.abs(actualArrowLength) > minArrowLength) {
                const leftArrow = new THREE.ArrowHelper(
                    leftToRightDir,
                    new THREE.Vector3(0, basePositionY, positionZ),
                    Math.abs(actualArrowLength), // Use absolute value for arrow length
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Store gap info in arrow userData
                leftArrow.userData.gapId = index;

                // Add arrow to group
                emptySpaceGroup.add(leftArrow);

                // Add to arrow objects array
                arrowObjects.push(leftArrow);

                // Create right arrow (pointing left/inward)
                const rightArrow = new THREE.ArrowHelper(
                    rightToLeftDir,
                    new THREE.Vector3(
                        actualArrowLength - 25,
                        basePositionY,
                        positionZ
                    ),
                    Math.abs(actualArrowLength),
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Add arrows to group
                emptySpaceGroup.add(leftArrow);
                emptySpaceGroup.add(rightArrow);

                // Add to arrow objects array
                arrowObjects.push(leftArrow);
                arrowObjects.push(rightArrow);
            }
        } else {
            const gapWidth = gap.width;

            const xPosition =
                gap.xPosition +
                (roomLength * scale) / 2 -
                (gap.width / 2 - 42) -
                gap.cabinetWidth;

            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            if (!context) return;

            // Fill background
            canvas.width = 156;
            canvas.height = 64;

            context.fillStyle = 'black';
            context.font = 'bold 27px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';

            let displayLabel = '';
            if (gapWidth !== null && gapWidth > 0) {
                displayLabel = `${Math.round(gapWidth / scale)} mm`;
            }

            context.fillText(displayLabel, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({map: texture});
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(100, 50, 1); // Size of the label

            // Position the label using the collision-adjusted Y position
            sprite.position.x = xPosition;
            sprite.position.y = basePositionY + 50;
            sprite.position.z = 25; // Ensure it's in front of arrows

            // Add label to group
            emptySpaceGroup.add(sprite);
            arrowObjects.push(spriteMaterial);

            // Calculate actual positions for left and right arrows

            const leftToRightDir = new THREE.Vector3(1, 0, 0).normalize();
            const rightToLeftDir = new THREE.Vector3(-1, 0, 0).normalize();

            // Create left arrow (pointing right/inward)
            const leftArrow = new THREE.ArrowHelper(
                leftToRightDir,
                new THREE.Vector3(xPosition, basePositionY, positionZ),
                gapWidth / 2, // Match style from image
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );

            emptySpaceGroup.add(leftArrow);
            arrowObjects.push(leftArrow);
            leftArrow.userData.gapId = index;

            // Store gap info in arrow userData
            // Create right arrow (pointing left/inward)
            const rightArrow = new THREE.ArrowHelper(
                rightToLeftDir,
                new THREE.Vector3(xPosition, basePositionY, positionZ),
                gapWidth / 2, // Match style from image
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );

            rightArrow.userData.gapId = index;

            emptySpaceGroup.add(rightArrow);
            arrowObjects.push(rightArrow);
        }
    });

    return emptySpaceGroup;
};

export const updateAllMeasurements = (
    wallRefs: React.MutableRefObject<THREE.Mesh[]>
) => {
    // Find the back wall
    const backWall = wallRefs.current.find(
        (wall) => wall.userData.id === 'backWall'
    );

    if (!backWall) {
        return;
    }

    // Find the cabinet measurements group
    const cabinetMeasurements = backWall.children.find(
        (child) => child.name === 'multipleMeasurementsGroup'
    ) as THREE.Group;

    // Find the empty space measurements group
    const emptySpaceMeasurements = backWall.children.find(
        (child) => child.name === 'emptySpaceMeasurementsGroup'
    ) as THREE.Group;

    // Update cabinet measurements if they exist
    if (
        cabinetMeasurements &&
        cabinetMeasurements.userData &&
        typeof cabinetMeasurements.userData.updateAllPositions === 'function'
    ) {
        (
            cabinetMeasurements.userData as {updateAllPositions: () => void}
        ).updateAllPositions();
    }

    // Update empty space measurements if they exist
    if (
        emptySpaceMeasurements &&
        emptySpaceMeasurements.userData &&
        typeof emptySpaceMeasurements.userData.updateEmptySpaces === 'function'
    ) {
        (
            emptySpaceMeasurements.userData as {updateEmptySpaces: () => void}
        ).updateEmptySpaces();
    }

    // After updating both measurement groups, handle collisions between them
    if (cabinetMeasurements && emptySpaceMeasurements) {
        handleCabinetEmptySpaceCollisions(
            cabinetMeasurements,
            emptySpaceMeasurements
        );
    }
};

export const setupMeasurements = (
    wallRefs: React.MutableRefObject<THREE.Mesh[]>,
    cabinetGroupList: THREE.Group<THREE.Object3DEventMap>[] | undefined,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial)[],
        arrowGroup?: THREE.Group
    ) => void,
    getGroupWidth: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    },
    roomType: string,
    rectangularDimension: {
        length: number;
        width: number;
        height: number;
    },
    lShapedDimension: {
        leftWidth: number;
        rightWidth: number;
        height: number;
    },
    scale: number,
    showArrows: boolean
) => {
    // Continue even without cabinets to show wall measurements

    // Find the back wall and front wall
    const backWall = wallRefs.current.find(
        (wall) => wall.userData.id === 'backWall'
    );

    const frontWall = wallRefs.current.find(
        (wall) => wall.userData.id === 'frontWall'
    );

    const leftWall = wallRefs.current.find(
        (wall) => wall.userData.id === 'leftWall'
    );

    if (!backWall) {
        return null;
    }

    // Get the scene
    const scene = backWall.parent;

    if (!scene) {
        return null;
    }

    // Calculate room dimensions
    const roomLength =
        roomType === 'RECTANGULAR'
            ? rectangularDimension.length
            : lShapedDimension.leftWidth;
    const roomWidth =
        roomType === 'RECTANGULAR'
            ? rectangularDimension.width
            : lShapedDimension.rightWidth;
    const roomHeight =
        roomType === 'RECTANGULAR'
            ? rectangularDimension.height * scale + 20
            : lShapedDimension.height * scale + 20;

    // Clean up any existing measurement groups
    const removeExistingMeasurements = (
        parent: THREE.Object3D<THREE.Object3DEventMap>
    ) => {
        // Get all direct children that are measurement groups
        const measurementChildren = [];
        [...parent.children].reverse().forEach((child) => {
            // Check if this is a measurement group by name or userData
            if (
                child.name === 'emptySpaceMeasurementsGroup' ||
                (child.userData &&
                    child.userData.type === 'emptySpaceMeasurements')
            ) {
                measurementChildren.push(child);
                parent.remove(child);
            }

            // Also recursively check this child's children
            if (child.children && child.children.length > 0) {
                removeExistingMeasurements(child);
            }
        });

        return measurementChildren.length;
    };

    // Clean the entire scene to be thorough
    removeExistingMeasurements(scene);

    // Also specifically clean all walls
    removeExistingMeasurements(backWall);
    if (frontWall) {
        removeExistingMeasurements(frontWall);
    }
    if (leftWall) {
        removeExistingMeasurements(leftWall);
    }

    const removeExistingMeasurementsFromWall = (wall: THREE.Mesh) => {
        // Find and remove any existing measurement groups
        const multipleMeasurementsGroup = wall.children.find(
            (child) => child.name === 'multipleMeasurementsGroup'
        );

        const emptySpaceMeasurementsGroup = wall.children.find(
            (child) => child.name === 'emptySpaceMeasurementsGroup'
        );

        if (multipleMeasurementsGroup) {
            wall.remove(multipleMeasurementsGroup);
        }

        if (emptySpaceMeasurementsGroup) {
            wall.remove(emptySpaceMeasurementsGroup);
        }
    };

    // Filter cabinets by direction
    const frontFacingCabinets = cabinetGroupList.filter(
        (cabinet) => cabinet?.userData?.currentDirection != 'back'
    );

    console.log('Re render the arrow 2', frontFacingCabinets);

    const backFacingCabinets = cabinetGroupList.filter(
        (cabinet) => cabinet?.userData?.currentDirection != 'front'
    );

    // const leftFacingCabinets = cabinetGroupList;

    // const rightFacingCabinets = cabinetGroupList.filter(
    //     (cabinet) => cabinet?.userData?.currentDirection === 'right'
    // );

    // Create measurements for back wall (only for front-facing cabinets)
    let backWallMeasurementsGroup = null;
    let backWallEmptySpaceGroup = null;

    if (frontFacingCabinets.length > 0) {
        backWallMeasurementsGroup = addMultipleMeasurementsToWall(
            backWall,
            handleArrowsAndGroup,
            roomHeight,
            frontFacingCabinets,
            getGroupWidth,
            roomWidth,
            roomLength,
            scale,
            'backwall'
        );
        if (
            backWallMeasurementsGroup &&
            typeof backWallMeasurementsGroup.userData.updateVisibility ===
                'function'
        ) {
            (
                backWallMeasurementsGroup.userData as {
                    updateVisibility: (showArrows: boolean) => void;
                }
            ).updateVisibility(showArrows);
        }

        // Now create empty space measurements group for back wall
        backWallEmptySpaceGroup = addEmptySpaceMeasurements(
            backWall,
            frontFacingCabinets,
            getGroupWidth,
            roomHeight,
            roomWidth,
            roomLength,
            scale,
            handleArrowsAndGroup,
            'backwall'
        );

        // Set visibility for back wall empty space measurements
        if (
            backWallEmptySpaceGroup &&
            typeof backWallEmptySpaceGroup.userData.updateVisibility ===
                'function'
        ) {
            (
                backWallEmptySpaceGroup.userData as {
                    updateVisibility: (showArrows: boolean) => void;
                }
            ).updateVisibility(showArrows);
        }

        // Handle collisions between cabinet and empty space measurements for back wall
        handleCabinetEmptySpaceCollisions(
            backWallMeasurementsGroup,
            backWallEmptySpaceGroup
        );
    } else {
        // When no cabinets exist but we have room dimensions, show empty wall measurements
        if (roomWidth) {
            removeExistingMeasurementsFromWall(backWall);

            // Create empty space measurements for the entire wall
            backWallEmptySpaceGroup = addEmptySpaceMeasurements(
                backWall,
                [], // Empty cabinet list
                getGroupWidth,
                roomHeight,
                roomWidth,
                roomLength,
                scale,
                handleArrowsAndGroup,
                'backwall'
            );

            // Set visibility
            if (
                backWallEmptySpaceGroup &&
                typeof backWallEmptySpaceGroup.userData.updateVisibility ===
                    'function'
            ) {
                (
                    backWallEmptySpaceGroup.userData as {
                        updateVisibility: (showArrows: boolean) => void;
                    }
                ).updateVisibility(showArrows);
            }
        } else {
            removeExistingMeasurementsFromWall(backWall);
        }
    }

    // Front wall measurements - only if front wall exists and there are back-facing cabinets
    let frontWallMeasurementsGroup = null;
    let frontWallEmptySpaceGroup = null;

    if (frontWall && backFacingCabinets.length > 0) {
        // Create a new cabinet measurements group for front wall
        frontWallMeasurementsGroup = addMultipleMeasurementsToWall(
            frontWall,
            handleArrowsAndGroup,
            roomHeight,
            backFacingCabinets,
            getGroupWidth,
            roomWidth,
            roomLength,
            scale,
            'frontwall'
        );

        // Set visibility for front wall cabinet measurements
        if (
            frontWallMeasurementsGroup &&
            typeof frontWallMeasurementsGroup.userData.updateVisibility ===
                'function'
        ) {
            (
                frontWallMeasurementsGroup.userData as {
                    updateVisibility: (showArrows: boolean) => void;
                }
            ).updateVisibility(showArrows);
        }

        // Now create empty space measurements group for front wall
        frontWallEmptySpaceGroup = addEmptySpaceMeasurements(
            frontWall,
            backFacingCabinets,
            getGroupWidth,
            roomHeight,
            roomWidth,
            roomLength,
            scale,
            handleArrowsAndGroup,
            'frontwall'
        );

        // Set visibility for front wall empty space measurements
        if (
            frontWallEmptySpaceGroup &&
            typeof frontWallEmptySpaceGroup.userData.updateVisibility ===
                'function'
        ) {
            (
                frontWallEmptySpaceGroup.userData as {
                    updateVisibility: (showArrows: boolean) => void;
                }
            ).updateVisibility(showArrows);
        }

        // Handle collisions between cabinet and empty space measurements for front wall
        handleCabinetEmptySpaceCollisions(
            frontWallMeasurementsGroup,
            frontWallEmptySpaceGroup
        );
    } else if (frontWall && roomWidth) {
        // When no cabinets exist but we have room dimensions, show empty wall measurements
        removeExistingMeasurementsFromWall(frontWall);

        // Create empty space measurements for the entire wall
        frontWallEmptySpaceGroup = addEmptySpaceMeasurements(
            frontWall,
            [], // Empty cabinet list
            getGroupWidth,
            roomHeight,
            roomWidth,
            roomLength,
            scale,
            handleArrowsAndGroup,
            'frontwall'
        );

        // Set visibility
        if (
            frontWallEmptySpaceGroup &&
            typeof frontWallEmptySpaceGroup.userData.updateVisibility ===
                'function'
        ) {
            (
                frontWallEmptySpaceGroup.userData as {
                    updateVisibility: (showArrows: boolean) => void;
                }
            ).updateVisibility(showArrows);
        }
    } else {
        if (frontWall) {
            removeExistingMeasurementsFromWall(frontWall);
        }
    }

    // Left wall measurements - only if left wall exists and there are right-facing cabinets
    // const leftWallMeasurementsGroup = null;
    // const leftWallEmptySpaceGroup = null;

    // if (leftWall && leftFacingCabinets.length > 0) {
    //     // Create a new cabinet measurements group for left wall
    //     // leftWallMeasurementsGroup = addMultipleMeasurementsToWallLeftRight(
    //     //     leftWall,
    //     //     handleArrowsAndGroup,
    //     //     roomHeight,
    //     //     leftFacingCabinets,
    //     //     getGroupWidth,
    //     //     roomLength, // Note: reversed dimensions for left wall
    //     //     roomWidth,
    //     //     scale,
    //     //     'leftwall'
    //     // );

    //     // Set visibility for left wall cabinet measurements
    //     if (
    //         leftWallMeasurementsGroup &&
    //         typeof leftWallMeasurementsGroup.userData.updateVisibility ===
    //             'function'
    //     ) {
    //         (
    //             leftWallMeasurementsGroup.userData as {
    //                 updateVisibility: (showArrows: boolean) => void;
    //             }
    //         ).updateVisibility(showArrows);
    //     }

    //     // Now create empty space measurements group for left wall
    //     // leftWallEmptySpaceGroup = addEmptySpaceMeasurementsLeftRight(
    //     //     leftWall,
    //     //     leftFacingCabinets,
    //     //     getGroupWidth,
    //     //     roomHeight,
    //     //     roomLength, // Note: reversed dimensions for left wall
    //     //     roomWidth,
    //     //     scale,
    //     //     handleArrowsAndGroup,
    //     //     'leftwall'
    //     // );

    //     // // Set visibility for left wall empty space measurements
    //     // if (
    //     //     leftWallEmptySpaceGroup &&
    //     //     typeof leftWallEmptySpaceGroup.userData.updateVisibility ===
    //     //         'function'
    //     // ) {
    //     //     (
    //     //         leftWallEmptySpaceGroup.userData as {
    //     //             updateVisibility: (showArrows: boolean) => void;
    //     //         }
    //     //     ).updateVisibility(false);
    //     // }

    //     // Handle collisions between cabinet and empty space measurements for left wall
    //     handleCabinetEmptySpaceCollisionsLeftRight(
    //         leftWallMeasurementsGroup,
    //         leftWallEmptySpaceGroup
    //     );
    // } else {
    //     removeExistingMeasurementsFromWall(leftWall);
    // }

    // If no cabinets present, add basic wall measurement arrows

    // Return all measurement groups for reference
    return {
        backWall: {
            cabinetMeasurements: backWallMeasurementsGroup,
            emptySpaceMeasurements: backWallEmptySpaceGroup,
        },
        frontWall: frontWall
            ? {
                  cabinetMeasurements: frontWallMeasurementsGroup,
                  emptySpaceMeasurements: frontWallEmptySpaceGroup,
              }
            : null,
        // leftWall: leftWall
        //     ? {
        //           cabinetMeasurements: leftWallMeasurementsGroup,
        //           //   emptySpaceMeasurements: leftWallEmptySpaceGroup,
        //       }
        //     : null,
    };
};

const generateMeasurementsFromCabinets = (
    cabinetGroupList: THREE.Group<THREE.Object3DEventMap>[],
    getGroupWidth: (group: THREE.Group<THREE.Object3DEventMap>) => {
        width: number;
        height: number;
        depth: number;
    }
) => {
    return cabinetGroupList.map((cabinet, index) => {
        try {
            const {width, height, depth} = getGroupWidth(cabinet);

            const position = cabinet.position.x;
            const positionZ = cabinet.position.z;

            const cabinetId =
                (cabinet.userData.id as string) || `cabinet-${index}`;

            // Choose label based on cabinet direction
            let label = '';
            const currentDirection = cabinet?.userData
                ?.currentDirection as string;
            if (currentDirection === 'front' || currentDirection === 'back') {
                // Use width for front/back orientation
                label =
                    (cabinet.userData.values as {cabinet_width?: string})
                        ?.cabinet_width || '';
            } else {
                // Use depth for left/right orientation
                label =
                    (
                        cabinet.userData.values as {cabinet_depth?: number}
                    )?.cabinet_depth?.toString() || '';
            }

            return {
                start: position - width / 2,
                end: position + width / 2,
                width,
                height,
                depth,
                position,
                cabinetId,
                label: `${label}`,
                dimensions: {
                    width,
                    height,
                    depth,
                },
                currentDirection: cabinet?.userData?.currentDirection as string,
                positionZ,
            };
        } catch (error) {
            return {
                start: 0,
                end: 0,
                width: 0,
                height: 0,
                depth: 0,
                position: 0,
                cabinetId:
                    (cabinet.userData.id as string) || `cabinet-${index}`,
                label: '',
                dimensions: {
                    width: 0,
                    height: 0,
                    depth: 0,
                },
                currentDirection: 'front',
                positionZ: 0,
            };
        }
    });
};

// Movement Arrow When clicking
export const addCabinetMovementArrows = (
    cabinetGroup: THREE.Group,
    handleArrowsAndGroup: (
        arrows: (THREE.ArrowHelper | THREE.SpriteMaterial | THREE.Mesh)[],
        arrowGroup?: THREE.Group<THREE.Object3DEventMap>
    ) => void,
    roomType: string,
    rectangularDimension: {width: number; length: number},
    lShapedDimension: {rightWidth: number; leftWidth: number},
    scale: number
) => {
    // Get cabinet dimensions
    const {width: w, depth: d} = getGroupWidth(cabinetGroup);

    const width = w;
    const depth = d;

    // Create a new group for the movement arrows
    const arrowGroup = new THREE.Group();
    arrowGroup.name = 'cabinetMovementArrows';

    // Add a unique ID to identify this arrow group
    arrowGroup.userData.id =
        (cabinetGroup.userData.id as string) || 'cabinetMovementArrows';

    // Store all arrow objects
    const arrowObjects: THREE.Mesh[] = [];

    // Store measurement objects
    const measurementObjects: THREE.Object3D[] = [];

    // Method to show/hide arrows based on active state
    arrowGroup.userData.updateVisibility = (isActive: boolean) => {
        arrowGroup.visible = isActive;
        arrowObjects.forEach((arrow) => {
            arrow.visible = isActive;
        });
        measurementObjects.forEach((obj) => {
            obj.visible = isActive;
        });
    };

    // Initial state - hidden
    arrowGroup.visible = false;

    // Create measurement line from cabinet to wall - similar to empty space measurements
    const createWallDistanceMeasurement = (
        // position: THREE.Vector3,
        direction: string
        // distance: number
    ) => {
        // Create a group for the measurement
        const measurementGroup = new THREE.Group();
        measurementGroup.name = 'wallDistanceMeasurement';
        measurementGroup.renderOrder = 998; // Set high render order

        // Create horizontal measurement line
        const arrowColor = 0x29b6f6; // Match the color with movement arrows

        // Horizontal position for the measurement line (slightly above the cabinet)
        const lineY = depth / 2;
        // const lineZ = 40; // Raise it above the cabinet a bit for visibility

        // Get room dimensions
        // const roomDims = getRoomDimensionsComputed();

        // Calculate room dimensions
        const roomWidth =
            roomType === 'RECTANGULAR'
                ? rectangularDimension.width
                : lShapedDimension.rightWidth;

        const roomLength =
            roomType === 'RECTANGULAR'
                ? rectangularDimension.length
                : lShapedDimension.leftWidth;

        // Get cabinet orientation and position data
        const currentDirection =
            (cabinetGroup.userData.currentDirection as string) || 'front';

        // Create start and end positions based on direction with offset to prevent overlap
        let startPoint;
        const arrowHeadLength = 8;
        const arrowHeadWidth = 8;

        const basePositionY = lineY;
        const positionZ = 0; // Match z-offset with createEmptySpaceMeasurements

        // Direction vectors for arrows - same as in createEmptySpaceMeasurements
        const leftToRightDir = new THREE.Vector3(1, 0, 0).normalize();
        const rightToLeftDir = new THREE.Vector3(-1, 0, 0).normalize();
        const frontToBackDir = new THREE.Vector3(0, 1, 0).normalize();
        const backToFrontDir = new THREE.Vector3(0, -1, 0).normalize();

        // Get the same on the gap
        const centerX = cabinetGroup.position.x;
        let adjustedCenterX = centerX;
        const adjustedLeft = centerX - width / 2;
        let adjustedRight = centerX + width / 2;
        const dimensions = getGroupWidth(cabinetGroup);

        if (currentDirection === 'left') {
            adjustedCenterX = centerX - width;
            adjustedRight -= width;
        }
        // Apply adjustment for left and back directions
        if (currentDirection === 'back') {
            adjustedCenterX = centerX - width;
            adjustedRight -= width;
        }

        const cabinetPositions = [
            {
                left: adjustedLeft,
                right: adjustedRight,
                width: width,
                xPosition: -adjustedCenterX,
                dimensions,
            },
        ];

        const gaps = [
            {
                start: 0,
                end: cabinetPositions[0].left,
                width: cabinetPositions[0].left,
                leftCabinetId: 'wallStart',
                xPosition: cabinetPositions[0].xPosition,
                dimensions: cabinetPositions[0].dimensions,
            },
            {
                start: cabinetPositions[cabinetPositions.length - 1].right,
                end: cabinetPositions[cabinetPositions.length - 1].left,
                width: roomWidth * scale,
                rightCabinetId: 'wallEnd',
                xPosition:
                    cabinetPositions[cabinetPositions.length - 1].xPosition,
                dimensions:
                    cabinetPositions[cabinetPositions.length - 1].dimensions,
                currentCabinetWidth: 0,
                nextCabinetWidth: 0,
            },
        ];

        let actualArrowLength;

        if (direction === 'left') {
            // Left wall to cabinet - don't offset the wall side
            startPoint = new THREE.Vector3(0, basePositionY, positionZ);
            // Create left arrow (pointing right/inward from wall) - match createEmptySpaceMeasurements
            actualArrowLength =
                (roomWidth * scale) / 2 - (gaps[0].xPosition + 30);
            const leftArrow = new THREE.ArrowHelper(
                rightToLeftDir,
                startPoint,
                actualArrowLength,
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );
            measurementGroup.add(leftArrow);
        } else if (direction === 'right') {
            const offset = Math.abs(gaps[1].start - gaps[1].end) - 40;
            actualArrowLength =
                gaps[1].width / 2 + (gaps[1].xPosition - offset);
            // Right wall to cabinet
            startPoint = new THREE.Vector3(
                gaps[1].start - gaps[1].end,
                basePositionY,
                positionZ
            );

            // Create right arrow (pointing left/inward from wall)
            const rightArrow = new THREE.ArrowHelper(
                leftToRightDir,
                startPoint,
                actualArrowLength,
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );
            measurementGroup.add(rightArrow);
        } else if (direction === 'front') {
            // Front wall to cabinet
            startPoint = new THREE.Vector3(width / 2, 0, positionZ);

            // Distance from front wall to cabinet
            actualArrowLength =
                (roomLength * scale) / 2 - cabinetGroup.position.z;

            if (actualArrowLength < 0)
                actualArrowLength = Math.abs(actualArrowLength);

            const frontArrow = new THREE.ArrowHelper(
                backToFrontDir,
                startPoint,
                actualArrowLength,
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );
            measurementGroup.add(frontArrow);
        } else if (direction === 'back') {
            // Back wall to cabinet
            startPoint = new THREE.Vector3(width / 2, depth, positionZ);

            // Distance from back wall to cabinet
            actualArrowLength =
                (roomLength * scale) / 2 + cabinetGroup.position.z - depth + 10;
            if (actualArrowLength < 0)
                actualArrowLength = Math.abs(actualArrowLength);

            const backArrow = new THREE.ArrowHelper(
                frontToBackDir,
                startPoint,
                actualArrowLength,
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );
            measurementGroup.add(backArrow);
        }

        // Create text label showing the distance
        const canvas = document.createElement('canvas');
        canvas.width = 156;
        canvas.height = 64;
        canvas.style.backgroundColor = 'transparent';

        const context = canvas.getContext('2d');
        if (context) {
            // Clear the canvas to make it transparent
            context.clearRect(0, 0, canvas.width, canvas.height);

            context.font = 'bold 27px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillStyle = '#333'; // Match the color with movement arrows

            // Display measurement in mm
            const displayWidth = actualArrowLength;
            let originalWidth =
                displayWidth !== null && scale
                    ? displayWidth / scale
                    : displayWidth;

            // Apply direction-specific adjustments to measurements
            if (originalWidth !== null && originalWidth > 0) {
                if (direction === 'left' || direction === 'right') {
                    originalWidth = Math.max(0, originalWidth - 5);
                } else if (direction === 'back') {
                    originalWidth = Math.max(0, originalWidth - 15);
                } else if (direction === 'front') {
                    originalWidth = Math.max(0, originalWidth - 93);
                }
            }

            let displayLabel = '';
            if (originalWidth !== null && originalWidth > 0) {
                displayLabel = `${Math.round(originalWidth)} mm`;
            }

            context.fillText(displayLabel, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            texture.premultiplyAlpha = true;
            const textMaterial = new THREE.SpriteMaterial({
                map: texture,
                transparent: true,
                depthTest: false,
                depthWrite: false,
                alphaTest: 0.01,
                sizeAttenuation: false,
            });

            const textSprite = new THREE.Sprite(textMaterial);

            if (direction === 'left') {
                textSprite.position.set(
                    -Math.abs(actualArrowLength) / 2,
                    depth / 2,
                    0 // Increase Z position to avoid z-fighting
                );
            } else if (direction === 'right') {
                // Position sprite at center between arrows
                textSprite.position.set(
                    Math.abs(actualArrowLength) / 2 + width,
                    depth / 2,
                    0 // Increase Z position to avoid z-fighting
                );
            } else if (direction === 'front') {
                // Position sprite at center for front measurement
                textSprite.position.set(
                    width / 2,
                    -actualArrowLength / 2,
                    0 // Increase Z position to avoid z-fighting
                );
            } else if (direction === 'back') {
                // Position sprite at center for back measurement
                textSprite.position.set(
                    width / 2,
                    depth + actualArrowLength / 2,
                    0 // Increase Z position to avoid z-fighting
                );
            }

            // Scale consistently with other dimension labels
            textSprite.scale.set(100, 50, 1);
            measurementGroup.add(textSprite);
        }

        arrowGroup.add(measurementGroup);
        measurementObjects.push(measurementGroup);

        return measurementGroup;
    };

    // Debug lines function removed as requested

    // Create movement arrows

    const createMovementArrows = () => {
        // Clear existing arrows
        while (arrowGroup.children.length > 0) {
            arrowGroup.remove(arrowGroup.children[0]);
        }
        arrowObjects.length = 0;
        measurementObjects.length = 0;

        // Create 6 directional flat arrows for movement
        const createFlatArrow = (
            direction: THREE.Vector3,
            position: THREE.Vector3
        ) => {
            // Create a custom shape for the arrow
            const arrowShape = new THREE.Shape();

            // Define arrow shape points for a more prominent arrow
            arrowShape.moveTo(0, -40); // Top point (enlarged)
            arrowShape.lineTo(30, 5); // Bottom right (enlarged)
            arrowShape.lineTo(15, 5); // Start of arrow shaft
            arrowShape.lineTo(15, 20); // Arrow shaft
            arrowShape.lineTo(-15, 20); // Arrow shaft
            arrowShape.lineTo(-15, 5); // End of arrow shaft
            arrowShape.lineTo(-30, 5); // Bottom left (enlarged)
            arrowShape.lineTo(0, -40); // Back to top

            // Extrude settings - thicker arrow
            const extrudeSettings = {
                depth: 7, // Increased thickness
                bevelEnabled: true,
                bevelThickness: 3, // Increased bevel
                bevelSize: 2, // Increased bevel size
                bevelSegments: 4, // Smoother bevel
            };

            // Create extruded geometry
            const arrowGeometry = new THREE.ExtrudeGeometry(
                arrowShape,
                extrudeSettings
            );

            // Create material - brighter and more vibrant
            const arrowMaterial = new THREE.MeshPhongMaterial({
                color: 0x29b6f6,
                shininess: 120, // Increased shininess
                transparent: true,
                opacity: 0.95, // Slightly increased opacity
                emissive: 0x0a4a7d, // Add a slight glow
                emissiveIntensity: 0.3,
            });

            // Create the arrow mesh
            const arrowMesh = new THREE.Mesh(arrowGeometry, arrowMaterial);
            arrowMesh.position.copy(position);

            // Set the rotation based on the direction to make arrows point outward from surfaces
            if (direction.x < 0) {
                // Left arrow - pointing left (negative X)
                arrowMesh.rotation.z = -Math.PI / 2;
            } else if (direction.x > 0) {
                // Right arrow - pointing right (positive X)
                arrowMesh.rotation.z = Math.PI / 2;
            } else if (direction.y < 0) {
                // Front arrow - pointing front (negative Y)
                arrowMesh.rotation.z = 0;
            } else if (direction.y > 0) {
                // Back arrow - pointing back (positive Y)
                arrowMesh.rotation.z = Math.PI;
            } else if (direction.z < 0) {
                // Down arrow - pointing down (negative Z)
                arrowMesh.rotation.x = Math.PI / 2;
            } else if (direction.z > 0) {
                // Up arrow - pointing up (positive Z)
                arrowMesh.rotation.x = -Math.PI / 2;
            }

            // Center the geometry
            arrowMesh.geometry.center();

            arrowGroup.add(arrowMesh);
            arrowObjects.push(arrowMesh);
            return arrowMesh;
        };

        // Get the current direction of the cabinet
        const currentDirection =
            (cabinetGroup.userData.currentDirection as string) || 'front';
        const {width, height, depth} = getGroupWidth(cabinetGroup);

        // Determine effective dimensions based on rotation
        const effectiveWidth =
            currentDirection === 'left' || currentDirection === 'right'
                ? depth
                : width;
        const effectiveDepth =
            currentDirection === 'left' || currentDirection === 'right'
                ? width
                : depth;

        // Direction vectors and positions with adjusted dimensions
        const directions = [
            {
                dir: new THREE.Vector3(-1, 0, 0),
                position: new THREE.Vector3(
                    -40,
                    effectiveDepth / 2,
                    height - 20
                ),
                direction: 'left',
            },
            {
                dir: new THREE.Vector3(1, 0, 0),
                position: new THREE.Vector3(
                    effectiveWidth + 40,
                    effectiveDepth / 2,
                    height - 20
                ),
                direction: 'right',
            },
            {
                dir: new THREE.Vector3(0, 0, 1),
                position: new THREE.Vector3(
                    effectiveWidth / 2,
                    effectiveDepth / 2,
                    height + 120
                ),
                direction: 'up',
            },
            {
                dir: new THREE.Vector3(0, 0, -1),
                position: new THREE.Vector3(
                    effectiveWidth / 2,
                    effectiveDepth / 2,
                    height + 30
                ),
                direction: 'down',
            },
            {
                dir: new THREE.Vector3(0, 1, 0),
                position: new THREE.Vector3(
                    effectiveWidth / 2,
                    effectiveDepth + 25,
                    height - 20
                ),
                direction: 'back',
            },
            {
                dir: new THREE.Vector3(0, -1, 0),
                position: new THREE.Vector3(
                    effectiveWidth / 2,
                    -25,
                    height - 20
                ),
                direction: 'front',
            },
        ];

        // Create arrows for each direction
        directions.forEach(({dir, position, direction}) => {
            const arrow = createFlatArrow(dir, position);
            // Store direction data in arrow's userData
            arrow.userData.direction = direction;
            arrow.userData.worldPosition = position.clone(); // Store world position for input field placement
        });

        // Add wall distance measurements using a similar approach to empty space measurements

        // Get room dimensions from the store using the imported getRoomDimensionsComputed
        // const roomDimensions = getRoomDimensionsComputed();

        // // Calculate cabinet position relative to room
        // const cabinetPos = cabinetGroup.position.x;

        // // Get cabinet orientation and position data

        // // Adjust measurements based on cabinet orientation
        // let adjustedCabinetPos = cabinetPos;

        // // // Apply direction-based position adjustments similar to addEmptySpaceMeasurements
        // if (currentDirection === 'left' || currentDirection === 'back') {
        //     // For left and back facing cabinets, adjust the position calculations
        //     adjustedCabinetPos = cabinetPos - effectiveWidth;
        // }

        // // Calculate distances to walls based on cabinet direction using same logic as createEmptySpaceMeasurements

        // Create wall-to-cabinet measurements (if distance is meaningful)
        // Left wall to cabinet - start from the left wall
        createWallDistanceMeasurement(
            // new THREE.Vector3(
            //     roomDimensions.computedWidth,
            //     depth / 2,
            //     height / 2
            // ),
            'left'
            // leftDistance
        );

        // Cabinet to right wall - start from the right edge of cabinet
        createWallDistanceMeasurement(
            // new THREE.Vector3(
            //     adjustedCabinetPos + effectiveWidth,
            //     depth / 2,
            //     height / 2
            // ),
            'right'
            // rightDistance
        );

        // Front wall to cabinet measurement
        createWallDistanceMeasurement(
            // new THREE.Vector3(width / 2, 0, height / 2),
            'front'
            // cabinetGroup.position.y
        );

        // Back wall to cabinet measurement
        createWallDistanceMeasurement(
            // new THREE.Vector3(width / 2, roomLength * scale, height / 2),
            'back'
            // roomLength * scale - (cabinetGroup.position.y + depth)
        );
    };

    // Method to update arrow positions when cabinet changes
    arrowGroup.userData.updateArrows = () => {
        // Reset hover states on all arrows before recreating
        arrowObjects.forEach((arrow) => {
            if (arrow.userData && arrow.userData.isHovered) {
                if (typeof arrow.userData.onHoverEnd === 'function') {
                    (
                        arrow.userData as {
                            onHoverEnd: () => void;
                        }
                    ).onHoverEnd();
                }
            }
        });

        // Update dimensions (recreates arrows)
        createMovementArrows();

        // Update position and rotation to match cabinet
        arrowGroup.position.copy(cabinetGroup.position);
        arrowGroup.rotation.copy(cabinetGroup.rotation);

        // Setup interactions again to ensure all user data is properly initialized
        (
            arrowGroup.userData as {
                setupInteractions: () => void;
            }
        ).setupInteractions();

        // Re-register with arrow handler
        handleArrowsAndGroup(arrowObjects, arrowGroup);

        // Log hover state transfer operation
    };

    // Methods for showing/hiding arrows
    arrowGroup.userData.onDrag = (
        width: number,
        depth: number,
        height: number
    ) => {
        // Only show arrows if this cabinet is the active group
        if (cabinetGroup.userData.isActiveGroup) {
            arrowGroup.visible = true;
            arrowObjects.forEach((arrow) => {
                arrow.visible = true;
            });
            measurementObjects.forEach((obj) => {
                obj.visible = true;
            });

            // Check if we need to update dimensions based on orientation
            const isHorizontal =
                cabinetGroup.userData.currentDirection === 'left' ||
                cabinetGroup.userData.currentDirection === 'right';

            if (!isHorizontal) {
                (
                    arrowGroup.userData as {
                        updateArrows: (
                            width: number,
                            depth: number,
                            height: number
                        ) => void;
                    }
                ).updateArrows(width, depth, height);
            } else {
                (
                    arrowGroup.userData as {
                        updateArrows: (
                            width: number,
                            depth: number,
                            height: number
                        ) => void;
                    }
                ).updateArrows(depth, width, height);
            }

            // Ensure arrow group stays with cabinet
            arrowGroup.position.copy(cabinetGroup.position);
            arrowGroup.rotation.copy(cabinetGroup.rotation);

            // Clear existing measurements and recreate them with updated positions
            // This ensures they reflect the new cabinet position
            measurementObjects.forEach((obj) => {
                if (obj.name === 'wallDistanceMeasurement') {
                    arrowGroup.remove(obj);
                }
            });

            // Filter out removed measurement objects
            const remainingObjects = measurementObjects.filter(
                (obj) => obj.name !== 'wallDistanceMeasurement'
            );
            measurementObjects.length = 0;
            remainingObjects.forEach((obj) => measurementObjects.push(obj));

            // Create new measurement lines with updated distances
            createWallDistanceMeasurement(
                // new THREE.Vector3(
                //     roomDimensions.computedWidth,
                //     depth / 2,
                //     height / 2
                // ),
                'left'
                // leftDistance
            );

            createWallDistanceMeasurement(
                // new THREE.Vector3(
                //     adjustedCabinetPos + width,
                //     depth / 2,
                //     height / 2
                // ),
                'right'
                // rightDistance
            );
            createWallDistanceMeasurement(
                // new THREE.Vector3(
                //     adjustedCabinetPos + width,
                //     depth / 2,
                //     height / 2
                // ),
                'front'
                // rightDistance
            );
            createWallDistanceMeasurement(
                // new THREE.Vector3(
                //     adjustedCabinetPos + width,
                //     depth / 2,
                //     height / 2
                // ),
                'back'
                // rightDistance
            );
        }
    };

    arrowGroup.userData.onDragEnd = () => {
        arrowGroup.visible = false;
        measurementObjects.forEach((obj) => {
            obj.visible = false;
        });
    };

    // Setup click interactions with the arrows
    arrowGroup.userData.setupInteractions = () => {
        arrowObjects.forEach((arrow, index) => {
            // Make the arrow interactive
            arrow.userData.clickable = true;
            arrow.userData.isArrow = true;
            arrow.userData.arrowIndex = index;
            arrow.userData.isHovered = false; // Initialize hover state

            // Add a hover effect
            arrow.userData.onHover = () => {
                // Scale the arrow up more noticeably when hovered
                arrow.scale.set(1.5, 1.5, 1.5);

                // Make the arrow brighter and more vibrant when hovered
                if (arrow.material instanceof THREE.MeshPhongMaterial) {
                    // Store original color if not already stored
                    if (!arrow.userData.originalColor) {
                        arrow.userData.originalColor =
                            arrow.material.color.getHex();
                        arrow.userData.originalEmissive =
                            arrow.material.emissive.getHex();
                        arrow.userData.originalOpacity = arrow.material.opacity;
                        arrow.userData.originalEmissiveIntensity =
                            arrow.material.emissiveIntensity;
                    }

                    // Brighten the color - more dramatic color change for better visibility
                    arrow.material.color.setHex(0x00e5ff); // Bright cyan blue
                    arrow.material.emissive.setHex(0x00a0b0); // Matching emissive glow
                    arrow.material.opacity = 1.0; // Full opacity
                    arrow.material.emissiveIntensity = 0.7; // Stronger glow
                }

                // Flag the arrow as currently being hovered
                arrow.userData.isHovered = true;
            };

            arrow.userData.onHoverEnd = () => {
                // Return to original scale
                arrow.scale.set(1, 1, 1);

                // Restore original colors
                if (
                    arrow.material instanceof THREE.MeshPhongMaterial &&
                    arrow.userData.originalColor !== undefined
                ) {
                    arrow.material.color.setHex(
                        arrow.userData.originalColor as number
                    );
                    arrow.material.emissive.setHex(
                        arrow.userData.originalEmissive as number
                    );
                    arrow.material.opacity = arrow.userData
                        .originalOpacity as number;
                    arrow.material.emissiveIntensity = arrow.userData
                        .originalEmissiveIntensity as number;
                }

                // Clear the hover flag
                arrow.userData.isHovered = false;
            };

            // Add click handler for each arrow
            arrow.userData.onClick = () => {
                // Get the clicked arrow's direction and world position
                const direction = arrow.userData.direction as string;
                const worldPosition = (
                    arrow.userData.worldPosition as THREE.Vector3
                ).clone();

                // Add the cabinet's position to get the actual world position
                worldPosition.add(cabinetGroup.position);

                // Convert world position to screen coordinates for input field placement
                const vector = worldPosition.clone();
                vector.project(cabinetGroup.userData.camera as THREE.Camera);

                const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
                const y = (-vector.y * 0.5 + 0.5) * window.innerHeight;

                // Hide all other arrows except this one
                arrowObjects.forEach((otherArrow) => {
                    if (otherArrow !== arrow) {
                        otherArrow.visible = false;
                    }
                });

                // Hide all measurement lines when an arrow is clicked
                measurementObjects.forEach((obj) => {
                    obj.visible = false;
                });

                // Trigger position editor display
                if (cabinetGroup.userData.onArrowClick) {
                    (
                        cabinetGroup.userData as {
                            onArrowClick: (params: {
                                direction: string;
                                screenX: number;
                                screenY: number;
                                arrowMesh: THREE.Mesh;
                            }) => void;
                        }
                    ).onArrowClick({
                        direction,
                        screenX: x,
                        screenY: y,
                        arrowMesh: arrow,
                    });
                }
            };
        });
    };

    // Add click handler to the arrow group
    arrowGroup.userData.handleClick = (
        event: MouseEvent,
        raycaster: THREE.Raycaster,
        camera: THREE.Camera
    ) => {
        // Store camera reference for coordinate conversion
        cabinetGroup.userData.camera = camera;

        // Only proceed if arrow group is visible
        if (!arrowGroup.visible) {
            return;
        }

        // Check which arrow was clicked - only consider visible arrows
        const intersects = raycaster.intersectObjects(
            arrowObjects.filter((arrow) => arrow.visible),
            true
        );

        if (intersects.length > 0) {
            const clickedArrow = intersects[0].object;

            // Find the parent arrow mesh (in case we clicked a child object)
            const arrowMesh = clickedArrow.parent?.userData.isArrow
                ? clickedArrow.parent
                : clickedArrow;

            // Only proceed if both the arrow and arrow group are visible
            if (arrowMesh && arrowMesh.visible && arrowGroup.visible) {
                // Hide all other arrows
                arrowObjects.forEach((arrow) => {
                    if (arrow !== arrowMesh) {
                        arrow.visible = false;
                    }
                });

                // Hide all measurement lines when an arrow is clicked
                measurementObjects.forEach((obj) => {
                    obj.visible = false;
                });

                // Get the direction and world position from the arrow's userData
                const direction = arrowMesh.userData.direction as string;
                const worldPosition = arrowMesh.getWorldPosition(
                    new THREE.Vector3()
                );

                // Convert world position to screen coordinates for input field placement
                const vector = worldPosition.clone();
                vector.project(camera);

                const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
                const y = (-vector.y * 0.5 + 0.5) * window.innerHeight;

                // Trigger position editor display
                if (cabinetGroup.userData.onArrowClick) {
                    (
                        cabinetGroup.userData as {
                            onArrowClick: (params: {
                                direction: string;
                                screenX: number;
                                screenY: number;
                                arrowMesh: THREE.Mesh;
                            }) => void;
                        }
                    ).onArrowClick({
                        direction,
                        screenX: x,
                        screenY: y,
                        arrowMesh: arrowMesh as THREE.Mesh,
                    });
                }
            }
        }
    };

    // Store references to arrows and their handlers in the cabinet group
    cabinetGroup.userData.movementArrows = arrowObjects;
    cabinetGroup.userData.arrowGroup = arrowGroup;

    // Add method to show/hide arrows
    cabinetGroup.userData.showMovementArrows = (show: boolean) => {
        if (arrowObjects) {
            arrowObjects.forEach((arrow) => {
                arrow.visible = show;
            });
        }
        if (measurementObjects) {
            measurementObjects.forEach((obj) => {
                obj.visible = show;
            });
        }
    };

    // Add method to reset arrow visibility
    cabinetGroup.userData.resetArrowVisibility = () => {
        if (arrowObjects) {
            arrowObjects.forEach((arrow) => {
                arrow.visible = true;
            });
        }
        if (measurementObjects) {
            measurementObjects.forEach((obj) => {
                obj.visible = true;
            });
        }
    };

    handleArrowsAndGroup(arrowObjects, arrowGroup);

    // Add method to reset arrow visibility
    arrowGroup.userData.resetArrows = () => {
        arrowObjects.forEach((arrow) => {
            arrow.visible = true;
        });
        measurementObjects.forEach((obj) => {
            obj.visible = true;
        });
    };

    // Add dispose method
    arrowGroup.userData.dispose = () => {
        // Properly dispose of materials and textures
        arrowObjects.forEach((arrow) => {
            if (arrow instanceof THREE.Mesh) {
                if (
                    arrow.material instanceof THREE.MeshBasicMaterial &&
                    arrow.material.map
                ) {
                    arrow.material.map.dispose();
                    arrow.material.dispose();
                }
                if (arrow.geometry) {
                    arrow.geometry.dispose();
                }
            }
        });

        // Dispose of measurement objects
        measurementObjects.forEach((obj) => {
            if (obj instanceof THREE.Group) {
                obj.children.forEach((child) => {
                    if (child instanceof THREE.Line) {
                        (child.geometry as THREE.BufferGeometry).dispose();
                        (child.material as THREE.Material).dispose();
                    }
                    if (child instanceof THREE.Sprite) {
                        if (
                            child.material instanceof THREE.SpriteMaterial &&
                            child.material.map
                        ) {
                            child.material.map.dispose();
                        }
                        child.material.dispose();
                    }
                });
            }
        });

        if (arrowGroup.parent) {
            arrowGroup.parent.remove(arrowGroup);
        }
    };

    // Add the dispose method to the cabinet group
    cabinetGroup.userData.disposeMovementArrows = () => {
        (
            arrowGroup.userData as {
                dispose: () => void;
            }
        ).dispose();
    };

    // Create initial arrows
    createMovementArrows();

    // Debug lines removed as requested
    // createDebugLines()

    // Setup interactions
    (
        arrowGroup.userData as {
            setupInteractions: () => void;
        }
    ).setupInteractions();

    // Add arrow group to the scene at the cabinet's position
    if (cabinetGroup.parent) {
        cabinetGroup.parent.add(arrowGroup);
    } else {
        return null;
    }

    // Position arrow group to match cabinet position
    arrowGroup.position.copy(cabinetGroup.position);
    arrowGroup.rotation.copy(cabinetGroup.rotation);

    // Set initial visibility to false
    arrowGroup.visible = false;

    // Register with arrow handler
    handleArrowsAndGroup(arrowObjects, arrowGroup);

    // Listen for active group changes
    cabinetGroup.userData.setActiveState = (isActive: boolean) => {
        cabinetGroup.userData.isActiveGroup = isActive;
        (
            arrowGroup.userData as {
                updateVisibility: (isActive: boolean) => void;
            }
        ).updateVisibility(isActive);
    };

    return arrowGroup;
};

const createEmptySpaceMeasurements = (
    gaps: {
        start: number;
        end: number;
        width: number;
        leftCabinetId: string | undefined;
        rightCabinetId: string | undefined;
        xPosition: number;
        cabinetWidth?: number;
        dimensions?: {
            width: number;
            height: number;
            depth: number;
        };
        currentCabinetWidth?: number;
        nextCabinetWidth?: number;
        cabinet?: THREE.Group<THREE.Object3DEventMap>;
        nextCabinet?: THREE.Group<THREE.Object3DEventMap>;
        nextCabinetDimension?: {
            width: number;
            height: number;
            depth: number;
        };
    }[],
    wallHeight: number,
    roomWidth?: number,
    roomLength?: number,
    scale?: number,
    arrowObjects: (THREE.ArrowHelper | THREE.SpriteMaterial)[] = [],
    emptySpaceGroup: THREE.Group = new THREE.Group(),
    wallRefId?: string
) => {
    const arrowColor = '#A9A9A9';
    const basePositionY = wallHeight + 10;
    const arrowHeadLength = 8;
    const arrowHeadWidth = 8;

    const filteredEdgeGaps = gaps;
    // If all gaps were filtered out, return early
    if (filteredEdgeGaps.length === 0) {
        return emptySpaceGroup;
    }

    // Check if this is an empty room (no cabinets) measurement
    const isEmptyRoom =
        filteredEdgeGaps.length === 1 &&
        filteredEdgeGaps[0].leftCabinetId === 'wallStart' &&
        filteredEdgeGaps[0].rightCabinetId === 'wallEnd';

    // Step 3: Create measurements for edge gaps
    filteredEdgeGaps.forEach((gap, index) => {
        // Calculate positions based on gap edges - keep original computation
        let actualArrowLength;
        const offset = Math.abs(gap.start - gap.end) - 40;

        // Special case for empty room - use simpler, more appropriate measurements
        if (isEmptyRoom && roomWidth && scale) {
            // Use the actual room width scaled appropriately
            actualArrowLength = roomWidth * scale;

            // Create label with appropriate style for the room measurement
            const canvas = document.createElement('canvas');
            canvas.width = 156;
            canvas.height = 64;

            const context = canvas.getContext('2d');
            if (!context) return;

            // Draw text
            context.fillStyle = '#222222';
            context.font = 'bold 27px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';

            // Display the width in mm, properly scaled
            const displayLabel = `${Math.round(roomWidth)} mm`;
            context.fillText(displayLabel, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            const textMaterial = new THREE.SpriteMaterial({map: texture});
            const textSprite = new THREE.Sprite(textMaterial);

            // Position sprite in the center of the wall
            textSprite.position.set(
                actualArrowLength / 2,
                basePositionY + 50,
                25
            );
            textSprite.scale.set(100, 50, 1);

            emptySpaceGroup.add(textSprite);
            arrowObjects.push(textMaterial);

            // Create left arrow (pointing right)
            const leftArrow = new THREE.ArrowHelper(
                new THREE.Vector3(1, 0, 0).normalize(),
                new THREE.Vector3(
                    0,
                    basePositionY,
                    wallRefId === 'frontwall' ? 30 : 0
                ),
                actualArrowLength,
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );

            leftArrow.userData.gapId = index;
            emptySpaceGroup.add(leftArrow);
            arrowObjects.push(leftArrow);

            // Create right arrow (pointing left)
            const rightArrow = new THREE.ArrowHelper(
                new THREE.Vector3(-1, 0, 0).normalize(),
                new THREE.Vector3(
                    actualArrowLength,
                    basePositionY,
                    wallRefId === 'frontwall' ? 30 : 0
                ),
                actualArrowLength,
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );

            rightArrow.userData.gapId = index;
            emptySpaceGroup.add(rightArrow);
            arrowObjects.push(rightArrow);

            // Skip the rest of the function for empty rooms
            return;
        }

        // Handle wall start case separately
        if (gap.leftCabinetId === 'wallStart') {
            actualArrowLength = (roomWidth * scale) / 2 - (gap.xPosition + 40);
        }
        if (gap.rightCabinetId === 'wallEnd') {
            actualArrowLength = gap.width / 2 + (gap.xPosition - offset);
        } else {
            // Apply rotation adjustment to the arrow length calculation
        }

        // Create label with matching style to createMeasurements
        const canvas = document.createElement('canvas');
        canvas.width = 156;
        canvas.height = 64;

        const context = canvas.getContext('2d');
        if (!context) return;

        // Draw text
        context.fillStyle = '#222222';
        context.font = 'bold 27px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';

        // Display the gap width in mm - no extra multiplier
        const displayWidth = Math.round(actualArrowLength);

        // Convert to original units using scale and ensure we're not displaying zero
        const originalWidth =
            displayWidth !== null && scale
                ? displayWidth / scale
                : displayWidth;

        // Only create a label if we have a non-zero, non-null value
        let displayLabel = '';
        if (originalWidth !== null && originalWidth > 0) {
            displayLabel = `${Math.round(originalWidth)} mm`;
        }

        context.fillText(displayLabel, canvas.width / 2, canvas.height / 2);

        const texture = new THREE.CanvasTexture(canvas);
        const textMaterial = new THREE.SpriteMaterial({map: texture});
        const textSprite = new THREE.Sprite(textMaterial);

        // Position sprite at center, but adjust for wide gaps
        textSprite.position.set(actualArrowLength / 2, basePositionY + 50, 25);

        // Scale consistently with other dimension labels
        textSprite.scale.set(100, 50, 1);

        // Minimum arrow length threshold - don't show arrows smaller than this
        const minArrowLength = 10;

        // Only create and add arrows if the length is sufficient

        // Width direction vectors
        const leftToRightDir = new THREE.Vector3(1, 0, 0).normalize();
        const rightToLeftDir = new THREE.Vector3(-1, 0, 0).normalize();
        const positionZ =
            wallRefId == 'frontwall' || wallRefId == 'leftwall' ? 30 : 0; // Use proper Z offset

        if (gap.leftCabinetId === 'wallStart') {
            if (Math.abs(actualArrowLength) > minArrowLength) {
                const spritePosition = roomWidth * scale;
                textSprite.position.set(
                    spritePosition / 2 +
                        spritePosition / 2 -
                        Math.abs(actualArrowLength) / 2,
                    basePositionY + 50,
                    25
                );

                emptySpaceGroup.add(textSprite);
                arrowObjects.push(textMaterial);

                // Create left arrow (pointing right/inward from wall start)
                const wallStartLeftArrow = new THREE.ArrowHelper(
                    leftToRightDir,
                    new THREE.Vector3(
                        roomWidth * scale,
                        basePositionY,
                        positionZ
                    ),
                    8, // Use the full gap width for wall start
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                wallStartLeftArrow.userData.gapId = index;
                emptySpaceGroup.add(wallStartLeftArrow);
                arrowObjects.push(wallStartLeftArrow);

                // // Create right arrow (pointing left/inward)
                const wallStartRightArrow = new THREE.ArrowHelper(
                    rightToLeftDir,
                    new THREE.Vector3(
                        roomWidth * scale,
                        basePositionY,
                        positionZ
                    ),
                    Math.abs(actualArrowLength),
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                wallStartRightArrow.userData.gapId = index;
                emptySpaceGroup.add(wallStartRightArrow);
                arrowObjects.push(wallStartRightArrow);
            }
        }
        if (gap.rightCabinetId === 'wallEnd') {
            emptySpaceGroup.add(textSprite);
            arrowObjects.push(textMaterial);
            // Create left arrow (pointing right/inward)
            if (Math.abs(actualArrowLength) > minArrowLength) {
                const leftArrow = new THREE.ArrowHelper(
                    leftToRightDir,
                    new THREE.Vector3(0, basePositionY, positionZ),
                    Math.abs(actualArrowLength), // Use absolute value for arrow length
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Store gap info in arrow userData
                leftArrow.userData.gapId = index;

                // Add arrow to group
                emptySpaceGroup.add(leftArrow);

                // Add to arrow objects array
                arrowObjects.push(leftArrow);

                // Create right arrow (pointing left/inward)
                const rightArrow = new THREE.ArrowHelper(
                    rightToLeftDir,
                    new THREE.Vector3(
                        actualArrowLength,
                        basePositionY,
                        positionZ
                    ),
                    Math.abs(actualArrowLength),
                    arrowColor,
                    arrowHeadLength,
                    arrowHeadWidth
                );

                // Add arrows to group
                emptySpaceGroup.add(leftArrow);
                emptySpaceGroup.add(rightArrow);

                // Add to arrow objects array
                arrowObjects.push(leftArrow);
                arrowObjects.push(rightArrow);
            }
        } else {
            const gapWidth = gap.width;

            const xPosition =
                gap.xPosition +
                (roomLength * scale) / 2 -
                (gap.width / 2 - 42) -
                gap.cabinetWidth;

            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            if (!context) return;

            // Fill background
            canvas.width = 156;
            canvas.height = 64;

            context.fillStyle = '#222222';
            context.font = 'bold 27px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';

            let displayLabel = '';
            if (gapWidth !== null && gapWidth > 0) {
                displayLabel = `${Math.round(gapWidth / scale)} mm`;
            }

            context.fillText(displayLabel, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({map: texture});
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(100, 50, 1); // Size of the label

            // Position the label using the collision-adjusted Y position
            sprite.position.x = xPosition;
            sprite.position.y = basePositionY + 50;
            sprite.position.z = 25; // Ensure it's in front of arrows

            // Add label to group
            emptySpaceGroup.add(sprite);
            arrowObjects.push(spriteMaterial);

            // Calculate actual positions for left and right arrows

            const leftToRightDir = new THREE.Vector3(1, 0, 0).normalize();
            const rightToLeftDir = new THREE.Vector3(-1, 0, 0).normalize();

            // Create left arrow (pointing right/inward)
            const leftArrow = new THREE.ArrowHelper(
                leftToRightDir,
                new THREE.Vector3(xPosition, basePositionY, positionZ),
                gapWidth / 2, // Match style from image
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );

            emptySpaceGroup.add(leftArrow);
            arrowObjects.push(leftArrow);
            leftArrow.userData.gapId = index;

            // Store gap info in arrow userData
            // Create right arrow (pointing left/inward)
            const rightArrow = new THREE.ArrowHelper(
                rightToLeftDir,
                new THREE.Vector3(xPosition, basePositionY, positionZ),
                gapWidth / 2, // Match style from image
                arrowColor,
                arrowHeadLength,
                arrowHeadWidth
            );

            rightArrow.userData.gapId = index;

            emptySpaceGroup.add(rightArrow);
            arrowObjects.push(rightArrow);
        }
    });

    return emptySpaceGroup;
};

const handleCabinetEmptySpaceCollisions = (
    cabinetMeasurements: THREE.Group,
    emptySpaceMeasurements: THREE.Group
) => {
    // Skip if either group doesn't exist
    if (!cabinetMeasurements || !emptySpaceMeasurements) return;

    // Get all cabinet label sprites
    const cabinetLabels: THREE.Sprite[] = [];
    cabinetMeasurements.traverse(
        (object: THREE.Sprite<THREE.Object3DEventMap>) => {
            if (object instanceof THREE.Sprite) {
                cabinetLabels.push(object);
            }
        }
    );

    // Get all empty space label sprites
    const emptySpaceLabels: THREE.Sprite[] = [];
    emptySpaceMeasurements.traverse(
        (object: THREE.Sprite<THREE.Object3DEventMap>) => {
            if (object instanceof THREE.Sprite) {
                cabinetLabels.push(object);
            }
        }
    );

    // Check for collisions between cabinet and empty space labels
    emptySpaceLabels.forEach((emptyLabel) => {
        // Get all related arrows in the same group as this label
        const relatedArrows: THREE.ArrowHelper[] = [];
        if (emptyLabel.parent) {
            emptyLabel.parent.traverse((child) => {
                if (
                    child instanceof THREE.ArrowHelper &&
                    Math.abs(child.position.x - emptyLabel.position.x) < 100
                ) {
                    relatedArrows.push(child);
                }
            });
        }

        // Check against each cabinet label
        cabinetLabels.forEach((cabinetLabel) => {
            // Calculate horizontal distance
            const distance = Math.abs(
                emptyLabel.position.x - cabinetLabel.position.x
            );

            // Approximate label width (adjust based on your actual label size)
            const labelWidth = 0;

            // Check if the labels are too close horizontally and at the same height
            if (
                distance < labelWidth &&
                Math.abs(emptyLabel.position.y - cabinetLabel.position.y) < 10
            ) {
                // Move the empty space label up by 50 units
                emptyLabel.position.y += 50;

                // Also move any associated arrows
                relatedArrows.forEach((arrow) => {
                    arrow.position.y += 50;
                });
            }
        });
    });
};

// const handleCabinetEmptySpaceCollisionsLeftRight = (
//     cabinetMeasurements: THREE.Group,
//     emptySpaceMeasurements: THREE.Group
// ) => {
//     // Skip if either group doesn't exist
//     if (!cabinetMeasurements || !emptySpaceMeasurements) return;

//     // Get all cabinet label sprites
//     const cabinetLabels: THREE.Sprite[] = [];
//     cabinetMeasurements.traverse(
//         (object: THREE.Sprite<THREE.Object3DEventMap>) => {
//             if (object instanceof THREE.Sprite) {
//                 cabinetLabels.push(object);
//             }
//         }
//     );

//     // Get all empty space label sprites
//     const emptySpaceLabels: THREE.Sprite[] = [];
//     emptySpaceMeasurements.traverse(
//         (object: THREE.Sprite<THREE.Object3DEventMap>) => {
//             if (object instanceof THREE.Sprite) {
//                 cabinetLabels.push(object);
//             }
//         }
//     );

//     // Check for collisions between cabinet and empty space labels
//     emptySpaceLabels.forEach((emptyLabel) => {
//         // Get all related arrows in the same group as this label
//         const relatedArrows: THREE.ArrowHelper[] = [];
//         if (emptyLabel.parent) {
//             emptyLabel.parent.traverse((child) => {
//                 if (
//                     child instanceof THREE.ArrowHelper &&
//                     Math.abs(child.position.x - emptyLabel.position.x) < 100
//                 ) {
//                     relatedArrows.push(child);
//                 }
//             });
//         }

//         // Check against each cabinet label
//         cabinetLabels.forEach((cabinetLabel) => {
//             // Calculate horizontal distance
//             const distance = Math.abs(
//                 emptyLabel.position.x - cabinetLabel.position.x
//             );

//             // Approximate label width (adjust based on your actual label size)
//             const labelWidth = 0;

//             // Check if the labels are too close horizontally and at the same height
//             if (
//                 distance < labelWidth &&
//                 Math.abs(emptyLabel.position.y - cabinetLabel.position.y) < 10
//             ) {
//                 // Move the empty space label up by 50 units
//                 emptyLabel.position.y += 50;

//                 // Also move any associated arrows
//                 relatedArrows.forEach((arrow) => {
//                     arrow.position.y += 50;
//                 });
//             }
//         });
//     });
// };

// };
