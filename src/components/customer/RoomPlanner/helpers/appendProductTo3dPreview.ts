import {store} from 'store/customer';
import {
    CabinetMesh,
    RoomPlannerProductWithTemplate,
} from 'components/customer/RoomPlanner/types';
import {get3dTemplate} from 'service/ProductService';
import {cloneDeep} from 'lodash';
import {PartialRoom} from 'shared/types/PartialRoom';
import {renderCabinetGroup} from 'components/customer/RoomPlanner/helpers/renderCabinetGroup';
import {getRoomDimensionsComputed} from 'components/customer/RoomPlanner/helpers/getComputedDimension';
import {ProductApi} from 'components/customer/Product/store/productApi';

// this is the shape of product sent to backend for save
export interface ProductInterface {
    job_cabinet_id?: number;
    room_cab_number?: number;
    upper_shelves?: {
        upper_shelf_style: number;
        upper_shelf_position: number;
        upper_shelf_offset: number;
    }[];
    lower_shelves?: {
        lower_shelf_style: number;
        lower_shelf_position: number;
        lower_shelf_offset: number;
    }[];
    middle_shelves?: {
        middle_shelf_style: number;
        middle_shelf_position: number;
        middle_shelf_offset: number;
    }[];
    drawers?: {
        //
        id: number;
        drawer_face_height: number;
        drawer_style: number;
        drawer_type: number;
        drawer_runner_specs: string;
    }[];
    shelf_offset?: number[];
    shelf_position?: number[];
    shelf_style?: number[];
    cabinet_door_gap?: number;
    cabinet_width?: number;
    cabinet_height?: number;
    cabinet_depth?: number;
    cabinet_door_top?: number;
    cabinet_door_bottom?: number;
    cabinet_door_left?: number;
    cabinet_door_right?: number;
    cabinet_drawer_top?: number;
    cabinet_drawer_bottom?: number;
    cabinet_drawer_left?: number;
    cabinet_drawer_right?: number;
    cabinet_drawer_gap?: number;
    cabinet_total_drawer_height?: number;
    drawer_face_height?: number[];
    simple_shelves_amount?: number;
    simple_shelf_type?: number;
    cabinet_vert_shelves?: number;
    cabinet_hori_shelves?: number;
    cabinet_toekick?: number;
    cabinet_left_width?: number;
    cabinet_right_width?: number;
    cabinet_left_depth?: number;
    cabinet_right_depth?: number;
    cabinet_ladder_frames?: number;
    cabinet_return_panel_width?: number;
    microwave_opening_height?: number;
    oven_opening_height?: number;
    rangehood_opening_height?: number;
    cabinet_partition_height?: number;
    cabinet_partition_width?: number;
    cabinet_upper_partition_height?: string;
    cabinet_lower_partition_height?: string;
    cabinet_cover_void?: boolean;
    cabinet_void_width?: number;
    cabinet_simple_shelves?: boolean;
    cabinet_edge_L1?: number;
    cabinet_edge_L2?: number;
    cabinet_edge_W1?: number;
    cabinet_edge_W2?: number;
    cabinet_top?: number;
    cabinet_extend?: number;
    cabinet_applied_panel_depth?: number;
    cabinet_panel_width?: number;
    cabinet_panel_length?: number;
    cabinet_width_depth?: number;
    cabinet_length_depth?: number;
    cabinet_upper_filler_depth?: number;
    cabinet_width1?: number;
    cabinet_quantity?: number;
    cabinet_type?: number;
    include_drawer_faces?: number | boolean;

    width2?: number;
    width?: number;
    length_depth?: number;
    width_depth?: number;
}

export const appendProductTo3dPreview = async (
    productValues: ProductInterface,
    productType: number,
    room: PartialRoom,
    isUpdate = false,
    useRoomMaterials = false
) => {
    if (isUpdate) return;

    const product = cloneDeep(productValues);
    const state = store.getState();
    const materialsData = state.materials.materials;

    let roomCabNumber = productValues.room_cab_number;
    const jobCabinetId = Number(productValues.job_cabinet_id);

    if (typeof roomCabNumber === 'undefined' || roomCabNumber === -1) {
        const response = await store.dispatch(
            ProductApi.endpoints.getRoomProducts.initiate({
                roomId: Number(room.id),
            })
        );

        if (response.error) {
            throw new Error('Failed to fetch products');
        }

        roomCabNumber = response.data.length + 1;
    }

    if (materialsData.length > 0) {
        const materials = materialsData[0];
        const template = await get3dTemplate(productType);

        if (
            !template ||
            (template && Array.isArray(template) && template.length < 2)
        ) {
            // eslint-disable-next-line no-console -- only print this if there is no template
            console.warn('No template to render');
            return;
        }

        const shelvesAndDrawers: Partial<RoomPlannerProductWithTemplate> = {};
        if (product && product.upper_shelves) {
            shelvesAndDrawers.upper_shelves = product.upper_shelves.map(
                (shelf) => ({
                    shelf_style: shelf.upper_shelf_style,
                    shelf_position: shelf.upper_shelf_position,
                    shelf_offset: shelf.upper_shelf_offset,
                })
            );
        }

        if (product && product.lower_shelves) {
            shelvesAndDrawers.lower_shelves = product.lower_shelves.map(
                (shelf) => ({
                    shelf_style: shelf.lower_shelf_style,
                    shelf_position: shelf.lower_shelf_position,
                    shelf_offset: shelf.lower_shelf_offset,
                })
            );
        }

        if (product && product.middle_shelves) {
            shelvesAndDrawers.middle_shelves = product.middle_shelves.map(
                (shelf) => ({
                    shelf_style: shelf.middle_shelf_style,
                    shelf_position: shelf.middle_shelf_position,
                    shelf_offset: shelf.middle_shelf_offset,
                })
            );
        }

        if (product && product.shelf_position) {
            shelvesAndDrawers.shelves = product.shelf_position.map(
                (position, index) => ({
                    shelf_style: product?.shelf_style[Number(index)],
                    shelf_position: position,
                    shelf_offset: product?.shelf_offset[Number(index)],
                })
            );
        }

        if (product && product.drawers) {
            shelvesAndDrawers.drawers = product.drawers;
        }

        let cabinetLowerPartitionHeight;
        let cabinetUpperPartitionHeight;

        if (product.hasOwnProperty('cabinet_lower_partition_height')) {
            cabinetLowerPartitionHeight =
                parseInt(product?.cabinet_lower_partition_height, 10) / 100;
        }

        if (product.hasOwnProperty('cabinet_upper_partition_height')) {
            cabinetUpperPartitionHeight =
                parseInt(product?.cabinet_upper_partition_height, 10) / 100;
        }

        let materialsInfo = {
            carcaseThickness: materials?.carcase?.thickness,
            carcaseImage: materials?.carcase?.image,
            carcaseEdgeImage: materials?.edgeCarcase?.image,
            exteriorThickness: materials?.material?.thickness,
            exteriorImage: materials?.material?.image,
            exteriorEdgeImage: materials?.edgeMaterial?.image,
        };

        if (
            useRoomMaterials ||
            (typeof materials?.carcase === 'undefined' &&
                typeof materials?.material === 'undefined')
        ) {
            materialsInfo = {
                carcaseThickness: room.carcMaterial.thickness,
                carcaseImage: room.carcMaterial.image,
                carcaseEdgeImage: room.carcEdge.image,
                exteriorThickness: room.extMaterial.thickness,
                exteriorImage: room.extMaterial.image,
                exteriorEdgeImage: room.extEdge.image,
            };
        }

        const products: RoomPlannerProductWithTemplate[] = [];
        const productWithTemplate: RoomPlannerProductWithTemplate = {
            ...shelvesAndDrawers,
            job_cabinet_id: jobCabinetId ? jobCabinetId : roomCabNumber,
            room_cab_number: roomCabNumber,
            cabinet_door_gap: product?.cabinet_door_gap,
            template_3d: template,
            cabinet_carc_thickness: materialsInfo?.carcaseThickness,
            cabinet_ext_thickness: materialsInfo?.exteriorThickness,
            cabinet_width: product?.cabinet_width,
            cabinet_height: product?.cabinet_height,
            cabinet_depth: product?.cabinet_depth,
            cabinet_door_top: product?.cabinet_door_top,
            cabinet_door_bottom: product?.cabinet_door_bottom,
            cabinet_door_left: product?.cabinet_door_left,
            cabinet_door_right: product?.cabinet_door_right,
            cabinet_drawer_top: product?.cabinet_drawer_top,
            cabinet_drawer_bottom: product?.cabinet_drawer_bottom,
            cabinet_drawer_left: product?.cabinet_drawer_left,
            cabinet_drawer_right: product?.cabinet_drawer_right,
            cabinet_drawer_gap:
                product?.cabinet_drawer_gap || Number(room.drawerGap),
            cabinet_total_drawer_height: product?.cabinet_total_drawer_height,
            cabinet_partition_height: product?.cabinet_partition_height,
            cabinet_partition_width: product?.cabinet_partition_width,
            rangehood_opening_height: product?.rangehood_opening_height,
            microwave_opening_height: product?.microwave_opening_height,
            oven_opening_height: product?.oven_opening_height,
            cabinet_return_panel_width: product?.cabinet_return_panel_width,
            cabinet_cover_void: product?.cabinet_cover_void ? 1 : 0,
            cabinet_void_width: product?.cabinet_void_width,
            width2: product?.width2 || product.cabinet_left_width,
            width: product?.width || product.cabinet_right_width,
            width_depth: product?.width_depth || product.cabinet_left_depth,
            length_depth: product?.length_depth || product.cabinet_right_depth,
            cabinet_left_width: product?.cabinet_left_width,
            cabinet_right_width: product?.cabinet_right_width,
            cabinet_left_depth: product?.cabinet_left_depth,
            cabinet_right_depth: product?.cabinet_right_depth,
            cabinet_toekick: product.hasOwnProperty('cabinet_toekick')
                ? product.cabinet_toekick
                : Number(room.toeKickHeight),
            cabinet_ladder_frames: product?.cabinet_ladder_frames,
            cabinet_hori_shelves: product?.cabinet_hori_shelves,
            cabinet_vert_shelves: product?.cabinet_vert_shelves,
            cabinet_simple_shelves: product?.cabinet_simple_shelves,
            cabinet_edge_L1: product?.cabinet_edge_L1 || 1,
            cabinet_edge_L2: product?.cabinet_edge_L2 || 1,
            cabinet_edge_W1: product?.cabinet_edge_W1 || 1,
            cabinet_edge_W2: product?.cabinet_edge_W2 || 1,
            cabinet_top: product?.cabinet_top || Number(room.topPart),
            cabinet_extend: product?.cabinet_extend,
            cabinet_applied_panel_depth: product?.cabinet_applied_panel_depth,
            cabinet_panel_width: product?.cabinet_panel_width,
            cabinet_panel_length: product?.cabinet_panel_length,
            cabinet_width_depth: product?.cabinet_width_depth,
            cabinet_length_depth: product?.cabinet_length_depth,
            cabinet_upper_filler_depth: product?.cabinet_upper_filler_depth,
            cabinet_width1: product?.cabinet_width1,
            cabinet_upper_partition_height: cabinetUpperPartitionHeight,
            cabinet_lower_partition_height: cabinetLowerPartitionHeight,
            drawer_face_height: product?.drawer_face_height,
            simple_shelves_amount: product?.simple_shelves_amount || 1,
            simple_shelf_type: product?.simple_shelf_type || 1,
            cabinet_ext_image: materialsInfo?.exteriorImage,
            cabinet_carc_image: materialsInfo?.carcaseImage,
            cabinet_ext_edge_image: materialsInfo?.exteriorEdgeImage,
            cabinet_carc_edge_image: materialsInfo?.carcaseEdgeImage,
            include_drawer_faces:
                typeof product.include_drawer_faces == 'number'
                    ? product.include_drawer_faces == 1
                    : product.include_drawer_faces,
        };

        const quantity = product.cabinet_quantity;
        if (quantity > 1) {
            for (let i = 0; i < quantity; i++) {
                products.push({
                    ...productWithTemplate,
                    duplicate_id: `${roomCabNumber}-${i}`,
                });
            }
        } else {
            products.push(productWithTemplate);
        }

        const dimensions = getRoomDimensionsComputed();
        let row = 1;
        let currentWidth = Number(dimensions.computedWidth);
        const wallWidth = Number(dimensions.roomWidth / 2);

        const cabinetGroups: CabinetMesh[] = [];

        for (const product of products) {
            if (currentWidth + 120 > wallWidth) {
                currentWidth = Number(dimensions.computedWidth);
                row += 1;
            }

            const {widthIncrement, cabinetGroup} = renderCabinetGroup(
                product,
                {
                    ...dimensions,
                    positionX: currentWidth + 5,
                },
                String(room.id),
                row
            );

            cabinetGroup.userData.values = {
                ...cabinetGroup.userData.values,
                type: productValues.cabinet_type,
                room_id: room.id,
            };

            // Mark as quick add pending to prevent interactions during loading
            cabinetGroup.userData.isQuickAddPending = true;

            cabinetGroups.push(cabinetGroup);

            currentWidth += widthIncrement;
        }

        return cabinetGroups;
    }
};
