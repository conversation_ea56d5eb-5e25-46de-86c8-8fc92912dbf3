import {loadDataXML} from 'components/customer/Preview3D/helpers';
import {Batch, PreviewFormValues} from 'components/customer/Preview3D/types';
import {TemplateVariable} from 'components/customer/Preview3D/usePreview3DData';
import {
    RoomPlannerFormValues,
    CabinetMesh,
    RoomPlannerProductWithTemplate,
} from 'components/customer/RoomPlanner/types';
import {json2xml, xml2json} from 'xml-js';
import {createSceneXML} from 'components/customer/RoomPlanner/helpers/scene';
import adjustableLegs from 'components/customer/RoomPlanner/hardware/useAdjustableLegs';
import roomPlannerData from 'components/customer/RoomPlanner/values/useRoomPlannerData';
import {
    renderLShapedPanel,
    renderLShapedPanelForReturn,
    renderShelvesWithOvenVents,
    renderUpperAndLowerLeftRightUShapedShelves,
    renderUShapedPanel,
    renderCornerShapedPanel,
    renderShelvesWithRangehoodCutout,
    renderClipPanel,
    renderRadiusPanel,
    renderCornerShapePanelFor3PartPantry,
    renderLShapedPanelForPantryReturn,
    renderLShapedPanelForPantry,
    renderCornerShapePanelForPantry,
    renderUpperAndLowerUShapeShelves,
} from 'components/customer/Preview3DCommon/shapes/advancedShelf';

export const generateCabinetMesh = (
    valuesParams: RoomPlannerProductWithTemplate,
    isWireframeMode = false
) => {
    let values = {...valuesParams};
    const variables = JSON.parse(
        values.template_3d[0].attributes.variables
    ) as TemplateVariable;

    const lShapedValues =
        variables?.hasLShapedShelves || variables?.isCornerProduct
            ? {
                  cabinet_left_width: values.width2,
                  cabinet_right_depth: values.length_depth,
                  cabinet_right_width: values.width,
                  cabinet_left_depth: values.width_depth,
              }
            : {};

    values = {...values, ...lShapedValues};

    const json = xml2json(values.template_3d[1], {
        compact: false,
    });

    const isRobeProduct = /Robe.*\.xml$/i.test(
        values.template_3d[0]?.attributes.template
    );
    const noBottom = Boolean(
        variables?.fields.find((field) => field.name === 'noBottom')?.expression
    );
    const isOvenDrawer =
        values.template_3d[0]?.attributes.template == 'BaseOvenDrawers.xml';
    const isMicrowaveDrawer =
        values.template_3d[0]?.attributes.template ==
        'BaseMicrowaveDrawers.xml';
    if (!isOvenDrawer && !variables.isWallOvenProduct) {
        values.oven_opening_height = 0;
    }
    if (!isMicrowaveDrawer && !variables.isWallOvenProduct) {
        values.microwave_opening_height = 0;
    }

    if (variables.hasRangehoodVent) {
        values.rangehood_opening_height = 175;
    }

    const parsedJson = roomPlannerData.replaceDimensions(
        json,
        values as unknown as RoomPlannerFormValues,
        variables,
        isRobeProduct
    );
    const data = loadDataXML(json2xml(parsedJson, {compact: false})) as Batch;

    const cabinetGroup = createSceneXML(
        data,
        values,
        variables,
        true,
        values.include_drawer_faces,
        false,
        isWireframeMode,
        {
            exteriorThickness: values.cabinet_ext_thickness,
            carcaseThickness: values.cabinet_carc_thickness,
        }
    );
    cabinetGroup.userData.isRobeProduct = isRobeProduct;
    cabinetGroup.userData.noBottom = noBottom;

    if (
        Number(values.cabinet_adjustable_legs) > 0 &&
        !variables?.isUpperProduct &&
        !isRobeProduct &&
        !noBottom
    ) {
        adjustableLegs.applyDynamicAdjustableLegs(
            cabinetGroup,
            values as unknown as PreviewFormValues,
            variables?.hasLShapedShelves || variables?.isCornerProduct
        );
    }

    if (variables?.hasLShapedShelves) {
        try {
            const lShapedPanels = renderLShapedPanel(
                values as unknown as PreviewFormValues,
                variables
            );
            cabinetGroup.add(...lShapedPanels);
        } catch {}
    }

    if (Boolean(values.oven_opening_height)) {
        const ovenVents = renderShelvesWithOvenVents(
            values as unknown as PreviewFormValues,
            variables
        );
        cabinetGroup.add(...ovenVents);
    }

    if (variables?.isReturnProduct) {
        const returnLShapedPanels = renderLShapedPanelForReturn(
            values as unknown as PreviewFormValues,
            variables
        );

        if (returnLShapedPanels?.length > 0) {
            cabinetGroup.add(...returnLShapedPanels);
        }
    }

    if (
        !(
            typeof values.lower_shelves !== 'undefined' ||
            typeof values.upper_shelves !== 'undefined'
        )
    ) {
        const hasUshapeShelves = values.shelves?.some(
            (shelf) => shelf.insert_id === 9
        );

        if (hasUshapeShelves) {
            const ushapedPanels = renderUShapedPanel(
                values as unknown as PreviewFormValues,
                variables
            );
            if (ushapedPanels?.length > 0) {
                cabinetGroup.add(...ushapedPanels);
            }
        }
    }

    if (
        !(
            !values?.upper_shelves ||
            values?.shelves ||
            variables?.isReturnProduct
        )
    ) {
        const upperAndLowerUshapePanels = renderUpperAndLowerUShapeShelves(
            values as unknown as PreviewFormValues
        );

        if (upperAndLowerUshapePanels?.length > 0) {
            cabinetGroup.add(...upperAndLowerUshapePanels);
        }
    }

    if (Boolean(values?.upper_shelves && values?.shelves)) {
        const upperUshapedPanels = renderUpperAndLowerLeftRightUShapedShelves(
            values as unknown as PreviewFormValues
        );
        cabinetGroup.add(...upperUshapedPanels);
    }

    const isCornerPantry3Part =
        !(
            !values?.upper_shelves ||
            values?.shelves ||
            variables?.isReturnProduct
        ) &&
        variables?.isCornerProduct &&
        Boolean(values.middle_shelves);

    const isCornerPantry =
        !(
            !values?.upper_shelves ||
            values?.shelves ||
            variables?.isReturnProduct
        ) &&
        variables?.isCornerProduct &&
        !Boolean(values.middle_shelves);

    if (variables?.hasCornerShapedShelves) {
        if (isCornerPantry3Part) {
            const cornerShapedPanels = renderCornerShapePanelFor3PartPantry(
                values as unknown as PreviewFormValues,
                variables
            );
            cabinetGroup.add(...cornerShapedPanels);
        } else if (isCornerPantry) {
            const cornerShapeForPantry = renderCornerShapePanelForPantry(
                values as unknown as PreviewFormValues,
                variables
            );
            cabinetGroup.add(...cornerShapeForPantry);
        } else {
            const cornerShapedPanels = renderCornerShapedPanel(
                values as unknown as PreviewFormValues,
                variables
            );
            cabinetGroup.add(...cornerShapedPanels);
        }
    }

    if (variables?.hasRangehoodVent) {
        const rangehoodCutout = renderShelvesWithRangehoodCutout(
            values as unknown as PreviewFormValues
        );
        cabinetGroup.add(...rangehoodCutout);
    }

    if (variables?.isClipProduct) {
        const clipPanel = renderClipPanel(
            values as unknown as PreviewFormValues,
            variables
        );
        cabinetGroup.add(...clipPanel);
    }

    if (variables?.isRadiusProduct) {
        const radiusPanel = renderRadiusPanel(
            values as unknown as PreviewFormValues,
            variables
        );
        cabinetGroup.add(...radiusPanel);
    }

    if (variables.isReturnProduct && variables?.shelfType === 'PANTRY') {
        const pantryReturn = renderLShapedPanelForPantryReturn(
            values as unknown as PreviewFormValues,
            variables
        );
        cabinetGroup.add(...pantryReturn);
    }

    if (variables.isLShapedPantry) {
        const lShapedForPantry = renderLShapedPanelForPantry(
            values as unknown as PreviewFormValues
        );
        cabinetGroup.add(...lShapedForPantry);
    }

    return cabinetGroup as CabinetMesh;
};
