import {getProduct, getProductStyle} from 'service/ProductService';
import {ItemType} from 'shared/components/Product/Item';
import {PartialRoom} from 'shared/types/PartialRoom';
import {
    appendProductTo3dPreview,
    ProductInterface,
} from 'components/customer/RoomPlanner/helpers/appendProductTo3dPreview';

export const quickAppendProductTo3dPreview = async (
    item: ItemType,
    quantity: number,
    Width: number,
    room: PartialRoom
) => {
    const productData = await getProduct(item.id);

    if (productData) {
        const productStyle = await getProductStyle(productData.style);

        if (productStyle) {
            const product: ProductInterface = {
                cabinet_width: Width,
                cabinet_quantity: quantity,
                cabinet_type: item.id,
                shelf_position: [],
                shelf_style: [],
                shelf_offset: [],
            };

            switch (productStyle.styleName) {
                case 'Base':
                    product.cabinet_height = Number(room.baseHeight);
                    product.cabinet_depth = Number(room.baseDepth);

                    if (
                        productData?.text &&
                        productData.text.toLowerCase().includes('microwave')
                    ) {
                        product.microwave_opening_height = 1;
                    }

                    if (
                        productData?.text &&
                        productData.text.toLowerCase().includes('oven')
                    ) {
                        product.oven_opening_height = 1;
                    }

                    if (item.lshape != 0) {
                        product.cabinet_left_width = item.leftWidth;
                        product.cabinet_left_depth = Number(room.baseDepth);
                        product.cabinet_right_width = item.rightWidth;
                        product.cabinet_right_depth = Number(room.baseDepth);
                    }
                    break;

                case 'Pantry':
                case 'Tall':
                    product.cabinet_height = Number(room.tallHeight);
                    product.cabinet_depth = Number(room.tallDepth);
                    product.cabinet_partition_height = 3;
                    product.cabinet_partition_width = 3;
                    product.upper_shelves = [];
                    product.lower_shelves = [];
                    product.middle_shelves = [];
                    break;

                case 'Upper':
                    product.cabinet_height = Number(room.upperHeight);
                    product.cabinet_depth = Number(room.upperDepth);
                    break;
            }

            return appendProductTo3dPreview(
                product,
                productData.id,
                room,
                false,
                true,
                true
            );
        }
    }
};
