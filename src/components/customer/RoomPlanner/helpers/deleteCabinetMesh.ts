import {CabinetMesh} from 'components/customer/RoomPlanner/types';
import {store} from 'store/customer';
import Camera from 'components/customer/RoomPlanner/lib/useCameraSetup';
import {cabinetGroupListSet} from 'components/customer/RoomPlanner/store/roomPlannerSlice';
import productEvents from 'components/customer/RoomPlanner/lib/useProductEvents';
import * as THREE from 'three';

export const deleteCabinetMesh = (
    cabinetGroup: CabinetMesh,
    deleteAll = false
) => {
    if (typeof cabinetGroup.userData.disposeDimensionArrows === 'function') {
        cabinetGroup.userData.disposeDimensionArrows();
    }

    if (typeof cabinetGroup.userData.disposeMovementArrows === 'function') {
        cabinetGroup.userData.disposeMovementArrows();
    }

    const state = store.getState();
    const cabinetGroupList = state.roomPlanner.cabinetGroupList;

    let updatedList = cabinetGroupList.filter(
        (cabinet) => cabinet.uuid !== cabinetGroup.uuid
    );
    let deleteList: CabinetMesh[] = [];

    if (deleteAll) {
        [updatedList, deleteList] = cabinetGroupList.reduce(
            ([toKeep, toDelete], cabinet) => {
                if (
                    cabinet.userData.values.id ===
                    cabinetGroup.userData.values.id
                ) {
                    return [toKeep, [...toDelete, cabinet]];
                } else {
                    return [[...toKeep, cabinet], toDelete];
                }
            },
            [[], []] as [CabinetMesh[], CabinetMesh[]]
        );
    }

    store.dispatch(cabinetGroupListSet(updatedList));
    productEvents.setProducts(updatedList);

    if (deleteAll) {
        deleteList.forEach((cabinet) => {
            deleteMeshAndEvents(cabinet);
        });
    } else {
        deleteMeshAndEvents(cabinetGroup);
    }
};

const deleteMeshAndEvents = (cabinetGroup: CabinetMesh) => {
    Camera.scene.remove(cabinetGroup);
    productEvents.clearEvent(cabinetGroup.uuid);

    cabinetGroup.traverse((child) => {
        if (child instanceof THREE.Mesh) {
            (child.geometry as THREE.BufferGeometry).dispose();
            if (child.material instanceof THREE.Material) {
                child.material.dispose();
            } else if (Array.isArray(child.material)) {
                (child.material as THREE.Material[]).forEach((material) =>
                    material.dispose()
                );
            }
        }
    });
};
