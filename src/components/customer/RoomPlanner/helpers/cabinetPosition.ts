import * as THREE from 'three';
import {CabinetMesh} from 'components/customer/RoomPlanner/types';
import {store} from 'store/customer';
import {
    setUpperProductCurrentWall,
    rotatingSet,
} from 'components/customer/RoomPlanner/store/roomPlannerSlice';
import {getRoomDimensionsComputed} from 'components/customer/RoomPlanner/helpers/getComputedDimension';
import ObjectNameMap from 'components/customer/RoomPlanner/constants/ObjectNameMap';

const {roomWidth} = getRoomDimensionsComputed();

const halfRoomWidth = roomWidth / 2;
const halfRoomDepth = roomWidth / 2;

let offset = new THREE.Vector3();

export const getGroupWidth = (group: THREE.Group<THREE.Object3DEventMap>) => {
    const boundingBox = new THREE.Box3().setFromObject(group);

    const width = boundingBox.max.x - boundingBox.min.x;
    const height = boundingBox.max.y - boundingBox.min.y;
    const depth = boundingBox.max.z - boundingBox.min.z;

    return {width, height, depth};
};

export const getDirection = (rotation: number) => {
    const normalizedRotation =
        ((rotation % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);

    if (Math.abs(normalizedRotation) < 0.1) return 'normal';
    if (Math.abs(normalizedRotation - Math.PI / 2) < 0.1) return 'right';
    if (Math.abs(normalizedRotation - Math.PI) < 0.1) return 'back';
    if (Math.abs(normalizedRotation - (3 * Math.PI) / 2) < 0.1) return 'left';
    return 'normal';
};

export const handleSnapping = (
    cabinetGroup: THREE.Group<THREE.Object3DEventMap>,
    targetPos: THREE.Vector3,
    productsRef: THREE.Group<THREE.Object3DEventMap>[]
) => {
    const snapPosition = targetPos.clone();
    let snapped = false;
    let snapDistance = 50;

    const {width: latestWidth, depth: latestDepth} =
        getGroupWidth(cabinetGroup);

    productsRef?.forEach((otherProduct) => {
        if (otherProduct.userData.id == cabinetGroup.userData.id) return;
        const otherProductBox = new THREE.Box3().setFromObject(otherProduct);
        const size = new THREE.Vector3();
        otherProductBox.getSize(size);
        const productSize = Math.max(size.x, size.z);

        const directions = [
            new THREE.Vector3(1, 0, 0), // Right
            new THREE.Vector3(-1, 0, 0), // Left
            new THREE.Vector3(0, 0, 1), // Forward
            new THREE.Vector3(0, 0, -1), // Backward
        ];

        directions.forEach((dir, index) => {
            const axis = dir.clone().multiply(size);
            const proposedPos = otherProduct.position.clone().add(axis);
            const selectedCabinetDirection = getDirection(
                cabinetGroup.rotation.z
            );

            const otherCabinetDirection = getDirection(otherProduct.rotation.z);
            const cabinetRotations = {
                selected: {
                    isLeft: selectedCabinetDirection === 'left',
                    isRight: selectedCabinetDirection === 'right',
                    isBack: selectedCabinetDirection === 'back',
                    isNormal: selectedCabinetDirection === 'normal',
                },
                other: {
                    isLeft: otherCabinetDirection === 'left',
                    isRight: otherCabinetDirection === 'right',
                    isBack: otherCabinetDirection === 'back',
                    isNormal: otherCabinetDirection === 'normal',
                },
            };
            let isBackToBack = false;
            let isFacingRightAndSnapToLShaped = false;
            const backToBackData = {
                isFrontAndBack: false,
            };
            let allowDirections = [0, 1];

            if (
                (cabinetRotations.selected.isBack &&
                    cabinetRotations.other.isNormal) ||
                (cabinetRotations.other.isBack &&
                    cabinetRotations.selected.isNormal)
            ) {
                isBackToBack = true;
                backToBackData.isFrontAndBack = true;
                snapDistance = 100;
            }

            if (
                (cabinetRotations.selected.isLeft &&
                    cabinetRotations.other.isRight) ||
                (cabinetRotations.other.isLeft &&
                    cabinetRotations.selected.isRight)
            ) {
                isBackToBack = true;
                backToBackData.isFrontAndBack = false;
                snapDistance = 100;
            }

            if (
                (cabinetRotations.selected.isRight ||
                    cabinetRotations.other.isNormal) &&
                otherProduct.userData.isLShapedProduct
            ) {
                isFacingRightAndSnapToLShaped = true;
            }

            let distancePos = targetPos.clone();
            const snappedProduct = getGroupWidth(otherProduct);

            if (
                productSize > 200 &&
                !otherProduct.userData.isLShapedProduct &&
                selectedCabinetDirection !== otherCabinetDirection
            ) {
                if (cabinetRotations.selected.isRight) {
                    proposedPos.copy(otherProduct.position);
                } else if (cabinetRotations.selected.isLeft) {
                    proposedPos.copy(otherProduct.position);
                    proposedPos.x = proposedPos.x - latestWidth;
                    proposedPos.z = proposedPos.z - 30;
                } else if (cabinetRotations.selected.isBack) {
                    proposedPos.copy(otherProduct.position);
                    proposedPos.x = proposedPos.x + 30;
                    proposedPos.z = proposedPos.z - snappedProduct.depth;
                } else if (cabinetRotations.selected.isNormal) {
                    proposedPos.copy(otherProduct.position);
                    proposedPos.x = proposedPos.x - latestWidth;
                    proposedPos.z = proposedPos.z + snappedProduct.depth;
                }
            }

            // Modify the anchor position for snapping
            if (isBackToBack) {
                if (
                    !backToBackData.isFrontAndBack &&
                    cabinetRotations.selected.isRight
                ) {
                    distancePos = new THREE.Vector3(
                        targetPos.x - latestWidth / 2,
                        targetPos.y,
                        targetPos.z - latestDepth / 2
                    );
                }

                if (
                    !backToBackData.isFrontAndBack &&
                    cabinetRotations.selected.isLeft
                ) {
                    distancePos = new THREE.Vector3(
                        targetPos.x + latestWidth / 2,
                        targetPos.y,
                        targetPos.z + latestDepth
                    );
                }

                if (
                    backToBackData.isFrontAndBack &&
                    cabinetRotations.selected.isNormal
                ) {
                    distancePos = new THREE.Vector3(
                        targetPos.x + latestWidth,
                        targetPos.y,
                        targetPos.z - latestDepth / 2
                    );
                }

                if (
                    backToBackData.isFrontAndBack &&
                    cabinetRotations.selected.isBack
                ) {
                    distancePos = new THREE.Vector3(
                        targetPos.x - latestWidth,
                        targetPos.y,
                        targetPos.z + latestDepth / 2
                    );
                }
            } else {
                if (cabinetRotations.selected.isNormal) {
                    if (index === 0) {
                        distancePos = new THREE.Vector3(
                            targetPos.x,
                            targetPos.y,
                            targetPos.z + (snappedProduct.depth - latestDepth)
                        );
                    }

                    if (index === 1) {
                        distancePos = new THREE.Vector3(
                            targetPos.x - (snappedProduct.width - latestWidth),
                            targetPos.y,
                            targetPos.z + (snappedProduct.depth - latestDepth)
                        );
                    }
                }

                if (cabinetRotations.selected.isRight) {
                    if (index === 2) {
                        distancePos = new THREE.Vector3(
                            targetPos.x + (snappedProduct.width - latestWidth),
                            targetPos.y,
                            targetPos.z + (snappedProduct.depth - latestDepth)
                        );
                    }

                    if (
                        index === 1 &&
                        productSize > 200 &&
                        !otherProduct.userData.isLShapedProduct
                    ) {
                        distancePos = new THREE.Vector3(
                            targetPos.x - latestWidth,
                            targetPos.y,
                            targetPos.z - latestDepth
                        );
                    }
                    if (index === 2 && otherProduct.userData.isLShapedProduct) {
                        distancePos = new THREE.Vector3(
                            targetPos.x - 50,
                            targetPos.y,
                            targetPos.z + (snappedProduct.depth - latestDepth)
                        );
                    }
                    if (index === 3) {
                        distancePos = new THREE.Vector3(
                            targetPos.x + (snappedProduct.depth - latestDepth),
                            targetPos.y,
                            targetPos.z
                        );
                    }
                }

                if (cabinetRotations.selected.isLeft) {
                    if (index === 2) {
                        distancePos = new THREE.Vector3(
                            targetPos.x - (snappedProduct.width - latestWidth),
                            targetPos.y,
                            targetPos.z
                        );
                    }

                    if (index === 3) {
                        distancePos = new THREE.Vector3(
                            targetPos.x - (snappedProduct.width - latestWidth),
                            targetPos.y,
                            targetPos.z - (snappedProduct.depth - latestDepth)
                        );
                    }
                }

                if (cabinetRotations.selected.isBack) {
                    if (index === 1) {
                        distancePos = new THREE.Vector3(
                            targetPos.x,
                            targetPos.y,
                            targetPos.z - (snappedProduct.depth - latestDepth)
                        );
                    }

                    if (index === 0) {
                        distancePos = new THREE.Vector3(
                            targetPos.x + (snappedProduct.width - latestWidth),
                            targetPos.y,
                            targetPos.z - (snappedProduct.depth - latestDepth)
                        );
                    }
                }
            }

            const distance = proposedPos.distanceTo(distancePos);

            if (
                distance < snapDistance &&
                (selectedCabinetDirection === otherCabinetDirection ||
                    isBackToBack ||
                    isFacingRightAndSnapToLShaped)
            ) {
                if (!isBackToBack) {
                    if (cabinetRotations.selected.isNormal) {
                        if (index === 0 || index === 1) {
                            proposedPos.z =
                                proposedPos.z -
                                (snappedProduct.depth - latestDepth);
                        }

                        if (index === 1) {
                            proposedPos.x =
                                proposedPos.x -
                                latestWidth +
                                snappedProduct.width;
                        }
                    } else if (cabinetRotations.selected.isRight) {
                        if (index === 2) {
                            proposedPos.z =
                                proposedPos.z -
                                (snappedProduct.depth - latestDepth);
                        }

                        if (index === 3 || index === 2) {
                            proposedPos.x =
                                proposedPos.x -
                                (snappedProduct.width - latestWidth);
                        }
                        allowDirections = [2, 3];
                    } else if (cabinetRotations.selected.isLeft) {
                        if (index === 3) {
                            proposedPos.z =
                                proposedPos.z -
                                (latestDepth - snappedProduct.depth);
                        }
                        if (index === 2 || index === 3) {
                            proposedPos.x =
                                proposedPos.x -
                                latestWidth +
                                snappedProduct.width;
                        }
                        allowDirections = [2, 3];
                    } else {
                        if (index === 0) {
                            proposedPos.x =
                                proposedPos.x +
                                latestWidth -
                                snappedProduct.width;
                        }

                        if (index === 0 || index === 1) {
                            proposedPos.z =
                                proposedPos.z +
                                (snappedProduct.depth - latestDepth);
                        }
                    }

                    // Fix L-shaped front face snapping (index === 2)
                    if (isFacingRightAndSnapToLShaped && index === 2) {
                        proposedPos.x =
                            proposedPos.x + snappedProduct.width - 4;
                        proposedPos.z = proposedPos.z + 4;
                    }
                } else {
                    const offset = 7;

                    if (index === 2) {
                        if (
                            backToBackData.isFrontAndBack &&
                            cabinetRotations.selected.isNormal
                        ) {
                            proposedPos.x = proposedPos.x - latestWidth;
                            proposedPos.z =
                                proposedPos.z + latestDepth - offset;
                            snapped = index === 2;
                        }
                    }

                    if (index === 3) {
                        if (
                            backToBackData.isFrontAndBack &&
                            cabinetRotations.selected.isBack
                        ) {
                            proposedPos.x = proposedPos.x + latestWidth;
                            proposedPos.z =
                                proposedPos.z - latestDepth + offset;
                            snapped = index === 3;
                        }
                    }

                    if (index === 0) {
                        if (
                            !backToBackData.isFrontAndBack &&
                            cabinetRotations.selected.isRight
                        ) {
                            proposedPos.x =
                                proposedPos.x + latestWidth - offset;
                            proposedPos.z = proposedPos.z + latestDepth;
                            snapped = index === 0;
                        }
                    }

                    if (index === 1) {
                        if (
                            !backToBackData.isFrontAndBack &&
                            cabinetRotations.selected.isLeft
                        ) {
                            proposedPos.x =
                                proposedPos.x - latestWidth + offset;
                            proposedPos.z = proposedPos.z - latestDepth;
                            snapped = index === 1;
                        }
                    }
                }

                snapPosition.copy(proposedPos);

                if (!isBackToBack) {
                    snapped = allowDirections.includes(index);
                }

                // Enhanced L-shaped cabinet direction allowance
                if (isFacingRightAndSnapToLShaped) {
                    allowDirections.push(2); // Front face
                }

                // Allow left and right snapping for L-shaped cabinets
                if (otherProduct.userData.isLShapedProduct) {
                    allowDirections.push(0, 1); // Right and left faces
                }

                if (cabinetGroup.userData.isFloorProduct) {
                    snapPosition.copy(otherProduct.position);
                }
            }
            // Fix larger cabinet snapping with dynamic positioning
            if (
                distance < snapDistance &&
                selectedCabinetDirection != otherCabinetDirection &&
                productSize > 200 &&
                !otherProduct.userData.isLShapedProduct
            ) {
                if (cabinetRotations.selected.isRight) {
                    // Use dynamic positioning based on actual cabinet dimensions
                    const targetBox = new THREE.Box3().setFromObject(
                        otherProduct
                    );
                    proposedPos.x =
                        targetBox.max.x +
                        latestWidth -
                        snappedProduct.width -
                        4;
                    proposedPos.z = targetBox.max.z + latestDepth;
                }

                if (cabinetRotations.selected.isLeft) {
                    const targetBox = new THREE.Box3().setFromObject(
                        otherProduct
                    );
                    proposedPos.x =
                        targetBox.min.x -
                        latestWidth +
                        snappedProduct.width +
                        4;
                    proposedPos.z = targetBox.min.z - latestDepth;
                }

                if (cabinetRotations.selected.isNormal) {
                    const targetBox = new THREE.Box3().setFromObject(
                        otherProduct
                    );
                    proposedPos.x =
                        targetBox.max.x - latestWidth - snappedProduct.width;
                    proposedPos.z =
                        targetBox.max.z +
                        latestDepth -
                        snappedProduct.depth -
                        4;
                }

                if (cabinetRotations.selected.isBack) {
                    const targetBox = new THREE.Box3().setFromObject(
                        otherProduct
                    );
                    proposedPos.x =
                        targetBox.min.x + latestWidth + snappedProduct.width;
                    proposedPos.z =
                        targetBox.min.z -
                        latestDepth +
                        snappedProduct.depth +
                        4;
                }

                snapPosition.copy(proposedPos);
                if (!isBackToBack) {
                    snapped = allowDirections.includes(index);
                }
            }
        });
    });
    // snapped = false;
    return {
        snapPosition,
        snapped: cabinetGroup.userData.ignoreSnapping ? false : snapped,
        latestWidth,
        latestDepth,
    };
};

export const checkCabinetOverlap = (
    cabinet: CabinetMesh,
    targetPos: THREE.Vector3,
    otherCabinets: CabinetMesh[],
    buffer = 0
): boolean => {
    // Create a temporary box for the cabinet at the target position
    const tempBox = new THREE.Box3().setFromObject(cabinet);
    const offset = new THREE.Vector3().subVectors(targetPos, cabinet.position);
    tempBox.translate(offset);

    // Check against all other cabinets
    for (const otherCabinet of otherCabinets) {
        if (otherCabinet.userData.id === cabinet.userData.id) continue; // Skip self

        const otherBox = new THREE.Box3().setFromObject(otherCabinet);

        // Add a small buffer to prevent exact touching
        otherBox.expandByScalar(buffer);

        if (tempBox.intersectsBox(otherBox)) {
            return true;
        }
    }
    return false;
};

const setRotating = () => {
    const state = store.getState();
    const isRotating = state.roomPlanner.isRotating;

    store.dispatch(rotatingSet(!isRotating));
};

export const productClamping = (
    scene: THREE.Scene,
    group: THREE.Group<THREE.Object3DEventMap>,
    wallGroup: THREE.Mesh<THREE.ExtrudeGeometry, THREE.MeshStandardMaterial>,
    camera: THREE.Camera,
    mouse: THREE.Vector2,
    newPosition: THREE.Vector3,
    roomType: string,
    isUpperProduct: boolean
) => {
    const currentWall = store.getState().roomPlanner.upperProductCurrentWall;

    const minX = 0;
    const maxX = 0;
    const minZ = 0;
    const maxZ = 0;

    const wallOffset = 26;

    const WallBox = new THREE.Box3();

    wallGroup.children.forEach((child) => {
        if ((child as THREE.Mesh).isMesh) {
            const mesh = child as THREE.Mesh;
            const geometry = mesh.geometry;
            // Check if the geometry has valid position attributes
            if (
                geometry.attributes.position &&
                geometry.attributes.position.count > 0 &&
                !hasNaNPositions(geometry)
            ) {
                geometry.computeBoundingBox();

                if (geometry.boundingBox) {
                    const childBox = geometry.boundingBox
                        .clone()
                        .applyMatrix4(mesh.matrixWorld);
                    WallBox.union(childBox);
                }
            }
        }
    });

    /**
     * Checks if a geometry's position attribute contains any NaN values
     * @param {THREE.BufferGeometry} geometry - The geometry to check
     * @return {boolean} True if NaN values are found, false otherwise
     */
    function hasNaNPositions(geometry: THREE.BufferGeometry): boolean {
        if (!geometry.attributes.position) return false;
        const positions = geometry.attributes.position.array;
        for (let i = 0; i < positions.length; i++) {
            const value = positions[Number(i)];
            if (value !== value) return true;
        }
        return false;
    }

    const {width: latestWidth, depth: latestDepth} = getGroupWidth(group);
    const direction = getDirection(group.rotation.z);

    const clampedResult = baseProductClamping(
        group,
        minX,
        maxX,
        minZ,
        maxZ,
        direction,
        wallOffset,
        latestWidth,
        latestDepth,
        WallBox,
        roomType,
        wallGroup
    );

    const raycaster = new THREE.Raycaster();
    raycaster.setFromCamera(mouse, camera);
    raycaster.layers.set(0);
    const hits = raycaster.intersectObjects(wallGroup.children);

    const visibleHits = hits.filter((hit) => hit.object.visible);

    if (visibleHits.length > 0) {
        const wallName = visibleHits[0].object;
        if (wallName.name !== currentWall.name && currentWall !== null) {
            const wallRotations = {
                'wall:back': 0,
                'wall:front': Math.PI,
                'wall:left': Math.PI / 2,
                'wall:right': -Math.PI / 2,
                leftWidthWall: Math.PI / 2,
                leftWidth2Wall: -Math.PI / 2,
                rightWidthWall: 0,
                rightWidth2Wall: Math.PI,
                leftDepthWall: Math.PI,
                rightDepthWall: -Math.PI / 2,
            };

            if (wallName.name in wallRotations) {
                setRotating();
                const box = new THREE.Box3().setFromObject(group);
                const oldCenter = box.getCenter(new THREE.Vector3());

                group.rotation.z =
                    wallRotations[wallName.name as keyof typeof wallRotations];

                group.updateMatrixWorld();

                const newBox = new THREE.Box3().setFromObject(group);
                const newCenter = newBox.getCenter(new THREE.Vector3());

                if (offset.lengthSq() < 0.0001) {
                    offset = newCenter.clone().sub(oldCenter);
                } else {
                    offset = new THREE.Vector3(0, 0, 0);
                }

                store.dispatch(
                    setUpperProductCurrentWall(wallName as THREE.Mesh)
                );
            }
        }
    }

    if (isUpperProduct) {
        if (roomType === 'RECTANGULAR') {
            const upperClampedResult = upperProductClamping(
                clampedResult.minX,
                clampedResult.maxX,
                clampedResult.minZ,
                clampedResult.maxZ,
                direction,
                wallOffset,
                latestWidth,
                latestDepth,
                WallBox
            );

            const clamped = newPosition.clone().sub(offset);
            clamped.x = THREE.MathUtils.clamp(
                clamped.x,
                upperClampedResult.minX,
                upperClampedResult.maxX
            );
            clamped.z = THREE.MathUtils.clamp(
                clamped.z,
                upperClampedResult.minZ,
                upperClampedResult.maxZ
            );

            group.position.copy(clamped);
        } else {
            const Lshapeclamped = autoRotateClamping(
                group,
                currentWall,
                newPosition
            );
            group.position.copy(Lshapeclamped);
        }
    } else {
        const clamped = newPosition.clone().sub(offset);
        clamped.x = THREE.MathUtils.clamp(
            clamped.x,
            clampedResult.minX,
            clampedResult.maxX
        );
        clamped.z = THREE.MathUtils.clamp(
            clamped.z,
            clampedResult.minZ,
            clampedResult.maxZ
        );
        return clamped;
    }

    // group.position.copy(clamped);
};

export const upperProductClamping = (
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    direction: string,
    wallOffset: number,
    latestWidth: number,
    latestDepth: number,
    WallBox: THREE.Box3
) => {
    const currentWall = store.getState().roomPlanner.upperProductCurrentWall;
    switch (currentWall.name) {
        case 'wall:back':
            if (direction === 'normal') {
                maxZ = WallBox.min.z + latestDepth - 4 + wallOffset;
            }
            if (direction === 'back') {
                maxZ = WallBox.min.z + 5 + wallOffset;
            }
            if (direction === 'right') {
                maxZ = WallBox.min.z + latestDepth + wallOffset + 2;
            }
            if (direction === 'left') {
                maxZ = WallBox.min.z + wallOffset + 2;
            }
            break;
        case 'wall:front':
            if (direction === 'normal') {
                minZ = WallBox.max.z - 5 - wallOffset;
            }
            if (direction === 'back') {
                minZ = WallBox.max.z - latestDepth + 4 - wallOffset;
            }
            if (direction === 'right') {
                minZ = WallBox.max.z - wallOffset;
            }
            if (direction === 'left') {
                minZ = WallBox.max.z - latestDepth - wallOffset;
            }
            break;
        case 'wall:left':
            if (direction === 'normal') {
                maxX = WallBox.min.x + wallOffset;
            }
            if (direction === 'back') {
                maxX = WallBox.min.x + latestWidth + wallOffset;
            }
            if (direction === 'right') {
                maxX = WallBox.min.x + latestWidth - 4 + wallOffset;
            }
            if (direction === 'left') {
                maxX = WallBox.min.x + 5 + wallOffset;
            }
            break;
        case 'wall:right':
            if (direction === 'normal') {
                minX = WallBox.max.x - latestWidth - wallOffset;
            }
            if (direction === 'back') {
                minX = WallBox.max.x - wallOffset;
            }
            if (direction === 'right') {
                minX = WallBox.max.x - 5 - wallOffset;
            }
            if (direction === 'left') {
                minX = WallBox.max.x - latestWidth + 2 - wallOffset;
            }
            break;
    }

    return {minX, maxX, minZ, maxZ};
};

export const baseProductClamping = (
    group: THREE.Group,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    direction: string,
    wallOffset: number,
    latestWidth: number,
    latestDepth: number,
    WallBox: THREE.Box3,
    roomType: string,
    wallGroup: THREE.Mesh<THREE.ExtrudeGeometry, THREE.MeshStandardMaterial>
) => {
    if (roomType === 'RECTANGULAR') {
        if (direction === 'normal') {
            minZ = WallBox.min.z + latestDepth - 4 + wallOffset;
            maxZ = WallBox.max.z - 5 - wallOffset;

            minX = WallBox.min.x + wallOffset;
            maxX = WallBox.max.x - latestWidth - wallOffset;
        }
        if (direction === 'back') {
            minZ = WallBox.min.z + 5 + wallOffset;
            maxZ = WallBox.max.z - latestDepth + 4 - wallOffset;

            minX = WallBox.min.x + latestWidth + wallOffset;
            maxX = WallBox.max.x - wallOffset;
        }
        if (direction === 'right') {
            minZ = WallBox.min.z + latestDepth + wallOffset + 2;
            maxZ = WallBox.max.z - wallOffset;

            minX = WallBox.min.x + latestWidth - 4 + wallOffset;
            maxX = WallBox.max.x - 5 - wallOffset;
        }
        if (direction === 'left') {
            minZ = WallBox.min.z + wallOffset + 2;
            maxZ = WallBox.max.z - latestDepth - wallOffset;

            minX = WallBox.min.x + 5 + wallOffset;
            maxX = WallBox.max.x - latestWidth + 2 - wallOffset;
        }
    } else if (roomType === 'LSHAPED') {
        // console.log(currentWall.name);
        const leftWidth2Wall = (
            wallGroup.parent as THREE.Scene
        ).getObjectByName('leftWidth2Wall') as THREE.Mesh;
        const rightWidth2Wall = (
            wallGroup.parent as THREE.Scene
        ).getObjectByName('rightWidth2Wall') as THREE.Mesh;

        const leftWidth2Wallbox = new THREE.Box3().setFromObject(
            leftWidth2Wall
        );
        const rightWidth2Wallbox = new THREE.Box3().setFromObject(
            rightWidth2Wall
        );

        const leftWidth2Wallboxsize = new THREE.Vector3();
        leftWidth2Wallbox.getSize(leftWidth2Wallboxsize);

        const rightWidth2Wallboxsize = new THREE.Vector3();
        rightWidth2Wallbox.getSize(rightWidth2Wallboxsize);

        if (direction === 'normal') {
            minZ = WallBox.min.z + latestDepth - 4 + wallOffset;
            minX = WallBox.min.x + wallOffset;
            maxZ = WallBox.max.z - 5 - wallOffset;
            maxX = WallBox.max.x - latestWidth - wallOffset;

            if (
                group.position.x + rightWidth2Wallboxsize.z >
                rightWidth2Wallbox.min.x
            ) {
                maxZ = rightWidth2Wallboxsize.z;
            }
            if (
                group.position.z - leftWidth2Wallboxsize.x - 10 >
                    leftWidth2Wallbox.min.z &&
                group.position.x + rightWidth2Wallboxsize.z <
                    rightWidth2Wallbox.min.x
            ) {
                maxX = -15;
            }
        }
        if (direction === 'back') {
            minZ = WallBox.min.z + 5 + wallOffset;
            minX = WallBox.min.x + latestWidth + wallOffset;
            maxZ = WallBox.max.z - latestDepth + 4 - wallOffset;
            maxX = WallBox.max.x - wallOffset;
            if (
                group.position.x + rightWidth2Wallboxsize.z >
                rightWidth2Wallbox.min.x
            ) {
                maxZ = -latestDepth + rightWidth2Wallboxsize.z;
            }
            if (
                group.position.z + latestDepth - leftWidth2Wallboxsize.x - 10 >
                    leftWidth2Wallbox.min.z &&
                group.position.x + rightWidth2Wallboxsize.z <
                    rightWidth2Wallbox.min.x
            ) {
                maxX = -15 + latestWidth;
            }
        }
        if (direction === 'right') {
            minZ = WallBox.min.z + latestDepth + wallOffset + 2;
            minX = WallBox.min.x + latestWidth - 4 + wallOffset;
            maxZ = WallBox.max.z - wallOffset;
            maxX = WallBox.max.x - 5 - wallOffset;

            if (
                group.position.x + rightWidth2Wallboxsize.z >
                rightWidth2Wallbox.min.x
            ) {
                maxZ = rightWidth2Wallboxsize.z;
            }
            if (
                group.position.z - leftWidth2Wallboxsize.x - 10 >
                    leftWidth2Wallbox.min.z &&
                group.position.x + rightWidth2Wallboxsize.z <
                    rightWidth2Wallbox.min.x
            ) {
                maxX = -40 + latestWidth;
            }
        }
        if (direction === 'left') {
            minZ = WallBox.min.z + wallOffset + 2;
            minX = WallBox.min.x + 5 + wallOffset;
            maxZ = WallBox.max.z - latestDepth - wallOffset;
            maxX = WallBox.max.x - latestWidth + 2 - wallOffset;

            if (
                group.position.z + latestDepth - leftWidth2Wallboxsize.x - 10 >
                leftWidth2Wallbox.min.z
            ) {
                maxX = -10 - leftWidth2Wallboxsize.x;
            }

            if (
                group.position.x + rightWidth2Wallboxsize.z >
                    rightWidth2Wallbox.min.x &&
                group.position.z + latestDepth - leftWidth2Wallboxsize.x - 10 <
                    leftWidth2Wallbox.min.z
            ) {
                maxZ = -latestDepth + rightWidth2Wallboxsize.z;
            }
        }
    }

    return {minX, maxX, minZ, maxZ};
};

export const getWallBehindCabinet = (
    cabinetGroup: THREE.Group<THREE.Object3DEventMap>,
    scene: THREE.Scene,
    camera: THREE.Camera,
    roomType: string,
    isUpperProduct: boolean
) => {
    offset = new THREE.Vector3(0, 0, 0);
    let wallBehind = '';

    const wallGroup = scene.children.find(
        (ch) =>
            ch.name === ObjectNameMap.rectangularWall ||
            ch.name === ObjectNameMap.lShapeWall
    ) as THREE.Group;

    if (!isUpperProduct) {
        const direction = getDirection(cabinetGroup.rotation.z);

        if (roomType === 'RECTANGULAR') {
            switch (direction) {
                case 'normal':
                    wallBehind = 'wall:back';
                    break;
                case 'back':
                    wallBehind = 'wall:front';
                    break;
                case 'right':
                    wallBehind = 'wall:left';
                    break;
                case 'left':
                    wallBehind = 'wall:right';
                    break;
            }
        } else if (roomType === 'LSHAPED') {
            switch (direction) {
                case 'normal':
                    wallBehind = 'rightWidthWall';
                    break;
                case 'back':
                    wallBehind = 'leftDepthWall';
                    break;
                case 'right':
                    wallBehind = 'leftWidthWall';
                    break;
                case 'left':
                    wallBehind = 'rightDepthWall';
                    break;
            }
        }

        if (!wallBehind) return null;

        const closestWall = (wallGroup.parent as THREE.Scene).getObjectByName(
            wallBehind
        ) as THREE.Mesh<THREE.ExtrudeGeometry, THREE.MeshStandardMaterial>;

        return closestWall;
    } else {
        const cabinetPosition = new THREE.Vector3();
        cabinetGroup.getWorldPosition(cabinetPosition);

        const direction = getDirection(cabinetGroup.rotation.z);

        const rayDirection = new THREE.Vector3(0, 0, 1);

        switch (direction) {
            case 'normal':
                rayDirection.set(0, 0, -1);
                break;
            case 'back':
                rayDirection.set(0, 0, 1);
                break;
            case 'right':
                rayDirection.set(-1, 0, 0);
                break;
            case 'left':
                rayDirection.set(1, 0, 0);
                break;
        }

        const raycaster = new THREE.Raycaster();
        raycaster.camera = camera;

        const intersects = raycaster
            .intersectObjects(wallGroup.children, true)
            .sort((a, b) => a.distance - b.distance);

        const wallHits = intersects.filter((hit) => hit.object.visible);

        if (wallHits.length > 0) {
            const closestWall = wallHits[0].object;
            return closestWall;
        }

        return null;
    }
};

export const autoRotateClamping = (
    group: THREE.Group,
    wallName: THREE.Mesh,
    newPosition: THREE.Vector3
) => {
    const {width: latestWidth, depth: latestDepth} = getGroupWidth(group);

    const wallbox = new THREE.Box3().setFromObject(wallName);
    const wallsize = new THREE.Vector3();
    wallbox.getSize(wallsize);

    let minX = -halfRoomWidth + 31.1;
    let maxX = halfRoomWidth - latestWidth + 40;
    let minZ = -halfRoomDepth + latestDepth - 15;
    let maxZ = halfRoomDepth - 16.5;

    const direction = getDirection(group.rotation.z);

    switch (wallName.name) {
        case 'wall:back':
            if (direction === 'back') {
                minZ = minZ - latestDepth + 5;
                maxZ = minZ - latestDepth + 5;
                minX = minX + latestWidth;
                maxX = maxX + latestWidth;
            }
            if (direction === 'normal') {
                minZ = minZ;
                maxZ = minZ;
                minX = minX;
                maxX = maxX;
            }
            if (direction === 'left') {
                minZ = minZ - latestDepth + 5;
                maxZ = minZ - latestDepth + 5;
                minX = minX + 5;
                maxX = maxX + 5;
            }
            if (direction === 'right') {
                minZ = minZ + 5;
                maxZ = minZ + 5;
                minX = minX + latestWidth - 5;
                maxX = maxX + latestWidth - 5;
            }
            break;
        case 'wall:front':
            if (direction === 'back') {
                minZ = maxZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + latestWidth;
                maxX = maxX + latestWidth;
            }
            if (direction === 'normal') {
                minZ = maxZ;
                maxZ = maxZ;
                minX = minX;
                maxX = maxX;
            }
            if (direction === 'left') {
                minZ = maxZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + 5;
                maxX = maxX + 5;
            }
            if (direction === 'right') {
                minZ = maxZ;
                maxZ = maxZ;
                minX = minX + latestWidth - 5;
                maxX = maxX + latestWidth - 5;
            }
            break;
        case 'wall:left':
            if (direction === 'back') {
                minZ = minZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + latestWidth;
                maxX = minX;
            }
            if (direction === 'normal') {
                minZ = minZ;
                maxZ = maxZ;
                minX = minX;
                maxX = minX;
            }
            if (direction === 'left') {
                minZ = minZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + 5;
                maxX = minX + 5;
            }
            if (direction === 'right') {
                minZ = minZ + 5;
                maxZ = maxZ + 5;
                minX = minX + latestWidth - 5;
                maxX = minX - latestWidth - 5;
            }
            break;
        case 'wall:right':
            if (direction === 'back') {
                minZ = minZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = maxX + latestWidth;
                maxX = maxX + latestWidth;
            }
            if (direction === 'normal') {
                minZ = minZ;
                maxZ = maxZ;
                minX = maxX;
                maxX = maxX;
            }
            if (direction === 'left') {
                minZ = minZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = maxX + 5;
                maxX = maxX + 5;
            }
            if (direction === 'right') {
                minZ = minZ + 5;
                maxZ = maxZ + 5;
                minX = maxX + latestWidth - 5;
                maxX = maxX - latestWidth - 5;
            }
            break;
        case 'leftWidthWall':
            if (direction === 'back') {
                minZ = minZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + latestWidth;
                maxX = minX;
            }
            if (direction === 'normal') {
                minZ = minZ;
                maxZ = maxZ;
                minX = minX;
                maxX = minX;
            }
            if (direction === 'left') {
                minZ = minZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + 5;
                maxX = minX + 5;
            }
            if (direction === 'right') {
                minZ = minZ + 5;
                maxZ = maxZ + 5;
                minX = minX + latestWidth - 5;
                maxX = minX - latestWidth - 5;
            }
            break;
        case 'rightWidthWall':
            if (direction === 'back') {
                minZ = minZ - latestDepth + 5;
                maxZ = minZ - latestDepth + 5;
                minX = minX + latestWidth;
                maxX = maxX + latestWidth;
            }
            if (direction === 'normal') {
                minZ = minZ;
                maxZ = minZ;
                minX = minX;
                maxX = maxX;
            }
            if (direction === 'left') {
                minZ = minZ - latestDepth + 5;
                maxZ = minZ - latestDepth + 5;
                minX = minX + 5;
                maxX = maxX + 5;
            }
            if (direction === 'right') {
                minZ = minZ + 5;
                maxZ = minZ + 5;
                minX = minX + latestWidth - 5;
                maxX = maxX + latestWidth - 5;
            }
            break;
        case 'leftDepthWall':
            if (direction === 'back') {
                minZ = maxZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + latestWidth;
                maxX = wallbox.max.x - wallsize.z;
            }
            if (direction === 'normal') {
                minZ = maxZ;
                maxZ = maxZ;
                minX = minX;
                maxX = wallbox.max.x - wallsize.z - latestWidth;
            }
            if (direction === 'left') {
                minZ = maxZ - latestDepth + 5;
                maxZ = maxZ - latestDepth + 5;
                minX = minX + 5;
                maxX = wallbox.max.x - wallsize.z - latestWidth;
            }
            if (direction === 'right') {
                minZ = maxZ;
                maxZ = maxZ;
                minX = minX + latestWidth - 5;
                maxX = wallbox.max.x - wallsize.z - 5;
            }
            break;
        case 'rightDepthWall':
            if (direction === 'back') {
                minZ = minZ - latestDepth + 5;
                maxZ = wallbox.max.z - latestDepth;
                minX = maxX + latestWidth;
                maxX = maxX + latestWidth;
            }
            if (direction === 'normal') {
                minZ = minZ;
                maxZ = wallbox.max.z - 5;
                minX = maxX;
                maxX = maxX;
            }
            if (direction === 'left') {
                minZ = minZ - latestDepth + 5;
                maxZ = wallbox.max.z - latestDepth;
                minX = maxX;
                maxX = maxX;
            }
            if (direction === 'right') {
                minZ = minZ + 5;
                maxZ = wallbox.max.z - 5;
                minX = maxX + latestWidth - 5;
                maxX = maxX - latestWidth - 5;
            }
            break;
        case 'rightWidth2Wall':
            if (direction === 'back') {
                minZ = wallbox.min.z - latestDepth;
                maxZ = wallbox.min.z - latestDepth;
                minX = wallbox.min.x + latestWidth - wallsize.z;
                maxX = wallbox.max.x - wallsize.z * 2;
            }
            if (direction === 'normal') {
                minZ = wallbox.min.z - 5;
                maxZ = wallbox.min.z - 5;
                minX = wallbox.min.x - wallsize.z;
                maxX = wallbox.max.x - latestWidth - wallsize.z * 2;
            }
            if (direction === 'left') {
                minZ = wallbox.min.z - latestDepth;
                maxZ = wallbox.min.z - latestDepth;
                minX = wallbox.min.x - wallsize.z + 5;
                maxX = wallbox.max.x - latestWidth - wallsize.z * 2 + 5;
            }
            if (direction === 'right') {
                minZ = wallbox.min.z;
                maxZ = wallbox.max.z - wallsize.z;
                minX = wallbox.min.x + latestWidth - wallsize.z;
                maxX = wallbox.max.x - wallsize.z * 2 - 5;
            }
            break;
        case 'leftWidth2Wall':
            if (direction === 'back') {
                minZ = wallbox.min.z + wallsize.x + 5;
                maxZ = wallbox.max.z - latestDepth;
                minX = wallbox.min.x;
                maxX = wallbox.max.x - latestWidth;
            }
            if (direction === 'normal') {
                minZ = wallbox.min.z + latestDepth + wallsize.x;
                maxZ = wallbox.max.z - 5;
                minX = wallbox.min.x - latestWidth;
                maxX = wallbox.min.x - latestWidth;
            }
            if (direction === 'left') {
                minZ = wallbox.min.z + wallsize.x;
                maxZ = wallbox.max.z - latestDepth;
                minX = wallbox.min.x - latestWidth;
                maxX = wallbox.min.x - latestWidth;
            }
            if (direction === 'right') {
                minZ = wallbox.min.z + latestDepth + wallsize.x;
                maxZ = wallbox.max.z;
                minX = wallbox.min.x - 5;
                maxX = wallbox.min.x - 5;
            }
            break;
        default:
            break;
    }
    const clamped = newPosition.clone();
    clamped.x = THREE.MathUtils.clamp(clamped.x, minX, maxX);
    clamped.z = THREE.MathUtils.clamp(clamped.z, minZ, maxZ);

    return clamped;
};
