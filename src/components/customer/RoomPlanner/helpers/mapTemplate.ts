import {RoomPlannerProduct} from 'components/customer/RoomPlanner/types';
import {get3dTemplate} from 'service/ProductService';
import {cloneDeep} from 'lodash';

export const mapTemplate = async (roomProduct: RoomPlannerProduct) => {
    const product = cloneDeep(roomProduct);
    const template = await get3dTemplate(product.type);

    if (template) {
        product.template_3d = template;
    }

    return product;
};
