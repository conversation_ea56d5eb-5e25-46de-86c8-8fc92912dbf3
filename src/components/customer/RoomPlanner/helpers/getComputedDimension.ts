import {store} from 'store/customer/storeSetup';
import {ComputedDimension} from 'components/customer/RoomPlanner/lib/useRoomPlanner';
export const getRoomDimensionsComputed = (): ComputedDimension => {
    // NOTE: for some reason jest runs this function. normally when used in browser
    // store is always defined so it will never be false.
    // delete this contidion wrapper when all tests are migrated to cypress and
    // jest runs in node mode
    if (store) {
        const state = store.getState();
        const {roomType, rectangularDimension, lShapedDimension} =
            state.roomPlanner;

        const scale = 0.2;
        return {
            computedWidth:
                ((roomType === 'RECTANGULAR'
                    ? rectangularDimension.width
                    : lShapedDimension.rightWidth) /
                    2) *
                    -scale +
                26.1,
            roomWidth:
                (roomType === 'RECTANGULAR'
                    ? rectangularDimension.width
                    : lShapedDimension.rightWidth) * scale,
            roomDepth:
                roomType === 'RECTANGULAR'
                    ? rectangularDimension.length
                    : lShapedDimension.leftWidth,
            roomDepthComputed:
                ((roomType === 'RECTANGULAR'
                    ? rectangularDimension.length
                    : lShapedDimension.leftWidth) /
                    2) *
                    -scale -
                25,
            wallHeight:
                (roomType === 'RECTANGULAR'
                    ? rectangularDimension.height
                    : lShapedDimension.height) * scale,
            scale,
            roomLeftDepth: lShapedDimension.leftDepth || 0,
            roomRightDepth: lShapedDimension.rightDepth || 0,
            roomLeftWidth: lShapedDimension.leftWidth || 0,
            roomRightWidth: lShapedDimension.rightWidth || 0,
            positionX: 0,
        };
    }

    return {
        computedWidth: 0,
        roomWidth: 0,
        roomDepth: 0,
        roomDepthComputed: 0,
        wallHeight: 0,
        scale: 0,
        roomLeftDepth: 0,
        roomRightDepth: 0,
        roomLeftWidth: 0,
        roomRightWidth: 0,
        positionX: 0,
    };
};
