import React, {useMemo} from 'react';
import {Row, Col, Image, Table} from 'react-bootstrap';
import {CBCButton, Icon, Loader} from 'shared/helpers';
import {Uploads, KDMax} from 'assets';
import {KDMaxProduct} from 'components/customer/KDMax/KDMaxProduct';
import {KDMaxProvider} from 'components/customer/KDMax/contexts/KDMaxContext';
import {useKDMaxImport} from 'components/customer/KDMax/hooks/useKDMaxImport';

import './KDMax.scss';

export const KDMaxImport = () => {
    const {loader, cabinets, dialog, fileUploadHandler, deleteAllProducts} =
        useKDMaxImport();

    const table = useMemo(() => {
        return (
            <Table className="cbc-table kdmax-table" cellSpacing={0}>
                <thead>
                    <tr>
                        <th>
                            <div>Product</div>
                        </th>
                        <th>
                            <div>Dimensions</div>
                        </th>
                        <th>
                            <div>Actions</div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {cabinets.map((cabinet, index) => {
                        return (
                            <KDMaxProduct
                                key={index}
                                product={cabinet}
                                even={index % 2 == 0}
                            />
                        );
                    })}
                </tbody>
            </Table>
        );
    }, [cabinets]);

    return (
        <>
            <Loader loader={loader} />
            {cabinets.length ? (
                <div className="job-dashboard">
                    <h1>
                        <Icon iconName="Header-Cart.svg" />
                        <label>KD Max Import </label>
                    </h1>
                    <div style={{margin: '15px 0', display: 'flex'}}>
                        <div
                            className="kdmax-file-upload"
                            style={{marginRight: '15px'}}>
                            <div className="file-uploader">
                                <Icon iconName="arrow_upward.svg" />
                                Upload new file
                            </div>
                            <input type="file" onChange={fileUploadHandler} />
                        </div>
                        <CBCButton
                            iconName="Button-Cancel.svg"
                            className="item-button button-light"
                            onClick={deleteAllProducts}>
                            Delete All
                        </CBCButton>
                    </div>
                    {table}
                </div>
            ) : (
                <Row className="job-form">
                    <Col md={{span: 8, offset: 2}}>
                        <h1>
                            <Icon iconName="Headers-New-Job.svg" />
                            <label>KD Max Import</label>
                        </h1>
                        <>
                            <div className="fileContainer">
                                <div className="fileSelector">
                                    <div className="info">
                                        <Image
                                            style={{width: '100px'}}
                                            src={Uploads}
                                            alt="Uploads.svg"
                                        />
                                        <br />
                                        <label htmlFor="file-upload">
                                            Drag and drop or browse files to
                                            upload
                                        </label>
                                    </div>
                                    <input
                                        id="file-upload"
                                        type="file"
                                        onChange={fileUploadHandler}
                                    />
                                </div>
                                <a
                                    href="https://cabinetsbycomputer.com.au/about-kd-max/"
                                    rel="noreferrer help"
                                    target="_blank">
                                    <Image
                                        style={{
                                            width: '100%',
                                            marginTop: '10px',
                                        }}
                                        src={KDMax}
                                        alt="KDMaxImport.svg"
                                    />{' '}
                                </a>
                            </div>
                        </>
                    </Col>
                </Row>
            )}
            {dialog}
        </>
    );
};

export const KDMaxWrapper = () => {
    return (
        <KDMaxProvider>
            <KDMaxImport />
        </KDMaxProvider>
    );
};

export default KDMaxWrapper;
