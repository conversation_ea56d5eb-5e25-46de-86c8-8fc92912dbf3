import {useAppContext} from 'contexts';
import {useKDMaxProduct} from 'components/customer/KDMax/hooks/useKDMaxProduct';
import React, {useMemo, useRef} from 'react';
import {Button} from 'react-bootstrap';
import {isMobile} from 'react-device-detect';
import {Icon} from 'shared/helpers';
import {isDeviceSmall} from 'shared/helpers/DeviceSize';
import {OverlayTrigger} from 'shared';
import {ProductBackgroundImage} from 'components';
import styled from 'styled-components';
import {useParams} from 'react-router-dom';
import {useGetRoomProductsQuery} from 'components/customer/Product/store/productApi';

const ImageContainer = styled.div`
    width: ${({$isMobile, $cabinetAdded}) =>
        $cabinetAdded ? '50px' : $isMobile ? '100px' : '150px'};
    height: ${({$isMobile, $cabinetAdded}) =>
        $cabinetAdded ? '50px' : $isMobile ? '100px' : '150px'};
    outline-width: 1px;
    outline-style: solid;
    outline-color: rgb(var(--primary_colour));
    border-radius: 10px;
`;

const KDMaxLoader = styled.div`
    width: ${({$isMobile, $cabinetAdded}) =>
        $cabinetAdded ? '50px' : $isMobile ? '100px' : '150px'};
    height: ${({$isMobile, $cabinetAdded}) =>
        $cabinetAdded ? '50px' : $isMobile ? '100px' : '150px'};
`;

const CUSTOM_COLOUR_NAME: string = 'custom colour';

export const KDMaxProduct = ({product, even}) => {
    const isSmallDevice = isDeviceSmall();
    const {
        loader,
        room,
        addCabinet,
        editCabinet,
        deleteCabinet,
        showProducts,
        cabinet,
    } = useKDMaxProduct(product);
    const {userProfile} = useAppContext();
    const {roomId} = useParams();

    const {data: products} = useGetRoomProductsQuery(
        {roomId: Number(roomId)},
        {
            skip: typeof roomId === 'undefined',
        }
    );

    // change style to get the exterior material image
    const styleNotAvailable = useRef({
        objectFit: 'cover',
        width: '80%',
        margin: 'auto',
        height: '80%',
    }).current;

    const {exteriorLabel, carcaseLabel} = useMemo(() => {
        return {
            exteriorLabel: !isMobile ? 'Exterior Material:' : 'Ext:',
            carcaseLabel: !isMobile ? 'Carcase Material:' : 'Carc:',
        };
    }, [isMobile]);

    const buttons = useMemo(() => {
        return [
            {
                iconName: 'Cart.svg',
                title: 'Save Product',
                action: () => addCabinet(),
                isHidden: cabinet.isAvailable ? false : true,
            },
            {
                iconName: 'Options-Edit.svg',
                title: 'Edit Product',
                action: () => editCabinet(),
                isHidden: cabinet.isAvailable ? false : true,
            },
            {
                iconName: 'Options-Delete.svg',
                title: 'Remove Product',
                action: () => deleteCabinet(),
                isHidden: cabinet.isAvailable ? false : true,
            },
            {
                iconName: 'search-black.svg',
                title: 'Search Product',
                action: () => showProducts(),
                isHidden:
                    cabinet.isAvailable || cabinet.isBenchtop ? true : false,
            },
        ].map((button, index) => {
            if (cabinet.isAdded) {
                return <React.Fragment key={index} />;
            }

            if (!cabinet.isValidated && button.title == 'Save Product') {
                return <React.Fragment key={index} />;
            }

            return (
                <OverlayTrigger key={index} overlay={button.title}>
                    <Button
                        style={button.isHidden ? {display: 'none'} : {}}
                        variant="link"
                        onClick={(e) => button.action()}
                        disabled={loader}>
                        <Icon iconName={button.iconName} />
                    </Button>
                </OverlayTrigger>
            );
        });
    }, [cabinet, loader, addCabinet, editCabinet, deleteCabinet, showProducts]);

    const backgroundImage = `url("/uploads/gocabinet_materials/${
        room &&
        room.extMaterial &&
        room.extMaterial.image != '' &&
        cabinet &&
        !cabinet?.changedImage?.endsWith('.svg') &&
        !cabinet?.image?.endsWith('.svg')
            ? room.extMaterial.image
            : 'default_exterior.jpg'
    }")`;

    const dimensionString = useMemo(() => {
        if (cabinet.hasOwnProperty('jobCabinetId') && products) {
            const jobCabinet = products.find(
                (item) => item.id == cabinet.jobCabinetId
            );

            if (jobCabinet) {
                return jobCabinet.dimensions;
            }
        }

        return cabinet.dimensionString;
    }, [cabinet, products]);

    return (
        <tr className={even ? 'even' : 'odd'}>
            <td>
                <div>
                    {cabinet.isAvailable ? (
                        cabinet.isValidated ? (
                            <div className="product-detail">
                                <div>
                                    {loader ? (
                                        <KDMaxLoader
                                            $isMobile={isMobile}
                                            $cabinetAdded={cabinet.isAdded}>
                                            <Icon iconName="Spinner.svg" />
                                        </KDMaxLoader>
                                    ) : (
                                        <ImageContainer
                                            $isMobile={isMobile}
                                            $cabinetAdded={cabinet.isAdded}>
                                            <ProductBackgroundImage
                                                image={backgroundImage}
                                                transform={room.extHorGrain}
                                                alt={cabinet.typeName}
                                                src={cabinet.changedImage}
                                                fallbackSrc={cabinet.image}
                                            />
                                        </ImageContainer>
                                    )}
                                </div>
                                <div
                                    style={
                                        isSmallDevice ? {} : {minWidth: '200px'}
                                    }>
                                    {cabinet.isAdded ? (
                                        <h3>
                                            <strong>
                                                {cabinet.name} -{' '}
                                                <em>Added To Cart</em>{' '}
                                            </strong>
                                        </h3>
                                    ) : (
                                        <h3>
                                            <strong>{cabinet.name}</strong>
                                        </h3>
                                    )}
                                    {!cabinet.isAdded &&
                                    room.extMaterial &&
                                    room.extMaterial.type != 7 ? (
                                        <>
                                            <h3
                                                style={{
                                                    fontSize: '12px',
                                                    marginBottom: '0px',
                                                }}>
                                                {exteriorLabel}{' '}
                                            </h3>
                                            <p>
                                                {room.extMaterial.name}{' '}
                                                {room.extMaterial.name
                                                    .toLowerCase()
                                                    .indexOf(
                                                        CUSTOM_COLOUR_NAME
                                                    ) > -1 &&
                                                    room.extCustomColour + ' '}
                                                <em>
                                                    {room.extMaterial.finish}
                                                </em>{' '}
                                                - {room.extMaterial.thickness}mm{' '}
                                                {room.extMaterial.brandName}{' '}
                                                {room.extMaterial.substrate &&
                                                    room.extMaterial.substrate +
                                                        ' '}
                                            </p>
                                        </>
                                    ) : null}
                                    {!cabinet.isAdded &&
                                    room.carcMaterial &&
                                    room.carcMaterial.type != 7 ? (
                                        <>
                                            <h3
                                                style={{
                                                    fontSize: '12px',
                                                    marginBottom: '0px',
                                                }}>
                                                {carcaseLabel}{' '}
                                            </h3>
                                            <p>
                                                {room.carcMaterial.name}{' '}
                                                {room.carcMaterial.name
                                                    .toLowerCase()
                                                    .indexOf(
                                                        CUSTOM_COLOUR_NAME
                                                    ) > -1 &&
                                                    room.extCustomColour + ' '}
                                                <em>
                                                    {room.carcMaterial.finish}
                                                </em>{' '}
                                                - {room.carcMaterial.thickness}
                                                mm {
                                                    room.carcMaterial.brandName
                                                }{' '}
                                                {room.carcMaterial.substrate &&
                                                    room.carcMaterial
                                                        .substrate + ' '}
                                            </p>
                                        </>
                                    ) : null}
                                    {cabinet.shelfCount > 6 ? (
                                        <h3
                                            style={{
                                                fontSize: '12px',
                                                color: 'red',
                                            }}>
                                            Note: Shelf details could not be
                                            imported - default shelf quantity
                                            and positions have been set.
                                        </h3>
                                    ) : null}
                                </div>
                            </div>
                        ) : (
                            <div className="product-detail">
                                <div>
                                    {loader ? (
                                        <KDMaxLoader
                                            $isMobile={isMobile}
                                            $cabinetAdded={cabinet.isAdded}>
                                            <Icon iconName="Spinner.svg" />
                                        </KDMaxLoader>
                                    ) : (
                                        <ImageContainer
                                            $isMobile={isMobile}
                                            $cabinetAdded={cabinet.isAdded}>
                                            <ProductBackgroundImage
                                                image={backgroundImage}
                                                transform={room.extHorGrain}
                                                alt={cabinet.typeName}
                                                src={cabinet.changedImage}
                                                fallbackSrc={cabinet.image}
                                            />
                                        </ImageContainer>
                                    )}
                                </div>
                                <div
                                    style={
                                        isSmallDevice ? {} : {minWidth: '200px'}
                                    }>
                                    <h3>
                                        <strong>
                                            {!cabinet.note
                                                ? cabinet.name
                                                : cabinet.note}
                                        </strong>
                                    </h3>
                                    <h3>
                                        <strong style={{color: 'red'}}>
                                            {cabinet.hasOwnProperty(
                                                'hasError'
                                            ) && cabinet.hasError
                                                ? 'Cannot match item to imported product'
                                                : 'Product validation error, please edit product to add to cart.'}
                                        </strong>
                                    </h3>
                                </div>
                            </div>
                        )
                    ) : cabinet.isBenchtop ? (
                        userProfile.isBenchtopAvailable ? (
                            <div className="product-detail">
                                <div>
                                    <div
                                        className="image-container"
                                        style={
                                            cabinet.isAdded
                                                ? isMobile
                                                    ? {
                                                          height: '100px',
                                                          width: '100px',
                                                      }
                                                    : {
                                                          height: '50px',
                                                          width: '50px',
                                                          marginRight: '100px',
                                                      }
                                                : {}
                                        }>
                                        <Icon
                                            iconName="product_not_found.svg"
                                            style={styleNotAvailable}
                                        />
                                    </div>
                                </div>
                                <div
                                    style={
                                        isSmallDevice ? {} : {minWidth: '200px'}
                                    }>
                                    <h3>
                                        <strong>
                                            {!cabinet.note
                                                ? cabinet.name
                                                : cabinet.note}
                                        </strong>
                                    </h3>
                                    <h3>
                                        <strong style={{color: 'red'}}>
                                            Benchtop not imported, please use
                                            benchtop designer to add one
                                        </strong>
                                    </h3>
                                </div>
                            </div>
                        ) : (
                            <div className="product-detail">
                                <div>
                                    <div
                                        className="image-container"
                                        style={
                                            cabinet.isAdded
                                                ? isMobile
                                                    ? {
                                                          height: '100px',
                                                          width: '100px',
                                                      }
                                                    : {
                                                          height: '50px',
                                                          width: '50px',
                                                          marginRight: '100px',
                                                      }
                                                : {}
                                        }>
                                        <Icon
                                            iconName="product_not_found.svg"
                                            style={styleNotAvailable}
                                        />
                                    </div>
                                </div>
                                <div
                                    style={
                                        isSmallDevice ? {} : {minWidth: '200px'}
                                    }>
                                    <h3>
                                        <strong>
                                            {!cabinet.note
                                                ? cabinet.name
                                                : cabinet.note}
                                        </strong>
                                    </h3>
                                    <h3>
                                        <strong style={{color: 'red'}}>
                                            Benchtop not imported.
                                        </strong>
                                    </h3>
                                </div>
                            </div>
                        )
                    ) : (
                        <div className="product-detail">
                            <div>
                                <div
                                    className="image-container"
                                    style={
                                        cabinet.isAdded
                                            ? isMobile
                                                ? {
                                                      height: '100px',
                                                      width: '100px',
                                                  }
                                                : {
                                                      height: '50px',
                                                      width: '50px',
                                                      marginRight: '100px',
                                                  }
                                            : {}
                                    }>
                                    <Icon
                                        iconName="product_not_found.svg"
                                        style={styleNotAvailable}
                                    />
                                </div>
                            </div>
                            <div
                                style={
                                    isSmallDevice ? {} : {minWidth: '200px'}
                                }>
                                <h3>
                                    <strong>
                                        {!cabinet.note
                                            ? cabinet.name
                                            : cabinet.note}
                                    </strong>
                                </h3>
                                <h3>
                                    <strong style={{color: 'red'}}>
                                        Product code not found, please search
                                        for a compatible product to import this
                                        item.
                                    </strong>
                                </h3>
                            </div>
                        </div>
                    )}
                </div>
            </td>
            <td>{cabinet.isAvailable ? dimensionString : ''}</td>
            <td>{buttons}</td>
        </tr>
    );
};
