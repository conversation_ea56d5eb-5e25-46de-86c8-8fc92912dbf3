@use 'assets/scss/mixins.scss' as mixins;
@use '../LayoutDesigner/TDLD.scss';

.modal-dialog-kdmax {
    max-width: 600px !important;
}

.kdmax-table {
    .image-container {
        position: relative;
        justify-content: center;
    }
}

.kdmax-file-upload {
    position: relative;
    cursor: pointer;

    .file-uploader {
        @include mixins.theme('background', 'ui-primary-color');
        color: white;
        border-radius: 30px;
        padding: 10px;

        img {
            width: 20px;
            margin-right: 10px;
            vertical-align: -3px;
        }
    }

    input {
        position: absolute;
        z-index: 1;
        top:0;
        bottom:0;
        right:0;
        left:0;
        width: 100%;
        opacity: 0;
    }
}

.kdmax-product-search {
    flex-direction: column;
}

.layout-menu {
    h2 {
        width: 100%;
    }
}

.kdmax-product-list-container {
    display: block !important;
    position: initial !important;
    width: auto !important;
    box-shadow: none !important;
    border-radius: 0px !important;
}
