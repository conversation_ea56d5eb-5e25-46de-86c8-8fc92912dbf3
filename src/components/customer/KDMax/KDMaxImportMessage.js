// @flow
import React from 'react';

export const KDMaxImportMessage = ({dialogString, handleCheck}) => {
    return dialogString.length ? (
        <>
            <strong>Product Not Found</strong> -
            <p>
                The following items in your import file havent been able to be
                matched to a product in the library. Please add them manually to
                your job.
            </p>
            <br />
            {dialogString.map((name, index) => {
                return (
                    <React.Fragment key = {index}>
                        {' '}
                        <strong> {name} </strong>
                        <br />
                    </React.Fragment>
                );
            })}
            <br />
            <div className="modal-header" style={{paddingTop: '0px'}}></div>
            <br />
            <strong style={{fontSize: '9px'}}>IMPORTANT</strong> -{' '}
            <label style={{fontSize: '9px'}}>
                Product type and product size have been populated from your
                import file. The sizes of products contained in your import file
                will be added to the products in the list as carcase sizes.
                <br />
                <br />
                Please check your job PDF prior to submitting your order and
                amend any product specifications including shelving, door
                margins, drawer front sizes etc. to match your requirements.
            </label>
            <br />
            <br />
            <input
                id="acknowledgeCheck"
                type="checkbox"
                onChange={handleCheck}
                style={{verticalAlign: '-2px'}}
            />{' '}
            <label style={{fontSize: '9px'}} htmlFor="acknowledgeCheck">
                I acknowledge that final product composition will be as per
                listing in the Job Properties PDF
            </label>
        </>
    ) : (
        <>
            <strong>IMPORTANT</strong> -{' '}
            <p>
                Product type and product size have been populated from your
                import file. The sizes of products contained in your import file
                will be added to the products in the list as carcase sizes.{' '}
                <br />
                <br />
                Please check your job PDF prior to submitting your order and
                amend any product specifications including shelving, door
                margins, drawer front sizes etc. to match your requirements.
            </p>
            <br />
            <br />
            <input
                id="acknowledgeCheck"
                type="checkbox"
                onChange={handleCheck}
                style={{verticalAlign: '-2px'}}
            />{' '}
            <label style={{fontSize: '9px'}} htmlFor="acknowledgeCheck">
                I acknowledge that final product composition will be as per
                listing in the Job Properties PDF
            </label>
        </>
    );
};
