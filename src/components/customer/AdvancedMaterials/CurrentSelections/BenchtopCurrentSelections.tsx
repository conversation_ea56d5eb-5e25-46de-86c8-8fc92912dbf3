import React from 'react';
import {shallowEqual} from 'react-redux';
import {
    selectHasDoors,
    selectHasEdges,
    selectMaterial,
} from 'components/customer/AdvancedMaterials/store/materialSlice';
import {CurrentSelections} from 'components/customer/AdvancedMaterials/CurrentSelections/CurrentSelections';
import {useAppSelector} from 'store/customer';
import {useAppContext} from 'contexts';

const withBenchtop = (Component: typeof CurrentSelections) => {
    const ComponentWithBenchtop = () => {
        const hasEdges = useAppSelector(selectHasEdges);
        const hasDoors = useAppSelector(selectHasDoors);
        const material = useAppSelector(selectMaterial, shallowEqual);
        const {userProfile} = useAppContext();
        const hideSubstrateName = userProfile?.hideSubstrateName;
        return (
            <Component
                material={material}
                hasDoors={hasDoors}
                hasEdges={hasEdges}
                hideSubstrateName={hideSubstrateName}
            />
        );
    };

    return ComponentWithBenchtop;
};

export default withBenchtop(CurrentSelections);
