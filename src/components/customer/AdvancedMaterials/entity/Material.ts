import {Brand} from 'components/customer/AdvancedMaterials/entity/Brand';
import {Type} from 'components/customer/AdvancedMaterials/entity/Type';
import {Finish} from 'components/customer/AdvancedMaterials/entity/Finish';

export interface Material<T = any> {
    id: number;
    name: string;
    brand: Brand;
    image: string;
    finish: Finish;
    substrate?: string;
    type: Type;
    thickness: number;
    data: T;
}
