import {
    useLazyListProductMaterialBrandsQuery,
    useLazyListMaterialFinishesQuery,
} from 'components/customer/Materials/store/materialApi';
import {useAppSelector} from 'store/customer';
import {
    selectBrandIds,
    selectDoor,
    selectMaterialType,
} from 'components/customer/AdvancedMaterials/store/materialSlice';
import {shallowEqual} from 'react-redux';
import {useDebounce, useDebouncedCallback} from 'use-debounce';
import {useCallback, useEffect, useRef} from 'react';
import {Door} from 'components/customer/Materials/entity';
import {Type} from 'components/customer/AdvancedMaterials/entity/Type';

export const useMaterialBrandsAndFinishes = (
    show: boolean,
    hasDoor: boolean,
    cabinetType: number
) => {
    const door = useAppSelector(selectDoor, shallowEqual);
    const selectedBrands = useAppSelector(selectBrandIds, shallowEqual);
    const materialType = useAppSelector(selectMaterialType, shallowEqual);

    const initiated = useRef(false);

    // We do not need door filter if we are browsing carase materials
    const [debouncedBrands] = useDebounce(selectedBrands, 1000);

    const [
        getBrands,
        {
            data: brandsData,
            isLoading: isLoadingBrands,
            isFetching: isFetchingBrands,
        },
    ] = useLazyListProductMaterialBrandsQuery();
    const [
        getFinishes,
        {
            data: finishesData,
            isLoading: isLoadingFinishes,
            isFetching: isFetchingFinishes,
        },
    ] = useLazyListMaterialFinishesQuery();

    const fetchBrands = useDebouncedCallback(
        useCallback(
            (door: Door, materialType: Type) => {
                if (((hasDoor && door) || !hasDoor) && materialType) {
                    void getBrands(
                        {
                            doorFilter:
                                hasDoor && door ? door.filter_name : undefined,
                            cabinetType,
                            materialType: materialType.id,
                        },
                        true
                    );
                }
            },
            [hasDoor, cabinetType]
        ),
        1000
    );

    const fetchFinishes = useDebouncedCallback(
        useCallback(
            (door: Door, materialType: Type, brands: string[]) => {
                if (((hasDoor && door) || !hasDoor) && materialType) {
                    void getFinishes(
                        {
                            doorFilter:
                                hasDoor && door ? door.filter_name : undefined,
                            cabinetType,
                            materialType: materialType.id,
                            brands:
                                brands && brands.length ? brands : undefined,
                        },
                        true
                    );
                }
            },
            [hasDoor, cabinetType]
        ),
        1000
    );

    useEffect(() => {
        if (initiated.current && !show) {
            return;
        }

        if (!initiated.current) {
            if (((hasDoor && door) || !hasDoor) && materialType) {
                initiated.current = true;
            }
        }

        fetchBrands(door, materialType);
        fetchFinishes(door, materialType, debouncedBrands);
    }, [
        door,
        hasDoor,
        materialType,
        debouncedBrands,
        fetchBrands,
        fetchFinishes,
        show,
    ]);

    return {
        brands: brandsData?.data,
        isLoadingBrands,
        isFetchingBrands,
        finishes: finishesData?.data,
        isLoadingFinishes,
        isFetchingFinishes,
    };
};
