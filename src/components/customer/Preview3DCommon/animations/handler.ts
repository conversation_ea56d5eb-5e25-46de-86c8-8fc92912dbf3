import * as THREE from 'three';
import {OperationGroup, Dimension} from 'components/customer/Preview3D/types';
import {TemplateVariable} from 'components/customer/Preview3D/usePreview3DData';
import {AnimationHandler} from 'components/customer/Preview3D/animation/entity/AnimationHandler';
import {toggleDoorHandler} from 'components/customer/Preview3D/animation/doorAnimationHandler';
import {toggleCornerHandler} from 'components/customer/Preview3D/animation/cornerAnimationHandler';
import {toggleDrawerHandler} from 'components/customer/Preview3D/animation/drawerAnimationHandler';
import {toggleApplianceExtendedDoorHandler} from 'components/customer/Preview3D/animation/applianceAnimationHandler';
import {toggleMainBifoldDoorHandler} from 'components/customer/Preview3D/animation/mainBifoldDoorAnimationHandler';
import {toggleExtendedBifoldDoorHandler} from 'components/customer/Preview3D/animation/extendedBifoldDoorAnimationHandler';
import {RoomPlannerFormValues} from 'components/customer/RoomPlanner/types';

export const attachAnimations = (
    mesh: THREE.Group<THREE.Object3DEventMap>,
    operationGroup: OperationGroup,
    values: RoomPlannerFormValues,
    variables: TemplateVariable,
    meshPos: Dimension,
    counter: number
) => {
    const animation = animationHandler(
        mesh,
        operationGroup,
        values,
        variables,
        meshPos,
        counter
    );
    mesh.userData.toggleDoor = ({
        isDoorOrDrawerOpen,
    }: {
        isDoorOrDrawerOpen: boolean;
    }) => {
        if (!animation) return;
        const {open, close} = animation;

        if (isDoorOrDrawerOpen) {
            if (open.length) {
                for (const openAnimation of open) {
                    void openAnimation();
                }
            }
        } else {
            if (close.length) {
                for (const closeAnimation of close) {
                    void closeAnimation();
                }
            }
        }
    };
};

export const animationHandler = (
    mesh: THREE.Group<THREE.Object3DEventMap>,
    operationGroup: OperationGroup,
    values: RoomPlannerFormValues,
    variables: TemplateVariable,
    meshPos: Dimension,
    counter: number
): AnimationHandler => {
    const exteriorThickness = values.cabinet_ext_thickness;
    const carcaseThickness = values.cabinet_carc_thickness;
    const isInnerDrawer = Boolean(variables.innerDrawerCount);
    const hasInsetDrawer = Boolean(variables.hasInsetDrawer);
    const isMicrowaveDrawer =
        typeof values.microwave_opening_height !== 'undefined' &&
        !variables?.isWallOvenProduct;
    let widthMultiplier = 2;
    let returnWidthOffset = 0;

    const drawerExteriorThickness = exteriorThickness * 0.2;

    if (variables?.isReturnProduct && variables?.isLeftReturn) {
        returnWidthOffset =
            Number(values.cabinet_return_panel_width) -
            values.cabinet_void_width;
    }

    if (
        variables?.isApplianceLeftBifoldRight ||
        variables?.isApplianceLeftRightBifold
    ) {
        widthMultiplier = 3;
    }

    if (variables?.isApplianceBifoldPair) {
        widthMultiplier = 4;
    }

    if (operationGroup.IsRightDoor) {
        return toggleDoorHandler({
            mesh,
            variables,
            isRightDoor: true,
            isPairDoor: operationGroup.IsPairDoor,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
            widthMultiplier,
            degrees: 90,
            positionXOffset: returnWidthOffset,
            isThreeDoor: variables.is3DoorProduct,
        });
    }

    if (operationGroup.IsLeftDoor) {
        return toggleDoorHandler({
            mesh,
            variables,
            isPairDoor: operationGroup.IsPairDoor,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
            degrees: 90,
            positionXOffset: returnWidthOffset,
        });
    }

    if (operationGroup.IsTopHangDoor) {
        return toggleDoorHandler({
            mesh,
            variables,
            isTopHangDoor: true,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
            degrees: 90,
        });
    }

    if (operationGroup.IsLeftApplianceMainDoor) {
        return toggleDoorHandler({
            mesh,
            variables,
            degrees: 87,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
            widthMultiplier,
        });
    }

    if (operationGroup.IsRightApplianceMainDoor) {
        return toggleDoorHandler({
            mesh,
            variables,
            isRightDoor: true,
            isPairDoor: true,
            degrees: 87,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
            widthMultiplier,
        });
    }

    if (operationGroup.IsCornerLeftDoor || operationGroup.IsCornerRightDoor) {
        return toggleCornerHandler({
            mesh,
            exteriorThickness,
            values,
            isRightDoor: Boolean(operationGroup.IsCornerRightDoor),
            origin: 'ROOM_PLANNER',
        });
    }

    if (operationGroup.IsDrawerFront || operationGroup.IsDoorPullout) {
        const depthOffsetCalculation = 50 * (counter + 1);
        return toggleDrawerHandler(
            mesh,
            variables,
            drawerExteriorThickness,
            isInnerDrawer,
            false,
            false,
            operationGroup.IsDoorPullout
                ? 250
                : depthOffsetCalculation >= 200
                ? 200
                : depthOffsetCalculation,
            meshPos,
            hasInsetDrawer || isMicrowaveDrawer
        );
    }

    if (operationGroup.IsDrawerBottom) {
        const depthOffsetCalculation = 50 * (counter + 1);
        return toggleDrawerHandler(
            mesh,
            variables,
            drawerExteriorThickness,
            isInnerDrawer,
            true,
            false,
            depthOffsetCalculation >= 200 ? 200 : depthOffsetCalculation,
            meshPos,
            hasInsetDrawer || isMicrowaveDrawer,
            isMicrowaveDrawer
        );
    }

    if (operationGroup.IsDrawerBack) {
        const depthOffsetCalculation = 50 * (counter + 1);
        return toggleDrawerHandler(
            mesh,
            variables,
            drawerExteriorThickness,
            isInnerDrawer,
            false,
            true,
            depthOffsetCalculation >= 200 ? 200 : depthOffsetCalculation,
            meshPos,
            hasInsetDrawer || isMicrowaveDrawer,
            isMicrowaveDrawer
        );
    }

    if (operationGroup.IsRightLShape) {
        if (operationGroup.IsBifoldRightDoor) {
            return toggleMainBifoldDoorHandler({
                mesh,
                isRightLShape: true,
                lshapedRightWidth: values.cabinet_right_width,
                origin: 'ROOM_PLANNER',
            });
        }

        if (operationGroup.IsBifoldLeftDoor) {
            return toggleExtendedBifoldDoorHandler(
                mesh,
                meshPos,
                variables,
                values,
                exteriorThickness,
                carcaseThickness,
                true,
                Boolean(operationGroup.IsLeftBifoldExtension),
                'ROOM_PLANNER'
            );
        }
    } else {
        if (operationGroup.IsBifoldLeftDoor) {
            return toggleMainBifoldDoorHandler({
                mesh,
                origin: 'ROOM_PLANNER',
            });
        }

        if (operationGroup.IsBifoldRightDoor) {
            return toggleExtendedBifoldDoorHandler(
                mesh,
                meshPos,
                variables,
                values,
                exteriorThickness,
                carcaseThickness
            );
        }
    }

    if (operationGroup.IsLeftApplianceExtendedDoor) {
        return toggleApplianceExtendedDoorHandler({
            mesh,
            variables,
            values,
            carcaseThickness,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
        });
    }

    if (operationGroup.IsRightApplianceExtendedDoor) {
        return toggleApplianceExtendedDoorHandler({
            mesh,
            variables,
            values,
            carcaseThickness,
            isRightExtendedDoor: true,
            origin: 'ROOM_PLANNER',
            exteriorThickness,
        });
    }
};
