import {DropdownOption} from 'shared/components/Forms/Dropdown';

interface Filters {
    Type: number[];
    MaterialBrand: number[];
    MaterialFinish: string[];
    MaterialSubstrate: string[];
    MaterialThickness: number[];
    EdgeBrand: number[];
    EdgeFinish: string[];
    EdgeThickness: number[];
    DoorFilter: string[];
    DoorName: string[];
    OtherEdgeFinishes: number[];
}

export interface EdgeFinish extends DropdownOption {
    id: number;
    code: string;
    name: string;
    restrict_adjacent: boolean;
    restrict_to_one_edge: boolean;
    restrictions: {
        adjacent?: number[];
        defaultIds?: number[];
    };
    unavailable: boolean;
    filters: Filters;
}
