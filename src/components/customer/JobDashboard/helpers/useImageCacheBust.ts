import {useEffect, useState} from 'react';

const useImageCacheBust = (condition: boolean, interval = 5000) => {
    const [timestamp, setTimestamp] = useState(Date.now());

    useEffect(() => {
        let timer = null;

        if (condition) {
            timer = setInterval(() => {
                setTimestamp(Date.now());
            }, interval);
        }

        return () => timer && clearInterval(timer);
    }, [condition, interval]);

    const applyCacheBustQuery = (url: string) => {
        return `${url}?cache_bust=${timestamp}`;
    };

    return {applyCacheBustQuery};
};

export default useImageCacheBust;
