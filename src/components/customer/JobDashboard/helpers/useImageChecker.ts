import {useState, useEffect} from 'react';

interface useImageCheckerProps {
    imagePath: string;
}

const useImageChecker = ({imagePath}: useImageCheckerProps) => {
    const [isValidImage, setIsValidImage] = useState<boolean | null>(null);

    const checkImageExists = async (path: string): Promise<void> => {
        try {
            const response = await fetch(path, {method: 'HEAD'});
            setIsValidImage(response.ok);
        } catch {
            setIsValidImage(false);
        }
    };

    useEffect(() => {
        if (imagePath) {
            setIsValidImage(null);
            void checkImageExists(imagePath);
        }
    }, [imagePath]);

    return {isValidImage};
};

export default useImageChecker;
