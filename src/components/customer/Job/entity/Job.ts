import {PartialRoom} from 'shared/types/PartialRoom';

// TODO: Please fill this entity with properties as needed
export interface Job {
    id: number;
    displayId: number;
    rooms: PartialRoom[];
    dispatchMethod: number;
    address?: string;
    suburb?: string;
    city?: string;
    addressRegion?: number;
    addressState?: number;
    postcode?: string;
    totalProductCount?: number;
}

export interface ExtraVariation {
    id: number;
    job_id: number;
    name: string;
    cost: number;
    removable: boolean;
}
