export interface FingerPullProductApi {
    id: string;
    room_number: string;
    quantity: string;
    drawer_amount: number;
    type_name: string;
    width: string;
    length: string;
    depth: string;
    material_name: string;
    is_finger_pull: boolean;
    is_recessed_rail: boolean;
}

export interface FingerPullProduct {
    id: number;
    room_number: string;
    quantity: number;
    drawer_amount: number;
    type_name: string;
    width: number;
    length: number;
    depth: number;
    material_name: string;
    is_finger_pull: boolean;
    is_recessed_rail: boolean;
}
