import React from 'react';
import {Icon, Loader} from 'shared/helpers';
import {BenchtopSelector} from 'components/customer/BTM/BenchtopSelector';
import {BenchtopMaterialSelector} from 'components/customer/BTM/BenchtopMaterialSelector';
import {Bencht<PERSON>} from 'components/customer/BTM/Benchtop';
import {Route, Routes} from 'react-router-dom';
import {useAppSelector} from 'store/customer';
import {
    selectMaterialType,
    selectType,
} from 'components/customer/BTM/store/btmSlice';
import {shallowEqual} from 'react-redux';
import {useSidebarStateToggle} from 'shared/helpers/useSidebarStateToggle';
import {useEditBenchtop} from 'components/customer/BTM/helper/useEditBenchtop';
import {useJobContext} from 'contexts';
import {PartialJob} from 'shared/types/PartialJob';
import {MobileRestriction} from 'shared/components/MobileRestriction';

export const BTM = () => {
    useSidebarStateToggle();
    const benchtopType = useAppSelector(selectType, shallowEqual);
    const materialType = useAppSelector(selectMaterialType, shallowEqual);
    const {job} = useJobContext() as PartialJob;
    const {loading} = useEditBenchtop({jobId: job.id});

    if (loading) return <Loader loader={true} />;

    return (
        <MobileRestriction message="This feature is only available in desktop version.">
            <div className="benchtop-designer">
                <h1
                    className={
                        benchtopType && materialType
                            ? ''
                            : 'no-bottom-left-border-radius'
                    }>
                    <Icon iconName="Header-Benchtop.svg" />
                    <label>Add Benchtop</label>
                </h1>

                <Routes>
                    <Route
                        path="/type/:benchType"
                        element={<BenchtopMaterialSelector />}
                    />
                    <Route
                        path="/type/:benchType/materialType/:materialTypeId/*"
                        element={<Benchtop />}
                    />
                    <Route path="/" element={<BenchtopSelector />} />
                </Routes>
            </div>
        </MobileRestriction>
    );
};

export default BTM;
