import {EndProfileOptions} from 'components/admin/BenchtopModule/store/benchtopModuleEdgeProfileApi';
import {Image} from 'components/customer/Materials/entity';

export interface BenchtopEdgeProfile {
    id: number;
    prefix?: string;
    name: string;
    code: string;
    symbol: string;
    edge_highlight: boolean;
    is_end_only: boolean;
    end_id: number;
    end_option?: EndProfileOptions;
    restrict_adjacent: boolean;
    restrict_corner_notch: boolean;
    restrict_corner_clip: boolean;
    is_end_roll_available: boolean;
    is_postformed_profile: boolean;
    image: Image;
}
