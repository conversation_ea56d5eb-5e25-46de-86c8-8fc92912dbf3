import {CornerType} from 'components/customer/BTM/entity/CornerType';
import {Point} from 'components/customer/BTM/entity/Point';
import {Direction} from 'components/customer/BTM/entity/Direction';
import {IBenchOption} from 'components/customer/BTM/entity/IBenchOption';

export interface Corner extends IBenchOption {
    index?: number;
    name?: string;
    cutoff: boolean;
    type: CornerType;
    depths: number[];
    isJoin?: boolean;
    point?: Point;
    direction?: Direction;
    addedThroughEndProfile?: boolean;
    isArc?: boolean;
    relatedCornerIndex?: number | null;
    hideCornerInPreview?: boolean;
    disabled?: {
        notch: boolean;
        clip: boolean;
    };
}
