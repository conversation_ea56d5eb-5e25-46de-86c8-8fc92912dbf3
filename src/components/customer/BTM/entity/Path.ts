import {Point} from 'components/customer/BTM/entity/Point';
import {Position} from 'components/customer/BTM/entity/Position';
import {Side} from 'components/customer/BTM/entity/Side';
import {Direction} from 'components/customer/BTM/entity/Direction';
import {Edge} from 'components/customer/BTM/entity/Edge';

export interface Path {
    edged?: Edge;
    edgeHighlight?: boolean;
    profile?: number;
    points: Point[];
    squaredPoints?: Point[];
    length?: number;
    side?: Side;
    position?: Position;
    direction?: Direction;
    index?: number;
}
