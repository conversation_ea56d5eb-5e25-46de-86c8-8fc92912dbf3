import {JoinType} from 'components/customer/BTM/entity/JoinType';
import {JoinOrientation} from 'components/customer/BTM/entity/Join';

export interface JoinInfo {
    id: number;
    name: string;
    identifier: 'masons_mitre' | 'dog_leg' | 'full_mitre' | 'butt_join';
    joinType: JoinType;
    direction: JoinOrientation;
    is_deleted: boolean;
    is_hidden: boolean;
    minimumEdgeDistance?: number;
}
