import {BenchOption} from 'components/customer/BTM/entity/BenchOption';
import {IBenchOption} from 'components/customer/BTM/entity/IBenchOption';
import {BenchtopMaterialType} from 'components/customer/BTM/entity/BenchtopMaterialType';
import {BenchtopMaterial} from 'components/customer/BTM/entity/BenchtopMaterial';
import {BenchtopEdgeProfile} from 'components/customer/BTM/entity/BenchtopEdgeProfile';
import {BenchtopType} from 'components/customer/BTM/entity/BenchtopType';

export interface Bench {
    id?: number;
    job_benchtop_number?: string;
    customer_id: number;
    job_id: number;
    type_id: number;
    shape_id: number;
    material_id: number;
    comment: string;
    edge_profile_id?: number;
    quantity: number;
    cost?: number;
    shape?: BenchtopType;
    type?: BenchtopMaterialType;
    material?: BenchtopMaterial;
    edgeProfile?: BenchtopEdgeProfile;
    options: BenchOption<IBenchOption>[];
    dimension?: string;
    variation_cost?: number;
    variation?: {
        id: number;
        name: string;
    };
    cached_image?: string;
    is_cached_image_loading?: boolean;
}
