import {Corner} from 'components/customer/BTM/entity/Corner';
import {Path, Point, Color} from 'paper';
import {CornerType} from 'components/customer/BTM/entity/CornerType';
import {getDeduction} from 'components/customer/BTM/helper/corner';
import {Direction} from 'components/customer/BTM/entity/Direction';
import {flatten} from 'lodash';

/**
 * This is simply a map of direction.
 *
 * For Example, If corner has direction as Direction.LEFT_DOWN,
 * that just means we use Direction.Right and Direction.Down to
 * calculate deduct white space.
 */
const DirectionMap: {[key: number]: Direction[]} = {
    [Direction.LEFT_DOWN]: [Direction.RIGHT, Direction.DOWN],
    [Direction.LEFT_UP]: [Direction.DOWN, Direction.LEFT],
    [Direction.RIGHT_UP]: [Direction.LEFT, Direction.UP],
    [Direction.RIGHT_DOWN]: [Direction.UP, Direction.RIGHT],
};

/**
 * This function draws the white material in the bench preview.
 *
 * @param {Corner[]} corners Corners are needed here to draw white space with notch and clips.
 * @param {number} scale scale is used to scale the material area with bench size.
 */
export const drawMaterial = (corners: Corner[], scale: number) => {
    const points = corners.map((corner) => {
        if (corner.cutoff) {
            const {x, y} = corner.point;
            const directionMap = DirectionMap[corner.direction];

            if (directionMap) {
                let deductions = getDeduction(directionMap[0], directionMap[1]);

                if (corner.type == CornerType.Clip) {
                    deductions = deductions.filter((_, index) => index != 1);
                }

                return deductions.map((deduction) => {
                    return new Point(
                        (Number(x) + corner.depths[0] * deduction[0]) * scale,
                        (Number(y) + corner.depths[1] * deduction[1]) * scale
                    );
                });
            }
        } else {
            return new Point(
                Number(corner.point.x) * scale,
                Number(corner.point.y) * scale
            );
        }
    });

    if (points && points.length > 0) {
        const allPoints = flatten(points);
        const path = new Path(allPoints);
        path.fillColor = new Color(255, 255, 255);
    }
};
