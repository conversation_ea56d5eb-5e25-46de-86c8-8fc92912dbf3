import React, {useState} from 'react';
import {FormControl} from 'react-bootstrap';
import {useAppDispatch, useAppSelector} from 'store/customer';
import {
    selectVariationRequest,
    variationRequestSet,
} from 'components/customer/BTM/store/btmSlice';

export const OverviewVariationRequest = () => {
    const dispatch = useAppDispatch();
    const variationRequest = useAppSelector(selectVariationRequest);
    const [value, setValue] = useState(
        variationRequest ? variationRequest : ''
    );

    const handleBlur = () => {
        dispatch(variationRequestSet(value));
    };

    return (
        <FormControl
            as="textarea"
            rows={3}
            value={value}
            onBlur={handleBlur}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setValue(e.target.value)
            }
        />
    );
};
