import React, {useMemo} from 'react';
import {useAppSelector} from 'store/customer';
import {selectCorners} from 'components/customer/BTM/store/btmSlice';
import styled from 'styled-components';
import {Icon} from 'shared/helpers';
import {CornerType} from 'components/customer/BTM/entity/CornerType';

const CornerOverview = styled.section`
    display: flex;
`;

const Corner = styled.div`
    margin-right: 15px;

    strong {
        font-size: 0.85em;
    }

    > div {
        display: flex;
        align-items: center;

        img {
            margin-left: 10px;
        }
    }
`;

const ModifierIcon = styled(Icon)`
    width: 30px;
    height: 30px;
`;

export const OverviewCorners = () => {
    const corners = useAppSelector(selectCorners);

    const cutoffs = useMemo(() => {
        if (corners) {
            const clips = corners
                .filter(
                    (corner) =>
                        corner.type == CornerType.Clip &&
                        !corner?.addedThroughEndProfile
                )
                .map((corner) => corner.name);
            const notches = corners
                .filter((corner) => corner.type == CornerType.Notch)
                .map((corner) => corner.name);

            return {
                notches,
                clips,
            };
        }
    }, [corners]);

    return (
        <CornerOverview>
            {cutoffs?.notches && cutoffs.notches.length ? (
                <Corner>
                    <div>
                        <div>Notch</div>
                        <ModifierIcon iconName="Notch.svg" />
                    </div>
                    <strong>Corners: </strong>
                    {cutoffs.notches.join(', ')}
                </Corner>
            ) : (
                <></>
            )}
            {cutoffs?.clips && cutoffs.clips.length ? (
                <Corner>
                    <div>
                        <div>Clip</div>
                        <ModifierIcon iconName="Clip.svg" />
                    </div>
                    <strong>Corners: </strong>
                    {cutoffs.clips.join(', ')}
                </Corner>
            ) : (
                <></>
            )}
        </CornerOverview>
    );
};
