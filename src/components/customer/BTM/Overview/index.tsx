import React, {useMemo} from 'react';
import {MaterialSwatch} from 'components/customer/BTM/Material/MaterialSwatch';
import {MaterialInformation} from 'components/customer/BTM/Material/MaterialInformation';
import {Title} from 'components/customer/BTM/Material';
import styled from 'styled-components';
import {OverviewProfiles} from 'components/customer/BTM/Overview/OverviewProfiles';
import {OverviewJoins} from 'components/customer/BTM/Overview/OverviewJoins';
import {OverviewCorners} from 'components/customer/BTM/Overview/OverviewCorners';
import {OverviewVariationRequest} from 'components/customer/BTM/Overview/OverviewVariationRequest';
import {AbsoluteContainer} from 'components/customer/BTM/EdgeProfile';
import {useAppSelector} from 'store/customer';
import {
    selectType,
    selectCorners,
    selectJoins,
} from 'components/customer/BTM/store/btmSlice';
import {Shape} from 'components/customer/BTM/entity/Shape';
import {shallowEqual} from 'react-redux';
import {JoinType} from 'components/customer/BTM/entity/JoinType';
import {useGetUserQuery} from 'components/customer/Auth/store/userSlice';

export const Overview = () => {
    const {data: user} = useGetUserQuery();
    const shape = useAppSelector(selectType, shallowEqual);
    const corners = useAppSelector(selectCorners, shallowEqual);
    const joins = useAppSelector(selectJoins, shallowEqual);

    const hasAtleastACorner = useMemo(() => {
        if (corners) {
            return corners
                .filter((corner) => !corner?.addedThroughEndProfile)
                .map((corner) => corner.cutoff)
                .some(Boolean);
        }

        return false;
    }, [corners]);

    const showJoins = useMemo(() => {
        if (shape && shape.type == Shape.SQR) {
            return (
                joins &&
                joins.some((join) => join.joinType == JoinType.BUTT_JOIN)
            );
        }

        return true;
    }, [joins, shape]);

    return (
        <AbsoluteContainer>
            <Title>Material:</Title>
            <OverviewSection>
                <OverviewMaterialSwatch smallPreview={true} />
                <OveriviewMaterialInfo />
            </OverviewSection>
            <StyledHr />

            <Title>Profile:</Title>
            <OverviewProfiles />

            {showJoins ? (
                <>
                    <StyledHr />
                    <Title>
                        {shape && shape.type == Shape.USHAPE ? 'Joins' : 'Join'}
                        :
                    </Title>
                    <OverviewJoins />
                </>
            ) : null}

            {hasAtleastACorner ? (
                <>
                    <StyledHr />
                    <Title>Corners:</Title>
                    <OverviewCorners />
                </>
            ) : null}

            {user && user?.manufacturerEnableVariation ? (
                <>
                    <StyledHr />
                    <Title>Variation Request:</Title>
                    <OverviewVariationRequest />
                </>
            ) : null}
        </AbsoluteContainer>
    );
};

const OverviewMaterialSwatch = styled(MaterialSwatch)`
    &&& {
        margin: 0;
    }
`;

const OveriviewMaterialInfo = styled(MaterialInformation)`
    &&& {
        margin-left: 15px;
    }
`;

const OverviewSection = styled.section`
    display: flex;
    align-items: center;
    padding-left: 15px;
`;

const StyledHr = styled.hr`
    margin: 15px 0;
`;
