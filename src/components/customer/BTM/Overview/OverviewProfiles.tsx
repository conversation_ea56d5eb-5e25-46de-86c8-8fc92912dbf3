import React, {useMemo} from 'react';
import {useAppSelector} from 'store/customer';
import {
    selectMaterial,
    selectPaths,
} from 'components/customer/BTM/store/btmSlice';
import {shallowEqual} from 'react-redux';
import styled from 'styled-components';
import {getSideName} from 'components/customer/BTM/helper/getSideByProfiles';
import {groupBy} from 'lodash';
import {useListEdgeProfilesQuery} from 'components/customer/BTM/store/btmApi';
import {BenchtopEdgeProfile} from 'components/customer/BTM/entity/BenchtopEdgeProfile';
import {OverlayTrigger} from 'shared/components/OverlayTrigger';

interface ProfileGroupInterface {
    profile: BenchtopEdgeProfile;
    paths: string[];
}

export const OverviewProfiles = () => {
    const paths = useAppSelector(selectPaths, shallowEqual);
    const material = useAppSelector(selectMaterial, shallowEqual);
    const {data: {data: edgeProfiles} = {data: [] as BenchtopEdgeProfile[]}} =
        useListEdgeProfilesQuery(
            {materialId: material && material.id},
            {skip: typeof material == 'undefined'}
        );

    const profiles = useMemo(() => {
        if (paths && edgeProfiles) {
            const profilesGroup = groupBy(
                paths.map((path, index) => ({...path, index})),
                'profile'
            );
            const profiles: ProfileGroupInterface[] = [];

            Object.keys(profilesGroup).forEach((key) => {
                if (key != 'undefined' && key != '0' && key != 'null') {
                    const profile = edgeProfiles.find(
                        (edgeProfile) => edgeProfile.id == Number(key)
                    );
                    const profilePaths = profilesGroup[String(key)];
                    let pathNames: string[];
                    if (profilePaths) {
                        pathNames = profilePaths.map(getSideName(paths));
                    }

                    profiles.push({
                        profile,
                        paths: pathNames,
                    });
                }
            });

            if (profiles.length > 0) return profiles;
        }

        return [];
    }, [edgeProfiles, paths]);

    return (
        <ProfileOverview>
            {profiles.map((profile, index) => {
                const edgeProfile = profile.profile;
                const profiledSides = profile.paths;

                return (
                    <Profile key={index}>
                        <div>
                            <div>{edgeProfile && edgeProfile.name}</div>
                            <ModifiedOverlay
                                placement="left-start"
                                overlay={
                                    <Overlay>
                                        <ImageContainer>
                                            <img
                                                src={
                                                    edgeProfile &&
                                                    edgeProfile?.image?.name
                                                        ? edgeProfile?.image
                                                              ?.name
                                                        : ''
                                                }
                                            />
                                        </ImageContainer>
                                    </Overlay>
                                }>
                                <img
                                    src={
                                        edgeProfile && edgeProfile?.image?.name
                                    }
                                />
                            </ModifiedOverlay>
                        </div>
                        {profiledSides.length > 0 ? (
                            <>
                                <strong>Edges: </strong>{' '}
                                {profiledSides.join(', ')} <br />
                            </>
                        ) : (
                            <></>
                        )}
                    </Profile>
                );
            })}
        </ProfileOverview>
    );
};

const ModifiedOverlay = styled(OverlayTrigger)`
    > .tooltip-inner {
        padding: 0;
        background-color: transparent !important;
        border: 0 !important;
    }
`;

const ImageContainer = styled.div`
    > img {
        width: 100%;
    }
`;

const ProfileOverview = styled.section`
    display: flex;
    flex-wrap: wrap;
`;

const Profile = styled.div`
    font-size: 0.85em;
    margin: 15px 15px 0 4px;
    background: white;
    border-radius: 5px;
    box-shadow: 0px 1px 6px 1px #b1b1b1;
    padding: 10px;

    > div {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        max-width: 150px;

        > div {
            width: 50%;
        }

        > img {
            padding-left: 10px;
            width: calc(50% - 10px);
        }
    }
`;

const Overlay = styled.div`
    z-index: 100;
    padding: 5px;
    background: white;
    border-radius: 8px;
    border: 1px solid rgb(var(--secondary_colour));

    > img {
        border-radius: 8px;
    }
`;
