import React, {useMemo} from 'react';
import {useAppSelector} from 'store/customer';
import {useListJoinQuery} from 'components/customer/BTM/store/btmApi';
import {selectJoins} from 'components/customer/BTM/store/btmSlice';
import {JoinOrientation} from 'components/customer/BTM/entity/Join';
import styled from 'styled-components';
import {Icon} from 'shared/helpers';
import {JoinInfo} from 'components/customer/BTM/entity/JoinInfo';
import {JoinType} from 'components/customer/BTM/entity/JoinType';
import {cloneDeep, groupBy} from 'lodash';
import {getSideSpec} from 'components/customer/BTM/entity/Side';

export const OverviewJoins = () => {
    const benchtopJoins = useAppSelector(selectJoins);
    const {isLoading, data: {data: joins} = {data: [] as JoinInfo[]}} =
        useListJoinQuery();

    const selectedJoins = useMemo(() => {
        if (joins && benchtopJoins) {
            return cloneDeep(benchtopJoins)
                .filter(
                    (join) =>
                        join.selected && join.joinType !== JoinType.BUTT_JOIN
                )
                .sort((a, b) => a.side - b.side)
                .map((join) => {
                    switch (join.joinType) {
                        case JoinType.DOG_LEG_MITRE:
                        case JoinType.FULL_MITRE:
                            join.image = 'join_diagonal.svg';
                            break;
                        case JoinType.MASONS_MITRE:
                            if (
                                join.orientation == JoinOrientation.HORIZONTAL
                            ) {
                                join.image = 'join_horizontal.svg';
                            } else {
                                join.image = 'join_vertical.svg';
                            }
                            break;
                    }

                    return join;
                })
                .map((join) => {
                    const joinInfo = joins.find(
                        (join_) => join_.joinType == join.joinType
                    );

                    return {
                        join,
                        joinInfo,
                    };
                });
        }
    }, [benchtopJoins, joins]);

    const buttJoins = useMemo(() => {
        if (benchtopJoins && joins) {
            return groupBy(
                benchtopJoins.filter(
                    (join) =>
                        join.selected && join.joinType === JoinType.BUTT_JOIN
                ),
                'benchSide'
            );
        }
    }, [benchtopJoins, joins]);

    if (isLoading) {
        return null;
    }

    return (
        <JoinSection>
            {selectedJoins
                ? selectedJoins.map((data) => {
                      return (
                          <div key={`${data.joinInfo.name}-${data.join.side}`}>
                              <strong>{data?.joinInfo?.name}</strong>
                              <JoinIcon iconName={data?.join?.image} />
                          </div>
                      );
                  })
                : null}

            {buttJoins
                ? Object.keys(buttJoins).map((side) => {
                      const sideSpec = getSideSpec(Number(side));
                      const join = buttJoins[Number(side)][0];

                      const joinInfo = joins.find(
                          (join_) => join_.joinType == join.joinType
                      );

                      if (joinInfo) {
                          return (
                              <div key={side}>
                                  <strong>
                                      {joinInfo.name} {sideSpec.key}:
                                  </strong>{' '}
                                  {buttJoins[Number(side)]
                                      .map((join) => join.value)
                                      .join('mm, ')}
                                  mm
                              </div>
                          );
                      }
                  })
                : null}
        </JoinSection>
    );
};

const JoinIcon = styled(Icon)`
    width: 30px;
    height: 30px;
`;

const JoinSection = styled.section`
    display: flex;
    align-items: center;
    font-size: 0.9em;
    gap: 10px;
    flex-wrap: wrap;
    align-items: flex-start;

    > div {
        flex: 1 0 calc(25% - 30px);
        > img {
            margin-left: 10px;
        }
    }
`;
