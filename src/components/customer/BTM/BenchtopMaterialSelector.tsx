import React, {useCallback} from 'react';
import {useNavigate} from 'shared/reload/helper/useNavigate';
import {
    Strong,
    CardContainer,
    Card,
    CardFooter,
    Text,
    BTMImageWrapper,
} from 'components/customer/BTM/BenchtopSelector';
import {useAppDispatch, useAppSelector} from 'store/customer';
import {
    materialTypeSet,
    selectMaterialType,
    selectType,
} from 'components/customer/BTM/store/btmSlice';
import {Loader} from 'shared/helpers';
import {useSelectedBenchAndMaterial} from 'components/customer/BTM/helper/useSelectedBenchAndMaterial';
import {BenchtopMaterialType} from 'components/customer/BTM/entity/BenchtopMaterialType';
import {shallowEqual} from 'react-redux';
import {Image} from 'shared/components/ImageLoader';

export const BenchtopMaterialSelector = () => {
    const benchtopType = useAppSelector(selectType, shallowEqual);
    const materialType = useAppSelector(selectMaterialType, shallowEqual);
    const {
        baseUrl,
        benchtopMaterialTypes,
        benchTypesLoading: isLoading,
    } = useSelectedBenchAndMaterial({
        selectedMaterialType: materialType,
    });
    const navigate = useNavigate();
    const dispatch = useAppDispatch();

    const selectHandler = useCallback(
        (materialType: BenchtopMaterialType) => () => {
            dispatch(materialTypeSet(materialType));
            navigate(
                `${baseUrl}/type/${benchtopType.type}/materialType/${materialType.id}/material`
            );
        },
        [navigate]
    );

    if (isLoading) return <Loader loader={true} />;

    return (
        <div className="benchtop-selector jumbotron">
            <Strong>Select Bench Material</Strong>

            <CardContainer>
                {benchtopMaterialTypes
                    ? benchtopMaterialTypes.map((materialType) => {
                          return (
                              <Card
                                  key={materialType.id}
                                  onClick={selectHandler(materialType)}>
                                  <BTMImageWrapper>
                                      <Image src={materialType?.image?.name} />
                                  </BTMImageWrapper>
                                  <CardFooter>{materialType.name}</CardFooter>
                              </Card>
                          );
                      })
                    : null}
            </CardContainer>

            <Text>
                The bench will be able to be customized in the next steps.
            </Text>
        </div>
    );
};
