import React, {useMemo} from 'react';
import {useAppSelector} from 'store/customer';
import {selectMaterial} from 'components/customer/BTM/store/btmSlice';
import styled from 'styled-components';
import {Title} from 'components/customer/BTM/Material';
import {useFetchEdgeProfile} from 'components/customer/BTM/helper/useFetchEdgeProfile';
import {Loader} from 'shared/helpers';
import {FormFactor} from 'components/customer/BTM/entity/BenchtopMaterial';
import {Profile} from 'components/customer/BTM/EdgeProfile/Profile';

export const EdgeProfile = () => {
    const material = useAppSelector(selectMaterial);
    const {edgeProfiles, isLoading, isUninitialized} = useFetchEdgeProfile();

    const displayEdgeProfiles = useMemo(() => {
        if (edgeProfiles) {
            if (material.formFactor == FormFactor.NONE) {
                return edgeProfiles.filter(
                    (profile) =>
                        !profile.is_postformed_profile || !profile.is_end_only
                );
            }

            return edgeProfiles.filter((profile) => !profile.is_end_only);
        }

        return [];
    }, [edgeProfiles, material]);

    if (isLoading || isUninitialized) return <Loader loader={true} />;

    return (
        <AbsoluteContainer>
            <Title>Select Profile Type:</Title>
            <ProfilesRow>
                <ProfilesColumn>
                    {displayEdgeProfiles
                        .filter((_, index) => (index + 1) & 1)
                        .map((edgeProfile) => {
                            return (
                                <Profile
                                    edgeProfile={edgeProfile}
                                    key={edgeProfile.id}
                                />
                            );
                        })}
                </ProfilesColumn>
                <ProfilesColumn>
                    {displayEdgeProfiles
                        .filter((_, index) => ((index + 1) & 1) == 0)
                        .map((edgeProfile) => {
                            return (
                                <Profile
                                    edgeProfile={edgeProfile}
                                    key={edgeProfile.id}
                                />
                            );
                        })}
                </ProfilesColumn>
            </ProfilesRow>
        </AbsoluteContainer>
    );
};

export const AbsoluteContainer = styled.div`
    padding: 15px 0 15px 15px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
`;

const ProfilesRow = styled.div`
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
`;

const ProfilesColumn = styled.div`
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
    flex: 1;
`;
