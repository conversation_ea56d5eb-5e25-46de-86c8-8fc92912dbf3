import React, {useCallback} from 'react';
import {useAppDispatch, useAppSelector} from 'store/customer';
import {
    edgeProfileSet,
    isProfileSelected,
} from 'components/customer/BTM/store/btmSlice';
import {Form} from 'react-bootstrap';
import {BenchtopEdgeProfile} from 'components/customer/BTM/entity/BenchtopEdgeProfile';
import styled from 'styled-components';
import {applyBenchEndShapes, updateProfiles} from '../helper/edgeProfiles';

interface ProfileInterface {
    edgeProfile: BenchtopEdgeProfile;
}

export const Profile = ({edgeProfile}: ProfileInterface) => {
    const dispatch = useAppDispatch();
    const isSelected = useAppSelector((state) =>
        isProfileSelected(state, edgeProfile.id)
    );

    const onClickHandler = useCallback(() => {
        dispatch(edgeProfileSet(edgeProfile));
        updateProfiles(edgeProfile)
            .then(() => {
                void applyBenchEndShapes();
            })
            .catch(() => {
                // log error here later if need be
            });
    }, []);

    return (
        <ProfileContainer onClick={onClickHandler}>
            <Checkbox className="cbc-radio">
                <Form.Check
                    type="radio"
                    id={`boolean_${edgeProfile.name}`}
                    name={`edge_profile`}
                    checked={isSelected}
                    label={edgeProfile.name}
                    readOnly={true}
                />
            </Checkbox>
            <img src={edgeProfile?.image?.name} />
        </ProfileContainer>
    );
};

const ProfileContainer = styled.div`
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    align-content: center;
    flex-basis: 50%;
    margin-bottom: 15px;
    cursor: pointer;

    img {
        max-width: 50%;
    }
`;

const Checkbox = styled.div`
    width: 30%;
    margin-right: 15px;
`;
