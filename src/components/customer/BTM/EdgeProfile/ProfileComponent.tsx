import React, {useCallback} from 'react';
import {useAppDispatch} from 'store/customer';
import {
    edgeProfileSet,
    isProfileSelected,
} from 'components/customer/BTM/store/btmSlice';
import {Form} from 'react-bootstrap';
import {connect} from 'react-redux';
import {AppState} from 'store/customer/storeSetup';
import {BenchtopEdgeProfile} from 'components/customer/BTM/entity/BenchtopEdgeProfile';
import styled from 'styled-components';

interface ProfileInterface {
    isSelected?: boolean;
    edgeProfile: BenchtopEdgeProfile;
}

const ProfileComponent = ({edgeProfile, isSelected}: ProfileInterface) => {
    const dispatch = useAppDispatch();

    const onClickHandler = useCallback(() => {
        dispatch(edgeProfileSet(edgeProfile));
    }, []);

    return (
        <Profile onClick={onClickHandler}>
            <Checkbox className="cbc-radio">
                <Form.Check
                    type="radio"
                    id={`boolean_${edgeProfile.name}`}
                    name={`edge_profile`}
                    checked={isSelected}
                    label={edgeProfile.name}
                    readOnly={true}
                />
            </Checkbox>
            <img src={edgeProfile?.image?.name} />
        </Profile>
    );
};

export const ProfileConnect = connect(
    (state: AppState, {edgeProfile}: ProfileInterface) => ({
        isSelected: isProfileSelected(state, edgeProfile.id),
    })
)(ProfileComponent);

const Profile = styled.div`
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    align-content: center;
    flex-basis: 50%;
    margin-bottom: 15px;
    cursor: pointer;

    img {
        max-width: 50%;
    }
`;

const Checkbox = styled.div`
    width: 30%;
    margin-right: 15px;
`;
