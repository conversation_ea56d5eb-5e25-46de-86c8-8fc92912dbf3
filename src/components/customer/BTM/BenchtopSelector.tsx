import React, {useCallback, useEffect} from 'react';
import {useNavigate} from 'shared/reload/helper/useNavigate';
import styled from 'styled-components';
import {BenchtopType} from 'components/customer/BTM/entity/BenchtopType';
import {useAppDispatch, useAppSelector} from 'store/customer';
import {
    clearAll,
    selectType,
    typeSet,
} from 'components/customer/BTM/store/btmSlice';
import {useSelectedBenchAndMaterial} from 'components/customer/BTM/helper/useSelectedBenchAndMaterial';
import {Loader} from 'shared/helpers';
import {shallowEqual} from 'react-redux';
import {ImageWrapper} from 'shared/components/ImageLoader';

import '../BenchtopDesigner/Benchtop.scss';

export const BenchtopSelector = () => {
    const benchtopType = useAppSelector(selectType, shallowEqual);
    const {
        baseUrl,
        benchTypes,
        benchTypesLoading: isLoading,
    } = useSelectedBenchAndMaterial({selectedBenchType: benchtopType});
    const navigate = useNavigate();
    const dispatch = useAppDispatch();

    const selectHandler = useCallback(
        (benchtop: BenchtopType) => () => {
            dispatch(typeSet(benchtop, true));
            navigate(`${baseUrl}/type/${benchtop.type}`);
        },
        [navigate]
    );

    useEffect(() => {
        dispatch(clearAll());
    }, []);

    if (isLoading) {
        return <Loader loader={true} />;
    }

    return (
        <div className="benchtop-selector jumbotron">
            <Strong>Select a Bench Type</Strong>

            <CardContainer>
                {benchTypes
                    ? benchTypes.map((benchtopType) => {
                          return (
                              <Card
                                  key={benchtopType.id}
                                  onClick={selectHandler(benchtopType)}>
                                  <BTMImageWrapper>
                                      <img src={benchtopType.image} />
                                  </BTMImageWrapper>
                                  <CardFooter>{benchtopType.name}</CardFooter>
                              </Card>
                          );
                      })
                    : null}
            </CardContainer>

            <Text>
                The bench will be able to be customized in the next steps.
            </Text>
        </div>
    );
};

export const BTMImageWrapper = styled(ImageWrapper)`
    display: flex;
    flex: 1;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    height: 250px;
    overflow: hidden;

    > img {
        width: 100%;
    }
`;

export const Text = styled.p`
    font-size: 0.95rem;
    color: rgb(102 102 102);
    margin: 0;
`;

export const Strong = styled.strong`
    color: rgb(var(--primary_colour));
    font-size: 0.95rem;
`;

export const CardContainer = styled.div`
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
`;

export const Card = styled.div`
    border-radius: 10px;
    border: 1px solid rgb(var(--primary_colour));
    width: 280px;
    height: 280px;
    margin: 15px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
`;

export const CardFooter = styled.div`
    background: rgb(var(--primary_colour));
    color: white;
    font-weight: bold;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    text-align: center;
    padding: 5px 0;
`;