import React, {useMemo} from 'react';
import {Corner} from 'components/customer/BTM/Corners/Corner';
import {Corner as CornerInterface} from 'components/customer/BTM/entity/Corner';
import {useAppSelector} from 'store/customer';
import {selectCorners} from 'components/customer/BTM/store/btmSlice';
import {AbsoluteContainer} from 'components/customer/BTM/EdgeProfile';
import {Title} from 'components/customer/BTM/Material';
import styled from 'styled-components';
import {Icon} from 'shared/helpers';
import {cloneDeep, sortBy} from 'lodash';

const CornerIcon = styled(Icon)`
    width: 20px !important;
    margin: 0 5px;
`;

export const Corners = () => {
    const corners = useAppSelector(selectCorners);

    const cutoffs = useMemo(() => {
        if (corners) {
            const cornersList = corners
                .map((corner, index) => ({...corner, index}))
                .filter(
                    (corner) =>
                        !corner.isJoin &&
                        !corner?.addedThroughEndProfile &&
                        corner.cutoff
                );

            if (cornersList) {
                return sortBy(cloneDeep(cornersList), (corner) =>
                    corner.name.toLowerCase()
                );
            }
        }

        return [] as CornerInterface[];
    }, [corners]);

    if (corners) {
        return (
            <AbsoluteContainer>
                {cutoffs.length == 0 ? (
                    <Title>
                        Please click on{' '}
                        <CornerIcon iconName="Button-Edit-Primary.svg" />
                        in the preview, to add Clip/Notch on your benchtop
                    </Title>
                ) : (
                    <></>
                )}

                {cutoffs.map((corner, index) => {
                    return (
                        <React.Fragment key={index}>
                            <Corner
                                key={index}
                                corner={corner}
                                index={corner.index}
                                inline={true}
                            />
                            {index < corners.length - 1 ? <hr /> : <></>}
                        </React.Fragment>
                    );
                })}
            </AbsoluteContainer>
        );
    }

    return <></>;
};
