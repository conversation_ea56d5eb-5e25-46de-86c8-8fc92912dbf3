import React from 'react';
import {Shape} from 'components/customer/BTM/entity/Shape';
import {Path} from 'components/customer/BTM/entity/Path';
import {useEdgeTogglePosition} from 'components/customer/BTM/helper/useEdgeTogglePosition';
import {EdgeToggle} from 'components/customer/BTM/Preview/EdgeToggle';
import {Tab} from 'components/customer/BTM/entity/Tab';

interface EdgeToggleInterface {
    dimension: number[];
    shape: Shape;
    paths: Path[];
    currentTab?: Tab;
}

export const EdgeToggles = ({
    dimension,
    shape,
    paths,
    currentTab,
}: EdgeToggleInterface) => {
    const positions = useEdgeTogglePosition(dimension, shape, paths);

    if (positions) {
        return positions.map((position) => (
            <EdgeToggle
                key={`${position.index}-${position.side}`}
                position={position}
                paths={paths}
                currentTab={currentTab}
                shape={shape}
            />
        ));
    }

    return null;
};
