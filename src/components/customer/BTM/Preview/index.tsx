import React, {useMemo} from 'react';
import styled from 'styled-components';
import {Preview} from 'components/customer/BTM/Preview/Preview';
import {Tab, getMessage} from 'components/customer/BTM/entity/Tab';

const BTMPreview = styled.div`
    background: #dddddd;
    flex: 1.85;
    display: flex;
    flex-direction: column;
`;

const Text = styled.p<{$textAlign?: string}>`
    color: #545454;
    margin: 8px 0;
    text-align: ${({$textAlign}) => ($textAlign ? $textAlign : 'center')};
    padding-right: ${({$textAlign}) => ($textAlign == 'right' ? '10px' : '0')};
    font-size: 0.85rem;
    height: 23px;

    > span {
        color: rgb(var(--secondary_colour));
    }
`;

export const BenchtopPreview = ({currentTab}: {currentTab: Tab}) => {
    const text = useMemo(() => {
        const message = getMessage(currentTab);

        if (message) {
            return message;
        }

        return '';
    }, [currentTab]);

    return (
        <BTMPreview id="btm-preview">
            <Text>{text}</Text>
            <div style={{padding: '0 100px', flex: '1', display: 'flex'}}>
                <Preview currentTab={currentTab} />
            </div>
            <Text $textAlign="right">
                All measurements in <span>mm</span>
            </Text>
        </BTMPreview>
    );
};
