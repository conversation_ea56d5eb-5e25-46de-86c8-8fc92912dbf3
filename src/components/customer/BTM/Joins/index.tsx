import React from 'react';
import styled from 'styled-components';
import {useAppSelector} from 'store/customer';
import {
    selectJoinSides,
    selectType,
} from 'components/customer/BTM/store/btmSlice';
import {shallowEqual} from 'react-redux';
import {Loader} from 'shared/helpers';
import {useListJoinQuery} from 'components/customer/BTM/store/btmApi';
import {JoinInfo} from 'components/customer/BTM/entity/JoinInfo';
import {Shape} from 'components/customer/BTM/entity/Shape';
import {Join} from 'components/customer/BTM/Joins/Join';

export const Joins = () => {
    const {
        isLoading,
        data: {data: joinsInformation} = {data: [] as JoinInfo[]},
    } = useListJoinQuery();
    const shape = useAppSelector(selectType);
    const sides = useAppSelector(
        (state) => selectJoinSides(state),
        shallowEqual
    );

    if (isLoading) {
        return <Loader loader={true} />;
    }

    return (
        <JoinContainer>
            {sides
                ? sides.map((side, index) => {
                      return (
                          <React.Fragment key={side}>
                              {shape.type == Shape.USHAPE ? (
                                  <JoinTitle>
                                      Select Join - #{index + 1}
                                  </JoinTitle>
                              ) : (
                                  <JoinTitle>Select Join</JoinTitle>
                              )}
                              <Join
                                  side={side}
                                  joinsInformation={joinsInformation}
                              />
                          </React.Fragment>
                      );
                  })
                : null}
        </JoinContainer>
    );
};

const JoinContainer = styled.div`
    padding: 30px 0 30px 30px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
`;

const JoinTitle = styled.strong`
    display: block;
    color: rgb(var(--primary_colour));
`;
