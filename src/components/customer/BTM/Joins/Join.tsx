import React, {useCallback} from 'react';
import {Icon} from 'shared/helpers';
import styled from 'styled-components';
import {Join as JoinInterface} from 'components/customer/BTM/entity/Join';
import {JoinInfo} from 'components/customer/BTM/entity/JoinInfo';
import {useAppDispatch, useAppSelector} from 'store/customer';
import {
    joinSet,
    selectJoinsBySide,
} from 'components/customer/BTM/store/btmSlice';
import {getJoinInfo} from 'components/customer/BTM/helper/getJoinInfo';
import {Form} from 'react-bootstrap';
import {shallowEqual} from 'react-redux';

interface JoinComponentInterface {
    side?: string;
    joins?: JoinInterface[];
    joinsInformation?: JoinInfo[];
}

export const Join = ({side, joinsInformation}: JoinComponentInterface) => {
    const dispatch = useAppDispatch();
    const joins = useAppSelector(
        (state) => selectJoinsBySide(state, side),
        shallowEqual
    );

    const clickHandle = useCallback(
        (join: JoinInterface) => () => {
            const index = join.index;
            const side = join.side;
            dispatch(joinSet(true, index, side));
        },
        []
    );

    return (
        <JoinComponentDiv key={`${side}_side`}>
            {joins.map((join, index) => {
                if (join.disabled) {
                    return (
                        <React.Fragment
                            key={btoa(`${join.joinType}_${join.side}`)}
                        />
                    );
                }

                const joinInfo = getJoinInfo(join, joinsInformation);

                return (
                    <JoinComponentDiv
                        key={`${join.joinType}_${join.side}_${join.orientation}`}>
                        <Checkbox className="cbc-radio">
                            <Form.Check
                                onClick={clickHandle(join)}
                                type="radio"
                                id={`boolean_${side}_${index}`}
                                name={`join_${join.side}`}
                                checked={join.selected}
                                label={joinInfo.name}
                                readOnly={true}
                                disabled={join.disabled}
                            />
                        </Checkbox>
                        <JoinIcon
                            onClick={clickHandle(join)}
                            iconName={join.image}
                        />
                    </JoinComponentDiv>
                );
            })}
        </JoinComponentDiv>
    );
};

const Checkbox = styled.div`
    margin-right: 15px;
`;

const JoinComponentDiv = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    cursor: pointer;
`;

const JoinIcon = styled(Icon)`
    width: 40px;
    height: 40px;
`;
