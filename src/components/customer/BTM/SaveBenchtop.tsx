import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useNavigate} from 'shared/reload/helper/useNavigate';
import {genericMessageHandler, Loader} from 'shared/helpers';
import {useGetUserQuery} from 'components/customer/Auth/store/userSlice';
import {useAppSelector} from 'store/customer';
import {
    selectMaterial,
    selectEdgeProfile,
    selectVariationRequest,
    selectCorners,
    selectDimension,
    selectJoins,
    selectPaths,
    selectType,
    selectCoordinates,
    selectMessage,
    selectCornerErrors,
    selectButtJoinPartErrors,
} from 'components/customer/BTM/store/btmSlice';
import {Bench} from 'components/customer/BTM/entity/Bench';
import {
    getCoordinates,
    getCorners,
    getDimensions,
    getJoins,
    getProfiles,
} from 'components/customer/BTM/helper/saveHelpers';
import {
    useListBenchesQuery,
    useListJoinQuery,
    useSaveBenchMutation,
    useUpdateBenchMutation,
} from 'components/customer/BTM/store/btmApi';
import {JoinInfo} from 'components/customer/BTM/entity/JoinInfo';
import {useFetchEdgeProfile} from 'components/customer/BTM/helper/useFetchEdgeProfile';
import {useGetBenchId} from 'components/customer/BTM/helper/useGetBenchId';
import {useJobContext, useNotificationContext} from 'contexts';
import {validateProfile} from 'components/customer/BTM/helper/validateProfile';
import {ShowValidationMessage} from 'shared/helpers/ShowValidationMessage';
import {getDimensionErrors} from 'components/customer/BTM/store/middleware/updateListenerMiddleware';
import {PartialJob} from 'shared/types/PartialJob';
import {uniq} from 'lodash';
import {JoinType} from 'components/customer/BTM/entity/JoinType';
import {SaveButton} from 'shared/components/SaveButton';
import {useMaterialHelper} from 'components/customer/BTM/helper/useMaterialHelper';

export const SaveBenchtop = () => {
    const navigate = useNavigate();
    const {job, updateTotalProductCountManual} = useJobContext() as PartialJob;
    const {notify} = useNotificationContext();
    const {benchId, setBench} = useGetBenchId();

    const benchType = useAppSelector(selectType);
    const dimensions = useAppSelector(selectDimension);
    const joins = useAppSelector(selectJoins);
    const material = useAppSelector(selectMaterial);
    const edgeProfile = useAppSelector(selectEdgeProfile);
    const note = useAppSelector(selectVariationRequest);
    const corners = useAppSelector(selectCorners);
    const paths = useAppSelector(selectPaths);
    const coordinates = useAppSelector(selectCoordinates);

    const cornerErrors = useAppSelector(selectCornerErrors);
    const dimensionMessages = useAppSelector(selectMessage);
    const buttJoinPartsErrors = useAppSelector(selectButtJoinPartErrors);
    const {checkMaterialAvailability} = useMaterialHelper();

    const [errorMessages, setErrorMessages] = useState<string[]>();

    const {
        isLoading: isLoadingEdgeProfile,
        isUninitialized,
        edgeProfiles,
    } = useFetchEdgeProfile();
    const [save, {isLoading: isSaving, error: saveError}] =
        useSaveBenchMutation();
    const [update, {isLoading: isUpdating, error: updateError}] =
        useUpdateBenchMutation();
    const {data: userProfile} = useGetUserQuery();
    const {
        isLoading,
        data: {data: joinsInformation} = {data: [] as JoinInfo[]},
    } = useListJoinQuery();
    const {data: {data: benches} = {data: [] as Bench[]}} = useListBenchesQuery(
        {jobId: Number(job.id)},
        {skip: benchId == null && typeof job.id === 'undefined'}
    );

    const benchDB = useMemo(() => {
        if (benchId && benches.length > 0) {
            const bench = benches.find((bench) => bench.id == Number(benchId));

            return bench;
        }
    }, [benches, benchId]);

    const redirectToAddAnotherTop = useCallback(() => {
        const {displayId: jobId} = job;
        const url = `/v2/job/${jobId}/benchtop-module`;
        navigate(url);
    }, [job]);

    const handleSave = useCallback(async () => {
        // Initiating bench object for saving the data
        const bench: Bench = {
            customer_id: userProfile.id,
            job_id: Number(job.id),
            shape_id: benchType.id,
            type_id: material.type.id,
            material_id: material.id,
            comment: note,
            quantity: 1,
            options: [],
        };

        let validBench = true;
        let messages: string[] = [];

        const isMaterialAvailable = checkMaterialAvailability(material);

        if (!isMaterialAvailable) {
            throw new Error('material not available');
        }

        // Checkig if profiles selected is valid based on material selected.
        const {isValid, message} = validateProfile(
            benchType.type,
            material,
            edgeProfiles,
            paths
        );

        // Setting edge profile validation message
        if (!isValid) {
            validBench = false;
            messages = [message];
        }

        // Checking for dimension issues as well.
        let dimensionErrors = getDimensionErrors(
            dimensions,
            benchType.type,
            joins,
            material,
            corners,
            true
        );

        if (dimensionMessages.length > 0) {
            const messages = dimensionMessages.filter(
                (message) => message.length > 0
            );

            if (messages.length > 0)
                dimensionErrors = uniq([...dimensionErrors, ...messages]);
        }

        // setting dimension validation messages
        if (dimensionErrors.length > 0) {
            validBench = false;
            messages = [...messages, ...dimensionErrors];
        }

        if (buttJoinPartsErrors && buttJoinPartsErrors.length > 0) {
            buttJoinPartsErrors.forEach((error) => {
                if (error.message) {
                    validBench = false;
                    messages.push(error.message);
                }
            });
        }

        if (joins && joins.length > 0) {
            joins.forEach((join) => {
                if (join.joinType == JoinType.BUTT_JOIN && join.error) {
                    validBench = false;
                    messages.push(join.error);
                }
            });
        }

        if (cornerErrors) {
            cornerErrors.forEach((corner) => {
                if (corner.x != '' || corner.y != '') {
                    validBench = false;
                    messages.push(
                        `${corner?.isArc ? 'Arc' : 'Corner'} ${
                            corner.name
                        } does not have valid depths`
                    );
                }
            });
        }

        // If bench is not valid stop function execution and set error messages
        if (!validBench) {
            setErrorMessages(messages);
            throw new Error('bench not valid');
        }

        bench.options.push(getCorners(corners, benchDB));
        bench.options.push(getDimensions(paths, dimensions, benchDB));

        if (joins && joins.length > 0) {
            bench.options.push(
                getJoins(joins, joinsInformation, paths, dimensions, benchDB)
            );
        }

        bench.options.push(getProfiles(paths, benchDB));
        bench.options.push(getCoordinates(coordinates, benchDB));

        try {
            let saveUpdate;
            let id: number;

            if (benchId == null) {
                saveUpdate = await save(bench);

                if (saveUpdate && 'data' in saveUpdate) {
                    id = saveUpdate.data;
                    if (typeof id !== 'undefined' && id !== null) {
                        setBench(id);
                    }
                }
            } else {
                saveUpdate = await update({
                    bench,
                    id: Number(benchId),
                });
            }

            if (saveUpdate && saveUpdate.hasOwnProperty('error')) {
                // NOTE: Check saveUpdate.error for more details about this failure
                throw new Error();
            }

            // note: only update the count manually when adding and not when updating
            if (benchId == null) updateTotalProductCountManual();
        } catch (e) {
            genericMessageHandler(notify, {
                message: 'Could not save the benchtop, Please try again later',
            });
            throw new Error('Could not save the benchtop');
        }
    }, [
        note,
        benchId,
        benchDB,
        userProfile,
        benchType,
        material,
        edgeProfile,
        corners,
        paths,
        dimensions,
        joins,
        joinsInformation,
        coordinates,
        job,
        edgeProfiles,
        dimensionMessages,
        cornerErrors,
        buttJoinPartsErrors,
        navigate,
        updateTotalProductCountManual,
    ]);

    const clearMessages = useCallback(() => {
        setErrorMessages([]);
    }, []);

    useEffect(() => {
        if (saveError) {
            if ('status' in saveError) {
                const errMsg: {
                    error?: string;
                } = 'error' in saveError ? saveError : saveError.data;
                setErrorMessages([errMsg.error]);
            } else if ('message' in saveError) {
                // Handle SerializedError
                setErrorMessages([saveError.message]);
            } else {
                setErrorMessages([
                    'Could not save the benchtop, Please try again later',
                ]);
            }
        }

        if (updateError) {
            if ('status' in updateError) {
                const errMsg: {
                    error?: string;
                } = 'error' in updateError ? updateError : updateError.data;
                setErrorMessages([errMsg.error]);
            } else if ('message' in updateError) {
                // Handle SerializedError
                setErrorMessages([updateError.message]);
            } else {
                setErrorMessages([
                    'Could not save the benchtop, Please try again later',
                ]);
            }
        }
    }, [saveError, updateError]);

    return (
        <>
            {isSaving || isUpdating ? <Loader loader={true} /> : null}
            <ShowValidationMessage
                title="Benchtop"
                messages={errorMessages}
                onHide={clearMessages}
            />
            <SaveButton
                disabled={
                    isLoading ||
                    typeof material == 'undefined' ||
                    isUninitialized ||
                    isLoadingEdgeProfile
                }
                title="Benchtops"
                message={<strong>Benchtop successfully saved</strong>}
                action={handleSave}
                anotherTopAction={redirectToAddAnotherTop}
            />
        </>
    );
};
