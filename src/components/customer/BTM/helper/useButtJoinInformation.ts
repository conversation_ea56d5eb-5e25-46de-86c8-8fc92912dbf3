import {useListJoinQuery} from 'components/customer/BTM/store/btmApi';
import {JoinInfo} from 'components/customer/BTM/entity/JoinInfo';
import {useEffect} from 'react';
import {useAppDispatch} from 'store/customer';
import {minButtJoinPartSizeSet} from 'components/customer/BTM/store/btmSlice';

export const useButtJoinInformation = () => {
    const dispatch = useAppDispatch();
    const {
        isLoading,
        data: {data: joinsInformation} = {data: [] as JoinInfo[]},
    } = useListJoinQuery();

    useEffect(() => {
        const buttJoin = joinsInformation.find(
            (join) => join.joinType === 'BUTT_JOIN'
        );

        if (buttJoin) {
            dispatch(minButtJoinPartSizeSet(buttJoin.minimumEdgeDistance));
        }
    }, [joinsInformation]);

    return {
        isLoading,
    };
};
