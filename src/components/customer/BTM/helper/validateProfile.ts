import {BenchtopMaterial} from 'components/customer/BTM/entity/BenchtopMaterial';
import {Path} from 'components/customer/BTM/entity/Path';
import {BenchtopEdgeProfile} from 'components/customer/BTM/entity/BenchtopEdgeProfile';
import {Shape} from 'components/customer/BTM/entity/Shape';
import {getValidationRules} from 'components/customer/BTM/helper/profileValidationMap';
import {Edge} from 'components/customer/BTM/entity/Edge';
import Excel from 'shared/Excel';
import {uniq} from 'lodash';

interface ValidateProfile {
    isValid: boolean;
    message: string;
}

/**
 * Checks if profiles selected are valid.
 *
 * @param {Shape} shape Shape of the bench
 * @param {BenchtopMaterial} material Profile is validated based on selected matrial
 * @param {BenchtopEdgeProfile[]} profiles list of edge profiles
 * @param {Path[]} paths profile information is stored in path
 * @return {ValidateProfile}
 */
export const validateProfile = (
    shape: Shape,
    material: BenchtopMaterial,
    profiles: BenchtopEdgeProfile[],
    paths: Path[]
): ValidateProfile => {
    let isValid = true;
    let message: string;

    const profiledPath = paths.find((path) => path.edged == Edge.PROFILED);
    let profile = {is_postformed_profile: false};

    if (profiledPath) {
        profile = profiles.find(
            (profile) => profile.id == profiledPath?.profile
        );
    }

    const scope = {
        profile,
        material,
        paths: paths.filter((path) => path.side != null),
    };

    // If there is no validation rules, then condition is true.
    const validationRules = getValidationRules(shape, material.formFactor);

    if (validationRules && scope) {
        isValid = Excel.calculate(validationRules.rules, scope);

        const messageData = validationRules.message;

        if (messageData) {
            let text = messageData.text;
            Object.keys(messageData)
                .filter((key) => key != 'text')
                .forEach((key) => {
                    const value = Excel.calculate(
                        messageData[String(key)],
                        scope
                    );

                    text = text.replace(`{${key}}`, String(value));
                });

            message = text;
        }
    }

    // if there are multiple prefixes in selected edge profiles, then throw an error
    const allPrefixesFromSelectedProfiles = paths
        .map((path) => profiles.find((profile) => profile.id == path.profile))
        .filter((profile) => profile) // remove null profile objects
        .map((profile) => profile.prefix)
        .filter((profile) => profile); // remove empty prefixes

    if (uniq(allPrefixesFromSelectedProfiles).length > 1) {
        isValid = false;
        message = `Selected profiles cannot be applied on this top.`;
    }
    return {
        isValid,
        message,
    };
};
