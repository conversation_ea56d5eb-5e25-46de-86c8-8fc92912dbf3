import {useDialogContext} from 'contexts';
import React, {useCallback} from 'react';
import {BenchtopMaterial} from 'components/customer/BTM/entity/BenchtopMaterial';
import {parseHtmlString} from 'shared/helpers/HTMLParser';

export const useMaterialHelper = () => {
    const {showDialog} = useDialogContext();

    const checkMaterialAvailability = useCallback(
        (material: BenchtopMaterial) => {
            if (material?.change?.is_hidden == 1) {
                showDialog({
                    title: 'Unavailable option',
                    message: (
                        <>
                            Following options are not available. Please update
                            these options before saving
                            <ul>
                                <li>
                                    <strong>
                                        {parseHtmlString(material.displayName)}
                                    </strong>
                                </li>
                            </ul>
                        </>
                    ),
                    hideYesButton: true,
                    hideNoButton: true,
                    buttons: [
                        {
                            name: 'Ok',
                            show: true,
                        },
                    ],
                });
                return false;
            }

            return true;
        },
        []
    );

    return {
        checkMaterialAvailability,
    };
};
