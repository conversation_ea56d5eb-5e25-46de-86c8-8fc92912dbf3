import {Join} from 'components/customer/BTM/entity/Join';
import {JoinInfo} from 'components/customer/BTM/entity/JoinInfo';
import {JoinType} from 'components/customer/BTM/entity/JoinType';

/**
 * Matches the join information against join data provided
 * and returns the suitable JoinInfo
 *
 * @param {Join} join Join data see btmSlice
 * @param {JoinInfo[]} joinsInformation Join data from backend
 * @return {JoinInfo}
 */
export const getJoinInfo = (join: Join, joinsInformation: JoinInfo[]) =>
    joinsInformation.find((joinInfo) => {
        if (joinInfo.joinType == join.joinType) {
            if (joinInfo.joinType == JoinType.MASONS_MITRE) {
                return joinInfo.direction == join.orientation;
            }

            return true;
        }
    });
