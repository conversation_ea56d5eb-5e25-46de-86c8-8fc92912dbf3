import {BenchtopEdgeProfile} from 'components/customer/BTM/entity/BenchtopEdgeProfile';
import {store} from 'store/customer';
import {
    cornerSet,
    cornersSet,
    profiledSet,
} from 'components/customer/BTM/store/btmSlice';
import {BTMApi} from 'components/customer/BTM/store/btmApi';
import {checkCutoutRestriction} from 'components/customer/BTM/helper/checkCutoutRestriction';
import {resetPostformedProfile} from 'components/customer/BTM/helper/resetPostformedProfile';
import {setDefaultProfiles} from 'components/customer/BTM/helper/setDefaultProfiles';
import {setClippedCornersFromEndProfile} from 'components/customer/BTM/helper/setCorners';
import {Direction} from 'components/customer/BTM/entity/Direction';
import {CornerType} from 'components/customer/BTM/entity/CornerType';
import {Shape} from 'components/customer/BTM/entity/Shape';
import {Side} from 'components/customer/BTM/entity/Side';
import {Edge} from 'components/customer/BTM/entity/Edge';
import {cloneDeep} from 'lodash';
import {Path} from 'components/customer/BTM/entity/Path';
import {Corner} from 'components/customer/BTM/entity/Corner';
import {getCornerName} from 'components/customer/BTM/helper/getCoordinates';

const shapeSideMap = [
    {
        shape: Shape.SQR,
        sides: [Side.B, Side.D],
    },
    {
        shape: Shape.ANG,
        sides: [Side.B, Side.E],
    },
    {
        shape: Shape.USHAPE,
        sides: [Side.C, Side.G],
    },
];

export const updateProfiles = async (edgeProfile: BenchtopEdgeProfile) => {
    const state = store.getState();
    const paths = state.btm.paths;
    const material = state.btm.material;
    const shape = state.btm.type.type;

    const {data: edgeProfilesData} = await store.dispatch(
        BTMApi.endpoints.listEdgeProfiles.initiate({materialId: material.id})
    );

    const edgeProfiles = edgeProfilesData?.data || [];

    if (edgeProfile.is_postformed_profile) {
        setDefaultProfiles(edgeProfile, paths, shape);
    }
    const sideMap = shapeSideMap.find((map) => map.shape == shape);

    paths.forEach((path, index) => {
        if (material.is_blank && !sideMap.sides.includes(path.side)) {
            return;
        }

        if (path.edged == Edge.EDGED) {
            if (!edgeProfile.is_postformed_profile) {
                const pathEdgeProfile = edgeProfiles.find(
                    (profile) => profile.id === path.profile
                );

                if (
                    !checkCutoutRestriction(paths, edgeProfile, index) &&
                    !pathEdgeProfile.is_end_only
                ) {
                    store.dispatch(
                        profiledSet(
                            Edge.EDGED,
                            edgeProfile,
                            index,
                            path.side,
                            edgeProfiles
                        )
                    );
                }
            }
        } else if (path.edged == Edge.PROFILED) {
            if (edgeProfile.is_postformed_profile) {
                resetPostformedProfile(path, edgeProfile, index, path.side);
            } else {
                if (!checkCutoutRestriction(paths, edgeProfile, index)) {
                    store.dispatch(
                        profiledSet(
                            Edge.EDGED,
                            edgeProfile,
                            index,
                            path.side,
                            edgeProfiles
                        )
                    );
                } else {
                    store.dispatch(
                        profiledSet(
                            Edge.NOT_EDGED,
                            null,
                            index,
                            path.side,
                            edgeProfiles
                        )
                    );
                }
            }
        }
    });
};

const setClippedCornersForPostformedProfile = (
    paths: Path[],
    profiles: BenchtopEdgeProfile[],
    corners: Corner[],
    updatedSide?: Side
) => {
    if (typeof updatedSide === 'undefined') return;

    let appliedPostFormedProfileCutoffs = false;
    const pathsWithoutCorners = paths.filter((path) => path.side != null);

    pathsWithoutCorners.forEach((path, currentIndex) => {
        if (path.side != updatedSide) return;
        const profile = profiles.find((profile) => profile.id == path.profile);

        if (
            profile &&
            profile.is_postformed_profile &&
            profile.is_end_roll_available
        ) {
            const lastIndex =
                currentIndex - 1 < 0
                    ? pathsWithoutCorners.length - 1
                    : currentIndex - 1;
            const nextIndex =
                currentIndex + 1 > pathsWithoutCorners.length - 1
                    ? 0
                    : currentIndex + 1;

            const adjoiningPaths = pathsWithoutCorners.filter(
                (path, index) => index == lastIndex || index == nextIndex
            );

            adjoiningPaths.forEach((adjoiningPath) => {
                if (adjoiningPath) {
                    const profile = profiles.find(
                        (profile) => profile.id == adjoiningPath.profile
                    );

                    const sides = [
                        [path.side, adjoiningPath.side],
                        [adjoiningPath.side, path.side],
                    ].map(([side1, side2]) => getCornerName(side1, side2));

                    sides.forEach((side) => {
                        if (side) {
                            const corner = corners
                                .map((corner, index) => ({
                                    ...corner,
                                    index,
                                }))
                                .find((corner) => corner.name == side);

                            if (corner) {
                                if (
                                    (profile &&
                                        profile.is_postformed_profile) ||
                                    corner.isArc
                                ) {
                                    appliedPostFormedProfileCutoffs = true;
                                    corner.cutoff =
                                        (profile &&
                                            profile.is_postformed_profile) ||
                                        !corner.isArc;
                                    corner.type = CornerType.Clip;
                                    corner.addedThroughEndProfile = false;
                                    corner.isArc = false;
                                    corner.hideCornerInPreview = false;
                                    corner.depths = [50, 50];
                                    corner.disabled = {
                                        notch:
                                            profile &&
                                            profile.restrict_corner_notch,
                                        clip:
                                            profile &&
                                            profile.restrict_corner_clip,
                                    };

                                    store.dispatch(
                                        cornerSet(corner, corner.index)
                                    );
                                }
                            }
                        }
                    });
                }
            });
        }
    });

    return appliedPostFormedProfileCutoffs;
};

export const applyBenchEndShapes = async (
    removeEndOnlyCutoffs = false,
    updatedSide?: Side
) => {
    const state = store.getState();
    const shape = state.btm.type;
    const dimension = state.btm.dimension;
    const material = state.btm.material;
    const paths = state.btm.paths;
    const corners = state.btm.corners;

    const sides = shapeSideMap.find((map) => map.shape == shape.type).sides;

    const {data: edgeProfilesData} = await store.dispatch(
        BTMApi.endpoints.listEdgeProfiles.initiate({materialId: material.id})
    );

    const edgeProfiles = edgeProfilesData?.data || [];
    const benchEndIsArched = sides.map((side) => {
        const otherSides = sides.filter((s) => s != side);
        const otherSide = otherSides[0];

        const path = paths.find((path) => path.side == otherSide);
        const profile = edgeProfiles.find(
            (profile) => profile.id == path.profile
        );

        return {
            side,
            check:
                typeof profile?.end_option !== 'undefined' &&
                profile?.end_option?.is_arc,
        };
    });

    paths.forEach((path) => {
        if (sides.includes(path.side)) {
            const profile = edgeProfiles.find(
                (profile) => profile.id == path.profile
            );

            if (profile && profile.is_end_only && profile.end_id) {
                if (profile.end_option.is_arc) {
                    const dimension = state.btm.dimension;
                    let firstCornerPoints;
                    let secondCornerPoints;
                    let name;
                    let direction;
                    let depth;
                    let index;
                    let hideCornerInPreview = [false, false];

                    if (shape.type == Shape.SQR) {
                        if (path.side == Side.B) {
                            firstCornerPoints = {x: dimension[0], y: 0};
                            secondCornerPoints = {
                                x: dimension[0],
                                y: dimension[1],
                            };
                            name = ['AB', 'BC'];
                            index = [1, 2];
                            direction = [
                                Direction.LEFT_DOWN,
                                Direction.LEFT_UP,
                            ];
                            // 1mm deduction is to prevent preview from touching 2 corners at 1 point
                            depth = [
                                profile.end_option.default_radius_start,
                                dimension[1] / 2 - 1,
                            ];
                            hideCornerInPreview = [false, true];
                        }

                        if (path.side == Side.D) {
                            firstCornerPoints = {x: 0, y: 0};
                            secondCornerPoints = {x: 0, y: dimension[1]};
                            name = ['DA', 'CD'];
                            index = [0, 3];

                            direction = [
                                Direction.RIGHT_DOWN,
                                Direction.RIGHT_UP,
                            ];
                            depth = [
                                profile.end_option.default_radius_start,
                                dimension[1] / 2 - 1,
                            ];
                            hideCornerInPreview = [true, false];
                        }
                    } else if (shape.type == Shape.ANG) {
                        if (path.side == Side.B) {
                            firstCornerPoints = {x: dimension[0], y: 0};
                            secondCornerPoints = {
                                x: dimension[0],
                                y: dimension[1],
                            };
                            name = ['AB', 'BC'];
                            index = [1, 2];
                            direction = [
                                Direction.LEFT_DOWN,
                                Direction.LEFT_UP,
                            ];
                            depth = [
                                profile.end_option.default_radius_start,
                                dimension[1] / 2 - 1,
                            ];
                            hideCornerInPreview = [true, false];
                        }

                        if (path.side == Side.E) {
                            firstCornerPoints = {
                                x: dimension[4],
                                y: dimension[5],
                            };
                            secondCornerPoints = {x: 0, y: dimension[5]};
                            name = ['DE', 'EF'];
                            index = [4, 5];

                            direction = [Direction.LEFT_UP, Direction.RIGHT_UP];
                            depth = [
                                dimension[4] / 2 - 1,
                                profile.end_option.default_radius_start,
                            ];
                            hideCornerInPreview = [false, true];
                        }
                    } else {
                        // USHAPE
                        if (path.side == Side.C) {
                            firstCornerPoints = {
                                x: dimension[0],
                                y: dimension[1],
                            };
                            secondCornerPoints = {
                                x: dimension[0] - dimension[2],
                                y: dimension[1],
                            };
                            name = ['BC', 'CD'];
                            index = [2, 3];
                            direction = [Direction.LEFT_UP, Direction.RIGHT_UP];
                            depth = [
                                dimension[2] / 2 - 1,
                                profile.end_option.default_radius_start,
                            ];
                            hideCornerInPreview = [false, true];
                        }

                        if (path.side == Side.G) {
                            firstCornerPoints = {
                                x: dimension[0] - (dimension[2] + dimension[4]),
                                y: dimension[7],
                            };
                            secondCornerPoints = {x: 0, y: dimension[7]};
                            name = ['FG', 'GH'];
                            index = [6, 7];

                            direction = [Direction.LEFT_UP, Direction.RIGHT_UP];
                            depth = [
                                dimension[6] / 2 - 1,
                                profile.end_option.default_radius_start,
                            ];
                            hideCornerInPreview = [false, true];
                        }
                    }

                    setClippedCornersFromEndProfile(
                        index[0],
                        firstCornerPoints,
                        depth,
                        direction[0],
                        name[0],
                        CornerType.Clip,
                        true,
                        true,
                        index[1],
                        hideCornerInPreview[0]
                    );

                    setClippedCornersFromEndProfile(
                        index[1],
                        secondCornerPoints,
                        depth,
                        direction[1],
                        name[1],
                        CornerType.Clip,
                        true,
                        true,
                        index[0],
                        hideCornerInPreview[1]
                    );
                } else {
                    // add 2 corner clips
                    if (shape.type == Shape.SQR) {
                        const pathA = paths.find((path) => path.side == Side.A);
                        const pathC = paths.find((path) => path.side == Side.C);

                        if (path.side == Side.B) {
                            setClippedCornersFromEndProfile(
                                1,
                                {x: dimension[0], y: 0},
                                [
                                    profile.end_option.default_radius_end,
                                    profile.end_option.default_radius_start,
                                ],
                                Direction.LEFT_DOWN,
                                'AB',
                                CornerType.Clip,
                                pathA.edged == Edge.PROFILED
                            );

                            setClippedCornersFromEndProfile(
                                2,
                                {x: dimension[0], y: dimension[1]},
                                [
                                    profile.end_option.default_radius_end,
                                    profile.end_option.default_radius_start,
                                ],
                                Direction.LEFT_UP,
                                'BC',
                                CornerType.Clip,
                                pathC.edged == Edge.PROFILED
                            );

                            if (
                                pathA.edged != Edge.PROFILED &&
                                pathC.edged != Edge.PROFILED
                            ) {
                                store.dispatch(
                                    profiledSet(
                                        Edge.NOT_EDGED,
                                        null,
                                        2,
                                        path.side,
                                        edgeProfiles
                                    )
                                );
                            }
                        }

                        if (path.side == Side.D) {
                            setClippedCornersFromEndProfile(
                                0,
                                {x: 0, y: 0},
                                [
                                    profile.end_option.default_radius_end,
                                    profile.end_option.default_radius_start,
                                ],
                                Direction.RIGHT_DOWN,
                                'DA',
                                CornerType.Clip,
                                pathA.edged == Edge.PROFILED
                            );

                            setClippedCornersFromEndProfile(
                                3,
                                {x: 0, y: dimension[1]},
                                [
                                    profile.end_option.default_radius_end,
                                    profile.end_option.default_radius_start,
                                ],
                                Direction.RIGHT_UP,
                                'CD',
                                CornerType.Clip,
                                pathC.edged == Edge.PROFILED
                            );

                            if (
                                pathA.edged != Edge.PROFILED &&
                                pathC.edged != Edge.PROFILED
                            ) {
                                const isOtherSideArched = benchEndIsArched.find(
                                    (data) => data.side == path.side
                                );

                                store.dispatch(
                                    profiledSet(
                                        Edge.NOT_EDGED,
                                        null,
                                        isOtherSideArched.check ? 5 : 3,
                                        path.side,
                                        edgeProfiles
                                    )
                                );
                            }
                        }
                    } else if (shape.type == Shape.ANG) {
                        if (path.side == Side.B) {
                            const pathA = paths.find(
                                (path) => path.side == Side.A
                            );
                            const pathC = paths.find(
                                (path) => path.side == Side.C
                            );

                            setClippedCornersFromEndProfile(
                                1,
                                {x: dimension[0], y: 0},
                                [
                                    profile.end_option.default_radius_end,
                                    profile.end_option.default_radius_start,
                                ],
                                Direction.LEFT_DOWN,
                                'AB',
                                CornerType.Clip,
                                pathA.edged == Edge.PROFILED
                            );

                            setClippedCornersFromEndProfile(
                                2,
                                {x: dimension[0], y: dimension[1]},
                                [
                                    profile.end_option.default_radius_end,
                                    profile.end_option.default_radius_start,
                                ],
                                Direction.LEFT_UP,
                                'BC',
                                CornerType.Clip,
                                pathC.edged == Edge.PROFILED
                            );

                            if (
                                pathA.edged != Edge.PROFILED &&
                                pathC.edged != Edge.PROFILED
                            ) {
                                store.dispatch(
                                    profiledSet(
                                        Edge.NOT_EDGED,
                                        null,
                                        1,
                                        path.side,
                                        edgeProfiles
                                    )
                                );
                            }
                        }

                        if (path.side == Side.E) {
                            const pathD = paths.find(
                                (path) => path.side == Side.D
                            );
                            const pathF = paths.find(
                                (path) => path.side == Side.F
                            );

                            setClippedCornersFromEndProfile(
                                4,
                                {x: dimension[4], y: dimension[5]},
                                [
                                    profile.end_option.default_radius_start,
                                    profile.end_option.default_radius_end,
                                ],
                                Direction.LEFT_UP,
                                'DE',
                                CornerType.Clip,
                                pathD.edged == Edge.PROFILED
                            );

                            setClippedCornersFromEndProfile(
                                5,
                                {x: 0, y: dimension[5]},
                                [
                                    profile.end_option.default_radius_start,
                                    profile.end_option.default_radius_end,
                                ],
                                Direction.RIGHT_UP,
                                'EF',
                                CornerType.Clip,
                                pathF.edged == Edge.PROFILED
                            );

                            if (
                                pathD.edged != Edge.PROFILED &&
                                pathF.edged != Edge.PROFILED
                            ) {
                                const isOtherSideArched = benchEndIsArched.find(
                                    (data) => data.side == path.side
                                );

                                store.dispatch(
                                    profiledSet(
                                        Edge.NOT_EDGED,
                                        null,
                                        isOtherSideArched.check ? 6 : 4,
                                        path.side,
                                        edgeProfiles
                                    )
                                );
                            }
                        }
                    } else {
                        // USHAPE
                        if (path.side == Side.C) {
                            const pathB = paths.find(
                                (path) => path.side == Side.B
                            );
                            const pathD = paths.find(
                                (path) => path.side == Side.D
                            );

                            setClippedCornersFromEndProfile(
                                2,
                                {x: dimension[0], y: dimension[1]},
                                [
                                    profile.end_option.default_radius_start,
                                    profile.end_option.default_radius_end,
                                ],
                                Direction.LEFT_UP,
                                'BC',
                                CornerType.Clip,
                                pathB.edged == Edge.PROFILED
                            );

                            setClippedCornersFromEndProfile(
                                3,
                                {
                                    x: dimension[0] - dimension[2],
                                    y: dimension[1],
                                },
                                [
                                    profile.end_option.default_radius_start,
                                    profile.end_option.default_radius_end,
                                ],
                                Direction.RIGHT_UP,
                                'CD',
                                CornerType.Clip,
                                pathD.edged == Edge.PROFILED
                            );

                            if (
                                pathD.edged != Edge.PROFILED &&
                                pathB.edged != Edge.PROFILED
                            ) {
                                store.dispatch(
                                    profiledSet(
                                        Edge.NOT_EDGED,
                                        null,
                                        2,
                                        path.side,
                                        edgeProfiles
                                    )
                                );
                            }
                        }

                        if (path.side == Side.G) {
                            const pathF = paths.find(
                                (path) => path.side == Side.F
                            );
                            const pathH = paths.find(
                                (path) => path.side == Side.H
                            );

                            setClippedCornersFromEndProfile(
                                6,
                                {
                                    x:
                                        dimension[0] -
                                        (dimension[2] + dimension[4]),
                                    y: dimension[7],
                                },
                                [
                                    profile.end_option.default_radius_start,
                                    profile.end_option.default_radius_end,
                                ],
                                Direction.LEFT_UP,
                                'FG',
                                CornerType.Clip,
                                pathF.edged == Edge.PROFILED
                            );

                            setClippedCornersFromEndProfile(
                                7,
                                {x: 0, y: dimension[7]},
                                [
                                    profile.end_option.default_radius_start,
                                    profile.end_option.default_radius_end,
                                ],
                                Direction.RIGHT_UP,
                                'GH',
                                CornerType.Clip,
                                pathH.edged == Edge.PROFILED
                            );

                            if (
                                pathF.edged != Edge.PROFILED &&
                                pathH.edged != Edge.PROFILED
                            ) {
                                const isOtherSideArched = benchEndIsArched.find(
                                    (data) => data.side == path.side
                                );

                                store.dispatch(
                                    profiledSet(
                                        Edge.NOT_EDGED,
                                        null,
                                        isOtherSideArched.check ? 8 : 6,
                                        path.side,
                                        edgeProfiles
                                    )
                                );
                            }
                        }
                    }
                }
            }
        }
    });

    const appliedPostFormedProfileCutoffs =
        setClippedCornersForPostformedProfile(
            paths,
            edgeProfiles,
            corners,
            updatedSide
        );

    if (removeEndOnlyCutoffs && !appliedPostFormedProfileCutoffs) {
        paths.forEach((path) => {
            const profile = edgeProfiles.find(
                (profile) => profile.id == path.profile
            );

            if (
                (profile &&
                    (!profile.is_end_only || !profile.restrict_corner_clip)) ||
                !profile
            ) {
                if (path.side == updatedSide) {
                    let indices: number[] = [];

                    if (shape.type == Shape.SQR) {
                        // This basically beans with side C is updated
                        // look at corner 2 and 3 to possibly remove clippings
                        switch (path.side) {
                            case Side.C:
                                indices = [2, 3];
                                break;
                            case Side.A:
                                indices = [0, 1];
                                break;
                            case Side.B:
                                indices = [1, 2];
                                break;
                            case Side.D:
                                indices = [0, 3];
                                break;
                        }

                        if (indices.length > 0) {
                            store.dispatch(
                                cornersSet(
                                    updateCorners(corners, indices, profile)
                                )
                            );
                        }
                    } else if (shape.type == Shape.ANG) {
                        switch (path.side) {
                            case Side.C:
                                indices = [2];
                                break;
                            case Side.A:
                                indices = [1];
                                break;
                            case Side.B:
                                indices = [1, 2];
                                break;
                            case Side.E:
                                indices = [4, 5];
                                break;
                            case Side.D:
                                indices = [4];
                                break;
                            case Side.F:
                                indices = [5];
                                break;
                        }

                        if (indices.length > 0) {
                            store.dispatch(
                                cornersSet(
                                    updateCorners(corners, indices, profile)
                                )
                            );
                        }
                    } else if (shape.type == Shape.USHAPE) {
                        switch (path.side) {
                            case Side.C:
                                indices = [2, 3];
                                break;
                            case Side.G:
                                indices = [6, 7];
                                break;
                            case Side.B:
                                indices = [2];
                                break;
                            case Side.D:
                                indices = [3];
                                break;
                            case Side.F:
                                indices = [6];
                                break;
                            case Side.H:
                                indices = [7];
                                break;
                        }

                        if (indices.length > 0) {
                            store.dispatch(
                                cornersSet(
                                    updateCorners(corners, indices, profile)
                                )
                            );
                        }
                    }
                }
            }
        });
    }
};

const updateCorner = (corner: Corner, profile: BenchtopEdgeProfile) => {
    if (corner.isArc) return corner;

    corner.cutoff = false;
    corner.type = CornerType.None;
    corner.isArc = false;
    corner.disabled = {
        notch: profile && profile.restrict_corner_notch,
        clip: profile && profile.restrict_corner_clip,
    };
    delete corner.addedThroughEndProfile;
    delete corner.hideCornerInPreview;
    delete corner.isArc;
    delete corner.relatedCornerIndex;
    return corner;
};

const updateCorners = (
    corners: Corner[],
    indices: number[],
    profile: BenchtopEdgeProfile
) => {
    return cloneDeep(corners).map((corner, index) => {
        if (indices.includes(index)) {
            return updateCorner(corner, profile);
        }
        return corner;
    });
};
