import {
    useGetBenchTypesQuery,
    useGetMaterialTypesQuery,
} from 'components/customer/BTM/store/btmApi';
import {useEffect, useMemo} from 'react';
import {useParams} from 'react-router-dom';
import {BenchtopMaterialType} from 'components/customer/BTM/entity/BenchtopMaterialType';
import {BenchtopType} from 'components/customer/BTM/entity/BenchtopType';
import {useAppDispatch} from 'store/customer';
import {typeSet, materialTypeSet} from 'components/customer/BTM/store/btmSlice';
import {getShape} from 'components/customer/BTM/entity/Shape';
import {useGetBenchId} from 'components/customer/BTM/helper/useGetBenchId';

interface SelectedBenchAndMaterialInterface {
    selectedBenchType?: BenchtopType;
    selectedMaterialType?: BenchtopMaterialType;
}

export const useSelectedBenchAndMaterial = ({
    selectedBenchType,
    selectedMaterialType,
}: SelectedBenchAndMaterialInterface) => {
    const {benchId} = useGetBenchId();
    const dispatch = useAppDispatch();
    const {jobId} = useParams();
    const {data: benchTypes, isLoading: benchTypesLoading} =
        useGetBenchTypesQuery();
    const {
        data: benchtopMaterialTypes,
        isLoading: benchtopMaterialTypesLoading,
    } = useGetMaterialTypesQuery();

    const {benchType, materialTypeId} = useParams();

    const baseUrl = useMemo(() => {
        return `/v2/job/${jobId}/benchtop-module`;
    }, [jobId]);

    useEffect(() => {
        if (
            benchType &&
            benchTypes &&
            benchTypes.length &&
            typeof selectedBenchType === 'undefined'
        ) {
            const benchtop = benchTypes.find(
                (benchTop) => benchTop.type == getShape(benchType)
            );

            if (benchtop) {
                dispatch(typeSet(benchtop, benchId == null));
            }
        }
    }, [benchType, benchTypes]);

    useEffect(() => {
        if (
            materialTypeId &&
            benchtopMaterialTypes &&
            benchtopMaterialTypes.length &&
            typeof selectedMaterialType === 'undefined'
        ) {
            const materialType = benchtopMaterialTypes.find(
                (benchtopMaterialType) =>
                    benchtopMaterialType.id == Number(materialTypeId)
            );

            if (materialType) {
                dispatch(materialTypeSet(materialType));
            }
        }
    }, [materialTypeId, benchtopMaterialTypes]);

    return {
        baseUrl,
        benchTypesLoading,
        benchTypes,
        benchtopMaterialTypes,
        benchtopMaterialTypesLoading,
    };
};
