import {FormFactor} from 'components/customer/BTM/entity/BenchtopMaterial';
import {Side} from 'components/customer/BTM/entity/Side';
import {Edge} from 'components/customer/BTM/entity/Edge';
import {ProfileValidation} from 'components/customer/BTM/helper/profileValidationMap';

/**
 * This map assumes that no notch/clip paths are supplied
 * to paths array
 *
 * Also maths.js uses index that starts from 1 not 0.
 */
export const SQUARE_PROFILE_VALIDATION_MAP: ProfileValidation[] = [
    {
        formFactor: FormFactor.NONE,
        rules: `AND(
            paths[${Side.A + 1}].edged != ${Edge.PROFILED},
            paths[${Side.C + 1}].edged != ${Edge.PROFILED}
        )`,
        message: {
            text: 'Bench with material {name} - {thickness}mm, cannot have any profiled sides',
            name: 'material.name',
            thickness: 'material.thickness',
        },
    },
    {
        formFactor: FormFactor.SP,
        rules: `OR(
            AND(
                paths[${Side.A + 1}].edged == ${Edge.PROFILED},
                paths[${Side.C + 1}].edged != ${Edge.PROFILED}
            ),
            AND(
                paths[${Side.A + 1}].edged != ${Edge.PROFILED},
                paths[${Side.C + 1}].edged == ${Edge.PROFILED}
            )
        )`,
        message: {
            text: 'Bench with material {name} - {thickness}mm, must have one profiled side',
            name: 'material.name',
            thickness: 'material.thickness',
        },
    },
    {
        formFactor: FormFactor.DP,
        rules: `AND(
            paths[${Side.A + 1}].edged == ${Edge.PROFILED},
            paths[${Side.C + 1}].edged == ${Edge.PROFILED}
        )`,
        message: {
            text: 'Bench with material {name} - {thickness}mm, must have two profiled sides',
            name: 'material.name',
            thickness: 'material.thickness',
        },
    },
    {
        formFactor: FormFactor.NONE_SP,
        rules: `OR(
            AND(
                paths[${Side.A + 1}].edged != ${Edge.PROFILED},
                paths[${Side.C + 1}].edged != ${Edge.PROFILED}
            ),
            OR(
                AND(
                    paths[${Side.A + 1}].edged == ${Edge.PROFILED},
                    paths[${Side.C + 1}].edged != ${Edge.PROFILED}
                ),
                AND(
                    paths[${Side.A + 1}].edged != ${Edge.PROFILED},
                    paths[${Side.C + 1}].edged == ${Edge.PROFILED}
                )
            )
        )`,
        message: {
            text: 'Bench with material {name} - {thickness}mm, can have upto one profiled side',
            name: 'material.name',
            thickness: 'material.thickness',
        },
    },
    {
        formFactor: FormFactor.NONE_DP,
        rules: `OR(
            AND(
                paths[${Side.A + 1}].edged != ${Edge.PROFILED},
                paths[${Side.C + 1}].edged != ${Edge.PROFILED}
            ),
            AND(
                paths[${Side.A + 1}].edged == ${Edge.PROFILED},
                paths[${Side.C + 1}].edged == ${Edge.PROFILED}
            )
        )`,
        message: {
            text: 'Bench with material {name} - {thickness}mm, must have both sides profiled or none at all',
            name: 'material.name',
            thickness: 'material.thickness',
        },
    },
    {
        formFactor: FormFactor.SP_DP,
        rules: `OR(
            paths[${Side.A + 1}].edged == ${Edge.PROFILED},
            paths[${Side.C + 1}].edged == ${Edge.PROFILED}
        )`,
        message: {
            text: 'Bench with material {name} - {thickness}mm, must have at least one side profiled',
            name: 'material.name',
            thickness: 'material.thickness',
        },
    },
];
