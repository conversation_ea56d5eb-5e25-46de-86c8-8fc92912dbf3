import {Shape} from 'components/customer/BTM/entity/Shape';
import {FormFactor} from 'components/customer/BTM/entity/BenchtopMaterial';
import {SQUARE_PROFILE_VALIDATION_MAP} from 'components/customer/BTM/helper/profileValidationMaps/squareProfileMap';
import {L_PROFILE_VALIDATION_MAP} from 'components/customer/BTM/helper/profileValidationMaps/lProfileMap';
import {U_PROFILE_VALIDATION_MAP} from 'components/customer/BTM/helper/profileValidationMaps/uProfileMap';

export interface ProfileValidation {
    formFactor: FormFactor;
    rules: string;
    message: {
        text: string;
        [key: string]: string;
    };
}

/**
 * This function simply returns the validation rule based on
 * the shape of the bench.
 *
 * @param {Shape} shape Shape of the bench
 * @param {FormFactor} formFactor formFactor of the material
 * @return {ProfileValidation}
 */
export const getValidationRules = (
    shape: Shape,
    formFactor: FormFactor
): ProfileValidation => {
    switch (shape) {
        // Validation rules for Rectangle shaped bench
        case Shape.SQR:
            return SQUARE_PROFILE_VALIDATION_MAP.find(
                (validation) => validation.formFactor == formFactor
            );

        // Validation rules for L shaped bench
        case Shape.ANG:
            return L_PROFILE_VALIDATION_MAP.find(
                (validation) => validation.formFactor == formFactor
            );

        // Validation rules for U shaped bench
        case Shape.USHAPE:
            return U_PROFILE_VALIDATION_MAP.find(
                (validation) => validation.formFactor == formFactor
            );
    }
};
