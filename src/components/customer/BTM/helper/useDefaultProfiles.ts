import {useEffect} from 'react';
import {useAppDispatch, useAppSelector} from 'store/customer';
import {useGetBenchId} from 'components/customer/BTM/helper/useGetBenchId';
import {
    profileReset,
    selectEdgeProfile,
    selectMaterial,
    selectType,
    selectPaths,
    selectDefaultEdgeProfileLoaded,
    defaultEdgeProfileLoadedSet,
    pathsSet,
} from 'components/customer/BTM/store/btmSlice';
import {shallowEqual} from 'react-redux';
import {FormFactor} from 'components/customer/BTM/entity/BenchtopMaterial';
import {setDefaultProfiles} from 'components/customer/BTM/helper/setDefaultProfiles';
import {useFetchEdgeProfile} from 'components/customer/BTM/helper/useFetchEdgeProfile';
import {cloneDeep, isEqual} from 'lodash';
import {Edge} from 'components/customer/BTM/entity/Edge';

/**
 * This hook basically sets selected edge profiles on
 * predefined sides based on shape.
 */
export const useDefaultProfiles = () => {
    const dispatch = useAppDispatch();
    const {benchId} = useGetBenchId();
    const defaultEdgeProfileLoaded = useAppSelector(
        selectDefaultEdgeProfileLoaded
    );
    const shape = useAppSelector(selectType);
    const edgeProfile = useAppSelector(selectEdgeProfile);
    const material = useAppSelector(selectMaterial, shallowEqual);
    const paths = useAppSelector(selectPaths);
    const {edgeProfiles, isLoading, isFetching, isUninitialized} =
        useFetchEdgeProfile();

    useEffect(() => {
        // This effect sets sides with unavailable edge profiles to
        // Not visible.
        if (isLoading || isUninitialized || isFetching) return;

        if (material && edgeProfiles && paths) {
            const profileIds = edgeProfiles.map(
                (edgeProfile) => edgeProfile.id
            );
            const updatedPaths = cloneDeep(paths).map((path) => {
                if (
                    (path.edged != Edge.NOT_EDGED &&
                        !profileIds.includes(path.profile)) ||
                    (path.edged == Edge.PROFILED &&
                        material.formFactor == FormFactor.NONE)
                ) {
                    path.edged = Edge.NOT_EDGED;
                    path.profile = null;
                }

                return path;
            });

            if (!isEqual(updatedPaths, paths)) {
                dispatch(pathsSet(updatedPaths, true));
            }
        }
    }, [edgeProfiles, isLoading, isFetching, isUninitialized]);

    useEffect(() => {
        // This effect sets the default profiles on the bench
        if (material && benchId == null && !defaultEdgeProfileLoaded) {
            dispatch(profileReset());

            if (material.formFactor == FormFactor.NONE) {
                dispatch(defaultEdgeProfileLoadedSet(true));
                return;
            }

            if (edgeProfile && shape) {
                if (!material.is_blank) {
                    // blank is handled differently. See, useBlank.ts
                    setDefaultProfiles(edgeProfile, paths, shape.type);
                }
                dispatch(defaultEdgeProfileLoadedSet(true));
            }
        }
    }, [benchId, shape, material, edgeProfile, defaultEdgeProfileLoaded]);
};
