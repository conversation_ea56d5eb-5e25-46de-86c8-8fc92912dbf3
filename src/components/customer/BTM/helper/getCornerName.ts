import {Path} from 'components/customer/BTM/entity/Path';
import {getCornerName as getCornerNameOG} from 'components/customer/BTM/helper/getCoordinates';
import {cloneDeep} from 'lodash';

/**
 * This function basically gets path name for cutoff sides.
 * DANGER NOTE: This will return wrong value for regular
 * side of the bench.
 *
 * @param {number} index index of the side
 * @param {Path[]} paths all paths information see btmSlice
 * @return {string}
 */
export const getCornerName = (index: number, paths: Path[]) => {
    let pathBefore = paths
        .slice(0, index)
        .reverse()
        .find((path) => path.side != null);

    // If the notch/clip cannot find path in first half of the array
    // we need to check the entire array in reverse order
    if (typeof pathBefore == 'undefined') {
        pathBefore = cloneDeep(paths)
            .reverse()
            .find((path) => path.side != null);
    }

    const pathAfter = paths
        .slice(index + 1, paths.length)
        .find((path) => path.side != null);

    if (pathBefore && pathAfter) {
        return getCornerNameOG(pathBefore.side, pathAfter.side);
    }
};
