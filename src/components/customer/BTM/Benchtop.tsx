import React, {useState, useMemo, useEffect, useCallback} from 'react';
import styled from 'styled-components';
import {Col, Container, Nav, Row} from 'react-bootstrap';
import {BaseConfig} from 'config';
import {
    NavLink,
    Route,
    Routes,
    useLocation,
    useNavigate,
    useParams,
} from 'react-router-dom';
import {Material} from 'components/customer/BTM/Material';
import {EdgeProfile} from 'components/customer/BTM/EdgeProfile';
import {Overview} from 'components/customer/BTM/Overview';
import {Corners} from 'components/customer/BTM/Corners';
import {Joins} from 'components/customer/BTM/Joins';
import {BenchtopPreview} from 'components/customer/BTM/Preview';
import {WizardNavigation} from 'shared/components/Wizard/WizardNavigation';
import {
    selectMaterialType,
    selectType,
    selectMaterial,
    selectThickness,
    selectDefaultMaterialLoaded,
    materialSet,
    defaultMaterialLoadedSet,
    selectJoins,
} from 'components/customer/BTM/store/btmSlice';
import {useSelectedBenchAndMaterial} from 'components/customer/BTM/helper/useSelectedBenchAndMaterial';
import {useAppSelector} from 'store/customer';
import {shallowEqual} from 'react-redux';
import {Tab, TabPage, PageTab} from 'components/customer/BTM/entity/Tab';
import {SaveBenchtop} from 'components/customer/BTM/SaveBenchtop';
import {useAppDispatch} from 'store/dashboard';
import {useGetBenchId} from 'components/customer/BTM/helper/useGetBenchId';
import {Shape} from 'components/customer/BTM/entity/Shape';
import {useDefaultProfiles} from 'components/customer/BTM/helper/useDefaultProfiles';
import {useGetMaterials} from 'components/customer/BTM/helper/useGetMaterials';
import {useFetchThickness} from 'components/customer/BTM/helper/useFetchThickness';
import {Icon, Loader} from 'shared/helpers';
import {useBlank} from 'components/customer/BTM/helper/useBlank';
import {JoinType} from 'components/customer/BTM/entity/JoinType';
import {useButtJoinInformation} from 'components/customer/BTM/helper/useButtJoinInformation';
import {useAppContext} from 'contexts';
import {CancelButton} from 'shared/components/CancelButton';

export const Benchtop = () => {
    const dispatch = useAppDispatch();
    const shape = useAppSelector(selectType, shallowEqual);
    const benchtopType = useAppSelector(selectType, shallowEqual);
    const materialType = useAppSelector(selectMaterialType, shallowEqual);
    const thickness = useAppSelector(selectThickness);
    const material = useAppSelector(selectMaterial, shallowEqual);
    const joins = useAppSelector(selectJoins, shallowEqual);
    const defaultMaterialIsLoaded = useAppSelector(selectDefaultMaterialLoaded);
    const {isLoading: isLoadingJoins} = useButtJoinInformation();
    const {userProfile} = useAppContext();
    const hideSubstrateName = userProfile?.hideSubstrateName;

    const displayCorners = useMemo(() => {
        return userProfile?.allowNotchesAndClips;
    }, [userProfile?.allowNotchesAndClips]);

    useSelectedBenchAndMaterial({
        selectedBenchType: benchtopType,
        selectedMaterialType: materialType,
    });

    useDefaultProfiles();
    useFetchThickness();
    useBlank();

    const {getMaterials} = useGetMaterials(hideSubstrateName);

    const location = useLocation();
    const navigate = useNavigate();
    const {jobId} = useParams();
    const {benchId} = useGetBenchId();
    const [currentTab, setCurrentTab] = useState<Tab>(Tab.MATERIAL);
    const maxTab = 4;

    const path = useMemo(() => {
        let path = `/v2/job/${jobId}/benchtop-module`;

        if (benchtopType && materialType) {
            path += `/type/${benchtopType.type}/materialType/${materialType.id}`;
        }

        return path;
    }, [jobId, benchtopType, materialType]);

    const setTabHandler = useCallback(
        (tab: Tab, currentTab: Tab) => {
            if (shape && shape.type == Shape.SQR && tab == Tab.JOINS) {
                if (currentTab == Tab.CORNERS) {
                    tab = Tab.EDGE_PROFILE;
                } else {
                    tab = Tab.CORNERS;
                }
            }

            if (materialType && !materialType.is_join_allowed) {
                if (tab == Tab.JOINS) {
                    if (currentTab == Tab.EDGE_PROFILE) tab = Tab.CORNERS;
                    else tab = Tab.EDGE_PROFILE;
                }
            }

            if (!displayCorners && tab == Tab.CORNERS) {
                const isJoinsDisplayed =
                    shape?.type != Shape.SQR && materialType?.is_join_allowed;
                if (currentTab == Tab.OVERVIEW) {
                    // Previous - Navigate to Joins if available, otherwise go to Edge Profile
                    tab = isJoinsDisplayed ? Tab.JOINS : Tab.EDGE_PROFILE;
                } else {
                    // Next - Regardless if coming from Joins/Edge Profile go to Overview
                    tab = Tab.OVERVIEW;
                }
            }

            setCurrentTab(tab);
            navigate(
                `${path}/${TabPage[tab]}${
                    benchId != null ? `?bench=${benchId}` : ''
                }`
            );
        },
        [shape, materialType, path, benchId, navigate, displayCorners]
    );

    const customNavHandler = useCallback(
        (event: React.MouseEvent) => {
            if (typeof material == 'undefined') event.preventDefault();
        },
        [material]
    );

    const getDefaultMaterial = async () => {
        const materials = await getMaterials(materialType.id, thickness);

        if (materials && materials.length > 0) {
            dispatch(materialSet(materials[0]));
            dispatch(defaultMaterialLoadedSet(true));
        }
    };

    const showJoinsNav = useCallback(
        (id: string) => {
            if (id == 'joins' && shape && shape.type == Shape.SQR) {
                if (Array.isArray(joins)) {
                    const hasButtJoins = joins.find(
                        (join) => join.joinType == JoinType.BUTT_JOIN
                    );

                    return typeof hasButtJoins !== 'undefined';
                }
                return false;
            }

            return true;
        },
        [shape, joins]
    );

    useEffect(() => {
        if (location?.pathname && shape) {
            const pageParam = location.pathname.split('/').filter(String).pop();
            const pageTab = PageTab[String(pageParam)] as Tab;
            if (
                pageTab === undefined ||
                (pageTab === Tab.CORNERS && !displayCorners) ||
                (pageTab === Tab.JOINS && !showJoinsNav('joins'))
            ) {
                const url = location.pathname?.replace(
                    pageParam,
                    TabPage[Tab.MATERIAL]
                );
                setCurrentTab(Tab.MATERIAL);
                navigate(`${url}${benchId != null ? `?bench=${benchId}` : ''}`);
            } else if (currentTab != pageTab) {
                setCurrentTab(pageTab);
            }
        }
    }, [location, benchId, displayCorners, shape, joins]);

    useEffect(() => {
        if (!defaultMaterialIsLoaded) {
            // set it before rendering this component if editing
            if (materialType && thickness) {
                if (typeof material === 'undefined' && benchId == null) {
                    void getDefaultMaterial();
                } else {
                    dispatch(defaultMaterialLoadedSet(true));
                }
            }
        }
    }, [material, materialType, thickness, benchId]);

    if (!defaultMaterialIsLoaded || isLoadingJoins) {
        return <Loader loader={true} />;
    }

    return (
        <>
            <BTMNav fill variant="pills">
                {BaseConfig.USER_CUSTOMER.benchtopModule.map((menu) => {
                    const showMenu = showJoinsNav(menu.id);
                    if (
                        !showMenu ||
                        (!displayCorners && menu.id === 'corners')
                    ) {
                        return <React.Fragment key={menu.id} />;
                    }

                    return (
                        <BTMNavItem
                            $active={location.pathname.indexOf(menu.id) > -1}
                            style={{padding: '5px', borderRadius: '20px'}}
                            key={menu.id}>
                            <CustomNavLink
                                $disabled={typeof material == 'undefined'}
                                onClick={customNavHandler}
                                to={`${path}/${menu.id}${
                                    benchId != null ? `?bench=${benchId}` : ''
                                }`}>
                                <BTMLink>
                                    <BTMIcon iconName={menu.icon} />
                                    <BTMText>
                                        {menu.id == 'joins' &&
                                        benchtopType &&
                                        benchtopType.type == Shape.USHAPE
                                            ? menu.pluralName
                                            : menu.name}
                                    </BTMText>
                                </BTMLink>
                            </CustomNavLink>
                        </BTMNavItem>
                    );
                })}
            </BTMNav>
            <BTMEditor>
                <BTMContainer className="cbc-form" id="benchtop-container">
                    <BenchtopPreview currentTab={currentTab} />
                    <BTMInnerContainer>
                        <Routes>
                            <Route path="/material" element={<Material />} />
                            <Route
                                path="/edge-profile"
                                element={<EdgeProfile />}
                            />
                            <Route path="/joins" element={<Joins />} />
                            {displayCorners ? (
                                <Route path="/corners" element={<Corners />} />
                            ) : null}
                            <Route path="/overview" element={<Overview />} />
                        </Routes>

                        <NavAndControl className="nav-and-control">
                            <Container>
                                <Row>
                                    <Col md={{offset: 1, span: 10}}>
                                        <WizardNavigation
                                            currentTab={currentTab}
                                            setCurrentTab={setTabHandler}
                                            max={maxTab}
                                            dots={maxTab + 1}
                                            disableNext={
                                                typeof material == 'undefined'
                                            }
                                        />
                                    </Col>
                                </Row>

                                <Row
                                    className="control-buttons"
                                    style={{padding: '15px 0'}}>
                                    <Col xs={6} md={{offset: 2, span: 4}}>
                                        <CancelButton
                                            title="Changes not yet saved"
                                            message="Are you sure you want to leave? Some of your changes have not been saved yet. If you proceed, these changes will be lost."
                                            okLabel="Proceed"
                                            cancelLabel="Cancel"
                                        />
                                    </Col>
                                    <Col xs={6} md={4}>
                                        <SaveBenchtop />
                                    </Col>
                                </Row>
                            </Container>
                        </NavAndControl>
                    </BTMInnerContainer>
                </BTMContainer>
            </BTMEditor>
        </>
    );
};

const NavAndControl = styled.div`
    box-shadow: 0 -4px 4px -3px gray;
    bottom: -134px;
`;

const BTMNav = styled(Nav)`
    margin-top: 15px;
    border-bottom: 3px solid rgb(var(--secondary_colour));
`;

const BTMNavItem = styled(Nav.Item)<{$active: boolean}>`
    background: white;
    border-radius: 0 !important;
    padding: ${({$active}) => ($active ? '0' : '0 0 5px')} !important;
    margin: 0 5px;

    &:hover {
        padding: 0 !important;
    }

    &:first-child {
        margin-left: 0 !important;
    }

    &:last-child {
        margin-right: 0 !important;
    }

    > a {
        background: rgb(var(--primary_colour));
        display: block;
        padding: 5px;
        border-radius: 20px;
        text-decoration: none;
    }
    > a.active,
    > a:hover {
        height: 100%;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        background: rgb(var(--secondary_colour));
    }
`;

const BTMIcon = styled(Icon)`
    width: 25px;
    background: white;
    border-radius: 20px;
    padding: 3px;
`;

const BTMLink = styled.div`
    display: flex;
    flex: 1;
    border-radius: 20px;
    justify-content: initial;
`;

const BTMText = styled.div`
    flex: 1;
    color: white;
    font-weight: bold;
`;

const BTMEditor = styled.div`
    display: flex;
    flex-grow: 1;
    flex-direction: column;
`;

const BTMContainer = styled.div`
    display: flex;
    flex: 1;
    padding: 0 !important;
`;

const BTMInnerContainer = styled.div`
    flex: 1;
    position: relative;
    margin-bottom: 134px;
`;

const CustomNavLink = styled(NavLink)<{$disabled: boolean}>`
    opacity: ${({$disabled}) => ($disabled ? '0.5' : '1')} !important;
    cursor: ${({$disabled}) =>
        $disabled ? 'not-allowed' : 'pointer'} !important;
`;
