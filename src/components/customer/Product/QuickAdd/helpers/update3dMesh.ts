import {
    Cabinet<PERSON>esh,
    RoomPlannerProduct,
    RoomPlannerProductWithTemplate,
} from 'components/customer/RoomPlanner/types';
import {useGetRoomProductsQuery} from 'components/customer/Product/store/productApi';
import {useCallback, useEffect, useRef} from 'react';
import {Product as ApiProduct} from 'components/customer/Product/entity/Product';
import {updateUserData} from 'hooks/Product/helpers/roomPlannerProducts';
import {mapTemplate} from 'components/customer/RoomPlanner/helpers/mapTemplate';
import {renderCabinetGroup} from 'components/customer/RoomPlanner/helpers/renderCabinetGroup';
import {getRoomDimensionsComputed} from 'components/customer/RoomPlanner/helpers/getComputedDimension';
import {useAppSelector} from 'store/customer';
import {getRoomType} from 'components/customer/RoomPlanner/store/roomPlannerSlice';
import {deleteCabinetMesh} from 'components/customer/RoomPlanner/helpers/deleteCabinetMesh';
import {autoSave} from 'components/customer/RoomPlanner/lib/useAutoSave';

interface Product {
    id: number;
    meshes: CabinetMesh[];
}

export const useUpdate3dMesh = (roomId: number) => {
    const productRef = useRef<Product>();
    const roomType = useAppSelector(getRoomType);

    const {data} = useGetRoomProductsQuery(
        {roomId: Number(roomId)},
        {skip: !roomId}
    );

    const setIdAndMesh = useCallback((id: number, meshes: CabinetMesh[]) => {
        productRef.current = {
            id,
            meshes,
        };
    }, []);

    const update3dMesh = useCallback(
        async (product: ApiProduct, meshes: CabinetMesh[]) => {
            updateUserData(meshes, Number(product.job_cabinet_id));

            const roomPlannerFinalData: RoomPlannerProduct[] = [];

            const mappedProducts = await Promise.all(
                [product].map(mapTemplate)
            );
            mappedProducts.forEach((product) => {
                const quantity = product.quantity;

                for (let i = 0; i < quantity; i++) {
                    roomPlannerFinalData.push({
                        ...product,
                        duplicate_id:
                            quantity > 1
                                ? `${product.job_cabinet_id}-${i}`
                                : undefined,
                        quantity: 1,
                        // TEMP
                        cabinet_toekick: 0,
                    });
                }
            });

            const dimensions = getRoomDimensionsComputed();
            let currentWidth = Number(dimensions.computedWidth);
            let index = 0;

            for (const product of roomPlannerFinalData) {
                const id =
                    roomPlannerFinalData.length > 1
                        ? `${product.job_cabinet_id}-${index}`
                        : product.job_cabinet_id;

                const mesh = meshes.find((mesh) => {
                    return mesh.userData.id == id;
                });

                if (mesh) {
                    autoSave(mesh, roomType);

                    deleteCabinetMesh(mesh);

                    const {widthIncrement} = renderCabinetGroup(
                        product as RoomPlannerProductWithTemplate,
                        {
                            ...dimensions,
                            positionX: currentWidth + 5,
                        },
                        String(roomId)
                    );

                    currentWidth += widthIncrement;
                }
                index++;
            }
        },
        [roomType, roomId]
    );

    useEffect(() => {
        if (!data || !productRef.current) return;

        const apiProduct = data.find(
            (product) => product.job_cabinet_id == productRef.current.id
        );

        if (apiProduct) {
            const meshes = productRef.current.meshes;
            productRef.current = undefined;

            void update3dMesh(apiProduct, meshes);
        }
    }, [data]);

    return {
        setIdAndMesh,
    };
};
