import React from 'react';
import {LeftMenu} from './LeftMenu';
import {useBenchtop} from 'components/customer/BenchtopDesigner/hooks/BenchtopDesigner';
import {Loader} from 'shared/helpers';
import {useGetConfigurationQuery} from 'store/customer/appSlice';

export const Designer = () => {
    const {iframe, buttonEvent, addButtonToAdapter, loader} = useBenchtop();

    const {data: configuration, isLoading: isConfigurationLoading} =
        useGetConfigurationQuery();

    return (
        <Loader loader={loader || isConfigurationLoading}>
            {!isConfigurationLoading ? (
                <div className="designer">
                    <div className="LeftMenu">
                        <LeftMenu
                            buttonEvent={buttonEvent}
                            addButtonToAdapter={addButtonToAdapter}
                        />
                    </div>
                    <div className="designer-content">
                        <Loader loader={!iframe}>
                            {iframe ? (
                                <iframe
                                    id="btdFrame"
                                    src={`${configuration.btdEmbedURL}/embedded/embedded.php`}
                                    width="100%"
                                    height="100%"
                                />
                            ) : (
                                <></>
                            )}
                        </Loader>
                    </div>
                </div>
            ) : (
                <></>
            )}
        </Loader>
    );
};
