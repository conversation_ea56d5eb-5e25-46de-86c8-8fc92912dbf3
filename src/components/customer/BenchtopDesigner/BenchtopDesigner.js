import React from 'react';
import {Icon, useQueryParams} from 'shared/helpers';
import {BenchtopSelector} from './BenchtopSelector';
import {Designer} from './Designer';

export const BenchtopDesigner = () => {
    const {benchType} = useQueryParams();

    return (
        <div className="benchtop-designer">
            <h1 className={benchType ? '' : 'no-bottom-left-border-radius'}>
                <Icon iconName="Header-Benchtop.svg" />
                <label>Add Benchtop</label>
            </h1>

            {benchType ? <Designer /> : <BenchtopSelector />}
        </div>
    );
};

export default BenchtopDesigner;
