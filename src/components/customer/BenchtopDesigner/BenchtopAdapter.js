class BenchtopAdapter {

    constructor(frame, origin) {
        this.frame = frame;
        this.origin = origin;
        this.savingBench = false;
        this.buttons = [];
    }

    setFrame(frame) { this.frame = frame; } 
    setOrigin(origin) { this.origin = origin; }

    addButton(key, setState, index) {
        if (index > -1)
            this.buttons[index] = { key, setState };
        else
            this.buttons.push({ key, setState });
    }

    getButton(key) {
        let button = this.buttons.filter(button => button.key === key);

        return button.length && button[0];
    }

    init(benchTopSaved, roomId) {
        this.roomId = roomId;


        const listener = (event) => {
            let originUrl = new URL(this.origin)
            let messageUrl = new URL(event.origin)

            if (originUrl.origin != messageUrl.origin) return;

            let message = event.data;

            if (message === "embedded frame ready") {
                this.readyMessage && this.sendMessage(this.readyMessage);
            } else if (message === "embedded frame session set") {
                this.loginInfoMessage && this.sendMessage(this.loginInfoMessage);
            } else if (message.hasOwnProperty('BENCH_SAVED') && message.BENCH_SAVED) {
                if (!this.savingBench) {
                    this.savingBench = true;
                    let benchId = message.bench_data.id;
                    benchTopSaved(benchId, this.roomId).finally(() => {
                        this.savingBench = false;
                    });
                }

            } else if (message === "INIT_EVENTS") {
                this.newUIMessage && this.sendMessage(this.newUIMessage);
                this.getButton("btnMaterial").setState(true);

            } else if (message === "ENABLE_SAVE_BUTTON") {

                this.getButton("btnSave").setState(true);

            } else if (message === "ENABLE_PROFILE_BUTTON") {

                this.getButton("btnProfile").setState(true);

            }
        };

        window.removeEventListener("message", listener);
        window.addEventListener("message", listener);
    }

    setCookie(sessionId, sessionName) {
        this.readyMessage = {
            "messageType" : "cookie",
            "sessionID"   : sessionId,
            "sessionName" : sessionName
        };
    }

    loadBench(benchId) {
        this.loginInfoMessage = {
            "messageType" : "loadBench",
            "benchId"   : benchId,
            "referer" : window.location.href
        };
    }

    setupNewUI() {
        this.newUIMessage = {
            "messageType" : "v2-ui"
        }
    }
    
    sendMessage(message) {
        document.querySelector(`#${this.frame}`).contentWindow.postMessage(
            JSON.stringify(message),
            this.origin
        );
    }
}

export default BenchtopAdapter;