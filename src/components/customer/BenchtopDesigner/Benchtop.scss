@use 'assets/scss/mixins.scss' as mixins;

.benchtop-selector {
    border-radius: 0 !important;
    margin: 0 !important;
    padding: 15px 15px 30px !important;
    box-sizing: border-box;
    border-bottom-left-radius: 20px !important;

    strong, p {
        display: block;
        text-align: center;
    }

    .benchtop-types {
        margin: 0; padding: 0;
        list-style: none;
        text-align: center;

        li {
            display: inline-block;
            width: 200px;
            vertical-align: top;
            background: white;
            margin-right: 15px;
            border-radius: 8px;
            padding: 15px;
            box-sizing: border-box;
            box-shadow: 0 0 5px -3px #000000;
            cursor: pointer;

            img {
                width: 100%;
            }
        }

        li:last-of-type {
            margin-right: 0;
        }
    }
}

.benchtop-designer {
    min-height: 100%;
    display: flex;
    flex-direction: column;

    h1 {margin-bottom: 0;}

    h1.no-bottom-left-border-radius {
        border-bottom-left-radius: 0;
    }

    .designer {
        display: flex !important;
        flex: 1;
        margin-bottom: 15px;

        .LeftMenu {
            width: 100px;
            padding-top: 15px;
            box-sizing: border-box;
            text-align: center;

            .menu:hover, .menu:focus, .menu:active {
                background: #B0C9E7;
            }
            .menu {
                text-align: center;
                height: 70px;
                padding-top: 10px;
                cursor: pointer;
                background: none;
                border: 0;

                img {
                    width: 45px;
                }
            }
        }
        .designer-content {
            flex: 1;
            box-sizing: border-box;
            border: 3px solid;
            @include mixins.theme('border-color', 'ui-primary-color');
            border-bottom-left-radius: 20px;
            border-bottom-right-radius: 20px;
            border-top: 0;

            iframe {
                border: 0;
                padding: 15px 15px 10px;
            }
        }
    }
}


@media (max-width: 695px) {
    .benchtop-selector{
        margin: 0 -10px !important;
        padding-bottom: 15px !important;   

        .benchtop-types{ 
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;

            li:nth-child(odd) {
                margin-right: 15px;
            }

            li:nth-child(even) {
                margin-right: 0;
            }

            li:nth-child(n + 3) {
                margin-bottom: 0;
            }

            li {
                width: 47%;
                margin-bottom: 15px;
            }
        }
    }
}