import React from 'react';
import {Image} from 'react-bootstrap';
import {useNavigate, useParams} from 'react-router-dom';

import './Benchtop.scss';

const BenchtopTypes = [
    {
        image: '/templates/benchtops/img/benchshapes/RectangleShape.jpg',
        type: 'SQR',
        name: 'Rectangle',
    },
    {
        image: '/templates/benchtops/img/benchshapes/LShape.jpg',
        type: 'ANG',
        name: 'L shape',
    },
    {
        image: '/templates/benchtops/img/benchshapes/UShape.jpg',
        type: 'USHAPE',
        name: 'U shape',
    },
    {
        image: '/templates/benchtops/img/benchshapes/GShape.jpg',
        type: 'GSHAPE',
        name: 'G shape',
    },
];

export const BenchtopSelector = () => {
    const navigate = useNavigate();
    const {jobId, roomId} = useParams();

    const selectHandler = (type) => {
        navigate(
            `/v2/job/${jobId}/room/${roomId}/benchtop-designer?benchType=${type}`
        );
    };

    return (
        <div className="benchtop-selector jumbotron">
            <strong>Select a Bench Type</strong>
            <p>The bench will be able to be customized in the next steps.</p>

            <ul className="benchtop-types">
                {BenchtopTypes.map((benchtopType, index) => {
                    return (
                        <li
                            key={index}
                            onClick={() => selectHandler(benchtopType.type)}>
                            <div>
                                <label>{benchtopType.name}</label>
                                <Image src={benchtopType.image} />
                            </div>
                        </li>
                    );
                })}
            </ul>
        </div>
    );
};
