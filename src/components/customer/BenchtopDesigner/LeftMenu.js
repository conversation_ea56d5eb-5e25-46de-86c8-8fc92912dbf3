import React, {useEffect, useState} from 'react';

import {BaseConfig} from 'config';
import {useAppContext} from 'contexts';
import {Icon, useQueryParams} from 'shared/helpers';
import {Button} from 'react-bootstrap';
import {OverlayTrigger} from 'shared';

const MenuItem = ({menu, buttonEvent, addButtonToAdapter}) => {
    const {benchType, benchId} = useQueryParams();
    const [active, setActive] = useState(false);

    useEffect(() => {
        setActive(false);
    }, [benchId, benchType]);

    addButtonToAdapter(menu.id, (state) => {
        setActive(state);
    });

    return (
        <OverlayTrigger placement="right" overlay={menu.name}>
            <Button
                disabled={!active}
                id={menu.id}
                className="menu"
                onClick={() => buttonEvent(menu.event)}>
                <Icon iconName={menu.icon} />
            </Button>
        </OverlayTrigger>
    );
};

export const LeftMenu = ({buttonEvent, addButtonToAdapter}) => {
    const {userLevel} = useAppContext();
    const [leftMenu, setLeftMenu] = useState([]);

    useEffect(() => {
        if (
            userLevel === 'USER_CUSTOMER' &&
            typeof BaseConfig[String(userLevel)] !== 'undefined'
        ) {
            setLeftMenu(BaseConfig[String(userLevel)]['benchtopDesignerMenu']);
        } else {
            setLeftMenu([]);
        }
    }, [userLevel]);

    return (
        <>
            {leftMenu.map((menu, index) => {
                return (
                    <MenuItem
                        menu={menu}
                        key={index}
                        buttonEvent={buttonEvent}
                        addButtonToAdapter={addButtonToAdapter}
                    />
                );
            })}
        </>
    );
};
