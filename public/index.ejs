<!DOCTYPE html>
<html lang="en" class="theme-gocabinets">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="manifest" href="/v2/manifest.json" />
  <link id="favicon" rel="shortcut icon" href="#" />
  <meta name="theme-color" content="#204381" />
  <meta name="apple-mobile-web-app-capable" content="true">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <title></title>
  <style>
    .initial-loader{
      position: fixed;
      top: calc(50% - 1rem);
      left: calc(50% - 1rem);
    }

    .browser-not-compatible {
      display: none;
      background: rgb(253, 237, 237);
      height: 80px;
      color: rgb(95, 33, 32);
      padding: 15px;
      text-align: center;
    }

    .error-image {
      width: 20px;
      vertical-align: sub;
      margin-right: 10px;
    }
  </style>
</head>

<body>
  <div id="browser-not-compatible" class="browser-not-compatible">
    <img class="error-image" src="./error.svg" /> <strong>Unsupported browser detected.</strong> 
    It looks like you are using a browser which this site doesn't support. Please upgrade your browser to a supported version.
    <br />
    We recommend using the latest version of <a href="https://www.google.com/chrome/" target="_blank" rel="noopener noreferrer">Google Chrome</a>, <a href="https://www.microsoft.com/edge" target="_blank" rel="noopener noreferrer">Microsoft Edge</a>, <a href="https://www.mozilla.org/firefox/" target="_blank" rel="noopener noreferrer">Mozilla Firefox</a> or <a href="https://www.apple.com/safari/" target="_blank" rel="noopener noreferrer">Apple Safari</a>
  </div>
  <div id="root">
    <div id="loader" class="initial-loader spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
  <noscript>
    You need to enable JavaScript to run this app.
  </noscript>
  </div>
  <script>
    let applicationVersion = "<%= applicationVersion %>";
    let versionPollInterval = <%= versionPollInterval %>;
    const browserRegex = <%= browserRegex %>;

    if (!browserRegex.test(navigator.userAgent)) {
      const loader = document.getElementById('loader');
      const browserNotCompatible = document.getElementById('browser-not-compatible');

      if (loader) {
        loader.style.display = 'none';
      }

      if (browserNotCompatible) {
        browserNotCompatible.style.display = 'block';
      }
    }
  </script>
</body>

</html>