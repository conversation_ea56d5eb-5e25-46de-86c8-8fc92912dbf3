// flow-typed signature: 3343a1089e2d9a4cc27e24d76da077dd
// flow-typed version: <<STUB>>/react-router-dom_v^6.1.1/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'react-router-dom'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'react-router-dom' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'react-router-dom/main' {
  declare module.exports: any;
}

declare module 'react-router-dom/react-router-dom.development' {
  declare module.exports: any;
}

declare module 'react-router-dom/react-router-dom.production.min' {
  declare module.exports: any;
}

declare module 'react-router-dom/server' {
  declare module.exports: any;
}

declare module 'react-router-dom/umd/react-router-dom.development' {
  declare module.exports: any;
}

declare module 'react-router-dom/umd/react-router-dom.production.min' {
  declare module.exports: any;
}

// Filename aliases
declare module 'react-router-dom/index' {
  declare module.exports: $Exports<'react-router-dom'>;
}
declare module 'react-router-dom/index.js' {
  declare module.exports: $Exports<'react-router-dom'>;
}
declare module 'react-router-dom/main.js' {
  declare module.exports: $Exports<'react-router-dom/main'>;
}
declare module 'react-router-dom/react-router-dom.development.js' {
  declare module.exports: $Exports<'react-router-dom/react-router-dom.development'>;
}
declare module 'react-router-dom/react-router-dom.production.min.js' {
  declare module.exports: $Exports<'react-router-dom/react-router-dom.production.min'>;
}
declare module 'react-router-dom/server.js' {
  declare module.exports: $Exports<'react-router-dom/server'>;
}
declare module 'react-router-dom/umd/react-router-dom.development.js' {
  declare module.exports: $Exports<'react-router-dom/umd/react-router-dom.development'>;
}
declare module 'react-router-dom/umd/react-router-dom.production.min.js' {
  declare module.exports: $Exports<'react-router-dom/umd/react-router-dom.production.min'>;
}
