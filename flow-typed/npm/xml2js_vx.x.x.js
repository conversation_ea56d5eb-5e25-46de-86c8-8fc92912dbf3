// flow-typed signature: 7bdafd4b8288af93a5ed11d6caf69f6d
// flow-typed version: <<STUB>>/xml2js_v^0.4.23/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'xml2js'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'xml2js' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'xml2js/lib/bom' {
  declare module.exports: any;
}

declare module 'xml2js/lib/builder' {
  declare module.exports: any;
}

declare module 'xml2js/lib/defaults' {
  declare module.exports: any;
}

declare module 'xml2js/lib/parser' {
  declare module.exports: any;
}

declare module 'xml2js/lib/processors' {
  declare module.exports: any;
}

declare module 'xml2js/lib/xml2js' {
  declare module.exports: any;
}

// Filename aliases
declare module 'xml2js/lib/bom.js' {
  declare module.exports: $Exports<'xml2js/lib/bom'>;
}
declare module 'xml2js/lib/builder.js' {
  declare module.exports: $Exports<'xml2js/lib/builder'>;
}
declare module 'xml2js/lib/defaults.js' {
  declare module.exports: $Exports<'xml2js/lib/defaults'>;
}
declare module 'xml2js/lib/parser.js' {
  declare module.exports: $Exports<'xml2js/lib/parser'>;
}
declare module 'xml2js/lib/processors.js' {
  declare module.exports: $Exports<'xml2js/lib/processors'>;
}
declare module 'xml2js/lib/xml2js.js' {
  declare module.exports: $Exports<'xml2js/lib/xml2js'>;
}
