// flow-typed signature: 2bbceafb5086f780643582b641ac0a3d
// flow-typed version: <<STUB>>/flow-typed_v^3.5.0/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'flow-typed'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'flow-typed' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'flow-typed/dist/cli' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/create-def' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/create-stub' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/install' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/runTests' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/search' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/update-cache' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/update' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/commands/validateDefs' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/cacheRepoUtils' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/codeSign' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/fileUtils' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/flowProjectUtils' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/flowVersion' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/git' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/github' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/isInFlowTypedRepo' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/libDefs' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/node' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/npm/npmLibDefs' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/npm/npmProjectUtils' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/semver' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/stubUtils' {
  declare module.exports: any;
}

declare module 'flow-typed/dist/lib/ValidationError' {
  declare module.exports: any;
}

// Filename aliases
declare module 'flow-typed/dist/cli.js' {
  declare module.exports: $Exports<'flow-typed/dist/cli'>;
}
declare module 'flow-typed/dist/commands/create-def.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/create-def'>;
}
declare module 'flow-typed/dist/commands/create-stub.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/create-stub'>;
}
declare module 'flow-typed/dist/commands/install.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/install'>;
}
declare module 'flow-typed/dist/commands/runTests.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/runTests'>;
}
declare module 'flow-typed/dist/commands/search.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/search'>;
}
declare module 'flow-typed/dist/commands/update-cache.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/update-cache'>;
}
declare module 'flow-typed/dist/commands/update.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/update'>;
}
declare module 'flow-typed/dist/commands/validateDefs.js' {
  declare module.exports: $Exports<'flow-typed/dist/commands/validateDefs'>;
}
declare module 'flow-typed/dist/lib/cacheRepoUtils.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/cacheRepoUtils'>;
}
declare module 'flow-typed/dist/lib/codeSign.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/codeSign'>;
}
declare module 'flow-typed/dist/lib/fileUtils.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/fileUtils'>;
}
declare module 'flow-typed/dist/lib/flowProjectUtils.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/flowProjectUtils'>;
}
declare module 'flow-typed/dist/lib/flowVersion.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/flowVersion'>;
}
declare module 'flow-typed/dist/lib/git.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/git'>;
}
declare module 'flow-typed/dist/lib/github.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/github'>;
}
declare module 'flow-typed/dist/lib/isInFlowTypedRepo.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/isInFlowTypedRepo'>;
}
declare module 'flow-typed/dist/lib/libDefs.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/libDefs'>;
}
declare module 'flow-typed/dist/lib/node.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/node'>;
}
declare module 'flow-typed/dist/lib/npm/npmLibDefs.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/npm/npmLibDefs'>;
}
declare module 'flow-typed/dist/lib/npm/npmProjectUtils.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/npm/npmProjectUtils'>;
}
declare module 'flow-typed/dist/lib/semver.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/semver'>;
}
declare module 'flow-typed/dist/lib/stubUtils.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/stubUtils'>;
}
declare module 'flow-typed/dist/lib/ValidationError.js' {
  declare module.exports: $Exports<'flow-typed/dist/lib/ValidationError'>;
}
