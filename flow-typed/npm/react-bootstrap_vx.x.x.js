// flow-typed signature: 363dd40f963e5213db2a5dc99fa49645
// flow-typed version: <<STUB>>/react-bootstrap_v^1.3.0/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'react-bootstrap'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'react-bootstrap' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'react-bootstrap/cjs/AbstractNav' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/AbstractNavItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Accordion' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/AccordionCollapse' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/AccordionContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/AccordionToggle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Alert' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Badge' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/BootstrapModalManager' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Breadcrumb' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/BreadcrumbItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Button' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ButtonGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ButtonToolbar' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Card' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CardColumns' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CardContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CardDeck' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CardGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CardImg' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Carousel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CarouselCaption' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CarouselItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/CloseButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Col' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Collapse' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Container' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/createChainedFunction' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/createWithBsPrefix' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/divWithClassName' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Dropdown' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/DropdownButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/DropdownItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/DropdownMenu' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/DropdownToggle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ElementChildren' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Fade' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Feedback' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Figure' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FigureCaption' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FigureImage' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Form' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormCheck' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormCheckInput' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormCheckLabel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormControl' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormFile' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormFileInput' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormFileLabel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormLabel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/FormText' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/helpers' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Image' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/InputGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Jumbotron' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ListGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ListGroupItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Media' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Modal' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ModalBody' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ModalContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ModalDialog' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ModalFooter' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ModalHeader' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ModalTitle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Nav' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Navbar' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavbarBrand' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavbarCollapse' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavbarContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavbarToggle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavDropdown' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/NavLink' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Overlay' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/OverlayTrigger' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/PageItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Pagination' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Popover' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/PopoverContent' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/PopoverTitle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ProgressBar' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ResponsiveEmbed' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Row' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/SafeAnchor' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/SelectableContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Spinner' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/SplitButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Switch' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Tab' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/TabContainer' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/TabContent' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/TabContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Table' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/TabPane' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Tabs' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ThemeProvider' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Toast' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ToastBody' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ToastContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ToastHeader' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ToggleButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/ToggleButtonGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/Tooltip' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/transitionEndListener' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/triggerBrowserReflow' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/types' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/usePopperMarginModifiers' {
  declare module.exports: any;
}

declare module 'react-bootstrap/cjs/useWrappedRefWithWarning' {
  declare module.exports: any;
}

declare module 'react-bootstrap/dist/react-bootstrap' {
  declare module.exports: any;
}

declare module 'react-bootstrap/dist/react-bootstrap.min' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/AbstractNav' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/AbstractNavItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Accordion' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/AccordionCollapse' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/AccordionContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/AccordionToggle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Alert' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Badge' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/BootstrapModalManager' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Breadcrumb' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/BreadcrumbItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Button' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ButtonGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ButtonToolbar' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Card' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CardColumns' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CardContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CardDeck' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CardGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CardImg' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Carousel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CarouselCaption' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CarouselItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/CloseButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Col' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Collapse' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Container' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/createChainedFunction' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/createWithBsPrefix' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/divWithClassName' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Dropdown' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/DropdownButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/DropdownItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/DropdownMenu' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/DropdownToggle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ElementChildren' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Fade' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Feedback' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Figure' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FigureCaption' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FigureImage' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Form' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormCheck' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormCheckInput' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormCheckLabel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormControl' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormFile' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormFileInput' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormFileLabel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormLabel' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/FormText' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/helpers' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Image' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/InputGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Jumbotron' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ListGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ListGroupItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Media' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Modal' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ModalBody' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ModalContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ModalDialog' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ModalFooter' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ModalHeader' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ModalTitle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Nav' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Navbar' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavbarBrand' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavbarCollapse' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavbarContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavbarToggle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavDropdown' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/NavLink' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Overlay' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/OverlayTrigger' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/PageItem' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Pagination' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Popover' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/PopoverContent' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/PopoverTitle' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ProgressBar' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ResponsiveEmbed' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Row' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/SafeAnchor' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/SelectableContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Spinner' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/SplitButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Switch' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Tab' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/TabContainer' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/TabContent' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/TabContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Table' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/TabPane' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Tabs' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ThemeProvider' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Toast' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ToastBody' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ToastContext' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ToastHeader' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ToggleButton' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/ToggleButtonGroup' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/Tooltip' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/transitionEndListener' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/triggerBrowserReflow' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/types' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/usePopperMarginModifiers' {
  declare module.exports: any;
}

declare module 'react-bootstrap/esm/useWrappedRefWithWarning' {
  declare module.exports: any;
}

// Filename aliases
declare module 'react-bootstrap/cjs/AbstractNav.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/AbstractNav'>;
}
declare module 'react-bootstrap/cjs/AbstractNavItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/AbstractNavItem'>;
}
declare module 'react-bootstrap/cjs/Accordion.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Accordion'>;
}
declare module 'react-bootstrap/cjs/AccordionCollapse.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/AccordionCollapse'>;
}
declare module 'react-bootstrap/cjs/AccordionContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/AccordionContext'>;
}
declare module 'react-bootstrap/cjs/AccordionToggle.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/AccordionToggle'>;
}
declare module 'react-bootstrap/cjs/Alert.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Alert'>;
}
declare module 'react-bootstrap/cjs/Badge.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Badge'>;
}
declare module 'react-bootstrap/cjs/BootstrapModalManager.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/BootstrapModalManager'>;
}
declare module 'react-bootstrap/cjs/Breadcrumb.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Breadcrumb'>;
}
declare module 'react-bootstrap/cjs/BreadcrumbItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/BreadcrumbItem'>;
}
declare module 'react-bootstrap/cjs/Button.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Button'>;
}
declare module 'react-bootstrap/cjs/ButtonGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ButtonGroup'>;
}
declare module 'react-bootstrap/cjs/ButtonToolbar.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ButtonToolbar'>;
}
declare module 'react-bootstrap/cjs/Card.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Card'>;
}
declare module 'react-bootstrap/cjs/CardColumns.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CardColumns'>;
}
declare module 'react-bootstrap/cjs/CardContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CardContext'>;
}
declare module 'react-bootstrap/cjs/CardDeck.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CardDeck'>;
}
declare module 'react-bootstrap/cjs/CardGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CardGroup'>;
}
declare module 'react-bootstrap/cjs/CardImg.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CardImg'>;
}
declare module 'react-bootstrap/cjs/Carousel.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Carousel'>;
}
declare module 'react-bootstrap/cjs/CarouselCaption.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CarouselCaption'>;
}
declare module 'react-bootstrap/cjs/CarouselItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CarouselItem'>;
}
declare module 'react-bootstrap/cjs/CloseButton.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/CloseButton'>;
}
declare module 'react-bootstrap/cjs/Col.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Col'>;
}
declare module 'react-bootstrap/cjs/Collapse.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Collapse'>;
}
declare module 'react-bootstrap/cjs/Container.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Container'>;
}
declare module 'react-bootstrap/cjs/createChainedFunction.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/createChainedFunction'>;
}
declare module 'react-bootstrap/cjs/createWithBsPrefix.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/createWithBsPrefix'>;
}
declare module 'react-bootstrap/cjs/divWithClassName.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/divWithClassName'>;
}
declare module 'react-bootstrap/cjs/Dropdown.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Dropdown'>;
}
declare module 'react-bootstrap/cjs/DropdownButton.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/DropdownButton'>;
}
declare module 'react-bootstrap/cjs/DropdownItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/DropdownItem'>;
}
declare module 'react-bootstrap/cjs/DropdownMenu.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/DropdownMenu'>;
}
declare module 'react-bootstrap/cjs/DropdownToggle.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/DropdownToggle'>;
}
declare module 'react-bootstrap/cjs/ElementChildren.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ElementChildren'>;
}
declare module 'react-bootstrap/cjs/Fade.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Fade'>;
}
declare module 'react-bootstrap/cjs/Feedback.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Feedback'>;
}
declare module 'react-bootstrap/cjs/Figure.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Figure'>;
}
declare module 'react-bootstrap/cjs/FigureCaption.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FigureCaption'>;
}
declare module 'react-bootstrap/cjs/FigureImage.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FigureImage'>;
}
declare module 'react-bootstrap/cjs/Form.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Form'>;
}
declare module 'react-bootstrap/cjs/FormCheck.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormCheck'>;
}
declare module 'react-bootstrap/cjs/FormCheckInput.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormCheckInput'>;
}
declare module 'react-bootstrap/cjs/FormCheckLabel.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormCheckLabel'>;
}
declare module 'react-bootstrap/cjs/FormContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormContext'>;
}
declare module 'react-bootstrap/cjs/FormControl.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormControl'>;
}
declare module 'react-bootstrap/cjs/FormFile.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormFile'>;
}
declare module 'react-bootstrap/cjs/FormFileInput.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormFileInput'>;
}
declare module 'react-bootstrap/cjs/FormFileLabel.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormFileLabel'>;
}
declare module 'react-bootstrap/cjs/FormGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormGroup'>;
}
declare module 'react-bootstrap/cjs/FormLabel.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormLabel'>;
}
declare module 'react-bootstrap/cjs/FormText.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/FormText'>;
}
declare module 'react-bootstrap/cjs/helpers.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/helpers'>;
}
declare module 'react-bootstrap/cjs/Image.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Image'>;
}
declare module 'react-bootstrap/cjs/index' {
  declare module.exports: $Exports<'react-bootstrap/cjs'>;
}
declare module 'react-bootstrap/cjs/index.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs'>;
}
declare module 'react-bootstrap/cjs/InputGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/InputGroup'>;
}
declare module 'react-bootstrap/cjs/Jumbotron.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Jumbotron'>;
}
declare module 'react-bootstrap/cjs/ListGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ListGroup'>;
}
declare module 'react-bootstrap/cjs/ListGroupItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ListGroupItem'>;
}
declare module 'react-bootstrap/cjs/Media.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Media'>;
}
declare module 'react-bootstrap/cjs/Modal.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Modal'>;
}
declare module 'react-bootstrap/cjs/ModalBody.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ModalBody'>;
}
declare module 'react-bootstrap/cjs/ModalContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ModalContext'>;
}
declare module 'react-bootstrap/cjs/ModalDialog.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ModalDialog'>;
}
declare module 'react-bootstrap/cjs/ModalFooter.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ModalFooter'>;
}
declare module 'react-bootstrap/cjs/ModalHeader.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ModalHeader'>;
}
declare module 'react-bootstrap/cjs/ModalTitle.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ModalTitle'>;
}
declare module 'react-bootstrap/cjs/Nav.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Nav'>;
}
declare module 'react-bootstrap/cjs/Navbar.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Navbar'>;
}
declare module 'react-bootstrap/cjs/NavbarBrand.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavbarBrand'>;
}
declare module 'react-bootstrap/cjs/NavbarCollapse.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavbarCollapse'>;
}
declare module 'react-bootstrap/cjs/NavbarContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavbarContext'>;
}
declare module 'react-bootstrap/cjs/NavbarToggle.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavbarToggle'>;
}
declare module 'react-bootstrap/cjs/NavContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavContext'>;
}
declare module 'react-bootstrap/cjs/NavDropdown.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavDropdown'>;
}
declare module 'react-bootstrap/cjs/NavItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavItem'>;
}
declare module 'react-bootstrap/cjs/NavLink.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/NavLink'>;
}
declare module 'react-bootstrap/cjs/Overlay.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Overlay'>;
}
declare module 'react-bootstrap/cjs/OverlayTrigger.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/OverlayTrigger'>;
}
declare module 'react-bootstrap/cjs/PageItem.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/PageItem'>;
}
declare module 'react-bootstrap/cjs/Pagination.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Pagination'>;
}
declare module 'react-bootstrap/cjs/Popover.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Popover'>;
}
declare module 'react-bootstrap/cjs/PopoverContent.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/PopoverContent'>;
}
declare module 'react-bootstrap/cjs/PopoverTitle.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/PopoverTitle'>;
}
declare module 'react-bootstrap/cjs/ProgressBar.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ProgressBar'>;
}
declare module 'react-bootstrap/cjs/ResponsiveEmbed.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ResponsiveEmbed'>;
}
declare module 'react-bootstrap/cjs/Row.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Row'>;
}
declare module 'react-bootstrap/cjs/SafeAnchor.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/SafeAnchor'>;
}
declare module 'react-bootstrap/cjs/SelectableContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/SelectableContext'>;
}
declare module 'react-bootstrap/cjs/Spinner.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Spinner'>;
}
declare module 'react-bootstrap/cjs/SplitButton.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/SplitButton'>;
}
declare module 'react-bootstrap/cjs/Switch.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Switch'>;
}
declare module 'react-bootstrap/cjs/Tab.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Tab'>;
}
declare module 'react-bootstrap/cjs/TabContainer.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/TabContainer'>;
}
declare module 'react-bootstrap/cjs/TabContent.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/TabContent'>;
}
declare module 'react-bootstrap/cjs/TabContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/TabContext'>;
}
declare module 'react-bootstrap/cjs/Table.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Table'>;
}
declare module 'react-bootstrap/cjs/TabPane.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/TabPane'>;
}
declare module 'react-bootstrap/cjs/Tabs.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Tabs'>;
}
declare module 'react-bootstrap/cjs/ThemeProvider.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ThemeProvider'>;
}
declare module 'react-bootstrap/cjs/Toast.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Toast'>;
}
declare module 'react-bootstrap/cjs/ToastBody.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ToastBody'>;
}
declare module 'react-bootstrap/cjs/ToastContext.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ToastContext'>;
}
declare module 'react-bootstrap/cjs/ToastHeader.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ToastHeader'>;
}
declare module 'react-bootstrap/cjs/ToggleButton.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ToggleButton'>;
}
declare module 'react-bootstrap/cjs/ToggleButtonGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/ToggleButtonGroup'>;
}
declare module 'react-bootstrap/cjs/Tooltip.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/Tooltip'>;
}
declare module 'react-bootstrap/cjs/transitionEndListener.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/transitionEndListener'>;
}
declare module 'react-bootstrap/cjs/triggerBrowserReflow.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/triggerBrowserReflow'>;
}
declare module 'react-bootstrap/cjs/types.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/types'>;
}
declare module 'react-bootstrap/cjs/usePopperMarginModifiers.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/usePopperMarginModifiers'>;
}
declare module 'react-bootstrap/cjs/useWrappedRefWithWarning.js' {
  declare module.exports: $Exports<'react-bootstrap/cjs/useWrappedRefWithWarning'>;
}
declare module 'react-bootstrap/dist/react-bootstrap.js' {
  declare module.exports: $Exports<'react-bootstrap/dist/react-bootstrap'>;
}
declare module 'react-bootstrap/dist/react-bootstrap.min.js' {
  declare module.exports: $Exports<'react-bootstrap/dist/react-bootstrap.min'>;
}
declare module 'react-bootstrap/esm/AbstractNav.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/AbstractNav'>;
}
declare module 'react-bootstrap/esm/AbstractNavItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/AbstractNavItem'>;
}
declare module 'react-bootstrap/esm/Accordion.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Accordion'>;
}
declare module 'react-bootstrap/esm/AccordionCollapse.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/AccordionCollapse'>;
}
declare module 'react-bootstrap/esm/AccordionContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/AccordionContext'>;
}
declare module 'react-bootstrap/esm/AccordionToggle.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/AccordionToggle'>;
}
declare module 'react-bootstrap/esm/Alert.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Alert'>;
}
declare module 'react-bootstrap/esm/Badge.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Badge'>;
}
declare module 'react-bootstrap/esm/BootstrapModalManager.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/BootstrapModalManager'>;
}
declare module 'react-bootstrap/esm/Breadcrumb.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Breadcrumb'>;
}
declare module 'react-bootstrap/esm/BreadcrumbItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/BreadcrumbItem'>;
}
declare module 'react-bootstrap/esm/Button.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Button'>;
}
declare module 'react-bootstrap/esm/ButtonGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ButtonGroup'>;
}
declare module 'react-bootstrap/esm/ButtonToolbar.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ButtonToolbar'>;
}
declare module 'react-bootstrap/esm/Card.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Card'>;
}
declare module 'react-bootstrap/esm/CardColumns.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CardColumns'>;
}
declare module 'react-bootstrap/esm/CardContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CardContext'>;
}
declare module 'react-bootstrap/esm/CardDeck.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CardDeck'>;
}
declare module 'react-bootstrap/esm/CardGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CardGroup'>;
}
declare module 'react-bootstrap/esm/CardImg.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CardImg'>;
}
declare module 'react-bootstrap/esm/Carousel.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Carousel'>;
}
declare module 'react-bootstrap/esm/CarouselCaption.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CarouselCaption'>;
}
declare module 'react-bootstrap/esm/CarouselItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CarouselItem'>;
}
declare module 'react-bootstrap/esm/CloseButton.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/CloseButton'>;
}
declare module 'react-bootstrap/esm/Col.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Col'>;
}
declare module 'react-bootstrap/esm/Collapse.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Collapse'>;
}
declare module 'react-bootstrap/esm/Container.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Container'>;
}
declare module 'react-bootstrap/esm/createChainedFunction.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/createChainedFunction'>;
}
declare module 'react-bootstrap/esm/createWithBsPrefix.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/createWithBsPrefix'>;
}
declare module 'react-bootstrap/esm/divWithClassName.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/divWithClassName'>;
}
declare module 'react-bootstrap/esm/Dropdown.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Dropdown'>;
}
declare module 'react-bootstrap/esm/DropdownButton.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/DropdownButton'>;
}
declare module 'react-bootstrap/esm/DropdownItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/DropdownItem'>;
}
declare module 'react-bootstrap/esm/DropdownMenu.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/DropdownMenu'>;
}
declare module 'react-bootstrap/esm/DropdownToggle.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/DropdownToggle'>;
}
declare module 'react-bootstrap/esm/ElementChildren.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ElementChildren'>;
}
declare module 'react-bootstrap/esm/Fade.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Fade'>;
}
declare module 'react-bootstrap/esm/Feedback.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Feedback'>;
}
declare module 'react-bootstrap/esm/Figure.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Figure'>;
}
declare module 'react-bootstrap/esm/FigureCaption.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FigureCaption'>;
}
declare module 'react-bootstrap/esm/FigureImage.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FigureImage'>;
}
declare module 'react-bootstrap/esm/Form.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Form'>;
}
declare module 'react-bootstrap/esm/FormCheck.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormCheck'>;
}
declare module 'react-bootstrap/esm/FormCheckInput.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormCheckInput'>;
}
declare module 'react-bootstrap/esm/FormCheckLabel.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormCheckLabel'>;
}
declare module 'react-bootstrap/esm/FormContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormContext'>;
}
declare module 'react-bootstrap/esm/FormControl.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormControl'>;
}
declare module 'react-bootstrap/esm/FormFile.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormFile'>;
}
declare module 'react-bootstrap/esm/FormFileInput.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormFileInput'>;
}
declare module 'react-bootstrap/esm/FormFileLabel.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormFileLabel'>;
}
declare module 'react-bootstrap/esm/FormGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormGroup'>;
}
declare module 'react-bootstrap/esm/FormLabel.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormLabel'>;
}
declare module 'react-bootstrap/esm/FormText.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/FormText'>;
}
declare module 'react-bootstrap/esm/helpers.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/helpers'>;
}
declare module 'react-bootstrap/esm/Image.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Image'>;
}
declare module 'react-bootstrap/esm/index' {
  declare module.exports: $Exports<'react-bootstrap/esm'>;
}
declare module 'react-bootstrap/esm/index.js' {
  declare module.exports: $Exports<'react-bootstrap/esm'>;
}
declare module 'react-bootstrap/esm/InputGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/InputGroup'>;
}
declare module 'react-bootstrap/esm/Jumbotron.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Jumbotron'>;
}
declare module 'react-bootstrap/esm/ListGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ListGroup'>;
}
declare module 'react-bootstrap/esm/ListGroupItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ListGroupItem'>;
}
declare module 'react-bootstrap/esm/Media.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Media'>;
}
declare module 'react-bootstrap/esm/Modal.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Modal'>;
}
declare module 'react-bootstrap/esm/ModalBody.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ModalBody'>;
}
declare module 'react-bootstrap/esm/ModalContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ModalContext'>;
}
declare module 'react-bootstrap/esm/ModalDialog.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ModalDialog'>;
}
declare module 'react-bootstrap/esm/ModalFooter.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ModalFooter'>;
}
declare module 'react-bootstrap/esm/ModalHeader.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ModalHeader'>;
}
declare module 'react-bootstrap/esm/ModalTitle.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ModalTitle'>;
}
declare module 'react-bootstrap/esm/Nav.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Nav'>;
}
declare module 'react-bootstrap/esm/Navbar.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Navbar'>;
}
declare module 'react-bootstrap/esm/NavbarBrand.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavbarBrand'>;
}
declare module 'react-bootstrap/esm/NavbarCollapse.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavbarCollapse'>;
}
declare module 'react-bootstrap/esm/NavbarContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavbarContext'>;
}
declare module 'react-bootstrap/esm/NavbarToggle.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavbarToggle'>;
}
declare module 'react-bootstrap/esm/NavContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavContext'>;
}
declare module 'react-bootstrap/esm/NavDropdown.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavDropdown'>;
}
declare module 'react-bootstrap/esm/NavItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavItem'>;
}
declare module 'react-bootstrap/esm/NavLink.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/NavLink'>;
}
declare module 'react-bootstrap/esm/Overlay.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Overlay'>;
}
declare module 'react-bootstrap/esm/OverlayTrigger.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/OverlayTrigger'>;
}
declare module 'react-bootstrap/esm/PageItem.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/PageItem'>;
}
declare module 'react-bootstrap/esm/Pagination.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Pagination'>;
}
declare module 'react-bootstrap/esm/Popover.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Popover'>;
}
declare module 'react-bootstrap/esm/PopoverContent.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/PopoverContent'>;
}
declare module 'react-bootstrap/esm/PopoverTitle.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/PopoverTitle'>;
}
declare module 'react-bootstrap/esm/ProgressBar.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ProgressBar'>;
}
declare module 'react-bootstrap/esm/ResponsiveEmbed.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ResponsiveEmbed'>;
}
declare module 'react-bootstrap/esm/Row.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Row'>;
}
declare module 'react-bootstrap/esm/SafeAnchor.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/SafeAnchor'>;
}
declare module 'react-bootstrap/esm/SelectableContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/SelectableContext'>;
}
declare module 'react-bootstrap/esm/Spinner.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Spinner'>;
}
declare module 'react-bootstrap/esm/SplitButton.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/SplitButton'>;
}
declare module 'react-bootstrap/esm/Switch.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Switch'>;
}
declare module 'react-bootstrap/esm/Tab.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Tab'>;
}
declare module 'react-bootstrap/esm/TabContainer.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/TabContainer'>;
}
declare module 'react-bootstrap/esm/TabContent.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/TabContent'>;
}
declare module 'react-bootstrap/esm/TabContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/TabContext'>;
}
declare module 'react-bootstrap/esm/Table.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Table'>;
}
declare module 'react-bootstrap/esm/TabPane.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/TabPane'>;
}
declare module 'react-bootstrap/esm/Tabs.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Tabs'>;
}
declare module 'react-bootstrap/esm/ThemeProvider.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ThemeProvider'>;
}
declare module 'react-bootstrap/esm/Toast.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Toast'>;
}
declare module 'react-bootstrap/esm/ToastBody.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ToastBody'>;
}
declare module 'react-bootstrap/esm/ToastContext.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ToastContext'>;
}
declare module 'react-bootstrap/esm/ToastHeader.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ToastHeader'>;
}
declare module 'react-bootstrap/esm/ToggleButton.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ToggleButton'>;
}
declare module 'react-bootstrap/esm/ToggleButtonGroup.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/ToggleButtonGroup'>;
}
declare module 'react-bootstrap/esm/Tooltip.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/Tooltip'>;
}
declare module 'react-bootstrap/esm/transitionEndListener.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/transitionEndListener'>;
}
declare module 'react-bootstrap/esm/triggerBrowserReflow.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/triggerBrowserReflow'>;
}
declare module 'react-bootstrap/esm/types.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/types'>;
}
declare module 'react-bootstrap/esm/usePopperMarginModifiers.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/usePopperMarginModifiers'>;
}
declare module 'react-bootstrap/esm/useWrappedRefWithWarning.js' {
  declare module.exports: $Exports<'react-bootstrap/esm/useWrappedRefWithWarning'>;
}
