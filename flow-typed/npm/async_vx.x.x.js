// flow-typed signature: a16ac6f49d3ef45118d1bd90192b6817
// flow-typed version: <<STUB>>/async_v^3.2.2/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'async'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'async' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'async/all' {
  declare module.exports: any;
}

declare module 'async/allLimit' {
  declare module.exports: any;
}

declare module 'async/allSeries' {
  declare module.exports: any;
}

declare module 'async/any' {
  declare module.exports: any;
}

declare module 'async/anyLimit' {
  declare module.exports: any;
}

declare module 'async/anySeries' {
  declare module.exports: any;
}

declare module 'async/apply' {
  declare module.exports: any;
}

declare module 'async/applyEach' {
  declare module.exports: any;
}

declare module 'async/applyEachSeries' {
  declare module.exports: any;
}

declare module 'async/asyncify' {
  declare module.exports: any;
}

declare module 'async/auto' {
  declare module.exports: any;
}

declare module 'async/autoInject' {
  declare module.exports: any;
}

declare module 'async/cargo' {
  declare module.exports: any;
}

declare module 'async/cargoQueue' {
  declare module.exports: any;
}

declare module 'async/compose' {
  declare module.exports: any;
}

declare module 'async/concat' {
  declare module.exports: any;
}

declare module 'async/concatLimit' {
  declare module.exports: any;
}

declare module 'async/concatSeries' {
  declare module.exports: any;
}

declare module 'async/constant' {
  declare module.exports: any;
}

declare module 'async/detect' {
  declare module.exports: any;
}

declare module 'async/detectLimit' {
  declare module.exports: any;
}

declare module 'async/detectSeries' {
  declare module.exports: any;
}

declare module 'async/dir' {
  declare module.exports: any;
}

declare module 'async/dist/async' {
  declare module.exports: any;
}

declare module 'async/dist/async.min' {
  declare module.exports: any;
}

declare module 'async/doDuring' {
  declare module.exports: any;
}

declare module 'async/doUntil' {
  declare module.exports: any;
}

declare module 'async/doWhilst' {
  declare module.exports: any;
}

declare module 'async/during' {
  declare module.exports: any;
}

declare module 'async/each' {
  declare module.exports: any;
}

declare module 'async/eachLimit' {
  declare module.exports: any;
}

declare module 'async/eachOf' {
  declare module.exports: any;
}

declare module 'async/eachOfLimit' {
  declare module.exports: any;
}

declare module 'async/eachOfSeries' {
  declare module.exports: any;
}

declare module 'async/eachSeries' {
  declare module.exports: any;
}

declare module 'async/ensureAsync' {
  declare module.exports: any;
}

declare module 'async/every' {
  declare module.exports: any;
}

declare module 'async/everyLimit' {
  declare module.exports: any;
}

declare module 'async/everySeries' {
  declare module.exports: any;
}

declare module 'async/filter' {
  declare module.exports: any;
}

declare module 'async/filterLimit' {
  declare module.exports: any;
}

declare module 'async/filterSeries' {
  declare module.exports: any;
}

declare module 'async/find' {
  declare module.exports: any;
}

declare module 'async/findLimit' {
  declare module.exports: any;
}

declare module 'async/findSeries' {
  declare module.exports: any;
}

declare module 'async/flatMap' {
  declare module.exports: any;
}

declare module 'async/flatMapLimit' {
  declare module.exports: any;
}

declare module 'async/flatMapSeries' {
  declare module.exports: any;
}

declare module 'async/foldl' {
  declare module.exports: any;
}

declare module 'async/foldr' {
  declare module.exports: any;
}

declare module 'async/forEach' {
  declare module.exports: any;
}

declare module 'async/forEachLimit' {
  declare module.exports: any;
}

declare module 'async/forEachOf' {
  declare module.exports: any;
}

declare module 'async/forEachOfLimit' {
  declare module.exports: any;
}

declare module 'async/forEachOfSeries' {
  declare module.exports: any;
}

declare module 'async/forEachSeries' {
  declare module.exports: any;
}

declare module 'async/forever' {
  declare module.exports: any;
}

declare module 'async/groupBy' {
  declare module.exports: any;
}

declare module 'async/groupByLimit' {
  declare module.exports: any;
}

declare module 'async/groupBySeries' {
  declare module.exports: any;
}

declare module 'async/inject' {
  declare module.exports: any;
}

declare module 'async/internal/applyEach' {
  declare module.exports: any;
}

declare module 'async/internal/asyncEachOfLimit' {
  declare module.exports: any;
}

declare module 'async/internal/awaitify' {
  declare module.exports: any;
}

declare module 'async/internal/breakLoop' {
  declare module.exports: any;
}

declare module 'async/internal/consoleFunc' {
  declare module.exports: any;
}

declare module 'async/internal/createTester' {
  declare module.exports: any;
}

declare module 'async/internal/DoublyLinkedList' {
  declare module.exports: any;
}

declare module 'async/internal/eachOfLimit' {
  declare module.exports: any;
}

declare module 'async/internal/filter' {
  declare module.exports: any;
}

declare module 'async/internal/getIterator' {
  declare module.exports: any;
}

declare module 'async/internal/Heap' {
  declare module.exports: any;
}

declare module 'async/internal/initialParams' {
  declare module.exports: any;
}

declare module 'async/internal/isArrayLike' {
  declare module.exports: any;
}

declare module 'async/internal/iterator' {
  declare module.exports: any;
}

declare module 'async/internal/map' {
  declare module.exports: any;
}

declare module 'async/internal/once' {
  declare module.exports: any;
}

declare module 'async/internal/onlyOnce' {
  declare module.exports: any;
}

declare module 'async/internal/parallel' {
  declare module.exports: any;
}

declare module 'async/internal/promiseCallback' {
  declare module.exports: any;
}

declare module 'async/internal/queue' {
  declare module.exports: any;
}

declare module 'async/internal/range' {
  declare module.exports: any;
}

declare module 'async/internal/reject' {
  declare module.exports: any;
}

declare module 'async/internal/setImmediate' {
  declare module.exports: any;
}

declare module 'async/internal/withoutIndex' {
  declare module.exports: any;
}

declare module 'async/internal/wrapAsync' {
  declare module.exports: any;
}

declare module 'async/log' {
  declare module.exports: any;
}

declare module 'async/map' {
  declare module.exports: any;
}

declare module 'async/mapLimit' {
  declare module.exports: any;
}

declare module 'async/mapSeries' {
  declare module.exports: any;
}

declare module 'async/mapValues' {
  declare module.exports: any;
}

declare module 'async/mapValuesLimit' {
  declare module.exports: any;
}

declare module 'async/mapValuesSeries' {
  declare module.exports: any;
}

declare module 'async/memoize' {
  declare module.exports: any;
}

declare module 'async/nextTick' {
  declare module.exports: any;
}

declare module 'async/parallel' {
  declare module.exports: any;
}

declare module 'async/parallelLimit' {
  declare module.exports: any;
}

declare module 'async/priorityQueue' {
  declare module.exports: any;
}

declare module 'async/queue' {
  declare module.exports: any;
}

declare module 'async/race' {
  declare module.exports: any;
}

declare module 'async/reduce' {
  declare module.exports: any;
}

declare module 'async/reduceRight' {
  declare module.exports: any;
}

declare module 'async/reflect' {
  declare module.exports: any;
}

declare module 'async/reflectAll' {
  declare module.exports: any;
}

declare module 'async/reject' {
  declare module.exports: any;
}

declare module 'async/rejectLimit' {
  declare module.exports: any;
}

declare module 'async/rejectSeries' {
  declare module.exports: any;
}

declare module 'async/retry' {
  declare module.exports: any;
}

declare module 'async/retryable' {
  declare module.exports: any;
}

declare module 'async/select' {
  declare module.exports: any;
}

declare module 'async/selectLimit' {
  declare module.exports: any;
}

declare module 'async/selectSeries' {
  declare module.exports: any;
}

declare module 'async/seq' {
  declare module.exports: any;
}

declare module 'async/series' {
  declare module.exports: any;
}

declare module 'async/setImmediate' {
  declare module.exports: any;
}

declare module 'async/some' {
  declare module.exports: any;
}

declare module 'async/someLimit' {
  declare module.exports: any;
}

declare module 'async/someSeries' {
  declare module.exports: any;
}

declare module 'async/sortBy' {
  declare module.exports: any;
}

declare module 'async/timeout' {
  declare module.exports: any;
}

declare module 'async/times' {
  declare module.exports: any;
}

declare module 'async/timesLimit' {
  declare module.exports: any;
}

declare module 'async/timesSeries' {
  declare module.exports: any;
}

declare module 'async/transform' {
  declare module.exports: any;
}

declare module 'async/tryEach' {
  declare module.exports: any;
}

declare module 'async/unmemoize' {
  declare module.exports: any;
}

declare module 'async/until' {
  declare module.exports: any;
}

declare module 'async/waterfall' {
  declare module.exports: any;
}

declare module 'async/whilst' {
  declare module.exports: any;
}

declare module 'async/wrapSync' {
  declare module.exports: any;
}

// Filename aliases
declare module 'async/all.js' {
  declare module.exports: $Exports<'async/all'>;
}
declare module 'async/allLimit.js' {
  declare module.exports: $Exports<'async/allLimit'>;
}
declare module 'async/allSeries.js' {
  declare module.exports: $Exports<'async/allSeries'>;
}
declare module 'async/any.js' {
  declare module.exports: $Exports<'async/any'>;
}
declare module 'async/anyLimit.js' {
  declare module.exports: $Exports<'async/anyLimit'>;
}
declare module 'async/anySeries.js' {
  declare module.exports: $Exports<'async/anySeries'>;
}
declare module 'async/apply.js' {
  declare module.exports: $Exports<'async/apply'>;
}
declare module 'async/applyEach.js' {
  declare module.exports: $Exports<'async/applyEach'>;
}
declare module 'async/applyEachSeries.js' {
  declare module.exports: $Exports<'async/applyEachSeries'>;
}
declare module 'async/asyncify.js' {
  declare module.exports: $Exports<'async/asyncify'>;
}
declare module 'async/auto.js' {
  declare module.exports: $Exports<'async/auto'>;
}
declare module 'async/autoInject.js' {
  declare module.exports: $Exports<'async/autoInject'>;
}
declare module 'async/cargo.js' {
  declare module.exports: $Exports<'async/cargo'>;
}
declare module 'async/cargoQueue.js' {
  declare module.exports: $Exports<'async/cargoQueue'>;
}
declare module 'async/compose.js' {
  declare module.exports: $Exports<'async/compose'>;
}
declare module 'async/concat.js' {
  declare module.exports: $Exports<'async/concat'>;
}
declare module 'async/concatLimit.js' {
  declare module.exports: $Exports<'async/concatLimit'>;
}
declare module 'async/concatSeries.js' {
  declare module.exports: $Exports<'async/concatSeries'>;
}
declare module 'async/constant.js' {
  declare module.exports: $Exports<'async/constant'>;
}
declare module 'async/detect.js' {
  declare module.exports: $Exports<'async/detect'>;
}
declare module 'async/detectLimit.js' {
  declare module.exports: $Exports<'async/detectLimit'>;
}
declare module 'async/detectSeries.js' {
  declare module.exports: $Exports<'async/detectSeries'>;
}
declare module 'async/dir.js' {
  declare module.exports: $Exports<'async/dir'>;
}
declare module 'async/dist/async.js' {
  declare module.exports: $Exports<'async/dist/async'>;
}
declare module 'async/dist/async.min.js' {
  declare module.exports: $Exports<'async/dist/async.min'>;
}
declare module 'async/doDuring.js' {
  declare module.exports: $Exports<'async/doDuring'>;
}
declare module 'async/doUntil.js' {
  declare module.exports: $Exports<'async/doUntil'>;
}
declare module 'async/doWhilst.js' {
  declare module.exports: $Exports<'async/doWhilst'>;
}
declare module 'async/during.js' {
  declare module.exports: $Exports<'async/during'>;
}
declare module 'async/each.js' {
  declare module.exports: $Exports<'async/each'>;
}
declare module 'async/eachLimit.js' {
  declare module.exports: $Exports<'async/eachLimit'>;
}
declare module 'async/eachOf.js' {
  declare module.exports: $Exports<'async/eachOf'>;
}
declare module 'async/eachOfLimit.js' {
  declare module.exports: $Exports<'async/eachOfLimit'>;
}
declare module 'async/eachOfSeries.js' {
  declare module.exports: $Exports<'async/eachOfSeries'>;
}
declare module 'async/eachSeries.js' {
  declare module.exports: $Exports<'async/eachSeries'>;
}
declare module 'async/ensureAsync.js' {
  declare module.exports: $Exports<'async/ensureAsync'>;
}
declare module 'async/every.js' {
  declare module.exports: $Exports<'async/every'>;
}
declare module 'async/everyLimit.js' {
  declare module.exports: $Exports<'async/everyLimit'>;
}
declare module 'async/everySeries.js' {
  declare module.exports: $Exports<'async/everySeries'>;
}
declare module 'async/filter.js' {
  declare module.exports: $Exports<'async/filter'>;
}
declare module 'async/filterLimit.js' {
  declare module.exports: $Exports<'async/filterLimit'>;
}
declare module 'async/filterSeries.js' {
  declare module.exports: $Exports<'async/filterSeries'>;
}
declare module 'async/find.js' {
  declare module.exports: $Exports<'async/find'>;
}
declare module 'async/findLimit.js' {
  declare module.exports: $Exports<'async/findLimit'>;
}
declare module 'async/findSeries.js' {
  declare module.exports: $Exports<'async/findSeries'>;
}
declare module 'async/flatMap.js' {
  declare module.exports: $Exports<'async/flatMap'>;
}
declare module 'async/flatMapLimit.js' {
  declare module.exports: $Exports<'async/flatMapLimit'>;
}
declare module 'async/flatMapSeries.js' {
  declare module.exports: $Exports<'async/flatMapSeries'>;
}
declare module 'async/foldl.js' {
  declare module.exports: $Exports<'async/foldl'>;
}
declare module 'async/foldr.js' {
  declare module.exports: $Exports<'async/foldr'>;
}
declare module 'async/forEach.js' {
  declare module.exports: $Exports<'async/forEach'>;
}
declare module 'async/forEachLimit.js' {
  declare module.exports: $Exports<'async/forEachLimit'>;
}
declare module 'async/forEachOf.js' {
  declare module.exports: $Exports<'async/forEachOf'>;
}
declare module 'async/forEachOfLimit.js' {
  declare module.exports: $Exports<'async/forEachOfLimit'>;
}
declare module 'async/forEachOfSeries.js' {
  declare module.exports: $Exports<'async/forEachOfSeries'>;
}
declare module 'async/forEachSeries.js' {
  declare module.exports: $Exports<'async/forEachSeries'>;
}
declare module 'async/forever.js' {
  declare module.exports: $Exports<'async/forever'>;
}
declare module 'async/groupBy.js' {
  declare module.exports: $Exports<'async/groupBy'>;
}
declare module 'async/groupByLimit.js' {
  declare module.exports: $Exports<'async/groupByLimit'>;
}
declare module 'async/groupBySeries.js' {
  declare module.exports: $Exports<'async/groupBySeries'>;
}
declare module 'async/index' {
  declare module.exports: $Exports<'async'>;
}
declare module 'async/index.js' {
  declare module.exports: $Exports<'async'>;
}
declare module 'async/inject.js' {
  declare module.exports: $Exports<'async/inject'>;
}
declare module 'async/internal/applyEach.js' {
  declare module.exports: $Exports<'async/internal/applyEach'>;
}
declare module 'async/internal/asyncEachOfLimit.js' {
  declare module.exports: $Exports<'async/internal/asyncEachOfLimit'>;
}
declare module 'async/internal/awaitify.js' {
  declare module.exports: $Exports<'async/internal/awaitify'>;
}
declare module 'async/internal/breakLoop.js' {
  declare module.exports: $Exports<'async/internal/breakLoop'>;
}
declare module 'async/internal/consoleFunc.js' {
  declare module.exports: $Exports<'async/internal/consoleFunc'>;
}
declare module 'async/internal/createTester.js' {
  declare module.exports: $Exports<'async/internal/createTester'>;
}
declare module 'async/internal/DoublyLinkedList.js' {
  declare module.exports: $Exports<'async/internal/DoublyLinkedList'>;
}
declare module 'async/internal/eachOfLimit.js' {
  declare module.exports: $Exports<'async/internal/eachOfLimit'>;
}
declare module 'async/internal/filter.js' {
  declare module.exports: $Exports<'async/internal/filter'>;
}
declare module 'async/internal/getIterator.js' {
  declare module.exports: $Exports<'async/internal/getIterator'>;
}
declare module 'async/internal/Heap.js' {
  declare module.exports: $Exports<'async/internal/Heap'>;
}
declare module 'async/internal/initialParams.js' {
  declare module.exports: $Exports<'async/internal/initialParams'>;
}
declare module 'async/internal/isArrayLike.js' {
  declare module.exports: $Exports<'async/internal/isArrayLike'>;
}
declare module 'async/internal/iterator.js' {
  declare module.exports: $Exports<'async/internal/iterator'>;
}
declare module 'async/internal/map.js' {
  declare module.exports: $Exports<'async/internal/map'>;
}
declare module 'async/internal/once.js' {
  declare module.exports: $Exports<'async/internal/once'>;
}
declare module 'async/internal/onlyOnce.js' {
  declare module.exports: $Exports<'async/internal/onlyOnce'>;
}
declare module 'async/internal/parallel.js' {
  declare module.exports: $Exports<'async/internal/parallel'>;
}
declare module 'async/internal/promiseCallback.js' {
  declare module.exports: $Exports<'async/internal/promiseCallback'>;
}
declare module 'async/internal/queue.js' {
  declare module.exports: $Exports<'async/internal/queue'>;
}
declare module 'async/internal/range.js' {
  declare module.exports: $Exports<'async/internal/range'>;
}
declare module 'async/internal/reject.js' {
  declare module.exports: $Exports<'async/internal/reject'>;
}
declare module 'async/internal/setImmediate.js' {
  declare module.exports: $Exports<'async/internal/setImmediate'>;
}
declare module 'async/internal/withoutIndex.js' {
  declare module.exports: $Exports<'async/internal/withoutIndex'>;
}
declare module 'async/internal/wrapAsync.js' {
  declare module.exports: $Exports<'async/internal/wrapAsync'>;
}
declare module 'async/log.js' {
  declare module.exports: $Exports<'async/log'>;
}
declare module 'async/map.js' {
  declare module.exports: $Exports<'async/map'>;
}
declare module 'async/mapLimit.js' {
  declare module.exports: $Exports<'async/mapLimit'>;
}
declare module 'async/mapSeries.js' {
  declare module.exports: $Exports<'async/mapSeries'>;
}
declare module 'async/mapValues.js' {
  declare module.exports: $Exports<'async/mapValues'>;
}
declare module 'async/mapValuesLimit.js' {
  declare module.exports: $Exports<'async/mapValuesLimit'>;
}
declare module 'async/mapValuesSeries.js' {
  declare module.exports: $Exports<'async/mapValuesSeries'>;
}
declare module 'async/memoize.js' {
  declare module.exports: $Exports<'async/memoize'>;
}
declare module 'async/nextTick.js' {
  declare module.exports: $Exports<'async/nextTick'>;
}
declare module 'async/parallel.js' {
  declare module.exports: $Exports<'async/parallel'>;
}
declare module 'async/parallelLimit.js' {
  declare module.exports: $Exports<'async/parallelLimit'>;
}
declare module 'async/priorityQueue.js' {
  declare module.exports: $Exports<'async/priorityQueue'>;
}
declare module 'async/queue.js' {
  declare module.exports: $Exports<'async/queue'>;
}
declare module 'async/race.js' {
  declare module.exports: $Exports<'async/race'>;
}
declare module 'async/reduce.js' {
  declare module.exports: $Exports<'async/reduce'>;
}
declare module 'async/reduceRight.js' {
  declare module.exports: $Exports<'async/reduceRight'>;
}
declare module 'async/reflect.js' {
  declare module.exports: $Exports<'async/reflect'>;
}
declare module 'async/reflectAll.js' {
  declare module.exports: $Exports<'async/reflectAll'>;
}
declare module 'async/reject.js' {
  declare module.exports: $Exports<'async/reject'>;
}
declare module 'async/rejectLimit.js' {
  declare module.exports: $Exports<'async/rejectLimit'>;
}
declare module 'async/rejectSeries.js' {
  declare module.exports: $Exports<'async/rejectSeries'>;
}
declare module 'async/retry.js' {
  declare module.exports: $Exports<'async/retry'>;
}
declare module 'async/retryable.js' {
  declare module.exports: $Exports<'async/retryable'>;
}
declare module 'async/select.js' {
  declare module.exports: $Exports<'async/select'>;
}
declare module 'async/selectLimit.js' {
  declare module.exports: $Exports<'async/selectLimit'>;
}
declare module 'async/selectSeries.js' {
  declare module.exports: $Exports<'async/selectSeries'>;
}
declare module 'async/seq.js' {
  declare module.exports: $Exports<'async/seq'>;
}
declare module 'async/series.js' {
  declare module.exports: $Exports<'async/series'>;
}
declare module 'async/setImmediate.js' {
  declare module.exports: $Exports<'async/setImmediate'>;
}
declare module 'async/some.js' {
  declare module.exports: $Exports<'async/some'>;
}
declare module 'async/someLimit.js' {
  declare module.exports: $Exports<'async/someLimit'>;
}
declare module 'async/someSeries.js' {
  declare module.exports: $Exports<'async/someSeries'>;
}
declare module 'async/sortBy.js' {
  declare module.exports: $Exports<'async/sortBy'>;
}
declare module 'async/timeout.js' {
  declare module.exports: $Exports<'async/timeout'>;
}
declare module 'async/times.js' {
  declare module.exports: $Exports<'async/times'>;
}
declare module 'async/timesLimit.js' {
  declare module.exports: $Exports<'async/timesLimit'>;
}
declare module 'async/timesSeries.js' {
  declare module.exports: $Exports<'async/timesSeries'>;
}
declare module 'async/transform.js' {
  declare module.exports: $Exports<'async/transform'>;
}
declare module 'async/tryEach.js' {
  declare module.exports: $Exports<'async/tryEach'>;
}
declare module 'async/unmemoize.js' {
  declare module.exports: $Exports<'async/unmemoize'>;
}
declare module 'async/until.js' {
  declare module.exports: $Exports<'async/until'>;
}
declare module 'async/waterfall.js' {
  declare module.exports: $Exports<'async/waterfall'>;
}
declare module 'async/whilst.js' {
  declare module.exports: $Exports<'async/whilst'>;
}
declare module 'async/wrapSync.js' {
  declare module.exports: $Exports<'async/wrapSync'>;
}
