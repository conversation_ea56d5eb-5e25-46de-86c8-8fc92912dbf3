// flow-typed signature: 1a3eb2e5c09f37fab5ec0dfe6a52ddc3
// flow-typed version: <<STUB>>/react-device-detect_v^2.1.2/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'react-device-detect'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'react-device-detect' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'react-device-detect/dist/lib' {
  declare module.exports: any;
}

declare module 'react-device-detect/rollup.config' {
  declare module.exports: any;
}

// Filename aliases
declare module 'react-device-detect/dist/lib.js' {
  declare module.exports: $Exports<'react-device-detect/dist/lib'>;
}
declare module 'react-device-detect/rollup.config.js' {
  declare module.exports: $Exports<'react-device-detect/rollup.config'>;
}
