// flow-typed signature: 486c7f7b9da89311ed9a3a54bab33b1d
// flow-typed version: <<STUB>>/buffer_v^6.0.3/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'buffer'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'buffer' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */


// Filename aliases
declare module 'buffer/index' {
  declare module.exports: $Exports<'buffer'>;
}
declare module 'buffer/index.js' {
  declare module.exports: $Exports<'buffer'>;
}
