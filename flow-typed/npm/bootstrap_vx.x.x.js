// flow-typed signature: 3a05a01afc36e060df1e67507ff9b081
// flow-typed version: <<STUB>>/bootstrap_v^4.5.2/flow_v0.167.1

/**
 * This is an autogenerated libdef stub for:
 *
 *   'bootstrap'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'bootstrap' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'bootstrap/dist/js/bootstrap.bundle' {
  declare module.exports: any;
}

declare module 'bootstrap/dist/js/bootstrap.bundle.min' {
  declare module.exports: any;
}

declare module 'bootstrap/dist/js/bootstrap' {
  declare module.exports: any;
}

declare module 'bootstrap/dist/js/bootstrap.min' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/alert' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/button' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/carousel' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/collapse' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/dropdown' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/modal' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/popover' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/scrollspy' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/tab' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/toast' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/tooltip' {
  declare module.exports: any;
}

declare module 'bootstrap/js/dist/util' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/alert' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/button' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/carousel' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/collapse' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/dropdown' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/modal' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/popover' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/scrollspy' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/tab' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/toast' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/tools/sanitizer' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/tooltip' {
  declare module.exports: any;
}

declare module 'bootstrap/js/src/util' {
  declare module.exports: any;
}

// Filename aliases
declare module 'bootstrap/dist/js/bootstrap.bundle.js' {
  declare module.exports: $Exports<'bootstrap/dist/js/bootstrap.bundle'>;
}
declare module 'bootstrap/dist/js/bootstrap.bundle.min.js' {
  declare module.exports: $Exports<'bootstrap/dist/js/bootstrap.bundle.min'>;
}
declare module 'bootstrap/dist/js/bootstrap.js' {
  declare module.exports: $Exports<'bootstrap/dist/js/bootstrap'>;
}
declare module 'bootstrap/dist/js/bootstrap.min.js' {
  declare module.exports: $Exports<'bootstrap/dist/js/bootstrap.min'>;
}
declare module 'bootstrap/js/dist/alert.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/alert'>;
}
declare module 'bootstrap/js/dist/button.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/button'>;
}
declare module 'bootstrap/js/dist/carousel.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/carousel'>;
}
declare module 'bootstrap/js/dist/collapse.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/collapse'>;
}
declare module 'bootstrap/js/dist/dropdown.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/dropdown'>;
}
declare module 'bootstrap/js/dist/index' {
  declare module.exports: $Exports<'bootstrap/js/dist'>;
}
declare module 'bootstrap/js/dist/index.js' {
  declare module.exports: $Exports<'bootstrap/js/dist'>;
}
declare module 'bootstrap/js/dist/modal.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/modal'>;
}
declare module 'bootstrap/js/dist/popover.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/popover'>;
}
declare module 'bootstrap/js/dist/scrollspy.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/scrollspy'>;
}
declare module 'bootstrap/js/dist/tab.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/tab'>;
}
declare module 'bootstrap/js/dist/toast.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/toast'>;
}
declare module 'bootstrap/js/dist/tooltip.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/tooltip'>;
}
declare module 'bootstrap/js/dist/util.js' {
  declare module.exports: $Exports<'bootstrap/js/dist/util'>;
}
declare module 'bootstrap/js/src/alert.js' {
  declare module.exports: $Exports<'bootstrap/js/src/alert'>;
}
declare module 'bootstrap/js/src/button.js' {
  declare module.exports: $Exports<'bootstrap/js/src/button'>;
}
declare module 'bootstrap/js/src/carousel.js' {
  declare module.exports: $Exports<'bootstrap/js/src/carousel'>;
}
declare module 'bootstrap/js/src/collapse.js' {
  declare module.exports: $Exports<'bootstrap/js/src/collapse'>;
}
declare module 'bootstrap/js/src/dropdown.js' {
  declare module.exports: $Exports<'bootstrap/js/src/dropdown'>;
}
declare module 'bootstrap/js/src/modal.js' {
  declare module.exports: $Exports<'bootstrap/js/src/modal'>;
}
declare module 'bootstrap/js/src/popover.js' {
  declare module.exports: $Exports<'bootstrap/js/src/popover'>;
}
declare module 'bootstrap/js/src/scrollspy.js' {
  declare module.exports: $Exports<'bootstrap/js/src/scrollspy'>;
}
declare module 'bootstrap/js/src/tab.js' {
  declare module.exports: $Exports<'bootstrap/js/src/tab'>;
}
declare module 'bootstrap/js/src/toast.js' {
  declare module.exports: $Exports<'bootstrap/js/src/toast'>;
}
declare module 'bootstrap/js/src/tools/sanitizer.js' {
  declare module.exports: $Exports<'bootstrap/js/src/tools/sanitizer'>;
}
declare module 'bootstrap/js/src/tooltip.js' {
  declare module.exports: $Exports<'bootstrap/js/src/tooltip'>;
}
declare module 'bootstrap/js/src/util.js' {
  declare module.exports: $Exports<'bootstrap/js/src/util'>;
}
