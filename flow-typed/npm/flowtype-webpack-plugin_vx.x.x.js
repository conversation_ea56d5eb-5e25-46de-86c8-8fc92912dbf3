// flow-typed signature: 3d4fcbd702a0bfdf883f60fddfb8d0e9
// flow-typed version: <<STUB>>/flowtype-webpack-plugin_v0.0.3/flow_v0.156.0

/**
 * This is an autogenerated libdef stub for:
 *
 *   'flowtype-webpack-plugin'
 *
 * Fill this stub out by replacing all the `any` types.
 *
 * Once filled out, we encourage you to share your work with the
 * community by sending a pull request to:
 * https://github.com/flowtype/flow-typed
 */

declare module 'flowtype-webpack-plugin' {
  declare module.exports: any;
}

/**
 * We include stubs for each file inside this npm package in case you need to
 * require those files directly. Feel free to delete any files that aren't
 * needed.
 */
declare module 'flowtype-webpack-plugin/lib/flowResult' {
  declare module.exports: any;
}

declare module 'flowtype-webpack-plugin/lib/flowStatus' {
  declare module.exports: any;
}

// Filename aliases
declare module 'flowtype-webpack-plugin/index' {
  declare module.exports: $Exports<'flowtype-webpack-plugin'>;
}
declare module 'flowtype-webpack-plugin/index.js' {
  declare module.exports: $Exports<'flowtype-webpack-plugin'>;
}
declare module 'flowtype-webpack-plugin/lib/flowResult.js' {
  declare module.exports: $Exports<'flowtype-webpack-plugin/lib/flowResult'>;
}
declare module 'flowtype-webpack-plugin/lib/flowStatus.js' {
  declare module.exports: $Exports<'flowtype-webpack-plugin/lib/flowStatus'>;
}
