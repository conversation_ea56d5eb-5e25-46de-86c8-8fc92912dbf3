import {DateTime, Format, DateType} from '../index';

describe('DateTime', () => {
    it('should create a DateTime instance from the start of a unit', () => {
        const dateTime = DateTime.startOf('month');
        const now = new Date();
        const startOfMonth = new Date(
            Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1)
        );

        expect(dateTime.get()).toEqual(startOfMonth);
    });

    it('should create a DateTime instance from the current local datetime', () => {
        const dateTime = DateTime.now();

        const now = new Date();
        const year = now.getUTCFullYear();
        const month = String(now.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-indexed in JavaScript
        const day = String(now.getUTCDate()).padStart(2, '0');

        const dateString = `${year}-${month}-${day}`;

        expect(dateTime.format().toString()).toEqual(dateString);
    });

    it('should create a DateTime instance from a JSDate', () => {
        const date = new Date();
        const dateTime = DateTime.fromDate(date);
        expect(dateTime.get()).toEqual(date);
    });

    it('should create a DateTime instance from a custom date string', () => {
        const date = '2023-04-21';
        const dateTime = DateTime.parseCustom(date);
        expect(dateTime.format().toString()).toEqual('2023-04-21');
    });

    it('should create a DateTime instance from a SQL date string', () => {
        const date = '2022-01-01 00:00:00';
        const dateTime = DateTime.parse(date);
        expect(dateTime.format().toString()).toEqual('2022-01-01');
    });

    it('should create a DateTime instance from an ISO date string', () => {
        const date = '2022-02-03T16:04:00';
        const dateTime = DateTime.parse(date, DateType.ISO);
        expect(
            dateTime.format(Format.DayMonthYearAtTimeFormat).toString()
        ).toEqual('03 of February 2022 at 04:04 PM');
    });

    it('should format the date', () => {
        const date = '2022-01-03';
        const dateTime = DateTime.parseCustom(date);
        expect(dateTime.format(Format.DateBritish).toString()).toEqual(
            '03/01/2022'
        );
    });

    it('should add an ordinal suffix to the date', () => {
        const date = '2022-01-01';
        const dateTime = DateTime.parseCustom(date, Format.Date);
        expect(
            dateTime
                .format(Format.DayMonthYearAtTimeFormat)
                .addOrdinalSuffix()
                .toString()
        ).toEqual('1st of January 2022 at 12:00 AM');
    });

    it('should add a duration to the date', () => {
        const date = '2022-01-01';
        const dateTime = DateTime.parseCustom(date, Format.Date);
        expect(dateTime.add({days: 1}).format().toString()).toEqual(
            '2022-01-02'
        );

        expect(dateTime.add({months: 3}).format().toString()).toEqual(
            '2022-04-02'
        );
    });

    it('should check if the date is before another date', () => {
        const date1 = '2022-01-01';
        const date2 = '2022-01-02';
        const dateTime1 = DateTime.parseCustom(date1, Format.Date);
        const dateTime2 = DateTime.fromDate(new Date(date2));
        expect(dateTime1.isBefore(dateTime2.get())).toEqual(true);
    });

    it('should calculate the difference between two dates in months', () => {
        const date1 = '2022-09-05';
        const date2 = '2022-01-05';
        const dateTime1 = DateTime.parseCustom(date1);
        const dateTime2 = DateTime.parseCustom(date2);
        expect(dateTime1.diff(dateTime2.get(), 'months')).toEqual({months: 8});
    });

    it('should calculate the difference between two dates when first date is later than second', () => {
        const date1 = '2022-02-05';
        const date2 = '2022-01-06';
        const dateTime1 = DateTime.parseCustom(date1);
        const dateTime2 = DateTime.parseCustom(date2);
        expect(dateTime1.diff(dateTime2.get(), 'days')).toEqual({days: 30});
    });

    it('should calculate the difference between two dates when first date is earlier than second', () => {
        const date1 = '2022-01-07';
        const date2 = '2022-02-09';

        const dateTime1 = DateTime.parseCustom(date1);
        const dateTime2 = DateTime.parseCustom(date2);

        expect(dateTime1.diff(dateTime2.get(), 'days')).toEqual({days: -33});
    });

    it('should calculate the difference between two dates without absolute roundoff', () => {
        const date1 = '2022-01-07';
        const date2 = '2022-02-09 16:22:00';

        const dateTime1 = DateTime.parseCustom(date1);
        const dateTime2 = DateTime.parseCustom(date2, Format.DateTime);

        expect(dateTime1.diff(dateTime2.get(), 'days', false)).toEqual({
            days: -33.68194444444445,
        });
    });
});
