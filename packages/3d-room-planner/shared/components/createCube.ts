import * as THREE from 'three';

/**
 * Creates a cube mesh with the specified size and color.
 *
 * @param {number} size - The size of the cube's edges.
 * @param {number} color - The color of the cube in hexadecimal format (e.g., 0xff0000 for red).
 * @return {THREE.Mesh} - A THREE.Mesh object representing the cube.
 */
export function createCube(size: number, color: number): THREE.Mesh {
    const geometry = new THREE.BoxGeometry(size, size, size);
    const material = new THREE.MeshBasicMaterial({color});
    return new THREE.Mesh(geometry, material);
}
