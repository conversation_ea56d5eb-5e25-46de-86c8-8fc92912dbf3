import {describe, expect, it} from '@jest/globals';
import * as THREE from 'three';
import {createCube} from '../shared/components/createCube';

describe('createCube', () => {
    it('should create a cube with the specified size and color', () => {
        // Arrange
        const size = 5;
        const color = 0xff0000;
        const cube = createCube(size, color);

        // Act and assert
        expect(cube).toBeInstanceOf(THREE.Mesh);

        expect(cube.geometry).toBeInstanceOf(THREE.BoxGeometry);
        expect((cube.geometry as THREE.BoxGeometry).parameters.width).toBe(
            size
        );
        expect((cube.geometry as THREE.BoxGeometry).parameters.height).toBe(
            size
        );
        expect((cube.geometry as THREE.BoxGeometry).parameters.depth).toBe(
            size
        );

        expect(cube.material).toBeInstanceOf(THREE.MeshBasicMaterial);
        expect((cube.material as THREE.MeshBasicMaterial).color.getHex()).toBe(
            color
        );
    });
});
