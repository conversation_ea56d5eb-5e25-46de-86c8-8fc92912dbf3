import {readFileSync, realpathSync} from 'node:fs';
import {dirname} from 'node:path';
import {argv, cwd} from 'node:process';

// Drop node interpreter and script paths
argv.shift();
argv.shift();

const entrypoints = argv
    .map((filename) => {
        if (!filename.endsWith('manifest.json')) {
            throw new Error(
                "Filename passed that doesn't end with 'manifest.json'"
            );
        }

        /* eslint-disable-next-line security/detect-non-literal-fs-filename --
         * We're as sure as we can be that the file is safe */
        const data = readFileSync(filename, 'utf8');

        const manifest = JSON.parse(data) as Record<string, unknown>;

        if (typeof manifest !== 'object') {
            throw new Error(`${filename} doesn't appear to be a manifest`);
        }

        const path = dirname(filename);
        const prefix = cwd();

        return [
            filename,
            ...Object.keys(manifest)
                .filter((file) => file.endsWith('.html'))
                .map((file) =>
                    /* eslint-disable-next-line security/detect-non-literal-fs-filename --
                     * We're trusting that WebPack produced sane filenames here */
                    realpathSync(`${path}/${file}`).slice(prefix.length + 1)
                ),
        ];
    })
    .flat();

/* eslint-disable-next-line no-console --
 * Needed to output the list of files */
entrypoints.forEach((file) => console.log(file));
