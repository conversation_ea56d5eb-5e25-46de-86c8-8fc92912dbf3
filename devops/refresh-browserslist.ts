/* eslint-disable no-console -- CLI tool, so we need to output stuff */
import {BigQuery} from '@google-cloud/bigquery';
import {parse} from 'browserslist-ga/src/caniuse-parser';
import {writeFile} from 'fs/promises';

const browserslistFilename = 'browserslist-stats.json';

const bigquery = new BigQuery({
    projectId: 'gocabinets',
});

const runQuery = async () => {
    console.log(`Retrieving browser stats from Google Analytics:`);

    const query = `
    WITH web_vitals AS (
    SELECT *, 'cabinetry.online' AS top_level_hostname
        FROM \`gocabinets.analytics_455745539.events_*\`
        WHERE TIMESTAMP_MICROS(event_timestamp) > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
    UNION ALL SELECT *, 'gocabinets1.com.au' AS top_level_hostname
        FROM \`gocabinets.analytics_455761293.events_*\`
        WHERE TIMESTAMP_MICROS(event_timestamp) > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
    UNION ALL SELECT *, 'benchtops.online' AS top_level_hostname
        FROM \`gocabinets.analytics_455763913.events_*\`
        WHERE TIMESTAMP_MICROS(event_timestamp) > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
    )
    SELECT
    NULL AS \`Operating system\`,
    NULL AS \`OS version\`,
    NULL AS \`Browser\`,
    NULL AS \`Browser version\`,
    NULL AS \`Device category\`,
    COUNT(*) AS \`Event count\`
    FROM web_vitals
    UNION ALL SELECT
    device.operating_system AS \`Operating system\`,
    SUBSTR(device.operating_system_version, LENGTH(device.operating_system) + 2) AS \`OS version\`,
    device.web_info.browser AS \`Browser\`,
    device.web_info.browser_version AS \`Browser version\`,
    device.category AS \`Device category\`,
    COUNT(*) AS \`Event count\`
    FROM web_vitals
    GROUP BY \`Operating system\`, \`OS version\`, \`Browser\`, \`Browser version\`, \`Device category\`
    ORDER BY \`Operating system\`, \`OS version\`, \`Browser\`, \`Browser version\`, \`Device category\`
    `;

    const [job] = await bigquery.createQueryJob({
        location: 'US',
        query,
    });

    console.log(`Job ${job.id} started.`);

    // Wait for the query to finish
    const [rows] = (await job.getQueryResults()) as [
        [
            {
                'Operating system': string | null;
                'OS version': string | null;
                Browser: string | null;
                'Browser version': string | null;
                'Device category': string | null;
                'Event count': number | null;
            }
        ]
    ];

    console.log(`Received ${rows.length} rows.`);

    const rawRows = rows.map((row) => {
        // Fix too-short version numbers
        if (
            row['Browser version'] &&
            row['Browser version'].indexOf('.') === -1
        ) {
            row['Browser version'] += '.0';
        }

        return [
            row['Operating system'] || '',
            row['OS version'] || '',
            row['Browser'] || '',
            row['Browser version'] || '',
            row['Device category'] || '',
            row['Event count'] || 0,
        ];
    });

    const parsedData = parse(rawRows);

    console.log(`Found stats for ${Object.keys(parsedData).length} browsers.`);

    await writeFile(browserslistFilename, JSON.stringify(parsedData, null, 2));

    console.log(`Data written to ${browserslistFilename}.`);
};

void runQuery();
