import React from 'react';
import {MockApp} from '../MockApp';
import JobDetailsPDF from 'components/customer/PDF/JobDetailsPDF';

describe('Job Details PDF', () => {
    before(() => {
        cy.viewport(1280, 720);
        cy.userFixture();
        cy.jobDetailsPdfFixture('getPreviewDetails', {
            queryParams: {id: '434419'},
        });
        cy.intercept('/v2/dist/css/pdf.css', {fixture: 'pdf/pdf.css'});
        cy.intercept(
            {
                method: 'GET',
                url: /\/.+\.(png|jpeg|jpg)/,
            },
            {fixture: 'Blank.png'}
        );
    });

    // not breaking test into multiple as pagedjs is not that compatible with remounting
    it('should render Job PDF', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                route="/v2/pdf/preview/434419?pdf=1"
                path="/v2/pdf/preview/:jobId"
                application={true}
                notifications={true}>
                <JobDetailsPDF />
            </MockApp>
        );
        cy.get('#page-1').within(() => {
            // header
            cy.getCy('job_header').should('have.text', 'JOB #434419');
            cy.getCy('job_name').should('have.text', 'JOB NAME:Cabinets');
            cy.getCy('job_submitted_date').should(
                'have.text',
                'SUBMITTED:17th Feb 2025 at 3:50pm'
            );
            cy.get('#job_details').should('contain.text', 'PROPERTY LIST');
            cy.get('#job_details').should('contain.text', 'JOB BELONGING TO');
            cy.get('#job_details').should('contain.text', 'Crafty Cabinets');
            cy.get('#job_details').should(
                'contain.text',
                '<EMAIL>'
            );
            cy.get('#job_details').should('contain.text', 'DELIVERED TO');
            cy.get('#job_details').should(
                'contain.text',
                'Your Address Goes Here'
            );
            cy.get('#job_details').should(
                'contain.text',
                'Your Suburb VIC 3000'
            );
            cy.get('#job_details').should('contain.text', 'Australia');
            cy.get('#job_details').should('contain.text', 'PHONE:0352543274');
            cy.get('#job_details').should(
                'contain.text',
                'JOB DESCRIPTION:Sample Cabinets'
            );

            // room
            cy.get('#job_details').should('contain.text', 'ROOM NUMBER:1');
            cy.get('#job_details').should('contain.text', 'ROOM NAME:Room 1');
            cy.get('#job_details').should(
                'contain.text',
                'ROOM DESCRIPTION:Hello Room'
            );
            cy.get('#job_details').should(
                'contain.text',
                'COMPANY LABEL:Crafty Cabinets 0352543274'
            );

            // product
            cy.contains('PRODUCT 1-1.00').should('exist');
            cy.contains('TALL LEFT').should('exist');

            // should render QR and iamge
            cy.getCy('qr-1-1.00').should('exist');
            cy.getCy('img-1-1.00').should('exist');
        });

        cy.get('#page-2').within(() => {
            // product
            cy.contains('PRODUCT 1-2.00').should('exist');
            cy.contains('BASE MICROWAVE DRAWER').should('exist');

            // should render QR and iamge
            cy.getCy('qr-1-2.00').should('exist');
            cy.getCy('img-1-2.00').should('exist');
        });
    });
});
