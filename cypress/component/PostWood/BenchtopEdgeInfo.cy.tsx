import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import BenchtopEdgeInfo from 'components/manufacturer/PostWood/component/BenchtopEdgeInfo';
describe('Benchtop Edge Info', () => {
    before(() => {
        cy.postwoodFixture('getEdgeInformation', {
            queryParams: {
                page_size: '25',
                current_page: '1',
            },
        });
        cy.viewport(1280, 720);
    });

    it('should render header', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopEdgeInfo />
            </MockManufacturerApp>
        );
        cy.getCy('header')
            .should('exist')
            .and('have.text', 'Postwood Benchtop Edge Information');
    });

    it('should render table headers', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopEdgeInfo />
            </MockManufacturerApp>
        );
        cy.contains('[data-column-id=id]', 'ID').should('exist');
        cy.contains('[data-column-id=edgeType]', 'Edge Type').should('exist');
        cy.contains(
            '[data-column-id=finishedEdgeThickness]',
            'Finished Edge Thickness'
        ).should('exist');
        cy.contains(
            '[data-column-id=substrateOverhang]',
            'Substrate Overhang'
        ).should('exist');
        cy.contains(
            '[data-column-id=surfaceOverhang]',
            'Surface Overhang'
        ).should('exist');
        cy.contains(
            '[data-column-id=substrateEdgeSeenOnLabel]',
            'Substrate Edge Seen On Label'
        ).should('exist');
        cy.contains(
            '[data-column-id=surfaceEdgeSeenOnLabel]',
            'Surface Edge Seen On Label'
        ).should('exist');
        cy.contains(
            '[data-column-id=sizingTrimRequired]',
            'Sizing Trim Required'
        ).should('exist');
        cy.contains(
            '[data-column-id=sizingRoughRequired]',
            'Sizing Rough Required'
        ).should('exist');
        cy.contains(
            '[data-column-id=siteFittingRebate]',
            'Site Fitting Rebate'
        ).should('exist');
        cy.contains('[data-column-id=dripLipRebate]', 'Drip Lip Rebate').should(
            'exist'
        );
        cy.contains('[data-column-id=routeOut]', 'Route Out').should('exist');
        cy.contains('[data-column-id=actions]', 'Actions').should('exist');
    });

    it('should open the Edit Edge Info form and prepopulate it when edit icon is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopEdgeInfo />
            </MockManufacturerApp>
        );
        cy.getCy('edit-1').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Edit Benchtop Edge Information');
        cy.get('input[name=EdgeType]')
            .should('exist')
            .and('not.be.disabled')
            .and('have.value', '180 Roll');
        cy.get('input[name=FinishedEdgeThickness]')
            .should('exist')
            .and('not.be.disabled')
            .and('have.value', '0');
        cy.get('input[name=SubstrateOverhang]')
            .should('exist')
            .and('not.be.disabled')
            .and('have.value', '20');
        cy.get('input[name=SurfaceOverhang]')
            .should('exist')
            .and('not.be.disabled')
            .and('have.value', '24');
        cy.get('textarea[name=SpecialInstructions]')
            .should('exist')
            .and('not.be.disabled')
            .and(
                'have.value',
                'IncorparateSubstrateThickness:1~SubstrateRadius:16~WrapBottomEdgeValue:38'
            );
        cy.get('input[name=SubstrateEdgeSeenOnLabel]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.checked');
        cy.get('input[name=SurfaceEdgeSeenOnLabel]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.checked');
        cy.get('input[name=SizingTrimRequired]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=SizingRoughRequired]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=SiteFittingRebate]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=DripLipRebate]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.getCy('cancel', 'button').should('exist');
        cy.getCy('save_changes', 'button').should('exist');
        cy.getCy('cancel', 'button').click();
    });

    it('should open the Add Edge Info form when Add New Record button is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopEdgeInfo />
            </MockManufacturerApp>
        );
        cy.get('button').contains('Add New Record').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Add New Benchtop Edge Information');
        cy.get('input[name=EdgeType]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.empty');
        cy.get('input[name=FinishedEdgeThickness]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.empty');
        cy.get('input[name=SubstrateOverhang]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.empty');
        cy.get('input[name=SurfaceOverhang]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.empty');
        cy.get('textarea[name=SpecialInstructions]')
            .should('exist')
            .and('not.be.disabled')
            .and('be.empty');
        cy.get('input[name=SubstrateEdgeSeenOnLabel]')
            .should('exist')
            .and('not.be.disabled');
        cy.get('input[name=SurfaceEdgeSeenOnLabel]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=SizingTrimRequired]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=SizingRoughRequired]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=SiteFittingRebate]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.get('input[name=DripLipRebate]')
            .should('exist')
            .and('not.be.disabled')
            .and('not.be.checked');
        cy.getCy('cancel', 'button').should('exist');
        cy.getCy('save_changes', 'button').should('exist');
        cy.getCy('cancel', 'button').click();
    });

    it('should open the confimation modal when delete icon is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopEdgeInfo />
            </MockManufacturerApp>
        );
        cy.getCy('delete-1').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Delete Benchtop Edge Information');
        cy.get('.modal-body')
            .should('exist')
            .and(
                'to.have.text',
                'Are you sure you want to delete the following Benchtop Edge Information: 180 Roll?'
            );
        cy.get('button[title=No]').should('exist');
        cy.get('button[title=Yes]').should('exist');
        cy.get('button[title=No]').click();
    });
});
