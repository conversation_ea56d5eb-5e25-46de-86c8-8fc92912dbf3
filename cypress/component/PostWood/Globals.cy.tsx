import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import Globals from 'components/manufacturer/PostWood/component/Globals';
describe('Globals', () => {
    before(() => {
        cy.viewport(1280, 720);
        cy.postwoodFixture('getGlobals', {
            queryParams: {
                page_size: '25',
                current_page: '1',
            },
        });
    });

    it('should render header', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <Globals />
            </MockManufacturerApp>
        );
        cy.getCy('header').should('exist').and('have.text', 'Postwood Globals');
    });

    it('should render table headers', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <Globals />
            </MockManufacturerApp>
        );
        cy.contains('[data-column-id=id]', 'ID').should('exist');
        cy.contains('[data-column-id=name]', 'Name').should('exist');
        cy.contains('[data-column-id=value]', 'Value').should('exist');
        cy.contains('[data-column-id=comment]', 'Comment').should('exist');
        cy.contains('[data-column-id=category]', 'Category').should('exist');
        cy.contains('[data-column-id=variType]', 'VariType').should('exist');
        cy.contains('[data-column-id=actions]', 'Actions').should('exist');
    });

    it('should open the Edit Globals form when edit icon is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <Globals />
            </MockManufacturerApp>
        );
        cy.getCy('edit-1').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Edit Setting');
        cy.get('input[name=Name]').should('exist').and('be.disabled');
        cy.get('input[name=Value]').should('exist').and('not.be.disabled');
        cy.get('input[name=Category]').should('exist').and('be.disabled');
        cy.get('input[name=VariType]').should('exist').and('be.disabled');
        cy.get('textarea[name=Comment]').should('exist').and('be.disabled');
        cy.getCy('cancel', 'button').should('exist');
        cy.getCy('save_changes', 'button').should('exist');
        cy.getCy('cancel', 'button').click();
    });

    it('should open the confimation modal when reset icon is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <Globals />
            </MockManufacturerApp>
        );
        cy.getCy('reset-1').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Reset to Defaults');
        cy.get('.modal-body')
            .should('exist')
            .and(
                'to.have.text',
                'Are you sure you want to reset AdditionalLsCornerRadius to its default settings?'
            );
        cy.get('button[title=No]').should('exist');
        cy.get('button[title=Yes]').should('exist');
        cy.get('button[title=No]').click();
    });

    it('should NOT display the reset icon if data is not overriden', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <Globals />
            </MockManufacturerApp>
        );
        cy.getCy('reset-1').should('exist');
        cy.getCy('reset-2').should('not.exist');
        cy.getCy('reset-3').should('not.exist');
        cy.getCy('reset-4').should('not.exist');
        cy.getCy('reset-5').should('not.exist');
    });
});
