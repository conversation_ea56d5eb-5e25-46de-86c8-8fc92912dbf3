import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import BenchtopTool from 'components/manufacturer/PostWood/component/BenchtopTool';
describe('Benchtop Tool Numbers', () => {
    before(() => {
        cy.postwoodFixture('getToolNumbers', {
            queryParams: {
                page_size: '25',
                current_page: '1',
            },
        });
        cy.viewport(1280, 720);
    });

    it('should render header', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopTool />
            </MockManufacturerApp>
        );
        cy.getCy('header')
            .should('exist')
            .and('have.text', 'Postwood Benchtop Tool Numbers');
    });

    it('should render table headers', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopTool />
            </MockManufacturerApp>
        );
        cy.contains('[data-column-id=id]', 'ID').should('exist');
        cy.contains('[data-column-id=toolJob]', 'Tool Job').should('exist');
        cy.contains('[data-column-id=toolNumber]', 'Tool Number').should(
            'exist'
        );
        cy.contains('[data-column-id=toolRadius]', 'Tool Radius').should(
            'exist'
        );
        cy.contains('[data-column-id=feedSpeed]', 'Feed Speed').should('exist');
        cy.contains(
            '[data-column-id=sequenceNumber]',
            'Sequence Number'
        ).should('exist');
        cy.contains('[data-column-id=description]', 'Description').should(
            'exist'
        );
        cy.contains(
            '[data-column-id=specialInstructions]',
            'Special Instructions'
        ).should('exist');
        cy.contains('[data-column-id=actions]', 'Actions').should('exist');
    });

    it('should open the Edit Benchtop Tool Numbers form when edit icon is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopTool />
            </MockManufacturerApp>
        );
        cy.getCy('edit-1').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Edit Benchtop Tool Number');
        cy.get('input[name=ToolJob]').should('exist').and('be.disabled');
        cy.get('input[name=ToolNumber]').should('exist').and('not.be.disabled');
        cy.get('input[name=ToolRadius]').should('exist').and('not.be.disabled');
        cy.get('input[name=FeedSpeed]').should('exist').and('not.be.disabled');
        cy.get('textarea[name=Description]')
            .should('exist')
            .and('not.be.disabled');
        cy.get('textarea[name=SpecialInstructions]')
            .should('exist')
            .and('not.be.disabled');
        cy.get('input[name=SequenceNumber]')
            .should('exist')
            .and('not.be.disabled');
        cy.getCy('cancel', 'button').should('exist');
        cy.getCy('save_changes', 'button').should('exist');
        cy.getCy('cancel', 'button').click();
    });

    it('should open the confimation modal when reset icon is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopTool />
            </MockManufacturerApp>
        );
        cy.getCy('reset-1').click();
        cy.get('.modal-title')
            .should('exist')
            .and('to.have.text', 'Reset to Defaults');
        cy.get('.modal-body')
            .should('exist')
            .and(
                'to.have.text',
                'Are you sure you want to reset BoltBody to its default settings?'
            );
        cy.get('button[title=No]').should('exist');
        cy.get('button[title=Yes]').should('exist');
        cy.get('button[title=No]').click();
    });

    it('should NOT display the reset icon if data is not overriden', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <BenchtopTool />
            </MockManufacturerApp>
        );
        cy.getCy('reset-1').should('exist');
        cy.getCy('reset-2').should('exist');
        cy.getCy('reset-3').should('exist');
        cy.getCy('reset-4').should('not.exist');
        cy.getCy('reset-5').should('not.exist');
    });
});
