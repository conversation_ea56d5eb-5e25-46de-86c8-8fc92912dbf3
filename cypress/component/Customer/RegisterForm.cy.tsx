import React from 'react';
import {MockApp} from '../MockApp';
import {RegisterForm} from 'components';

describe('RegisterForm', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.userFixture();
    });

    it('should render the header', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.contains('h1', 'Create New Account').should('exist');
    });

    it('should render name input', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=fullName]').should('exist');
    });

    it('should render business name input', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=businessName]').should('exist');
    });

    it('should render email input', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=email]').should('exist');
    });

    it('should render phone input', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=phone]').should('exist');
    });

    it('should render country selection when generic domain', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.getCy('country').should('exist');
    });

    it('should render address input and should be disabled when no country is selected', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=addressSearch]').should('exist').and('be.disabled');
    });

    it('should enable address input when country is selected', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.get('input[name=addressSearch]').should('exist').and('be.enabled');
    });

    it('should render the address manual input fields when address is not found by customer search', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.get('input[name=addressSearch]').type('anything');
        cy.get('li.cantFindAddress').click();
        cy.get('input[name=address]').should('exist');
        cy.get('input[name=address2]').should('exist');
        cy.get('input[name=suburb]').should('exist');
        cy.get('input[name=postcode]').should('exist');
        cy.get('input[name=postcode]').should('exist');
        cy.getCy('state').should('exist');
    });

    it('should not render company details fields initially', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=abn]').should('not.exist');
        cy.get('input[name=acn]').should('not.exist');
        cy.get('input[name=webUrl]').should('not.exist');
    });

    it('should render company details fields when expanded', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.contains('div', 'Company Details').should('exist').click();
        cy.get('input[name=abn]').should('exist');
        cy.get('input[name=acn]').should('exist');
        cy.get('input[name=webUrl]').should('exist');
    });

    it('should render submit and back to login buttons', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.contains('a[href="/v2/login"]', 'Back to Login').should('exist');
        cy.contains('button', 'Submit').should('exist');
    });

    it('should trigger validations for required fields upon submission', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.contains('button', 'Submit').click();
        cy.contains('div[role=alert]', 'Please enter your full name').should(
            'exist'
        );
        cy.contains('div[role=alert]', 'Please enter your email').should(
            'exist'
        );
        cy.contains('div[role=alert]', 'Please enter your phone number').should(
            'exist'
        );
        cy.contains('div[role=alert]', 'Please choose a country').should(
            'exist'
        );
    });

    it('should trigger address validation when country is selected', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.contains('button', 'Submit').click();
        cy.contains('div[role=alert]', 'Please enter your address').should(
            'exist'
        );
    });

    it('should trigger required address fields validation when manual address input', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.get('input[name=addressSearch]').type('anything');
        cy.get('li.cantFindAddress').click();
        cy.contains('button', 'Submit').click();
        cy.contains('div[role=alert]', 'Please enter your address').should(
            'exist'
        );
        cy.contains('div[role=alert]', 'Please enter your suburb/city').should(
            'exist'
        );
        cy.contains('div[role=alert]', 'Please enter your postcode').should(
            'exist'
        );
        cy.contains('div[role=alert]', 'Please choose a state').should('exist');
    });

    it('should trigger validation for invalid email', () => {
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=email]').type('any.co');
        cy.get('input[name=email]').blur();
        cy.contains(
            'div[role=alert]',
            'Please enter a valid email address'
        ).should('exist');
    });

    it('should be able to submit the form successfully', () => {
        const PLACE_ID = '2001';
        const EMAIL = '<EMAIL>';
        cy.addressFixture('getPredictions', {
            queryParams: {input: '123 ABC', country: '1'},
        });
        cy.addressFixture('getPredictionDetail', {
            queryParams: {place_id: PLACE_ID},
        });
        cy.intercept('POST', '/api/account/register_customer', {
            body: {
                email: EMAIL,
            },
        }).as('saveApi');
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=fullName]').type('John Doe');
        cy.get('input[name=businessName]').type('Business ABC');
        cy.get('input[name=email]').type(EMAIL);
        cy.get('input[name=phone]').type('1234 5678 90');
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.get('input[name=addressSearch]').type('123 ABC');
        cy.getCy(`suggestion-${PLACE_ID}`).click();

        cy.contains('div', 'Company Details').click();
        cy.get('input[name=abn]').type('1234');
        cy.get('input[name=acn]').type('5678');
        cy.get('input[name=webUrl]').type('cabinetry.online');

        cy.contains('button', 'Submit').click();
        cy.wait('@saveApi').its('request.body').should('include', {
            name: 'John Doe',
            email: EMAIL,
            phone: '1234 5678 90',
            customer_address: '123 ABC Street',
            customer_address2: '',
            customer_suburb: 'Melbourne',
            customer_country_state: 7,
            customer_postcode: '3000',
            country: 1,
            abn: '1234',
            acn: '5678',
            web_url: 'cabinetry.online',
            business_name: 'Business ABC',
            manufacturerId: 6,
            signup_source: 3,
        });
    });

    it('should display the error from API upon submission failure', () => {
        const PLACE_ID = '2001';
        const EMAIL = '<EMAIL>';
        cy.addressFixture('getPredictions', {
            queryParams: {input: '123 ABC', country: '1'},
        });
        cy.addressFixture('getPredictionDetail', {
            queryParams: {place_id: PLACE_ID},
        });
        cy.intercept('POST', '/api/account/register_customer', {
            body: {
                error: 'Error from API',
            },
            statusCode: 500,
        }).as('saveApi');
        cy.mount(
            <MockApp redux router>
                <RegisterForm />
            </MockApp>
        );
        cy.get('input[name=fullName]').type('John Doe');
        cy.get('input[name=businessName]').type('Business ABC');
        cy.get('input[name=email]').type(EMAIL);
        cy.get('input[name=phone]').type('1234 5678 90');
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.get('input[name=addressSearch]').type('123 ABC');
        cy.getCy(`suggestion-${PLACE_ID}`).click();

        cy.contains('button', 'Submit').click();

        cy.contains('div[role=alert]', 'Error from API').should('exist');
    });
});
