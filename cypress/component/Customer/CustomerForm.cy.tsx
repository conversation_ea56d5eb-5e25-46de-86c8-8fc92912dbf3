import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import AddEditCustomer from 'components/manufacturer/Customer/component/AddEditCustomer';
describe('Customer Form', () => {
    before(() => {
        cy.userFixture();
        cy.brandingFixture();
        cy.adminManufacturersFixture({
            page_size: '500',
            sort_orders: 'name ASC',
            filter_groups: '',
        });
        cy.viewport(1280, 720);
    });

    beforeEach(() => {
        window.isAdmin = 1;
    });

    afterEach(() => {
        // clean up location state
        cy.get('.nav-link:eq(0)').click();
    });

    it('should display the Add Customer Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.getCy('customer-header')
            .should('exist')
            .and('have.text', 'Add Customer');
    });

    it('should display the Nav Bar', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(0)').should('exist').and('have.text', 'Details');
        cy.get('.nav-link:eq(1)').should('exist').and('have.text', 'Address');
        cy.get('.nav-link:eq(2)').should('exist').and('have.text', 'Defaults');
    });

    it('should display the Customer Fields', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('#manufacturer_id')
            .should('exist')
            .and('have.text', 'Select Manufacturer');
        cy.getCy('name').should('exist');
        cy.getCy('business_name').should('exist');
        cy.getCy('email').should('exist');
        cy.getCy('cc_email').should('exist');
        cy.getCy('phone').should('exist');
        cy.getCy('mobile').should('exist');
        cy.getCy('abn').should('exist');
        cy.getCy('acn').should('exist');
        cy.getCy('web_url').should('exist');
        cy.getCy('checkbox-is_beta').should('exist');
        cy.getCy('checkbox-is_edit_info_text_allowed').should('exist');
    });

    it('should NOT display the Manufacturer Field and Edit Info Field if NOT Admin user', () => {
        window.isAdmin = 0;
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('#manufacturer_id').should('not.exist');
        cy.getCy('checkbox-is_edit_info_text_allowed').should('not.exist');
    });

    it('should display the Address Fields when Address Nav is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(1)').click();
        cy.get('#address_search')
            .should('exist')
            .and(
                'have.text',
                'Search an address to auto-fill the details below'
            );
        cy.getCy('contact_street').should('exist');
        cy.getCy('contact_street2').should('exist');
        cy.getCy('contact_suburb').should('exist');
        cy.getCy('contact_postcode').should('exist');
        cy.get('#address_state_region').should('exist');
    });

    it('should display the Defaults Fields when Defaults Nav is clicked', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(2)').click();
        cy.get('#enable_assembly').should('exist');
        cy.get('#enable_variation').should('exist');
        cy.getCy('customer_discount').should('exist');
        cy.getCy('min_charge').should('exist');
        cy.getCy('minimum_freight_amount').should('exist');
    });

    it('should display the Override Fields when user is Admin', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(2)').click();
        cy.getCy('percentage_charged').should('exist');
        cy.getCy('percentage_charged_sundries').should('exist');
        cy.getCy('percentage_charged_benchtops').should('exist');
    });

    it('should NOT display the Override Fields when user is NOT Admin', () => {
        window.isAdmin = 0;
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(2)').click();
        cy.getCy('percentage_charged').should('not.exist');
        cy.getCy('percentage_charged_sundries').should('not.exist');
        cy.getCy('percentage_charged_benchtops').should('not.exist');
    });

    it('should display the Cancel and Save buttons', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.getCy('save_changes')
            .should('exist')
            .and('have.text', 'Save changes');
        cy.getCy('cancel').should('exist').and('have.text', 'Cancel');
    });

    it('should display Customer Label field once manufacturer is selected', () => {
        cy.manufacturerDefaultsFixture('getManufacturerDefaultsByID', {
            queryParams: {id: '186'},
        });
        cy.manufacturerCountryFixture('186');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(0)').click();
        cy.get('#manufacturer_id').click();
        cy.get('#manufacturer_id')
            .contains(
                '[role="option"]',
                'Your Business Name (Cabinetry.Online NZ)'
            )
            .click();
        cy.get('#customer_label').should('exist');
    });

    it('should display Region field once NZ manufacturer is selected', () => {
        cy.manufacturerDefaultsFixture('getManufacturerDefaultsByID', {
            queryParams: {id: '186'},
        });
        cy.manufacturerCountryFixture('186');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(0)').click();
        cy.get('#manufacturer_id').click();
        cy.get('#manufacturer_id')
            .contains(
                '[role="option"]',
                'Your Business Name (Cabinetry.Online NZ)'
            )
            .click();
        cy.get('.nav-link:eq(1)').click();
        cy.get('#address_state_region').should('have.text', 'Select Region');
    });

    it('should display Depot field once manufacturer is selected and depot is enabled', () => {
        cy.manufacturerDefaultsFixture('getManufacturerDefaultsByID', {
            queryParams: {id: '186'},
        });
        cy.manufacturerCountryFixture('186');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(0)').click();
        cy.get('#manufacturer_id').click();
        cy.get('#manufacturer_id')
            .contains(
                '[role="option"]',
                'Your Business Name (Cabinetry.Online NZ)'
            )
            .click();
        cy.get('.nav-link:eq(2)').click();
        cy.get('#depot_id').should('exist');
    });

    it('should prepopulate the defaults once manufacturer is selected', () => {
        cy.manufacturerDefaultsFixture('getManufacturerDefaultsByID', {
            queryParams: {id: '186'},
        });
        cy.manufacturerCountryFixture('186');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.get('.nav-link:eq(0)').click();
        cy.get('#manufacturer_id').click();
        cy.get('#manufacturer_id')
            .contains(
                '[role="option"]',
                'Your Business Name (Cabinetry.Online NZ)'
            )
            .click();
        cy.get('.nav-link:eq(2)').click();
        cy.get('#depot_id').should('have.text', 'Auckland');
        cy.getCy('customer_discount').should('have.value', '40');
        cy.getCy('min_charge').should('have.value', '100');
        cy.getCy('minimum_freight_amount').should('have.value', '100');
    });

    it('should display the Ban Status in the header when editing', () => {
        window.isSuperAdmin = 1;
        cy.customerFixture('200532');
        cy.manufacturerDefaultsFixture('getManufacturerDefaultsByID', {
            queryParams: {id: '186'},
        });
        cy.manufacturerCountryFixture('186');
        cy.mount(
            <MockManufacturerApp
                redux
                notifications
                router
                path="/customerForm.php"
                route={`/customerForm.php?customerId=200532`}>
                <AddEditCustomer />
            </MockManufacturerApp>
        );
        cy.getCy('customer-header')
            .should('exist')
            .and('contain.text', 'This account is banned');
    });
});
