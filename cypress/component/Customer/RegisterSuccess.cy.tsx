import React from 'react';
import {MockApp} from '../MockApp';
import {Outlet} from 'react-router-dom';
import {RegisterForm, RegisterSuccess} from 'components';

// Simplified route configuration for the purpose of the test
const testRoutes = [
    {path: 'register', element: <RegisterForm />},
    {path: 'register-success', element: <RegisterSuccess />},
];

describe('RegisterForm', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.userFixture();
    });

    it('should render success page when form is submitted successfully', () => {
        const PLACE_ID = '2001';
        const EMAIL = '<EMAIL>';
        const responseBody = {
            email: EMAIL,
            manufacturer: {
                email: '<EMAIL>',
                phone: '*********',
            },
        };
        cy.addressFixture('getPredictions', {
            queryParams: {input: '123 ABC', country: '1'},
        });
        cy.addressFixture('getPredictionDetail', {
            queryParams: {place_id: PLACE_ID},
        });
        cy.intercept('POST', '/api/account/register_customer', {
            body: responseBody,
        }).as('saveApi');

        cy.mount(
            <MockApp
                redux
                router
                path="/v2/*"
                route="/v2/register"
                childRoutes={testRoutes}>
                <Outlet />
            </MockApp>
        );
        cy.get('input[name=fullName]').type('John Doe');
        cy.get('input[name=businessName]').type('Business ABC');
        cy.get('input[name=email]').type(EMAIL);
        cy.get('input[name=phone]').type('1234 5678 90');
        cy.getCy('country').click();
        cy.getCy('country-dropdown-item-australia').click();
        cy.get('input[name=addressSearch]').type('123 ABC');
        cy.getCy(`suggestion-${PLACE_ID}`).click();

        cy.contains('div', 'Company Details').click();
        cy.get('input[name=abn]').type('1234');
        cy.get('input[name=acn]').type('5678');
        cy.get('input[name=webUrl]').type('cabinetry.online');

        cy.contains('button', 'Submit').click();

        cy.getCy('success-header').should(
            'have.text',
            'Your account was successfully created!'
        );

        cy.getCy('success-message').should(
            'have.text',
            `An email with an activation link was sent to ${responseBody.email}. If you haven't received this within the next few minutes, please check your spam folder or contact us via ${responseBody.manufacturer.email} or ${responseBody.manufacturer.phone}.`
        );

        cy.contains('a', responseBody.manufacturer.email).should(
            'have.attr',
            'href',
            `mailto:${responseBody.manufacturer.email}`
        );
        cy.contains('a', responseBody.manufacturer.phone).should(
            'have.attr',
            'href',
            `tel:${responseBody.manufacturer.phone}`
        );
        cy.contains('a', 'Go to Login').should(
            'have.attr',
            'href',
            `/v2/login`
        );
    });
});
