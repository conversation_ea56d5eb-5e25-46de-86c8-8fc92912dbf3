import {NotificationProvider} from 'contexts';
import React from 'react';
import {Provider} from 'react-redux';
import {BrowserRouter, MemoryRouter, Route, Routes} from 'react-router-dom';
import store from 'store/dashboard';
import {ThemeProvider} from 'styled-components';
import {baseTheme} from 'theme';

interface MockAppProps {
    children: JSX.Element;
    redux?: boolean;
    notifications?: boolean;
    theme?: boolean;
    router?: boolean;
    route?: string;
    path?: string;
}

export const MockManufacturerApp = ({
    children,
    redux = false,
    notifications = false,
    theme = true,
    router = false,
    route,
    path,
}: MockAppProps) => {
    let output = children;

    if (notifications) {
        output = <NotificationProvider>{output}</NotificationProvider>;
    }
    if (redux) {
        output = <Provider store={store}>{output}</Provider>;
    }
    if (router) {
        output =
            !!path && !!route ? (
                <MemoryRouter initialEntries={[route]}>
                    <Routes>
                        <Route path={path} element={output} />
                    </Routes>
                </MemoryRouter>
            ) : (
                <BrowserRouter>{output}</BrowserRouter>
            );
    }
    if (theme) {
        output = <ThemeProvider theme={baseTheme}>{output}</ThemeProvider>;
    }

    return output;
};
