import React from 'react';
import {MockApp} from '../MockApp';
import {Header} from 'shared';
describe('Header Component', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.configurationFixture().as('Configuration Api Request');
    });

    it('should render manufacturer logo', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Header
                    visible={true}
                    userProfile={{manufacturerName: 'Man 1'}}
                />
            </MockApp>
        );

        cy.getCy('header-logo')
            .should('be.visible')
            .and('have.attr', 'alt', 'Man 1')
            .and('have.attr', 'title', 'Man 1');
    });

    it('should render homepage link for large devices', () => {
        cy.viewport(1280, 720);
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Header
                    visible={true}
                    userProfile={{manufacturerName: 'Man 1'}}
                />
            </MockApp>
        );
        cy.getCy('header-home-link').should('be.visible');
    });

    it('should not display homepage link in the header for small devices', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Header
                    visible={true}
                    userProfile={{manufacturerName: 'Man 1'}}
                />
            </MockApp>
        );
        cy.getCy('header-home-link').should('not.exist');
    });
});
