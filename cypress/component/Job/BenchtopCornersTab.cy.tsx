import React from 'react';
import {<PERSON>ckApp} from '../MockApp';
import {ApplicationContext, JobContext} from 'contexts';
import {Benchtop} from 'components/customer/BTM/Benchtop';
import {store} from 'store/customer';
import {BenchtopType} from 'components/customer/BTM/entity/BenchtopType';
import {materialTypeSet, typeSet} from 'components/customer/BTM/store/btmSlice';
import {BenchtopMaterialType} from 'components/customer/BTM/entity/BenchtopMaterialType';
import 'assets/scss/app.scss';

describe('Benchtop Corners Tab', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.userFixture();
        cy.benchtopFixture('getShapes');
        cy.benchtopFixture('getJoins');
        cy.benchtopFixture('getTypes', {
            queryParams: {
                related_includes: 'image',
                filter_groups: '((materials.changes.is_hidden:equals:0))',
            },
        });
        cy.benchtopFixture('getMaterials', {
            queryParams: {
                related_includes: 'image',
                filter_groups: '((materials.changes.is_hidden:equals:0))',
            },
        });
        cy.benchtopFixture('getSubstrateThickness', {
            queryParams: {
                type_id: '1',
            },
        });
        cy.benchtopFixture('getMaterials', {
            queryParams: {
                current_page: '1',
                page_size: '1',
                filter_groups:
                    '((type_id:equals:1)) AND ((thickness:equals:33))',
                related_includes:
                    'image,type,brand,finish,substrate,formfactors',
                sort_orders: 'name ASC',
            },
        });
        cy.benchtopFixture('getMaterials', {
            queryParams: {
                current_page: '1',
                page_size: '35',
                filter_groups:
                    '((type_id:equals:1)) AND ((thickness:equals:33))',
                related_includes:
                    'image,type,brand,finish,substrate,formfactors',
                sort_orders: 'name ASC',
            },
        });
        cy.benchtopFixture('getEdgeProfiles', {
            queryParams: {
                filter_groups: '((material.id:equals:1))',
            },
        });
        cy.benchtopFixture('getSales', {
            queryParams: {
                related_includes:
                    'material.image,variation,shape,type,material,options,material.brand,material.finish,material.substrate,material.formfactors',
                filter_groups: '((job_id:equals:1))',
            },
        });
        cy.fixture('benchtop/shapes.json').then(
            ({items}: {items: BenchtopType[]}) => {
                store.dispatch(typeSet(items[1], true));
            }
        );
        cy.fixture('benchtop/types.json').then(
            ({items}: {items: BenchtopMaterialType[]}) => {
                store.dispatch(materialTypeSet(items[0]));
            }
        );
    });

    it('should display the Corners Tab if allowNotchesAndClips is true', () => {
        cy.mount(
            <MockApp redux router defaultState={store.getState()}>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowNotchesAndClips: true,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider value={{job: {id: 1}}}>
                        <Benchtop />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.contains('.nav-pills', 'Corners').should('exist');
    });

    it('should NOT display the Corners Tab if allowNotchesAndClips is false', () => {
        cy.mount(
            <MockApp redux router defaultState={store.getState()}>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowNotchesAndClips: false,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider value={{job: {id: 1}}}>
                        <Benchtop />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.contains('.nav-pills', 'Corners').should('not.exist');
    });

    it('should NOT display the Corners Tab if allowNotchesAndClips is false AND previous button from Overview tab is clicked', () => {
        cy.mount(
            <MockApp
                redux
                router
                defaultState={store.getState()}
                path="/v2/job/:jobId/benchtop-module/type/:benchType/materialType/:materialTypeId/*"
                route="/v2/job/1/benchtop-module/type/l_shape/materialType/1/material">
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowNotchesAndClips: false,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider value={{job: {id: 1}}}>
                        <Benchtop />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.contains('.nav-item a', 'Overview').click();
        cy.contains('button', 'Previous').click();

        cy.then(() => {
            cy.contains('.nav-pills', 'Corners').should('not.exist');
            cy.get('.nav-item .active').should('contain.text', 'Join');
        });
    });

    it('should NOT display the Corners Tab if allowNotchesAndClips is false AND next button from Join tab is clicked', () => {
        cy.mount(
            <MockApp
                redux
                router
                defaultState={store.getState()}
                path="/v2/job/:jobId/benchtop-module/type/:benchType/materialType/:materialTypeId/*"
                route="/v2/job/1/benchtop-module/type/l_shape/materialType/1/material">
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowNotchesAndClips: false,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider value={{job: {id: 1}}}>
                        <Benchtop />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.contains('.nav-item a', 'Join').should('be.visible').click();
        cy.contains('button', 'Next').should('be.visible').click();
        cy.then(() => {
            cy.contains('.nav-pills', 'Corners').should('not.exist');
            cy.get('.nav-item .active').should('contain.text', 'Overview');
        });
    });
});
