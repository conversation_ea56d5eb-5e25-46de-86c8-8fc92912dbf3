import {JobPriceExpire} from 'components';
import {JobProvider} from 'contexts';
import React from 'react';
import {MockApp} from '../MockApp';

const Job = {
    id: 130201,
    displayId: 140201,
    name: 'Test Job',
    cost: 410.***********,
    dateUpdatedDateTime: {
        date: '2020-05-05 14:11:40.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    dateDeliveryDateTime: {
        date: '2020-05-19 23:58:00.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    dateSubmittedDateTime: {
        date: '2020-05-05 13:57:42.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    dateAcceptedDateTime: {
        date: '2020-05-05 14:11:40.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    priceExpire: true,
    priceExpireData: '28/04/2025',
};

describe('JobPriceExpire', () => {
    beforeEach(() => {
        cy.userFixture();

        cy.intercept('POST', 'api/job/130201/resetPrice', {
            success: 1,
        }).as('ResetPrice');
    });

    it('should render priceExpire prompt', () => {
        cy.intercept('GET', 'api/jobs/1000', {
            success: 1,
            data: Job,
        });

        cy.mount(
            <MockApp application redux router>
                <JobProvider jobId={11000} roomId={1}>
                    <JobPriceExpire />
                </JobProvider>
            </MockApp>
        );

        cy.wait('@User Api Request');

        cy.contains('Job quote has expired').should('exist');

        cy.contains('Return to dashboard').should('exist');

        cy.contains('This process may take some time.').should('not.exist');

        cy.contains('Reset Price').should('exist').click();

        cy.then(() => {
            cy.contains('This process may take some time.').should('exist');
            cy.wait('@ResetPrice').its('response.statusCode').should('eq', 200);
        });
    });

    it('should not render priceExpire prompt', () => {
        cy.intercept('GET', 'api/jobs/1000', {
            success: 1,
            data: {
                ...Job,
                priceExpire: false,
            },
        });

        cy.mount(
            <MockApp application redux router>
                <JobProvider jobId={11000} roomId={1}>
                    <JobPriceExpire />
                </JobProvider>
            </MockApp>
        );

        cy.wait('@User Api Request');

        cy.contains('Job quote has expired').should('not.exist');
    });
});
