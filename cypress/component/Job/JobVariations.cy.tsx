import React from 'react';
import {MockApp} from '../MockApp';
import {JOB, ROOM} from '../../support/fixtures/jobFixture';

import {
    JobVariations,
    VariationStateProps,
} from 'components/customer/JobDashboard/JobVariations';
import {JobProvider} from 'contexts';
import JobVariationFreightCost from 'components/customer/JobDashboard/JobVariationFreightCost';
import JobPendingVariation from 'components/customer/JobDashboard/JobPendingVariation';

describe('JobVariations Component', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.jobFixture();
        cy.jobCabinetVariationFixture();
        cy.extraVariationFixture();
        cy.deliveryAddressFixture();
        cy.jobCostFixture();
        cy.depotsFixture();
        cy.viewport(1280, 720);
    });

    it('render JobVariations', () => {
        cy.fixture('job.json').then(
            (job: {
                data: {
                    accepted: boolean;
                    variationsAllAccepted: boolean;
                };
            }) => {
                job.data.accepted = true;
                job.data.variationsAllAccepted = false;
                cy.intercept('GET', /\/api\/jobs\/\d+/, job);
            }
        );

        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/dashboard"
                route={`/job/${JOB}/dashboard/`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <JobVariations />
                </JobProvider>
            </MockApp>
        );

        cy.get('div').contains('Variation Request').should('exist');

        cy.get('button').contains('Delete All Variations').should('be.visible');
        cy.get('button').contains('Edit Job').should('be.visible');
        cy.get('button').contains('Confirm Variations').should('be.visible');
    });

    it('render JobPendingVariation', () => {
        const mockProps: VariationStateProps = {
            removedBenchIds: [],
            setRemovedBenchIds: cy.stub(),
            removedJobCabinetIds: [],
            setRemovedJobCabinetIds: cy.stub(),
            removedExtraVariationIds: [],
            setRemovedExtraVariationIds: cy.stub(),
            totalVariationCost: 0,
            setTotalVariationCost: cy.stub(),
        };

        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/dashboard"
                route={`/job/${JOB}/dashboard/`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <JobPendingVariation {...mockProps} />
                </JobProvider>
            </MockApp>
        );

        // job product variations
        const productvariations = [
            {name: 'Test', price: '$100.00'},
            {name: 'Test 2', price: '$200.00'},
        ];

        cy.get('table').should('exist');
        productvariations.forEach((variation, index) => {
            cy.get(`tbody > :nth-child(${index + 1}) > :nth-child(2)`)
                .contains(variation.name)
                .should('be.visible');

            cy.get(`tbody > :nth-child(${index + 1}) > :nth-child(3)`)
                .contains(variation.price)
                .should('be.visible');
        });

        // job extra variations
        const extraVariations = [
            {name: 'A', price: '$1,000.00', removable: false},
            {name: 'B', price: '$2,000.00', removable: true},
        ];

        extraVariations.forEach((variation, index) => {
            const rowIndex = index + 4;
            cy.get(`:nth-child(${rowIndex}) > :nth-child(2)`)
                .contains(variation.name)
                .should('be.visible');

            cy.get(`:nth-child(${rowIndex}) > :nth-child(3)`)
                .contains(variation.price)
                .should('be.visible');

            if (variation.removable) {
                cy.get(
                    `:nth-child(${rowIndex}) > :nth-child(4) > button`
                ).should('exist');
            } else {
                cy.get(`:nth-child(${rowIndex}) > :nth-child(4) button`).should(
                    'not.exist'
                );
            }
        });
    });

    it('render JobVariationFreightCost', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/dashboard"
                route={`/job/${JOB}/dashboard/`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <table>
                        <tbody>
                            <JobVariationFreightCost totalVariationCost={100} />
                        </tbody>
                    </table>
                </JobProvider>
            </MockApp>
        );

        cy.getCy('edit-cancel-button').click();
        // Note: I am leaving this {force: true} here because I cannot figure out
        // what is wrong here. The element is not covered and is visible.
        // manual clicking works but this does not. other weird stuff is this has
        // 100% pass rate in chrome and firefox browser. Only fails in electron
        // intermittenly. using force is the only way out i see right now. That
        // being said, do not use force unless you really have to. try to fix the
        // underlying issue rather.
        cy.getCy('delivery-method').should('be.visible').click({force: true});
        cy.get('a').contains('Freight to address').should('be.visible').click();

        cy.contains('button', '123 Road St Charlemont')
            .should('be.visible')
            .click();
        cy.contains('123 Road St, Charlemont').should('be.visible');
        cy.contains('PH Manila, Cavite').should('be.visible');
        cy.contains('321 Street Rd, Geelong').should('be.visible');

        cy.contains('Freight').should('be.visible');
        cy.contains('$152.00').should('be.visible');
        cy.contains('TOTAL').should('be.visible');
        cy.contains('$252.00').should('be.visible');
    });
});
