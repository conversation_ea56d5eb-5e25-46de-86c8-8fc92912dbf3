import React from 'react';
import {Job} from 'components';
import {<PERSON>ckApp} from '../MockApp';
import {MemoryRouter, Route, Routes, useParams} from 'react-router-dom';

const TestDashboard = () => {
    const {jobId} = useParams();

    return <h1>Redirected to dashboard: {jobId}</h1>;
};

describe('Job Component', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.deliveryAddressFixture();
        cy.depotsFixture();
        cy.createJobFixture();
        cy.jobFixture();
        cy.viewport(1280, 720);
    });

    it('renders form - new job - pickup', () => {
        cy.mount(
            <MemoryRouter initialEntries={['/v2/job']}>
                <Routes>
                    <Route
                        path="/v2/job"
                        element={
                            <MockApp
                                redux
                                router={false}
                                application
                                job
                                notifications
                                jobId={null}
                                roomId={null}>
                                <Job />
                            </MockApp>
                        }
                    />
                    <Route
                        path="/v2/job/:jobId/room/:room/dashboard"
                        element={<TestDashboard />}
                    />
                </Routes>
            </MemoryRouter>
        );

        cy.contains('Please enter a Job name').should('not.exist');

        cy.contains('button', 'Create Job').click();
        cy.then(() => {
            cy.contains('Please enter a Job name').should('exist');
        });

        cy.get('input[name="jobName"]').type('New Job Name');
        cy.contains('button', 'Create Job').click();

        cy.contains('Redirected to dashboard').should('exist');
    });
});
