import React from 'react';
import {MockApp} from '../MockApp';
import {ApplicationContext, JobContext} from 'contexts';
import {useRequestedDeliveryDatePrompt} from 'components/customer/JobDashboard/helpers/useRequestedDeliveryDatePrompt';
import {DateTime} from '@cabinetsbycomputer/datetime';
import {RequestedDeliveryDateFrequencyEnum as RDD} from 'components/manufacturer/Preferences/entity/Preferences';

const TestComponent = () => {
    const {dialog: requestedDeliveryPrompt} = useRequestedDeliveryDatePrompt();
    return <div>{requestedDeliveryPrompt}</div>;
};

const MIN_LEAD_TIME = 1;
const EXPIRED_DATE = '2020-08-14';

describe('Requested Delivery Date Prompt', () => {
    before(() => {
        cy.viewport(1280, 720);
    });

    it('should display the Expired RDD prompt onload when date is expired and no callback function defined', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowDeliveryDateRequest: true,
                            minimumLeadTime: MIN_LEAD_TIME,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider
                        value={{
                            job: {id: 1, requestedDeliveryDate: EXPIRED_DATE},
                        }}>
                        <TestComponent />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('[role=dialog] .modal-header').should(
            'have.text',
            'Select a new Requested Delivery Date'
        );
        cy.get('[role=dialog] .modal-body').should(
            'contain.text',
            'The Requested Delivery Date for your job is no longer available, please update to continue with your order.'
        );
        cy.get('[role=dialog] input[type=text]').should('exist');
    });

    it('should only allow to choose dates after the allowed lead time', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowDeliveryDateRequest: true,
                            minimumLeadTime: MIN_LEAD_TIME,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider
                        value={{
                            job: {id: 1, requestedDeliveryDate: EXPIRED_DATE},
                        }}>
                        <TestComponent />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );

        cy.get('[role=dialog] input[type=text]').click();
        // test below is a bit britle as it's relying with library classes
        // this can be improve if we can assign identifiers to the date values
        cy.get(`.react-datepicker__day--today`).should(
            'have.attr',
            'aria-disabled',
            'true' // today is disabled as we have 1 day lead time
        );
        cy.get(`.react-datepicker__day--today`)
            .next()
            .should('have.attr', 'aria-disabled', 'false');
    });

    it('should NOT display the Expired RDD prompt onload when date is NOT expired', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowDeliveryDateRequest: true,
                            minimumLeadTime: MIN_LEAD_TIME,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider
                        value={{
                            job: {
                                id: 1,
                                requestedDeliveryDate: DateTime.now()
                                    .add({days: MIN_LEAD_TIME})
                                    .format()
                                    .toString(),
                            },
                        }}>
                        <TestComponent />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );

        cy.get('[role=dialog]').should('not.exist');
    });

    it('should NOT display the Expired RDD prompt onload when it is configured as disabled', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowDeliveryDateRequest: true,
                            minimumLeadTime: MIN_LEAD_TIME,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider
                        value={{
                            job: {
                                id: 1,
                                requestedDeliveryDateFrequency: RDD.NEVER,
                            },
                        }}>
                        <TestComponent />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );

        cy.get('[role=dialog]').should('not.exist');
    });

    it('should NOT display the Expired RDD prompt onload when prompt frequency is set to on job submit', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowDeliveryDateRequest: true,
                            minimumLeadTime: MIN_LEAD_TIME,
                            requestedDeliveryDateFrequency: RDD.JOB_SUBMISSION,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider
                        value={{
                            job: {id: 1, requestedDeliveryDate: EXPIRED_DATE},
                        }}>
                        <TestComponent />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );

        cy.get('[role=dialog]').should('not.exist');
    });

    it('should NOT display the Expired RDD prompt onload when allowDeliveryDateRequest is Falsy', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        userProfile: {
                            allowDeliveryDateRequest: false,
                            minimumLeadTime: MIN_LEAD_TIME,
                        },
                        places: {},
                    }}>
                    <JobContext.Provider
                        value={{
                            job: {id: 1, requestedDeliveryDate: EXPIRED_DATE},
                        }}>
                        <TestComponent />
                    </JobContext.Provider>
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('[role=dialog]').should('not.exist');
    });
});
