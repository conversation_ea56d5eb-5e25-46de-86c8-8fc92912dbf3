import React from 'react';
import {MockApp} from '../MockApp';
import {SupplyDefaults} from 'components/customer/Room/SupplyDefaults';
import {Formik} from 'formik';

const doNothing = () => {
    // do nothing
};
describe('Supply Defaults', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.jobFixture();
        cy.intercept('GET', '/api/customer/manufacturerHardwareOptions', {
            fixture: `hardwareOptions.json`,
        });
        cy.intercept('GET', '/api/countries', {fixture: 'countries.json'});
    });

    it('should display all cards when configs are all true', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room">
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').should('exist');
        cy.contains('.cbc-radio', 'Flat Pack').should('exist');
        cy.contains('.cbc-radio', 'Supply Hardware').should('exist');
        cy.contains('.cbc-radio', 'Drill Panels Only').should('exist');
        cy.contains('.cbc-radio', /^Adjustable Legs$/).should('exist');
        cy.contains('.cbc-radio', 'No Adjustable Legs').should('exist');
    });

    it('should not display Assembled card if isSupplyMethodVisible is false', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: false,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room">
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').should('not.exist');
        cy.contains('.cbc-radio', 'Flat Pack').should('exist');
        cy.contains('.cbc-radio', 'Supply Hardware').should('exist');
        cy.contains('.cbc-radio', 'Drill Panels Only').should('exist');
        cy.contains('.cbc-radio', /^Adjustable Legs$/).should('exist');
        cy.contains('.cbc-radio', 'No Adjustable Legs').should('exist');
    });

    it('should not display Assembled card and Supply Hardware card if isHardwareInclusionVisible is false', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: false,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room">
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').should('not.exist');
        cy.contains('.cbc-radio', 'Flat Pack').should('exist');
        cy.contains('.cbc-radio', 'Supply Hardware').should('not.exist');
        cy.contains('.cbc-radio', 'Drill Panels Only').should('exist');
        cy.contains('.cbc-radio', /^Adjustable Legs$/).should('exist');
        cy.contains('.cbc-radio', 'No Adjustable Legs').should('exist');
    });

    it('should not display Adjustable Legs card if isAdjustableLegsVisible is false', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: false,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room">
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').should('exist');
        cy.contains('.cbc-radio', 'Flat Pack').should('exist');
        cy.contains('.cbc-radio', 'Supply Hardware').should('exist');
        cy.contains('.cbc-radio', 'Drill Panels Only').should('exist');
        cy.contains('.cbc-radio', /^Adjustable Legs$/).should('not.exist');
        cy.contains('.cbc-radio', 'No Adjustable Legs').should('exist');
    });

    it('should not display Adjustable Legs cards if isAddProductAvailable is false', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 0,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room">
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').should('exist');
        cy.contains('.cbc-radio', 'Flat Pack').should('exist');
        cy.contains('.cbc-radio', 'Supply Hardware').should('exist');
        cy.contains('.cbc-radio', 'Drill Panels Only').should('exist');
        cy.contains('.cbc-radio', /^Adjustable Legs$/).should('not.exist');
        cy.contains('.cbc-radio', 'No Adjustable Legs').should('not.exist');
    });

    it('should block Assemble selection and display drill only message when drawerStyle is drill only', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room"
                defaultState={{room: {drawerStyle: {drillOnly: true}}}}>
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').click();
        cy.contains('div[role=dialog]', 'Option Unavailable').should('exist');
    });

    it('should block Assemble selection and display drill only message when hingeStyle is drill only', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room"
                defaultState={{
                    room: {hingeStyle: {name: 'Test (Drill Only)'}},
                }}>
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Assembled').click();
        cy.contains('div[role=dialog]', 'Option Unavailable').should('exist');
    });

    it('should block Supply Hardware selection and display drill only message when drawerStyle is drill only', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room"
                defaultState={{room: {drawerStyle: {drillOnly: true}}}}>
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Supply Hardware').click();
        cy.contains('div[role=dialog]', 'Option Unavailable').should('exist');
    });

    it('should block Supply Hardware selection and display drill only message when hingeStyle is drill only', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room"
                defaultState={{room: {drawerStyle: {drillOnly: true}}}}>
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.contains('.cbc-radio', 'Supply Hardware').click();
        cy.contains('div[role=dialog]', 'Option Unavailable').should('exist');
    });

    it('should disable Display Panels Only selection when supply method is Assembled', () => {
        cy.intercept('GET', '/api/user', {
            body: {
                user: {
                    show_pricing: true,
                    isAddProductAvailable: 1,
                    isSupplyMethodVisible: true,
                    isHardwareInclusionVisible: true,
                    isAdjustableLegsVisible: true,
                },
            },
        });
        cy.mount(
            <MockApp
                redux
                router
                application
                job
                path="/v2/job/:jobId/room"
                route="/v2/job/300367/room">
                <Formik initialValues={{}} onSubmit={doNothing}>
                    <SupplyDefaults title={''} eventKey={0} />
                </Formik>
            </MockApp>
        );
        cy.get('#boolean_supplyMethod_yes').click();
        cy.get('#boolean_supplyMethod_yes').should('be.checked');
        cy.once('fail', (err) => {
            // verify element was click - should throw an error since element is not clickable
            expect(err.message).to.include(
                '`cy.click()` failed because this element is `disabled`'
            );
        });
        // forced the timeout to 0 so you don't need to wait the retry
        cy.get('#boolean_hardwareInclusions_no').click({timeout: 0});
        cy.get('#boolean_hardwareInclusions_yes').should('be.checked');
        cy.get('#boolean_hardwareInclusions_no').should('not.be.checked');
    });
});
