import React from 'react';
import {MockApp} from '../MockApp';
import {Jobs} from 'components';

describe('JobStatusFilter.cy.tsx', () => {
    beforeEach(() => {
        cy.userFixture();

        cy.jobStatisticsFixture();
        cy.jobStatusFixture('all');
        cy.jobStatusFixture('gcprocessing');
        cy.jobStatusFixture('gcjobcompleted');
        cy.viewport(1280, 720);
    });

    it('Render component initially with filter', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                route="/v2/jobs/all"
                path="/v2/jobs/:status"
                application={true}
                notifications={true}>
                <Jobs />
            </MockApp>
        );

        cy.contains('button', 'Status');
        cy.contains('button', 'Sort By');
        cy.contains('button', 'Order By');
        cy.get('.btn-job-list').should('contain', 'Create a New Job');
        cy.get('table tbody tr').its('length').should('be.gte', 2);

        // Check dates, cost, and id format
        cy.get('table tbody')
            .find('tr')
            .first()
            .within(() => {
                // Date Entered
                cy.get('td')
                    .contains('23rd of April 2024 at 10:31 AM')
                    .should('exist');
                // Last Updated
                cy.get('td')
                    .contains('24th of April 2024 at 11:40 AM')
                    .should('exist');
                // ID
                cy.get('td').contains('359381').should('exist');
            });

        // Click Status
        cy.get('#status-dropdown').click({force: true});

        cy.get('.dropdown-menu').contains('Processing').click({force: true});
        cy.get('table tbody tr td').contains('Processing').should('exist');

        cy.get('.dropdown-menu').contains('Job Completed').click({force: true});
        cy.get('table tbody tr td').contains('Job Completed').should('exist');
    });

    it('should be able to update sort fields', () => {
        cy.jobStatusFixture('all', {
            page: '1',
            limit: '15',
            orderBy: 'desc',
            sortBy: '3',
        }).as('sortBy');
        cy.jobStatusFixture('all', {
            page: '1',
            limit: '15',
            orderBy: 'asc',
            sortBy: '3',
        }).as('orderBy');
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                route="/v2/jobs/all"
                path="/v2/jobs/:status"
                application={true}
                notifications={true}>
                <Jobs />
            </MockApp>
        );

        cy.get('#sort-by-dropdown').click();
        cy.contains('.dropdown-item', 'Status').click();
        cy.get('#sort-by-dropdown').should('have.text', 'Status');
        cy.wait('@sortBy').its(`request.query.sortBy`).should('equal', '3');

        cy.get('#order-by-dropdown').click();
        cy.contains('.dropdown-item', 'Asc').click();
        cy.get('#order-by-dropdown').should('have.text', 'Asc');
        cy.wait('@orderBy').its(`request.query.orderBy`).should('equal', 'asc');
    });
});
