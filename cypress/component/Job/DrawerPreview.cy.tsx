import React from 'react';
import {MockApp} from '../MockApp';
import {DrawerPreview} from 'components/customer/Product';
import {PreviewProvider} from 'components/customer/Product/Preview/PreviewContext';
import {MaterialOptions} from 'shared/types';

const mockFunction = (): MaterialOptions => {
    return {};
};

// mock productDataStore so we can atleast load the preview containers
const productDataStore = {
    current: {
        template_3d: [
            {
                attributes: {
                    variables: JSON.stringify({
                        fields: [],
                    }),
                },
            },
        ],
    },
};

const product = {
    drawers: [
        {id: -1, drawer_face_height: 362, drawer_style: 1, drawer_type: 19},
        {id: -1, drawer_face_height: 362, drawer_style: 1, drawer_type: 19},
    ],
};

describe('Drawer Preview', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.jobFixture();
        cy.intercept('GET', 'api/cabinets/productStyles', {
            fixture: 'productStyles.json',
        });
    });

    it('should be able to display the 3D Drawer Preview when has3DPreview is true', () => {
        cy.mount(
            <MockApp redux router application job notifications>
                <PreviewProvider
                    currentTab={2}
                    product={product}
                    productDataStore={productDataStore}
                    getMaterialOptions={mockFunction}>
                    <DrawerPreview has3DPreview={true} />
                </PreviewProvider>
            </MockApp>
        );
        cy.contains('button', 'Switch to Drawer Face View').should('exist');
        cy.get('#preview_3d').should('not.be.hidden');
        cy.get('#canvas-container-drawer').should('be.hidden');
    });

    it('should be able to switch to Drawer Face Preview when has3DPreview is true', () => {
        cy.mount(
            <MockApp redux router application job notifications>
                <PreviewProvider
                    currentTab={2}
                    product={product}
                    productDataStore={productDataStore}
                    getMaterialOptions={mockFunction}>
                    <DrawerPreview has3DPreview={true} />
                </PreviewProvider>
            </MockApp>
        );
        cy.contains('button', 'Switch to Drawer Face View').click();
        cy.get('#preview_3d').should('be.hidden');
        cy.then(() => {
            cy.get('#canvas-container-drawer').should('not.be.hidden');
        });
    });

    it('should NOT be able to switch to 3D Drawer Preview when has3DPreview is false', () => {
        cy.mount(
            <MockApp redux router application job notifications>
                <PreviewProvider
                    currentTab={2}
                    product={product}
                    productDataStore={productDataStore}
                    getMaterialOptions={mockFunction}>
                    <DrawerPreview has3DPreview={false} />
                </PreviewProvider>
            </MockApp>
        );
        cy.contains('button', 'Switch to 3D View').should('not.exist');
        cy.get('#preview_3d').should('not.exist');
        cy.get('#canvas-container-drawer').should('not.be.hidden');
    });
});
