import React from 'react';
import {MockManufacturerApp} from '../../MockManufacturerApp';
import Logout from 'components/admin/Header/Logout';

describe('Logout Component', () => {
    beforeEach(() => {
        cy.setupBranding();
    });

    it('should render Logout button and clicking on it should redirect user', () => {
        const logoutStub = cy.stub();
        cy.mount(
            <MockManufacturerApp>
                <Logout logoutHandler={logoutStub} />
            </MockManufacturerApp>
        );
        cy.contains('Logout').should('exist');

        expect(logoutStub).to.not.have.been.called;
        cy.getCy('logout-button').click();
        cy.then(() => {
            expect(logoutStub).to.be.called;
        });
    });
});
