import React from 'react';
import {MockManufacturerApp} from '../../MockManufacturerApp';
import Menu from 'components/admin/Header/Menu';
import {HeaderContext} from 'components/admin/Header';
import {Role} from 'components/admin/Header/entity/Role';

let test = 1;
let contextTest = 1;

/**
 * Increments the value of `test` by 1.
 * This function is typically used as a callback for click events.
 */
function onClick() {
    test++;
}

/**
 * Increments the value of `contextTest` by 1.
 * This function is typically used as a callback for context menu events.
 */
function onContextMenu() {
    contextTest++;
}

describe('Menu Component', () => {
    beforeEach(() => {
        cy.setupBranding();
        cy.intercept('GET', /\/v2\/dist-compat\/header-images\/.*\.svg$/, {
            fixture: 'Blank.svg',
        });
    });

    it('Renders menu correctly', () => {
        const menu = {
            id: 'Jobs',
            name: 'Job<PERSON>',
            url: '/jobList.php',
            icon: 'Job.svg',
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.ADMIN,
                        shortcutMenu: {order: 1, id: '1', name: 'test'},
                    }}>
                    <Menu
                        menu={menu}
                        onClick={onClick}
                        onContextMenu={onContextMenu}
                    />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        // check if onClick event is triggered
        expect(test).to.equal(1);
        cy.contains('Jobs').should('exist');
        cy.contains('Jobs').click();
        cy.then(() => {
            expect(test).to.equal(2);
        });

        // check if onContextMenu event is triggered
        expect(contextTest).to.equal(1);
        cy.contains('Jobs').trigger('contextmenu');
        cy.then(() => {
            expect(contextTest).to.equal(2);
        });
    });

    it('Renders alternate name if manufacturer and alternate name is available', () => {
        const menu = {
            id: 'Jobs',
            name: 'Jobs',
            url: '/jobList.php',
            icon: 'Job.svg',
            alternateName: 'Test 1',
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.MANUFACTURER,
                    }}>
                    <Menu menu={menu} />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.contains('Test 1').should('exist');
    });

    it('Renders name if admin but alternate name is available', () => {
        const menu = {
            id: 'Jobs',
            name: 'Jobs',
            url: '/jobList.php',
            icon: 'Job.svg',
            alternateName: 'Test 1',
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.ADMIN,
                    }}>
                    <Menu menu={menu} />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.contains('Jobs').should('exist');
    });

    it('Renders menu based on role', () => {
        const menu = {
            id: 'Jobs',
            name: 'Jobs',
            url: '/jobList.php',
            icon: 'Job.svg',
            role: [Role.MANUFACTURER],
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.ADMIN,
                    }}>
                    <Menu menu={menu} />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        // Check if nothing is rendered
        cy.get('body').then(($body) => {
            expect($body.text().trim()).to.equal('');
        });
    });

    it('Renders menu based on role', () => {
        const menu = {
            id: 'Jobs',
            name: 'Jobs',
            url: '/jobList.php',
            icon: 'Job.svg',
            role: [Role.ADMIN],
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.ADMIN,
                    }}>
                    <Menu menu={menu} />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.contains('Jobs').should('exist');
    });

    it('Renders menu with submenu', () => {
        const menu = {
            id: 'Jobs',
            name: 'Jobs',
            icon: 'Job.svg',
            submenu: [
                {
                    id: 'Add a Customer',
                    name: 'Add a Customer',
                    url: '/customerForm.php',
                },
                {
                    id: 'Customers',
                    name: 'Customers',
                    url: '/customerList.php',
                },
            ],
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.ADMIN,
                    }}>
                    <Menu menu={menu} />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        // checking submenu is only visible after clicking on the menu
        cy.contains('Add a Customer').should('exist').should('not.be.visible');
        cy.contains('Customers').should('exist').should('not.be.visible');

        cy.contains('Jobs').should('exist');
        cy.contains('Jobs').click();
        cy.then(() => {
            cy.contains('Add a Customer').should('exist');
            cy.contains('Customers').should('exist');
        });
    });
});
