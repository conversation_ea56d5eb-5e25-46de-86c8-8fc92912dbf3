import React from 'react';
import {MockManufacturerApp} from '../../MockManufacturerApp';
import {HeaderContext} from 'components/admin/Header';
import {Role} from 'components/admin/Header/entity/Role';
import {Menu} from 'components/admin/Header/entity/Menu';
import {Shortcut} from 'components/admin/Header/Shortcut';
import BaseConfig from 'config/base-config.json';

describe('Shortcut Component', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.setupBranding();

        cy.intercept('GET', /\/v2\/dist-compat\/header-images\/.*\.svg$/, {
            fixture: 'Blank.svg',
        });
    });

    it('Renders shortcut and context menu', () => {
        const menu: Menu = {
            order: 2,
            id: 'Shortbut-1',
            name: 'Menu 1',
            url: '#',
            icon: 'Shortcuts-1.svg',
        };
        let shortcutMenu: Menu;
        const setShortcutMenu = (menu: Menu) => {
            shortcutMenu = menu;
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.MANUFACTURER,
                        shortcutsFetched: true,
                        config: BaseConfig,
                        setShortcutMenu,
                    }}>
                    <Shortcut menu={menu} />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.contains('Edit').should('exist').should('not.be.visible');
        cy.contains('Open Link in New Tab')
            .should('exist')
            .should('not.be.visible');
        cy.contains('Open Link in New Window')
            .should('exist')
            .should('not.be.visible');
        cy.contains('Copy link').should('exist').should('not.be.visible');

        cy.get('a').trigger('contextmenu');
        cy.then(() => {
            cy.contains('Edit').should('be.visible');
            cy.contains('Open Link in New Tab').should('be.visible');
            cy.contains('Open Link in New Window').should('be.visible');
            cy.contains('Copy link').should('be.visible');
        });

        expect(shortcutMenu).to.be.undefined;
        cy.contains('Edit').click();
        cy.then(() => {
            expect(shortcutMenu.order).to.equal(2);
        });
    });
});
