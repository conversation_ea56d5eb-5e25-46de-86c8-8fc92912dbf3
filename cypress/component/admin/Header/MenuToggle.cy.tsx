import React from 'react';
import {MockManufacturerApp} from '../../MockManufacturerApp';
import MenuToggle from 'components/admin/Header/MenuToggle';

/**
 * This function is a placeholder for setting the navigation visibility.
 * Currently, it doesn't perform any operations.
 */
function setShowNavigation() {
    return;
}

let test = true;

/**
 * Updates the value of `test` by applying a callback function to it.
 *
 * @param {function} callback - A callback function that takes the
 * current value of `test` as a parameter and returns the new value.
 */
function updateTest(callback: (test: boolean) => boolean) {
    test = callback(test);
}

describe('MenuToggle Component', () => {
    beforeEach(() => {
        cy.viewport('iphone-8');
        cy.setupBranding();
    });

    it('Renders the menu in mobile view', () => {
        cy.mount(
            <MockManufacturerApp>
                <MenuToggle setShowNavigation={setShowNavigation} />
            </MockManufacturerApp>
        );

        cy.contains('Menu').should('exist').should('be.visible');
        cy.contains('Logout').should('exist').should('be.visible');
    });

    it('Updates parent state properly', () => {
        cy.mount(
            <MockManufacturerApp>
                <MenuToggle setShowNavigation={updateTest} />
            </MockManufacturerApp>
        );

        expect(test).to.equal(true);
        cy.getCy('menu-toggle').click();
        cy.then(() => {
            expect(test).to.equal(false);
        });
    });

    it('Does not render in desktop computer', () => {
        cy.viewport(1280, 720);
        cy.mount(
            <MockManufacturerApp>
                <MenuToggle setShowNavigation={setShowNavigation} />
            </MockManufacturerApp>
        );

        cy.contains('Menu').should('exist').should('not.be.visible');
    });
});
