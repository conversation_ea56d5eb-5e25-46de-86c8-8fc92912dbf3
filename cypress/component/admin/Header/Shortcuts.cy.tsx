import React from 'react';
import {MockManufacturerApp} from '../../MockManufacturerApp';
import {Shortcuts} from 'components/admin/Header/Shortcuts';
import {HeaderContext} from 'components/admin/Header';
import {Role} from 'components/admin/Header/entity/Role';
import {Menu} from 'components/admin/Header/entity/Menu';

describe('Shortcuts Component', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.setupBranding();

        cy.intercept('GET', /\/v2\/dist-compat\/header-images\/.*\.svg$/, {
            fixture: 'Blank.svg',
        });
    });

    it('Renders shortcut menus correctly', () => {
        const menu = [
            {
                order: 1,
                id: 'Shortbut-1',
                name: 'Menu 1',
                url: '#',
                icon: 'Shortcuts-1.svg',
            },
            {
                order: 2,
                id: 'Shortbut-2',
                name: 'Menu 2',
                url: '#',
                icon: 'Shortcuts-2.svg',
            },
        ];

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.MANUFACTURER,
                        shortcutsFetched: true,
                        shortcuts: menu,
                    }}>
                    <Shortcuts />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.contains('Menu 1').should('exist');
        cy.contains('Menu 2').should('exist');
    });

    it('Renders menu correctly if only 1 shortcut is set', () => {
        const menu = [
            {
                order: 1,
                id: 'Shortbut-1',
                name: 'Menu 1',
                url: '#',
                icon: 'Shortcuts-1.svg',
            },
        ];

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.MANUFACTURER,
                        shortcutsFetched: true,
                        shortcuts: menu,
                    }}>
                    <Shortcuts />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.contains('Menu 1').should('exist');
        cy.contains('Shortcut').should('exist');
    });

    it('Renders menu correctly if no shortcut is set', () => {
        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.MANUFACTURER,
                        shortcutsFetched: true,
                    }}>
                    <Shortcuts />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.get('a:contains("Shortcut")').should('have.length', 2);
    });

    it('Does the right thing when clicked on empty shortcut', () => {
        let shortcutMenu: Menu;
        const setShortcutMenu = (menu: Menu) => {
            shortcutMenu = menu;
        };

        cy.mount(
            <MockManufacturerApp>
                <HeaderContext.Provider
                    value={{
                        role: Role.MANUFACTURER,
                        shortcutsFetched: true,
                        shortcutMenu,
                        setShortcutMenu,
                    }}>
                    <Shortcuts />
                </HeaderContext.Provider>
            </MockManufacturerApp>
        );

        cy.get('a:contains("Shortcut")').should('have.length', 2);
        cy.get('a:contains("Shortcut")').eq(0).click();
        cy.then(() => {
            expect(shortcutMenu.order).to.equal(1);
        });

        cy.get('a:contains("Shortcut")').eq(1).click();
        cy.then(() => {
            expect(shortcutMenu.order).to.equal(2);
        });
    });
});
