import React from 'react';
import {Header} from 'components/admin/Header';
import {Role} from 'components/admin/Header/entity/Role';
import {MockManufacturerApp} from '../../MockManufacturerApp';

describe('Header Component', () => {
    beforeEach(() => {
        cy.viewport(1280, 720);
        cy.setupBranding();

        cy.intercept('GET', 'api/favourites/menus', {
            items: [
                {
                    id: 9,
                    order: 1,
                    link: '/reportsPage.php',
                    name: 'Reports Page',
                },
                {
                    id: 12,
                    order: 2,
                    link: '/statementList.php',
                    name: 'Statements',
                },
            ],
            success: 1,
        }).as('User Api Request');
        cy.intercept('GET', /\/v2\/dist-compat\/header-images\/.*\.png$/, {
            fixture: 'Blank.png',
        });
        cy.intercept('GET', /\/v2\/dist-compat\/header-images\/.*\.svg$/, {
            fixture: 'Blank.svg',
        });
    });

    it('should render manufacturer header and navigation', () => {
        cy.mount(
            <MockManufacturerApp
                redux={false}
                notifications={false}
                theme={false}>
                <Header role={Role.MANUFACTURER} />
            </MockManufacturerApp>
        );

        cy.contains('Powered By').should('exist');
        cy.contains('Prices').should('exist');
        cy.contains('Tools').should('exist');
        cy.contains('Help & Support').should('exist');

        // check custom menu are loaded
        cy.contains('a', 'Reports Page').should(
            'have.attr',
            'href',
            '/reportsPage.php'
        );

        cy.contains('a', 'Statements').should(
            'have.attr',
            'href',
            '/statementList.php'
        );
    });

    it('should render admin header and navigation', () => {
        cy.mount(
            <MockManufacturerApp
                redux={false}
                notifications={false}
                theme={false}>
                <Header role={Role.ADMIN} />
            </MockManufacturerApp>
        );

        cy.contains('Powered By').should('not.exist');

        cy.contains('Prices').should('not.exist');
        cy.contains('Components').should('exist');

        cy.contains('Tools').should('not.exist');
        cy.contains('Settings').should('exist');

        cy.contains('Help & Support').should('not.exist');

        // check custom menu are loaded
        cy.contains('a', 'Reports Page').should(
            'have.attr',
            'href',
            '/reportsPage.php'
        );

        cy.contains('a', 'Statements').should(
            'have.attr',
            'href',
            '/statementList.php'
        );
    });
});
