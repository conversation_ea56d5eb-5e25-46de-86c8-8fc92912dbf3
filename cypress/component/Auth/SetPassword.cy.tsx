import React from 'react';
import {MockApp} from '../MockApp';
import SetPasswordForm from 'components/customer/Auth/SetPasswordForm';

const initialValues = {
    password: '',
    confirmPassword: '',
};

describe('SetPassword', () => {
    it('should render header title', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    headerTitle="Header Title"
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('Header Title').should('exist');
    });

    it('should render password criteria section', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    headerTitle="Header Title"
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('1 upper case letter').should('exist');
        cy.contains('1 lower case letter').should('exist');
        cy.contains('1 number').should('exist');
    });

    it('should render error message if provided', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error="ERROR 1"
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    headerTitle="Header Title"
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('ERROR 1').should('exist');
    });

    it('should render success message if provided', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message="SUCCESS"
                    isLoading={false}
                    isSuccess={false}
                    headerTitle="Header Title"
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('SUCCESS').should('exist');
    });

    it('should render user account section if provided', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    headerTitle="Header Title"
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                    userAccountSlot={<div>ACCOUNT</div>}
                />
            </MockApp>
        );

        cy.contains('ACCOUNT').should('exist');
    });

    it('should render password fields', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    headerTitle="Header Title"
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                    userAccountSlot={<div>ACCOUNT</div>}
                />
            </MockApp>
        );

        // Check if the input with placeholder 'Password' exists
        cy.get('input[placeholder="Password"]').should('exist');

        // Check if the input with placeholder 'Confirm Password' exists
        cy.get('input[placeholder="Confirm Password"]').should('exist');
    });

    it('should render submit and cancel buttons', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    submitButtonName="Submit Button"
                    cancelButtonName="Cancel Button"
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('Submit Button').should('exist');
        cy.contains('Cancel Button').should('exist');
    });

    it('should render defaults when not provided', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('Reset Password').should('exist');
        cy.contains('Update Password').should('exist');
        cy.contains('Back to Login').should('exist');
    });

    it('should disable the form once successfully submitted', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={true}
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.get('input[placeholder="Password"]').should('be.disabled');

        // Check if the input with placeholder 'Confirm Password' is disabled
        cy.get('input[placeholder="Confirm Password"]').should('be.disabled');

        // Check if the button with text 'Update Password' is disabled
        cy.contains('button', 'Update Password').should('be.disabled');
    });

    it('should disable the submit button if loading and should display a loader', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={true}
                    isSuccess={false}
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('Loading...').should('exist');
        cy.contains('Loading...').should('be.disabled');
    });

    it('should validate password if does not meet criteria', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.get('input[placeholder="Password"]').type('password');
        cy.get('input[placeholder="Password"]').blur();

        cy.get('input[placeholder="Password"]').should(
            'have.value',
            'password'
        );

        cy.contains('Please enter a valid password').should('be.visible');
    });

    it('should validate confirm password if match with the password', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('Passwords do not match').should('not.exist');

        cy.get('input[placeholder="Password"]').type('password1');
        cy.get('input[placeholder="Password"]').blur();

        cy.get('input[placeholder="Confirm Password"]').type('password2');
        cy.get('input[placeholder="Confirm Password"]').blur();

        cy.contains('Password does not match!').should('exist');
    });

    it('should validate password fields if empty', () => {
        cy.mount(
            <MockApp redux router>
                <SetPasswordForm
                    initialValues={initialValues}
                    error={null}
                    message={null}
                    isLoading={false}
                    isSuccess={false}
                    submitButtonName="Submit"
                    onSubmit={cy.stub()}
                />
            </MockApp>
        );

        cy.contains('Please enter your new password').should('not.exist');
        cy.contains('Please enter your confirmation password').should(
            'not.exist'
        );

        cy.contains('Submit').should('exist').click();

        cy.then(() => {
            cy.contains('Please enter your new password').should('exist');
            cy.contains('Please enter your confirmation password').should(
                'exist'
            );
        });
    });
});
