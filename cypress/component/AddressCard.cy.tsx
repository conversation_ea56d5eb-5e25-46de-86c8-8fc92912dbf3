import React from 'react';
import {MockApp} from './MockApp';
import {ApplicationContext} from 'contexts';
import {AddressCard} from 'components/customer/Settings/address/AddressCard';

const DEFAULT_ADDRESS = {
    id: 92511,
    name: 'My default address',
    street: '192 Cormiston Road',
    city: null,
    suburb: 'Riverside',
    state: 1,
    postcode: '7250',
    is_default: true,
};

const OTHER_ADDRESS = {
    id: 92512,
    name: 'My other address',
    street: ['100 Lonely Road', 'But'],
    city: null,
    suburb: 'Happyland',
    state: 2,
    postcode: '1234',
    is_default: false,
};

const COUNTRIES = {
    Australia: [
        {
            id: 1,
            name: 'ACT',
        },
        {
            id: 2,
            name: 'NSW',
        },
    ],
    Unknown: [
        {
            id: 999,
            name: 'XYZ',
        },
    ],
};

describe('AddressCard', () => {
    it('should display Default header on default address', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={DEFAULT_ADDRESS} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-header').should('exist').and('contain.text', 'Default');
    });

    it('should NOT display Default header on non default address', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={OTHER_ADDRESS} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-header').should('not.exist');
    });

    it('should only display the edit and delete button on default address', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={DEFAULT_ADDRESS} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('img[alt="Options-Delete.svg"]').should('exist');
        cy.get('img[alt="Options-Edit.svg"]').should('exist');
        cy.get('img[alt="Delivery-Default.svg"]').should('not.exist');
    });

    it('should display default, edit and delete button on non default address', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={OTHER_ADDRESS} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('img[alt="Options-Delete.svg"]').should('exist');
        cy.get('img[alt="Options-Edit.svg"]').should('exist');
        cy.get('img[alt="Delivery-Default.svg"]').should('exist');
    });

    it('should NOT display buttons when noButton is true', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={OTHER_ADDRESS} noButton />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('img[alt="Options-Delete.svg"]').should('not.exist');
        cy.get('img[alt="Options-Edit.svg"]').should('not.exist');
        cy.get('img[alt="Delivery-Default.svg"]').should('not.exist');
    });

    it('should accept string only for street address', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={DEFAULT_ADDRESS} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-title')
            .should('exist')
            .and('contain.text', DEFAULT_ADDRESS.name);
        cy.get('.card-text')
            .should('exist')
            .and('contain.text', '192 Cormiston Road, Riverside, 7250');
    });

    it('should accept string array for street address', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={OTHER_ADDRESS} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-title')
            .should('exist')
            .and('contain.text', OTHER_ADDRESS.name);
        cy.get('.card-text')
            .should('exist')
            .and('contain.text', '100 Lonely Road, But, Happyland, 1234');
    });

    it('should display the city when available', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {},
                    }}>
                    <AddressCard address={{...OTHER_ADDRESS, city: 'Super'}} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-text')
            .should('exist')
            .and(
                'contain.text',
                '100 Lonely Road, But, Super, Happyland, 1234'
            );
    });

    it('should display the State based on the state id', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {
                            Countries: COUNTRIES,
                        },
                    }}>
                    <AddressCard address={{...OTHER_ADDRESS, state: 999}} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-text')
            .should('exist')
            .and('contain.text', '100 Lonely Road, But, Happyland, XYZ, 1234');
    });

    it('should NOT display the State if not found', () => {
        cy.mount(
            <MockApp redux router>
                <ApplicationContext.Provider
                    value={{
                        places: {
                            Countries: COUNTRIES,
                        },
                    }}>
                    <AddressCard address={{...OTHER_ADDRESS, state: 1000}} />
                </ApplicationContext.Provider>
            </MockApp>
        );
        cy.get('.card-text')
            .should('exist')
            .and('contain.text', '100 Lonely Road, But, Happyland, 1234');
    });
});
