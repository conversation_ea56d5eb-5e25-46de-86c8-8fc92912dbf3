import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import Preferences from 'components/manufacturer/Preferences';
import assertSaving from '../shared/assertSaving';

describe('Custom Preferences', () => {
    before(() => {
        cy.brandingFixture();
        cy.preferencesFixture('getPreferences');
        cy.viewport(1280, 720);
    });

    it('should display the Custom Preferences Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        cy.contains('Custom Preferences');
    });

    it('should be able to update Permit ordering of glass cut outs setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'permitGlassSubPanel', true);
    });

    it('should be able to update Permit ordering of notches and clips setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'allowNotchesAndClips', 1);
    });

    it('should be able to update Permit coupons setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'permitCoupons', 1);
    });

    it('should be able to update Permit customer self-registration setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'permitSelfRegistration', 1);
    });

    it('should be able to update Permit file uploads setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'permitFileUpload', 1);
    });

    it('should be able to update Door Hang Symbol Reversal setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'doorHangReversal', 1);
    });

    it('should be able to update Display Estimated Completion setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'displayEstimatedCompletion', 1);
    });

    it('should be able to update Max Age Job Price setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('text', 'maxAgeJobPrice', '90');
    });

    it('should be able to update Create Job Landing Page setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('select', 'createJobLandingPage', '7');
    });

    it('should be able to update Email Notification Type setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('select', 'defaultEmailNotificationType', '2');
    });

    it('should be able to update Allow Customer Login setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'allowDirectLogin', true);
    });
});
