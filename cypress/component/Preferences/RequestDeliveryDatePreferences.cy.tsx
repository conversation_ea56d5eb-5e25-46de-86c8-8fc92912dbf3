import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import Preferences from 'components/manufacturer/Preferences';
import assertSaving from '../shared/assertSaving';

describe('Request Delivery Date Preferences', () => {
    before(() => {
        cy.brandingFixture();
        cy.preferencesFixture('getPreferences');
        cy.viewport(1280, 720);
    });

    it('should display the Request Delivery Date Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        cy.contains('Request Delivery Date');
    });

    it('should be able to update Allow Customers to Request a Delivery Date setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        cy.getCy(`checkbox-allowDeliveryDateRequest-label`).click();
        assertSaving('checkbox', 'allowDeliveryDateRequest', false);
    });

    it('should be able to update Minimum Lead time setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        cy.getCy(`checkbox-allowDeliveryDateRequest-label`)
            .should('exist')
            .click();
        assertSaving('text', 'minimumLeadTime', '15');
    });

    it('should be able to update Requested Delivery Date Frequency setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );

        cy.getCy(`checkbox-allowDeliveryDateRequest-label`)
            .should('exist')
            .click();
        cy.get(`input[name=minimumLeadTime]`).type('15');
        assertSaving('select', 'requestedDeliveryDateFrequency', '2');
    });

    it('should be able to update Require Customers to Request a Delivery Date setting', () => {
        cy.preferencesFixture('savePreferences').as('saveApi');
        cy.preferencesFixture('getPreferences');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <Preferences />
            </MockManufacturerApp>
        );
        cy.getCy(`checkbox-allowDeliveryDateRequest-label`)
            .should('exist')
            .click();
        cy.get(`input[name=minimumLeadTime]`).type('15');
        assertSaving('checkbox', 'requireDeliveryDateRequest', false);
    });
});
