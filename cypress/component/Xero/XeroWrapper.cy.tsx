import React from 'react';
import XeroConfiguration from 'components/manufacturer/XeroSettings/index';
import {MockManufacturerApp} from '../MockManufacturerApp';

describe('XeroSettings.cy.tsx', () => {
    before(() => {
        cy.xeroErrorFixture();
        cy.viewport(1280, 720);
    });
    it('Render component correctly with given error message', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <XeroConfiguration />
            </MockManufacturerApp>
        );
        cy.get('[data-cy="error_message"]').should('exist');
        cy.get('[data-cy="error_message"]').should(
            'have.text',
            "Oops! We're encountering an issue loading your Xero settings. Please try refreshing the page or try again later."
        );
    });
});
