import React from 'react';
import XeroSettings from 'components/manufacturer/XeroSettings/component/XeroSettings';
import {MockManufacturerApp} from '../MockManufacturerApp';

const XERO_SETTINGS_DATA = {
    branding_theme_id: 'def',
    prefix: 'Man',
    payment_terms: '11',
    invoice_account_code: '200',
    bill_account_code: '433',
    bill_frequency: '0',
    is_bill_enabled: true,
    is_invoice_enabled: true,
};

const XERO_SETTINGS_DATA_WITH_ONLY_BILL_ENABLED = {
    branding_theme_id: 'def',
    prefix: 'Man',
    payment_terms: '11',
    invoice_account_code: '200',
    bill_account_code: '433',
    bill_frequency: '0',
    is_bill_enabled: true,
    is_invoice_enabled: false,
};

const XERO_SETTINGS_DATA_WITH_ONLY_INVOICE_ENABLED = {
    branding_theme_id: 'def',
    prefix: 'Man',
    payment_terms: '11',
    invoice_account_code: '200',
    bill_account_code: '433',
    bill_frequency: '0',
    is_bill_enabled: false,
    is_invoice_enabled: true,
};

const XERO_CONNECTION_DETAILS = {
    is_connected: true,
    is_enabled: true,
};

const XERO_TENANT_DETAILS = [
    {
        tenantType: 'ORGANISATION',
        tenantName: 'Demo Company (AU)',
    },
];

const XERO_DISCONNECTION_DETAILS = {
    is_connected: false,
    is_enabled: true,
};
describe('XeroSettings.cy.tsx', () => {
    before(() => {
        cy.xeroFixture();
        cy.viewport(1280, 720);
    });
    it('Render component correctly with given xero settings details', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <XeroSettings
                    xeroSettingsDetails={XERO_SETTINGS_DATA}
                    connectionStatus={XERO_CONNECTION_DETAILS}
                    tenantDetails={XERO_TENANT_DETAILS}
                />
            </MockManufacturerApp>
        );

        // Test if the component is rendered correctly
        cy.get('[data-cy="tenant_name"]').should('exist');
        cy.get('[data-cy="disconnect_button"]').should('exist');
        cy.get('[data-cy="xero_details"]').should('exist');
        cy.get('[data-cy="invoice_details"]').should('exist');
        cy.get('[data-cy="bill_details"]').should('exist');
        cy.get('[data-cy="save_changes"]').should('exist');
        cy.get('[data-cy="checkbox-is_bill_enabled-label"]').should('exist');
        cy.get('[data-cy="checkbox-is_invoice_enabled-label"]').should('exist');

        // Test if the component is rendered with correct values
        cy.get('[data-cy="connection_status"]').should(
            'have.text',
            ' Connected'
        );
        cy.get('[data-cy="tenant_name"]').should(
            'have.text',
            'Demo Company (AU) (ORGANISATION)'
        );
        cy.get('[data-cy="disconnect_button"]').should(
            'have.text',
            'Disconnect from Xero'
        );
        cy.get('[data-cy="prefix"]').should('have.value', 'Man');
        cy.get('[data-cy="payment_terms"]').should('have.value', '11');
        cy.get('[data-cy="invoice_account_code"]').should('have.value', '200');
        cy.get('[data-cy="branding_theme_id"]').should('have.value', 'def');
        cy.get('[data-cy="bill_account_code"]').should('have.value', '433');
        cy.get('[data-cy="bill_frequency"]').should('have.value', '0');
        cy.get('[data-cy="checkbox-is_invoice_enabled"]').should('be.checked');
        cy.get('[data-cy="checkbox-is_bill_enabled"]').should('be.checked');

        // Test if the component behaves correctly when the invoice slider is clicked
        cy.get('[data-cy="checkbox-is_invoice_enabled-label"]').click();
        cy.get('[data-cy="checkbox-is_invoice_enabled"]').should(
            'not.be.checked'
        );
        cy.get('[data-cy="invoice_details"]').should('not.exist');
        cy.get('[data-cy="checkbox-is_bill_enabled"]').should('be.checked');
        cy.get('[data-cy="bill_details"]').should('exist');

        // Test if the component behaves correctly when the bill slider is clicked
        cy.get('[data-cy="checkbox-is_bill_enabled-label"]').click();
        cy.get('[data-cy="checkbox-is_bill_enabled"]').should('not.be.checked');
        cy.get('[data-cy="bill_details"]').should('not.exist');
    });

    it('Render component correctly with only invoice details when given xero settings details have only invoice enabled', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <XeroSettings
                    xeroSettingsDetails={
                        XERO_SETTINGS_DATA_WITH_ONLY_INVOICE_ENABLED
                    }
                    connectionStatus={XERO_CONNECTION_DETAILS}
                    tenantDetails={XERO_TENANT_DETAILS}
                />
            </MockManufacturerApp>
        );
        cy.get('[data-cy="checkbox-is_invoice_enabled"]').should('be.checked');
        cy.get('[data-cy="invoice_details"]').should('exist');
        cy.get('[data-cy="prefix"]').should('have.value', 'Man');
        cy.get('[data-cy="payment_terms"]').should('have.value', '11');
        cy.get('[data-cy="invoice_account_code"]').should('have.value', '200');
        cy.get('[data-cy="branding_theme_id"]').should('have.value', 'def');
        cy.get('[data-cy="checkbox-is_bill_enabled"]').should('not.be.checked');
        cy.get('[data-cy="bill_details"]').should('not.exist');
    });

    it('Render component correctly with only bill details when given xero settings details have only bill enabled', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <XeroSettings
                    xeroSettingsDetails={
                        XERO_SETTINGS_DATA_WITH_ONLY_BILL_ENABLED
                    }
                    connectionStatus={XERO_CONNECTION_DETAILS}
                    tenantDetails={XERO_TENANT_DETAILS}
                />
            </MockManufacturerApp>
        );

        cy.get('[data-cy="xero_details"]').should('exist');
        cy.get('[data-cy="checkbox-is_invoice_enabled"]').should(
            'not.be.checked'
        );
        cy.get('[data-cy="invoice_details"]').should('not.exist');
        cy.get('[data-cy="checkbox-is_bill_enabled"]').should('be.checked');
        cy.get('[data-cy="bill_details"]').should('exist');
        cy.get('[data-cy="bill_account_code"]').should('have.value', '433');
        cy.get('[data-cy="bill_frequency"]').should('have.value', '0');
    });

    it('Render component correctly with only xero integration disabled', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <XeroSettings
                    xeroSettingsDetails={
                        XERO_SETTINGS_DATA_WITH_ONLY_BILL_ENABLED
                    }
                    connectionStatus={XERO_DISCONNECTION_DETAILS}
                    tenantDetails={XERO_TENANT_DETAILS}
                />
            </MockManufacturerApp>
        );

        cy.get('[data-cy="connection_status"]').should(
            'have.text',
            ' Disconnected'
        );
        cy.get('[data-cy="tenant_name"]').should('not.exist');
        cy.get('[data-cy="disconnect_button"]').should('not.exist');
        cy.get('[data-cy="xero_details"]').should('not.exist');
        cy.get('[data-cy="save_changes"]').should('not.exist');
        cy.get('[data-cy="connect_button"]').should('exist');
        cy.get('[data-cy="connect_button"]').should(
            'have.text',
            'Connect to Xero'
        );
    });
});
