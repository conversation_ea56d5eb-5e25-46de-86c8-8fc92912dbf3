import React from 'react';
import {<PERSON>ckApp} from './MockApp';
import {CartToggleButton} from 'shared/components/Header/CartToggleButton';

const JOB_WITHOUT_PRODUCTS = 10000000;
const JOB_WITH_PRODUCTS = 100000;

describe('CartToggleButton', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.intercept(
            'GET',
            `/api/jobs/${JOB_WITHOUT_PRODUCTS - 10000}/total-product-count`,
            {
                body: {
                    total_product_count: 0,
                    success: 1,
                },
            }
        ).as('getTotalProductCount');
        cy.intercept(
            'GET',
            `/api/jobs/${JOB_WITH_PRODUCTS - 10000}/total-product-count`,
            {
                body: {
                    total_product_count: 10,
                    success: 1,
                },
            }
        ).as('getTotalProductCountNonZero');
        cy.viewport(1280, 720);
    });

    it('show 0 when total product count is 0', () => {
        const defaultState = {
            UI: {
                rightSidebar: {
                    status: 'OPEN',
                    behaviour: 'TOGGLE',
                    mainContentVisible: true,
                    softOpen: false,
                    smallScreenStatus: 'CLOSED',
                },
                leftSidebar: {
                    status: 'CLOSED',
                    behaviour: 'TOGGLE',
                    mainContentVisible: true,
                    softOpen: false,
                    smallScreenStatus: 'CLOSED',
                },
                showSidebarOverlays: false,
                isSmallScreenLayout: false,
                isCartToggleVisible: false,
                softOpenSidebarLoaded: false,
            },
        };

        cy.mount(
            <MockApp
                router
                route={`/v2/job/${JOB_WITHOUT_PRODUCTS}/room`}
                redux
                application
                defaultState={defaultState}>
                <CartToggleButton showOnOpen />
            </MockApp>
        );

        cy.wait('@getTotalProductCount');

        cy.get('[data-cy=quantity-badge').should('have.text', '0');
    });

    it('shows quantity badge when total product count is not 0', () => {
        const defaultState = {
            UI: {
                rightSidebar: {
                    status: 'OPEN',
                    behaviour: 'TOGGLE',
                    mainContentVisible: true,
                    softOpen: false,
                    smallScreenStatus: 'CLOSED',
                },
                leftSidebar: {
                    status: 'CLOSED',
                    behaviour: 'TOGGLE',
                    mainContentVisible: true,
                    softOpen: false,
                    smallScreenStatus: 'CLOSED',
                },
                showSidebarOverlays: false,
                isSmallScreenLayout: false,
                isCartToggleVisible: false,
                softOpenSidebarLoaded: false,
            },
        };

        cy.mount(
            <MockApp
                router
                route={`/v2/job/${JOB_WITH_PRODUCTS}/room`}
                redux
                application
                notifications
                defaultState={defaultState}>
                <CartToggleButton showOnOpen />
            </MockApp>
        );

        cy.wait('@getTotalProductCountNonZero');

        cy.get('[data-cy=quantity-badge').should('have.text', '10');
    });
});
