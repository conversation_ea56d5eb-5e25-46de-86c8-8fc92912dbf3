import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import CustomerList from 'components/manufacturer/CustomerList/component/CustomerList';
describe('CustomerList', () => {
    before(() => {
        cy.brandingFixture();
        cy.customerListFixture();
        cy.labelsFixture();
        cy.viewport(1280, 720);
        window.isAdmin = 0;
    });

    it('should render header', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('header').should('exist').and('have.text', 'Customers');
        cy.getCy('download-csv')
            .should('exist')
            .and('have.text', 'Download CSV');
        cy.getCy('add-new-customer')
            .should('exist')
            .and('have.text', 'Add New Customer');
    });

    it('should render filters', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('customerSearch').should('exist');
        cy.get('#signupSourceFilter').should('exist');
        cy.get('#countryFilter').should('exist');
        cy.get('#suspendedFilter').should('exist');
    });

    it('should render customer table headers', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.contains('[data-column-id=name]', 'Name').should('exist');
        cy.contains('[data-column-id=customer_label]', 'Label').should('exist');
        cy.contains('[data-column-id=fee]', 'Fee').should('exist');
        cy.contains('[data-column-id=address]', 'Address').should('exist');
        cy.contains(
            '[data-column-id=customer_discount]',
            'Price Adjust.'
        ).should('exist');
        cy.contains('[data-column-id=min_charge]', 'Min. Charge').should(
            'exist'
        );
        cy.contains(
            '[data-column-id=minimum_freight_amount]',
            'Min. Freight'
        ).should('exist');
        cy.contains('[data-column-id=created_date]', 'Date Added').should(
            'exist'
        );
        cy.contains(
            '[data-column-id=last_login_date]',
            'Last Logged On'
        ).should('exist');
        cy.contains(
            '[data-column-id=last_order_date]',
            'Last Order Date'
        ).should('exist');
        cy.contains('[data-column-id=options]', 'Options').should('exist');
    });

    it('should display depot column if available for manufacturer', () => {
        window.isDepotEnabled = 1;
        cy.depotsFixture();
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.contains('[data-column-id=depot]', 'Depot').should('exist');
    });

    it('should display Xero column if available for manufacturer', () => {
        window.isXeroEnabled = 1;
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.contains('[data-column-id=xero_status]', 'Xero Status').should(
            'exist'
        );
    });

    describe('Admin User', () => {
        before(() => {
            cy.adminDepotFixture();
            cy.adminManufacturersFixture();
            window.isAdmin = 1;
        });

        it('should render the admin filters', () => {
            cy.mount(
                <MockManufacturerApp redux={true} notifications={true}>
                    <CustomerList />
                </MockManufacturerApp>
            );
            cy.get('#manufacturerFilter').should('exist');
        });

        it('should render customer table headers available for admin only', () => {
            cy.mount(
                <MockManufacturerApp redux={true} notifications={true}>
                    <CustomerList />
                </MockManufacturerApp>
            );
            cy.contains('[data-column-id=manufacturer]', 'Manufacturer').should(
                'exist'
            );
        });

        it('should display depot column if available', () => {
            cy.mount(
                <MockManufacturerApp redux={true} notifications={true}>
                    <CustomerList />
                </MockManufacturerApp>
            );
            cy.contains('[data-column-id=depot]', 'Depot').should('exist');
        });

        it('should display date columns correctly when available', () => {
            cy.mount(
                <MockManufacturerApp redux={true} notifications={true}>
                    <CustomerList />
                </MockManufacturerApp>
            );

            cy.viewport(1200, 800);
            // Only asserts date strings for first row in table
            cy.get('div.rdt_TableBody div[role="row"]')
                .first()
                .should('exist')
                .within(() => {
                    cy.get('div[data-column-id="created_date"]')
                        .children()
                        .first()
                        .should('exist')
                        .contains('div', '21st of June 2024 at 05:03 AM');

                    cy.get('div[data-column-id="last_login_date"]')
                        .children()
                        .first()
                        .should('exist')
                        .contains('div', '-');

                    cy.get('div[data-column-id="last_order_date"]')
                        .children()
                        .first()
                        .should('exist')
                        .contains('div', '-');
                });
        });
    });
});
