import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import CustomerList from 'components/manufacturer/CustomerList/component/CustomerList';
describe('CustomerList - Filters', () => {
    before(() => {
        cy.brandingFixture();
        cy.customerListFixture();
        cy.adminDepotFixture();
        cy.adminManufacturersFixture();
        cy.viewport(1280, 720);
        window.isAdmin = 1;
    });

    it('should be able to filter data by Signup Source', () => {
        cy.customerListFixture('signup_source', {
            current_page: '1',
            page_size: '25',
            sort_orders: 'created_date DESC',
            filter_groups:
                '((hidden:equals:0))AND((gfp:equals:0))AND((signup_source:equals:1))',
        });
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.get('[data-column-id=name][role=cell]').should('have.length', 25);
        cy.get('#signupSourceFilter').click();
        cy.get('#signupSourceFilter')
            .contains('[role=option]', 'Manually added by manufacturer')
            .click();
        cy.get('[data-column-id=name][role=cell]').should('have.length', 5);
    });

    it('should be able to filter data by Country', () => {
        cy.customerListFixture('country', {
            current_page: '1',
            page_size: '25',
            sort_orders: 'created_date DESC',
            filter_groups:
                '((hidden:equals:0))AND((gfp:equals:0))AND((manufacturer.country_id:equals:2))',
        });
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.get('[data-column-id=name][role=cell]').should('have.length', 25);
        cy.get('#countryFilter').click();
        cy.get('#countryFilter')
            .contains('[role=option]', 'New Zealand')
            .click();
        cy.get('[data-column-id=name][role=cell]').should('have.length', 2);
    });

    it('should be able to filter data by Status', () => {
        cy.customerListFixture('status', {
            current_page: '1',
            page_size: '25',
            sort_orders: 'created_date DESC',
            filter_groups:
                '((hidden:equals:0))AND((gfp:equals:0))AND((is_suspended:equals:1))',
        });
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.get('[data-column-id=name][role=cell]').should('have.length', 25);
        cy.get('#suspendedFilter').click();
        cy.get('#suspendedFilter').contains('[role=option]', 'Yes').click();
        cy.get('[data-column-id=name][role=cell]').should('have.length', 1);
    });

    it('should be able to filter data by Manufacturer', () => {
        cy.customerListFixture('manufacturer', {
            current_page: '1',
            page_size: '25',
            sort_orders: 'created_date DESC',
            filter_groups:
                '((hidden:equals:0))AND((gfp:equals:0))AND((manufacturer_id:equals:148))',
        });
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.get('[data-column-id=name][role=cell]').should('have.length', 25);
        cy.get('#manufacturerFilter').click();
        cy.get('#manufacturerFilter')
            .contains('[role=option]', 'Your Business Name (Cabinetry.Online)')
            .click();
        cy.get('[data-column-id=name][role=cell]').should('have.length', 4);
    });

    it('should be able to filter data by customer label', () => {
        window.isAdmin = 0;
        window.isManufacturer = 1;
        window.manufacturerId = 186;
        cy.manufacturerDefaultsFixture('getManufacturerDefaultsByID', {
            queryParams: {id: '186'},
        });
        cy.customerListFixture('retail', {
            current_page: '1',
            page_size: '25',
            sort_orders: 'created_date DESC',
            filter_groups:
                '((hidden:equals:0))AND((gfp:equals:0))AND((customer_label.label_id:either:160))',
        }).as('retailApiCall');
        cy.customerListFixture('retail_trade', {
            current_page: '1',
            page_size: '25',
            sort_orders: 'created_date DESC',
            filter_groups:
                '((hidden:equals:0))AND((gfp:equals:0))AND((customer_label.label_id:either:160,71))',
        }).as('retailTradeApiCall');
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.get('[data-column-id=name][role=cell]').should('have.length', 25);
        cy.get('#customerLabels').click();
        cy.get('#customerLabels').contains('[role=option]', 'RETAIL').click();
        cy.wait('@retailApiCall');
        cy.get('[data-column-id=name][role=cell]').should('have.length', 2);

        cy.get('#customerLabels').click();
        cy.get('#customerLabels').contains('[role=option]', 'TRADE').click();
        cy.wait('@retailTradeApiCall');
        cy.get('[data-column-id=name][role=cell]').should('have.length', 3);
    });
});
