import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import CustomerList from 'components/manufacturer/CustomerList/component/CustomerList';
describe('CustomerList - Buttons', () => {
    before(() => {
        cy.brandingFixture();
        cy.customerListFixture();
        cy.adminDepotFixture();
        cy.adminManufacturersFixture();
        cy.viewport(1280, 720);
    });

    beforeEach(() => {
        window.isAdmin = 1;
        window.isCustomerEditingEnabled = 1;
        window.isSuperAdmin = 0;
        window.isManufacturer = 0;
    });

    it('should download CSV when Download CSV button is clicked', () => {
        cy.customerListFixture(
            'signup_source',
            {
                current_page: '1',
                page_size: '25',
                sort_orders: 'created_date DESC',
                filter_groups:
                    '((hidden:equals:0))AND((gfp:equals:0))AND((signup_source:equals:1))',
            },
            100
        ).as('signupSource');
        cy.exportCustomerFixture({
            letter: '',
            signupSource: '1',
            country: '',
            suspended: '',
            supplier: '',
            customer_label_ids: '',
            totalItems: '5',
            manufacturer: '',
        }).as('exportCSV');
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.get('#signupSourceFilter').click();
        cy.get('#signupSourceFilter')
            .contains('[role=option]', 'Manually added by manufacturer')
            .click();
        cy.contains('Loading...').should('exist');
        cy.wait('@signupSource');
        cy.contains('Loading...').should('not.exist');
        cy.getCy('download-csv').click();
        cy.wait('@exportCSV');
        // cypress removes assets on cypress:run via this config "trashAssetsBeforeRuns" which is true by default
        cy.readFile(
            `${Cypress.config('downloadsFolder')}/customer_list.csv`
        ).should('exist');
    });

    it('should redirect to add customer page when Add New Customer is clicked', () => {
        cy.window().then((win) => {
            cy.stub(win.LocationHelper, 'redirectTo').as('redirectTo');
        });
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('add-new-customer').click();
        cy.get('@redirectTo').should(
            'have.been.calledOnceWith',
            '/customerForm.php'
        );
    });

    it('should redirect to edit customer page when Edit Icon is clicked for a customer', () => {
        cy.window().then((win) => {
            cy.stub(win.LocationHelper, 'redirectTo').as('redirectToEdit');
        });

        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('edit-200539').click();
        cy.get('@redirectToEdit').should(
            'have.been.calledOnceWith',
            'customerForm.php?customerId=200539'
        );
    });

    it('should login the customer when Login As Icon is clicked if user type is superAdmin', () => {
        window.isSuperAdmin = 1;
        cy.window().then((win) => {
            cy.stub(win.LocationHelper, 'redirectTo').as('loginAs');
        });

        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('more-200539').click();
        cy.getCy('login-as-200539').click({force: true});
        cy.get('@loginAs').should(
            'have.been.calledOnceWith',
            'ronnie.php?loginId=200539&level=2'
        );
    });

    it('should display login as Icon when user type is manufacturer and manufacturer login is allowed', () => {
        window.isManufacturer = 1;
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('login-as-200539').should('not.exist');
        cy.getCy('more-200538').click();
        cy.getCy('login-as-200538').should('exist');
    });

    it('should login the customer when Login As Icon is clicked if user type is manufacturer and manufacturer login is allowed', () => {
        window.isManufacturer = 1;
        cy.window().then((win) => {
            cy.stub(win.LocationHelper, 'redirectTo').as('loginAs');
        });

        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('more-200538').click();
        cy.getCy('login-as-200538').click({force: true});
        cy.get('@loginAs').should(
            'have.been.calledOnceWith',
            'ronnie.php?loginId=200538&level=2'
        );
    });

    it('should show suspend customer modal when Suspend Icon is clicked for a customer', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('suspend-200539').click();
        cy.get('[role=dialog] .modal-header').should(
            'have.text',
            'Suspend Customer?'
        );
        cy.get('[role=dialog] .modal-body').should(
            'have.text',
            'Are you sure you want to Suspend this Customer?'
        );
    });

    it('should show unsuspend customer modal when Unsuspend Icon is clicked for a customer', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('unsuspend-200538').click();
        cy.get('[role=dialog] .modal-header').should(
            'have.text',
            'Unsuspend Customer?'
        );
        cy.get('[role=dialog] .modal-body').should(
            'have.text',
            'Are you sure you want to Unsuspend this Customer?'
        );
    });

    it('should show delete customer modal when Delete Icon is clicked for a customer', () => {
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('delete-200538').click();
        cy.get('[role=dialog] .modal-header').should(
            'have.text',
            'Delete Customer?'
        );
        cy.get('[role=dialog] .modal-body').should(
            'have.text',
            'Are you sure you want to Delete this Customer?'
        );
    });

    it('should show ban/unban icons when superAdmin is logged in', () => {
        window.isSuperAdmin = 1;
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('ban-200539').should('exist');
        cy.getCy('unban-200537').should('exist');
    });

    it('should show ban customer modal when Ban Icon is clicked for a customer', () => {
        window.isSuperAdmin = 1;
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('ban-200539').click();
        cy.get('[role=dialog] .modal-header').should(
            'have.text',
            'Ban Customer?'
        );
        cy.get('[role=dialog] .modal-body').should(
            'have.text',
            'Are you sure you want to Ban this Customer?'
        );
    });

    it('should show unban customer modal when Unsuspend Icon is clicked for a customer', () => {
        window.isSuperAdmin = 1;
        cy.mount(
            <MockManufacturerApp redux={true} notifications={true}>
                <CustomerList />
            </MockManufacturerApp>
        );
        cy.getCy('unban-200537').click();
        cy.get('[role=dialog] .modal-header').should(
            'have.text',
            'Unban Customer?'
        );
        cy.get('[role=dialog] .modal-body').should(
            'have.text',
            'Are you sure you want to Unban this Customer?'
        );
    });
});
