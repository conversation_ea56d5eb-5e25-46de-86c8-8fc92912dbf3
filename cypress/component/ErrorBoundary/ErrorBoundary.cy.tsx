import React from 'react';
import {MockApp} from '../MockApp';
import {ErrorBoundary} from 'shared';

const TestAppError = () => {
    throw new Error('Test Error');
};

const TestAppNoError = () => {
    return <div>No Error</div>;
};

describe('ErrorBoundary', () => {
    beforeEach(() => {
        cy.intercept('GET', 'api/user', {
            user: {
                manufacturerName: 'Test Manufacturer',
                manufacturerEmail: '<EMAIL>',
                manufacturerPhone: '0459987715',
            },
        }).as('UserApiRequest');
    });

    it('Should render children', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <ErrorBoundary>
                    <TestAppNoError />
                </ErrorBoundary>
            </MockApp>
        );

        cy.wait('@UserApiRequest');
        cy.contains('No Error').should('exist');
    });

    it('Should render error message', () => {
        cy.configurationFixture().as('Configuration Api Request');
        // Ignore exceptions for this test
        cy.on('uncaught:exception', () => {
            // As soon as components throw error
            // cypress fails them, so we need to ignore them
            // to test the behaviour of ErrorBoundary
            return false;
        });

        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <ErrorBoundary>
                    <TestAppError />
                </ErrorBoundary>
            </MockApp>
        );

        cy.contains('Something went wrong while loading your page.').should(
            'exist'
        );
        cy.contains('<EMAIL>').should('exist');
        cy.contains('0459987715').should('exist');
    });
});
