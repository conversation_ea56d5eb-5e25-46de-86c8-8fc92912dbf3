import {ApplicationProvider, JobProvider, NotificationProvider} from 'contexts';
import React from 'react';
import {Provider} from 'react-redux';
import {
    RouteObject,
    RouterProvider,
    createMemoryRouter,
} from 'react-router-dom';
import {AppState, setupStore} from 'store/customer/storeSetup';
import {ThemeProvider} from 'styled-components';
import {baseTheme} from 'theme';

const getStore = (state: AppState) => {
    return setupStore(state);
};

interface MockAppProps {
    children: JSX.Element;
    redux?: boolean;
    router?: boolean;
    route?: string;
    path?: string;
    application?: boolean;
    notifications?: boolean;
    job?: boolean;
    defaultState?: AppState;
    jobId?: number | null;
    roomId?: number | null;
    childRoutes?: RouteObject[];
}

const memoryRouter = (
    element: JSX.Element,
    initialEntries: string[] = [],
    path: string,
    children?: RouteObject[]
) => {
    return createMemoryRouter(
        [
            {
                path,
                element,
                children,
            },
        ],
        {
            initialEntries,
        }
    );
};

export const MockApp = ({
    children,
    router = false,
    route = '/',
    path = '/*',
    redux = false,
    notifications = false,
    application = false,
    job = false,
    defaultState,
    jobId = 310367,
    roomId = 374038,
    childRoutes,
}: MockAppProps) => {
    let output = children;

    if (job) {
        output = (
            <JobProvider jobId={jobId} roomId={roomId}>
                {output}
            </JobProvider>
        );
    }

    if (application) {
        output = <ApplicationProvider>{output}</ApplicationProvider>;
    }

    if (router) {
        output = (
            <RouterProvider
                router={memoryRouter(output, [route], path, childRoutes)}
            />
        );
    }

    if (notifications) {
        output = <NotificationProvider>{output}</NotificationProvider>;
    }

    if (notifications) {
        output = <NotificationProvider>{output}</NotificationProvider>;
    }

    if (redux) {
        output = <Provider store={getStore(defaultState)}>{output}</Provider>;
    }

    output = <ThemeProvider theme={baseTheme}>{output}</ThemeProvider>;

    return output;
};
