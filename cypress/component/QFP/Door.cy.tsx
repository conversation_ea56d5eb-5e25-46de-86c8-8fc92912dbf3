import React from 'react';
import {<PERSON><PERSON>A<PERSON>} from '../MockApp';
import {JobProvider} from 'contexts';
import {JOB, ROOM} from '../../support/fixtures/jobFixture';
import {DOOR} from '../../support/fixtures/legacyMaterialFixture';
import {QFP} from 'components/customer/QFPRedux/QFP';

describe('QFP Door product', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.jobFixture().as('Job Api Request');
        cy.productFixture(DOOR, ROOM);
        cy.legacyMaterialFixture(DOOR);
        cy.legacyMaterialTypeFixture(DOOR);
        cy.qfpFixture();
        cy.viewport(1280, 720);

        cy.intercept('GET', `/api/room/${ROOM}`, {
            body: {success: 1, job_cabinets: []},
        });
        cy.intercept('GET', 'api/jobcabinets/room/*/qfp', {
            success: 1,
            existing_quick_flat_products: [],
        });
    });

    it('should render the input fields table', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/room/:roomId/quick-flat-product"
                route={`/job/${JOB}/room/${ROOM}/quick-flat-product`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <QFP />
                </JobProvider>
            </MockApp>
        );

        cy.get('[data-cy="cabinet_type_0"]').should('have.text', 'Door');
        cy.get('[data-cy="cabinet_quantity_0"]').should('have.value', '1');
        cy.get('[data-cy="height_0"]').should('have.value', '900');
        cy.get('[data-cy="width_0"]').should('have.value', '550');
        cy.get('[data-cy="material_search_0"]').should(
            'have.attr',
            'placeholder',
            'Alabaster Gloss - 18mm ULTRAglaze MR MDF'
        );
        cy.get('[data-cy="door_search_0"]').should(
            'have.attr',
            'placeholder',
            'Flat Panel'
        );
        cy.get('[data-cy="edge_search_0"]').should(
            'have.attr',
            'placeholder',
            '1mm Alabaster ULTRAglaze Gloss'
        );
        cy.get('[data-cy="cabinet_note_0"]').should('exist');
        cy.get('[id="0-price-unit"]').should('have.text', '$0.00');
        cy.get('[id="0-price"]').should('have.text', '$0.00');
    });

    it('should render the Panel Edging Design dropdowns', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/room/:roomId/quick-flat-product"
                route={`/job/${JOB}/room/${ROOM}/quick-flat-product`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <QFP />
                </JobProvider>
            </MockApp>
        );

        cy.get('#panel_edge_top').should('exist');
        cy.get('#panel_edge_bottom').should('exist');
        cy.get('#panel_edge_left').should('exist');
        cy.get('#panel_edge_right').should('exist');
    });

    it('should be able to search and update the Panel Edging Design dropdowns', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/room/:roomId/quick-flat-product"
                route={`/job/${JOB}/room/${ROOM}/quick-flat-product`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <QFP />
                </JobProvider>
            </MockApp>
        );

        cy.get('#panel_edge_top').click();
        cy.get('[role=option]').should('have.length', 3);
        cy.get('#panel_edge_top').type('45');
        cy.get('[role=option]').should('have.length', 1);
        cy.get('[role=option]').click();
        cy.get('#panel_edge_top').should('contain.text', '45° Straight');

        cy.get('#panel_edge_bottom').click();
        cy.get('[role=option]').should('have.length', 3);
        cy.get('#panel_edge_bottom').type('Not');
        cy.get('[role=option]').should('have.length', 1);
        cy.get('[role=option]').click();
        cy.get('#panel_edge_bottom').should('contain.text', 'Not Edged');
    });
});
