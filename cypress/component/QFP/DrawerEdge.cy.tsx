import React from 'react';

import {<PERSON><PERSON><PERSON><PERSON>} from '../MockApp';
import {JobProvider} from 'contexts';
import {JOB, ROOM} from '../../support/fixtures/jobFixture';
import {DOOR, DRAWER} from '../../support/fixtures/legacyMaterialFixture';
import {QFP} from 'components/customer/QFPRedux/QFP';

interface JobCabinet {
    id: number;
    name: string;
    [key: string]: boolean | string | number;
}

interface Room {
    id: number;
    name: string;
    jobCabinets: JobCabinet[];
}

interface JobData {
    rooms: Room[];
}

interface Job {
    data: JobData;
}

describe('QFP Drawer product', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.jobFixture().as('jobApiRequest');
        cy.fixture('job.json').then((job: Job) => {
            cy.fixture(`jobCabinet/${ROOM}.json`).then(
                (jobCabinets: JobCabinet[]) => {
                    job.data.rooms = job.data.rooms.map((room) => ({
                        ...room,
                        jobCabinets: jobCabinets,
                    }));
                    cy.intercept('GET', /\/api\/jobs\/\d+/, job).as('getJob');
                }
            );
        });

        cy.productFixture(DOOR, ROOM);
        cy.productFixture(DRAWER, ROOM);
        cy.legacyMaterialFixture(DRAWER);
        cy.legacyMaterialTypeFixture(DRAWER);

        cy.intercept('GET', `/api/jobcabinets/room/${ROOM}/qfp`, {
            fixture: `room/${ROOM}.json`,
        }).as('roomQfp');

        cy.intercept('GET', `/api/jobcabinets/room/${ROOM}/qfp`, {
            body: {success: 1, existing_quick_flat_products: []},
        });
        cy.intercept('GET', `/api/room/${ROOM}`, {
            body: {success: 1, job_cabinets: []},
        });

        cy.qfpFixture();
        cy.viewport(1280, 720);
    });

    it('should render drawer edges', () => {
        cy.fixture('user.json').then(
            (data: {
                user: {
                    allowDrawerFaceEdgeFinish: boolean;
                };
            }) => {
                data.user.allowDrawerFaceEdgeFinish = true;
                cy.intercept('GET', '/api/user', data).as('getUser');
            }
        );

        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}
                path="/job/:jobId/room/:roomId/quick-flat-product"
                route={`/job/${JOB}/room/${ROOM}/quick-flat-product`}>
                <JobProvider jobId={JOB} roomId={ROOM}>
                    <QFP />
                </JobProvider>
            </MockApp>
        );
        // TODO: Revisit later for proper test scenarios
    });
});
