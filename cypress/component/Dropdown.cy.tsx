import React, {useCallback, useState} from 'react';
import {Dropdown} from 'shared';
import {MockApp} from './MockApp';

const TestComponent = () => {
    const [selected, setSelected] = useState(1);

    const selectHandler = useCallback(
        (name: string, value: number) => setSelected(value),
        [setSelected]
    );

    return (
        <Dropdown
            name="text"
            value={selected}
            selectHandler={selectHandler}
            options={[
                {id: 1, name: 'One', value: 1},
                {id: 2, name: 'Two', value: 2},
            ]}
        />
    );
};

describe('Dropdown.cy.tsx', () => {
    before(() => {
        cy.userFixture();
    });

    it('playground', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <TestComponent />
            </MockApp>
        );

        cy.get('[data-cy=text').should('have.text', 'One');
        cy.get('[data-cy=text]').click();

        cy.get('[data-cy="text-dropdown-item-two"]').should('have.text', 'Two');
        cy.get('[data-cy="text-dropdown-item-two"]').click();

        cy.get('[data-cy=text').should('have.text', 'Two');
    });
});
