import React from 'react';
import {MockApp} from '../../MockApp';
import {QuickAdd, QuickAddContext} from 'components/customer/Product/QuickAdd';
import {AppState} from 'store/customer/storeSetup';
import {Width} from 'components/customer/Product/QuickAdd/Width';
import {NumberSpinner} from 'components/customer/Product/QuickAdd/NumberSpinner';

const product = {
    favourites: 8,
    id: 350,
    sundryCategoryId: 2,
    name: 'Base Applied Panel',
    changed_name: 'Base Applied Panel',
    changedImage: 'BaseAppliedPanel01.png',
    imageUrl: 'BaseAppliedPanel01.png',
    stockStatus: 0,
    stockStatusText: '',
    advanced: false,
    hidden: false,
    defaultWidth: 600,
    lshape: 0,
};

const defualtState: Partial<AppState> = {
    theme: {
        branding: {
            primary_colour: '255, 255, 255',
            secondary_colour: '0, 0, 0',
        },
        loginBranding: {
            manufacturer_active: true,
            permit_customer_self_registration: false,
        },
    },
};
describe('Quick Add', () => {
    before(() => {
        cy.userFixture();
        cy.jobFixture();
    });

    it('should render quick add overlay', () => {
        const onClickHandler = cy.stub();

        cy.mount(
            <MockApp
                defualtState={defualtState}
                redux={true}
                router={true}
                application={true}
                notifications={true}
                job={true}>
                <QuickAdd
                    isFocused={true}
                    onClickHandler={onClickHandler}
                    item={product}
                />
            </MockApp>
        );

        cy.contains('Add').should('be.visible');
        cy.contains('or quickly add below').should('be.visible');

        cy.getCy('quantity-350').should('have.value', '1');
        cy.getCy('width-350').should('have.value', '600');

        expect(onClickHandler).to.not.be.called;
        cy.contains('CLICK to CUSTOMISE').click();
        cy.then(() => {
            expect(onClickHandler).to.be.calledOnce;
        });
    });

    it('should render width input and call right change event', () => {
        const width = 20;
        const setWidth = cy.stub();

        cy.mount(
            <MockApp>
                <QuickAddContext.Provider
                    value={{
                        width,
                        setWidth,
                        item: product,
                        open: true,
                        onClickHandler: cy.stub(),
                        validationMessage: '',
                        setValidationMessage: cy.stub(),
                    }}>
                    <Width />
                </QuickAddContext.Provider>
            </MockApp>
        );

        cy.getCy('width-350').should('be.visible');
        expect(setWidth).to.not.be.called;

        cy.getCy('width-350').type('2');
        cy.then(() => {
            expect(setWidth).to.be.calledOnceWith(202);
        });
    });

    it('should render quantity field with spinner', () => {
        const quantity = 9;
        const setQuantity = cy.stub();

        cy.mount(
            <MockApp defualtState={defualtState} redux={true}>
                <QuickAddContext.Provider
                    value={{
                        quantity,
                        setQuantity,
                        item: product,
                        open: true,
                        onClickHandler: cy.stub(),
                    }}>
                    <NumberSpinner />
                </QuickAddContext.Provider>
            </MockApp>
        );

        cy.getCy('quantity-350').should('be.visible');

        cy.getCy('quantity-350').type('2');
        cy.then(() => {
            expect(setQuantity).to.be.calledOnceWith(92);
        });

        cy.getCy('increment-button').should('be.visible').click();
        cy.then(() => {
            expect(setQuantity).to.be.calledWith(10);
        });

        cy.getCy('decrement-button').should('be.visible').click();
        cy.then(() => {
            expect(setQuantity).to.be.calledWith(8);
        });
    });
});
