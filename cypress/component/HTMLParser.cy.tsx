import React from 'react';
import {parseHtmlString} from 'shared/helpers/HTMLParser';

const TestComponent = ({
    content,
    removeStrongTag = false,
}: {
    content: string;
    removeStrongTag?: boolean;
}) => {
    const parseContent = parseHtmlString(content, {removeStrongTag});
    return <div id="root">{parseContent}</div>;
};

describe('HTMLParser', () => {
    it('correctly parses html string', () => {
        cy.mount(
            <TestComponent content="<div>Test text</div><span>This is it</span>" />
        );

        cy.contains('Test text').should('exist');

        cy.contains('This is it').should('exist');

        // This should not exist as div separates them visually
        cy.contains('Test text This is it').should('not.exist');
    });

    it('correctly parses html string with strong tag', () => {
        cy.mount(<TestComponent content="<strong>Test</strong> content" />);
        cy.get('strong').should('have.text', 'Test');
    });

    it('correctly parses html string with strong tag removed', () => {
        cy.mount(
            <TestComponent
                content="<strong>Test</strong> content without strong"
                removeStrongTag={true}
            />
        );
        cy.get('strong').should('not.exist');
        cy.contains('Test content without strong').should('exist');
    });

    it('correctly removes multiple strong tags', () => {
        cy.mount(
            <TestComponent
                content="<strong>First</strong> and <strong>Second</strong> content"
                removeStrongTag={true}
            />
        );
        cy.get('strong').should('not.exist');
        cy.contains('First and Second content').should('exist');
    });

    it('correctly parses nested html elements', () => {
        cy.mount(
            <TestComponent content="<div><span>Nested <strong>text</strong></span></div>" />
        );

        cy.contains('Nested text').should('exist');
        cy.get('strong').should('have.text', 'text');
    });

    it('handles invalid html gracefully', () => {
        cy.mount(<TestComponent content="<div><span>Invalid HTML</div>" />);
        cy.contains('Invalid HTML').should('exist');
    });

    it('handles empty content gracefully', () => {
        cy.mount(<TestComponent content="" />);
        cy.get('#root').should('be.empty');
    });
});
