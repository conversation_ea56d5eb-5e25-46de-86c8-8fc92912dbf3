import {JobContext} from 'contexts';
import React from 'react';
import {MemoryRouter, Route, Routes} from 'react-router-dom';
import {JobRoute} from 'shared';

const FourZeroFour = () => {
    return <>404 page</>;
};

const Test = () => {
    return <>Test Component</>;
};

const DefaultPage = () => {
    return <>Default Page</>;
};

describe('Job Route', () => {
    it('redirects to 404 correctly', () => {
        cy.mount(
            <MemoryRouter initialEntries={['/']}>
                <Routes>
                    <Route path="/v2/job/:jobId" element={<JobRoute />}>
                        <Route path="" element={<Test />} />
                    </Route>
                    <Route path="*" element={<FourZeroFour />} />
                </Routes>
            </MemoryRouter>
        );

        cy.contains('404 page').should('exist');
    });

    it('renders component correctly if no errors', () => {
        const job = {error: false};

        cy.mount(
            <MemoryRouter initialEntries={[{pathname: '/v2/job/1'}]}>
                <JobContext.Provider value={{job, setJob: cy.stub()}}>
                    <Routes>
                        <Route path="/v2/job/:jobId" element={<JobRoute />}>
                            <Route path="" element={<Test />} />
                        </Route>
                        <Route path="*" element={<FourZeroFour />} />
                    </Routes>
                </JobContext.Provider>
            </MemoryRouter>
        );

        cy.contains('Test Component').should('exist');
    });

    it('handles processing jobs correctly', () => {
        const job = {status: 1, id: 1, name: 'Test job'};

        cy.mount(
            <MemoryRouter initialEntries={[{pathname: '/v2/job/1'}]}>
                <JobContext.Provider
                    value={{job, isJobProcessing: true, setJob: cy.stub()}}>
                    <Routes>
                        <Route
                            path="/v2/job/:jobId"
                            element={<JobRoute checkProcessingStatus={true} />}>
                            <Route path="" element={<Test />} />
                        </Route>
                        <Route path="*" element={<FourZeroFour />} />
                    </Routes>
                </JobContext.Provider>
            </MemoryRouter>
        );

        cy.contains('Test Component').should('exist');
    });

    it('handles not processing jobs correctly', () => {
        const job = {status: 2, id: 1, name: 'Test job'};

        cy.mount(
            <MemoryRouter initialEntries={[{pathname: '/v2/job/1'}]}>
                <JobContext.Provider
                    value={{job, isJobProcessing: false, setJob: cy.stub()}}>
                    <Routes>
                        <Route path="/v2" element={<DefaultPage />} />
                        <Route
                            path="/v2/job/:jobId"
                            element={<JobRoute checkProcessingStatus={true} />}>
                            <Route path="" element={<Test />} />
                        </Route>
                        <Route path="*" element={<FourZeroFour />} />
                    </Routes>
                </JobContext.Provider>
            </MemoryRouter>
        );

        cy.contains('Default Page').should('exist');
    });
});
