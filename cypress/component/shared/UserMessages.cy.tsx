import React from 'react';
import {<PERSON>ckApp} from '../MockApp';
import {UserMessages} from 'shared';

const message = {
    expiry: '2021-09-02 15:00:00',
    heading: 'Message From Manufacturer - Manufacturer 1',
    id: '115505',
    redirect: '',
    text: 'test',
    userId: '928',
    userLevel: '2',
};

describe('UserMessages', () => {
    beforeEach(() => {
        cy.intercept('GET', 'api/messages', {
            success: 1,
            data: [message, {...message, id: '115506', text: 'Message 2'}],
        });
        cy.intercept('POST', 'api/message/read-all', {success: 1}).as(
            'readAll'
        );
        cy.intercept('POST', 'api/message/read/*', {success: 1}).as(
            'read-specific-message'
        );
    });

    it('should render dialog when customer has message', () => {
        cy.mount(
            <MockApp redux router>
                <UserMessages />
            </MockApp>
        );

        cy.contains('Message From Manufacturer - Manufacturer 1').should(
            'exist'
        );
        cy.contains('Message 2').should('exist');

        cy.contains('Ok').should('exist');

        cy.contains('Skip').should('exist');

        cy.contains('Skip All').click();

        cy.wait('@readAll').its('response.statusCode').should('eq', 200);

        cy.contains('Message From Manufacturer - Manufacturer 1').should(
            'not.exist'
        );
    });

    it('should hide dialog when pressed OK on dialog', () => {
        cy.mount(
            <MockApp redux router notifications>
                <UserMessages />
            </MockApp>
        );

        cy.contains('Ok').click();

        cy.wait('@read-specific-message')
            .its('response.statusCode')
            .should('eq', 200);
    });

    it('should render next message when clicked on skip', () => {
        cy.mount(
            <MockApp redux router notifications>
                <UserMessages />
            </MockApp>
        );

        cy.contains('Message 2').should('exist');
        cy.contains('test').should('not.exist');

        cy.contains('Skip').click();

        cy.then(() => {
            cy.contains('Message 2').should('not.exist');
            cy.contains('test').should('exist');
        });
    });
});
