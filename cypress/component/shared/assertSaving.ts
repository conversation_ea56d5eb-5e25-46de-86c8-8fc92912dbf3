const assertCheckboxSaving = (fieldName: string, value: boolean | number) => {
    cy.getCy(`checkbox-${fieldName}-label`).should('exist').click();
    cy.contains('button[type=submit]', 'Save changes').click();
    cy.wait('@saveApi').its(`request.body.${fieldName}`).should('equal', value);
};

const assertSelectSaving = (fieldName: string, value: string) => {
    cy.get(`select[name=${fieldName}]`).should('exist');
    cy.get(`select[name=${fieldName}]`).select(value);
    cy.contains('button[type=submit]', 'Save changes').click();
    cy.wait('@saveApi').its(`request.body.${fieldName}`).should('equal', value);
};

const assertInputSaving = (fieldName: string, value: string | number) => {
    cy.get(`input[name=${fieldName}]`).should('exist');
    cy.get(`input[name=${fieldName}]`).clear();
    cy.get(`input[name=${fieldName}]`).type(String(value));
    cy.contains('button[type=submit]', 'Save changes').click();
    cy.wait('@saveApi').its(`request.body.${fieldName}`).should('equal', value);
};

const assertTextAreaSaving = (fieldName: string, value: string) => {
    cy.get(`textarea[name=${fieldName}]`).should('exist');
    cy.get(`textarea[name=${fieldName}]`).clear();
    cy.get(`textarea[name=${fieldName}]`).type(String(value));
    cy.contains('button[type=submit]', 'Save changes').click();
    cy.wait('@saveApi').its(`request.body.${fieldName}`).should('equal', value);
};

const assertRichTextSaving = (fieldName: string, value: string) => {
    cy.getCy(fieldName).within((editor) => {
        // reset active option if any
        if (editor.find('.rdw-option-active')?.length) {
            cy.get('.rdw-option-active').click();
        }
        // clear everything
        cy.get('div[role=textbox]').clear();
        // update some styling
        cy.get('.rdw-fontsize-dropdown').click();
        cy.contains('.rdw-fontsize-option', '24').click();
        // type in the input
        cy.get('div[role=textbox]').type(value);
    });

    cy.contains('button[type=submit]', 'Save changes').click();
    cy.wait('@saveApi')
        .its(`request.body.${fieldName}`)
        .should(
            'equal',
            `<p><span style="font-size: 24px;">${value}</span></p>\n`
        );
};

export default (
    type: 'checkbox' | 'select' | 'text' | 'number' | 'textarea' | 'richtext',
    fieldName: string,
    value: boolean | number | string
) => {
    if (type === 'checkbox') {
        assertCheckboxSaving(fieldName, value);
    } else if (type === 'select') {
        assertSelectSaving(fieldName, value);
    } else if (type === 'text') {
        assertInputSaving(fieldName, String(value));
    } else if (type === 'number') {
        assertInputSaving(fieldName, Number(value));
    } else if (type === 'textarea') {
        assertTextAreaSaving(fieldName, String(value));
    } else if (type === 'richtext') {
        assertRichTextSaving(fieldName, String(value));
    }
};
