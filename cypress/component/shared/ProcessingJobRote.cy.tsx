import {JobContext} from 'contexts';
import React from 'react';
import {MemoryRouter, Route, Routes} from 'react-router-dom';
import {ProcessingJobRoute} from 'shared';

const Test = () => {
    return <>Test Component</>;
};

describe('Job Route', () => {
    it('handles not processing jobs correctly', () => {
        const job = {status: 2, id: 1, name: 'Test job'};

        cy.mount(
            <MemoryRouter initialEntries={[{pathname: '/'}]}>
                <JobContext.Provider value={{job, isJobProcessing: true}}>
                    <Routes>
                        <Route path="/" element={<ProcessingJobRoute />}>
                            <Route path="/" element={<Test />} />
                        </Route>
                    </Routes>
                </JobContext.Provider>
            </MemoryRouter>
        );

        cy.contains('Test Component').should('exist');
    });
});
