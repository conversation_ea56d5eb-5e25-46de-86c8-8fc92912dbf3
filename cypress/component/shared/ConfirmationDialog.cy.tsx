import React, {useState, useCallback} from 'react';
import {ConfirmationDialog} from 'shared';
import {MockApp} from '../MockApp';

const TestConfirmationDialog = () => {
    const [show, setShow] = useState(true);

    const hideDialog = useCallback(() => {
        setShow(false);
    }, []);

    return (
        <ConfirmationDialog
            show={show}
            title="Test title"
            message="Test message"
            setShow={setShow}
            setConfirmed={cy.stub()}
            hideDialog={hideDialog}
            backdrop="static"
            options={{}}
        />
    );
};

const TestCustomButtonControlledBehaviour = () => {
    const [show, setShow] = useState(true);

    const hideDialog = useCallback(() => {
        setShow(false);
    }, []);

    return (
        <ConfirmationDialog
            show={show}
            title="Test title"
            message="Test message"
            setShow={setShow}
            setConfirmed={cy.stub()}
            hideDialog={hideDialog}
            backdrop="static"
            options={{
                buttons: [
                    {
                        title: 'Button 1',
                        name: 'Button 1',
                        action: cy.stub(),
                        show: true,
                        controlledHideDialog: true,
                    },
                    {
                        name: 'Button 2',
                        action: cy.stub(),
                        show: true,
                        controlledHideDialog: false,
                    },
                    {
                        name: 'Button 3',
                        action: cy.stub(),
                        show: false,
                        controlledHideDialog: false,
                    },
                ],
            }}
        />
    );
};

describe('ConfirmationDialog', () => {
    it('correctly renders title and text message', () => {
        cy.mount(
            <MockApp>
                <ConfirmationDialog
                    show={true}
                    title="Test title"
                    message="Test message"
                    setShow={cy.stub()}
                    setConfirmed={cy.stub()}
                    hideDialog={cy.stub()}
                    backdrop="static"
                    options={{}}
                />
            </MockApp>
        );

        cy.contains('Test title').should('exist');
        cy.contains('Test message').should('exist');

        cy.contains('button', 'Yes').should('exist');
        cy.contains('button', 'No').should('exist');
    });

    it('correctly renders title and html message', () => {
        cy.mount(
            <MockApp>
                <ConfirmationDialog
                    show={true}
                    title="Test title"
                    message="<div>Lorem ipsum dolor sit amet, consectetur adipiscing elit</div><div><ul><li>Ut at odio ac quam semper dignissim.</li><li>Ut at odio ac quam semper dignissim.</li></ul></div>"
                    setShow={cy.stub()}
                    setConfirmed={cy.stub()}
                    hideDialog={cy.stub()}
                    backdrop="static"
                    options={{}}
                />
            </MockApp>
        );

        cy.contains('Lorem ipsum dolor sit amet').should('exist');
        cy.get('ul').should('exist'); // Check if the list exists
        cy.get('ul li').should('have.length', 2); // Check if the list  has 2 items
    });

    it('correctly renders jsx message', () => {
        cy.mount(
            <MockApp>
                <ConfirmationDialog
                    show={true}
                    title="Test title"
                    message={
                        <>
                            <div>
                                Lorem ipsum dolor sit amet, consectetur
                                adipiscing elit
                            </div>
                            <div>
                                <ul>
                                    <li>
                                        Ut at odio ac quam semper dignissim.
                                    </li>
                                    <li>
                                        Ut at odio ac quam semper dignissim.
                                    </li>
                                    <li>
                                        Ut at odio ac quam semper dignissim.
                                    </li>
                                </ul>
                            </div>
                        </>
                    }
                    setShow={cy.stub()}
                    setConfirmed={cy.stub()}
                    hideDialog={cy.stub()}
                    backdrop="static"
                    options={{}}
                />
            </MockApp>
        );

        cy.contains('Lorem ipsum dolor sit amet').should('exist');
        cy.get('ul').should('exist'); // Check if the list exists
        cy.get('ul li').should('have.length', 3); // Check if the list  has 2 items
    });

    it('correctly hides dialog when hideDialog is called', () => {
        cy.mount(
            <MockApp>
                <TestConfirmationDialog />
            </MockApp>
        );

        cy.contains('Test title').should('exist');
        cy.contains('Test message').should('exist');

        cy.get('button').contains('Yes').click();
        cy.then(() => {
            cy.contains('Test title').should('not.exist');
            cy.contains('Test message').should('not.exist');
        });
    });

    it('correctly hides No button', () => {
        cy.mount(
            <MockApp>
                <ConfirmationDialog
                    show={true}
                    title="Test title"
                    message="Test message"
                    setShow={cy.stub()}
                    setConfirmed={cy.stub()}
                    hideDialog={cy.stub()}
                    backdrop="static"
                    options={{
                        hideNoButton: true,
                    }}
                />
            </MockApp>
        );

        cy.contains('button', 'Yes').should('exist');
        cy.contains('button', 'No').should('not.exist');
    });

    it('correctly hides entire footer', () => {
        cy.mount(
            <MockApp>
                <ConfirmationDialog
                    show={true}
                    title="Test title"
                    message="Test message"
                    setShow={cy.stub()}
                    setConfirmed={cy.stub()}
                    hideDialog={cy.stub()}
                    backdrop="static"
                    options={{
                        hideFooter: true,
                    }}
                />
            </MockApp>
        );

        cy.contains('Test title').should('exist');
        cy.contains('Test message').should('exist');

        cy.contains('button', 'Yes').should('not.exist');
        cy.contains('button', 'No').should('not.exist');
    });

    it('Correctly renders custom buttons', () => {
        const button1 = cy.stub();
        const button2 = cy.stub();

        cy.mount(
            <MockApp>
                <ConfirmationDialog
                    show={true}
                    title="Test title"
                    message="Test message"
                    setShow={cy.stub()}
                    setConfirmed={cy.stub()}
                    hideDialog={cy.stub()}
                    backdrop="static"
                    options={{
                        buttons: [
                            {
                                title: 'Button 1',
                                name: 'Button 1',
                                action: button1,
                                show: true,
                                controlledHideDialog: true,
                            },
                            {
                                name: 'Button 2',
                                action: button2,
                                show: true,
                                controlledHideDialog: true,
                            },
                        ],
                    }}
                />
            </MockApp>
        );

        cy.contains('button', 'Yes').should('exist');
        cy.contains('button', 'No').should('exist');

        cy.contains('button', 'Button 1').should('exist').click();
        cy.wrap(button1).should('have.been.called');

        cy.contains('button', 'Button 2').should('exist').click();
        cy.wrap(button2).should('have.been.called');
    });

    it('correctly assigns controlled behaviour to custom buttons', () => {
        cy.mount(
            <MockApp>
                <TestCustomButtonControlledBehaviour />
            </MockApp>
        );

        // show: false button is not visible
        cy.contains('button', 'Button 3').should('not.exist');

        // clicking on controlledHideDialog:true button does not automatically close
        // the dialog
        cy.contains('button', 'Button 1').should('exist').click();
        cy.then(() => {
            cy.contains('button', 'Yes').should('exist');
            cy.contains('button', 'No').should('exist');
        });

        // clicking on controlledHideDialog: false button automatically closes
        // the dialog
        cy.contains('button', 'Button 2').should('exist').click();
        cy.then(() => {
            cy.contains('button', 'Yes').should('not.exist');
            cy.contains('button', 'No').should('not.exist');
        });
    });
});
