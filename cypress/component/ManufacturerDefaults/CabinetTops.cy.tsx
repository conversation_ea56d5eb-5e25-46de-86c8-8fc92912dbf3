import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import ManufacturerDefaults from 'components/manufacturer/ManufacturerDefaults';
import assertSaving from '../shared/assertSaving';

describe('Cabinet Tops Section', () => {
    const testData = [
        {fieldName: 'cabinetTop24', label: 'SolidTop', expectedResult: false},
        {
            fieldName: 'cabinetTop25',
            label: 'Rail On Edge',
            expectedResult: false,
        },
        {
            fieldName: 'cabinetTop26',
            label: 'Rail On Flat',
            expectedResult: false,
        },
        {
            fieldName: 'cabinetTop174',
            label: 'Timber Rail On Flat',
            expectedResult: false,
        },
        {
            fieldName: 'cabinetTop175',
            label: 'Timber Rail On Edge',
            expectedResult: false,
        },
    ];

    before(() => {
        cy.brandingFixture();
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.viewport(1280, 720);
    });

    it('should display the Cabinet Tops Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Cabinet Tops').should('exist');
    });

    testData.forEach((test) => {
        it(`should be able to update Cabinet Top: ${test.label}`, () => {
            cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
                'saveApi'
            );
            cy.manufacturerDefaultsFixture('getManufacturerDefaults');
            cy.mount(
                <MockManufacturerApp redux notifications router>
                    <ManufacturerDefaults />
                </MockManufacturerApp>
            );
            cy.contains('label', test.label).should('exist');
            assertSaving('checkbox', test.fieldName, test.expectedResult);
        });
    });
});
