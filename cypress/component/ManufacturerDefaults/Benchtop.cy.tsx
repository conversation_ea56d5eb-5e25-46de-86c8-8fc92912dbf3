import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import ManufacturerDefaults from 'components/manufacturer/ManufacturerDefaults';
import assertSaving from '../shared/assertSaving';
import {clearManufacturerApiState} from '../../support/commands/removeRTKCache';

describe('Default Values Section', () => {
    before(() => {
        cy.brandingFixture();
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.viewport(1280, 720);
    });

    it('should display the Benchtop Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Benchtop').should('exist');
    });

    it('should be able to update Benchtop Markup', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'benchtopMarkup', 25);
    });

    it('should be able to update Allow Benchtop Module', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'allowBenchtopModule', false);
    });

    it('should not display bechtop section if benchtop is disabled', () => {
        clearManufacturerApiState();
        cy.intercept('GET', '/api/manufacturer/settings/defaults', {
            body: {
                isBenchtopEnabled: false,
                allCabinetTops: [],
                defaults: {},
            },
        });
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Benchtop').should('not.exist');
        cy.get(`input[name=benchtopMarkup]`).should('not.exist');
        cy.getCy(`checkbox-allowBenchtopModule-label`).should('not.exist');
    });
});
