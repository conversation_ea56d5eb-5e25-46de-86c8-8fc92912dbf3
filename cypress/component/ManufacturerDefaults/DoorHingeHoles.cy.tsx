import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import ManufacturerDefaults from 'components/manufacturer/ManufacturerDefaults';
import assertSaving from '../shared/assertSaving';

describe('Door Hinge Holes Section', () => {
    before(() => {
        cy.brandingFixture();
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.viewport(1280, 720);
    });

    it('should display the Door Hinge Holes Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Door Hinge Holes').should('exist');
    });

    it('should be able to update 3 Hinge Holes', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'hingeHole3', 600);
    });

    it('should be able to update 4 Hinge Holes', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'hingeHole4', 1200);
    });

    it('should be able to update 5 Hinge Holes', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'hingeHole5', 1800);
    });
});
