import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import ManufacturerDefaults from 'components/manufacturer/ManufacturerDefaults';
import assertSaving from '../shared/assertSaving';

describe('Adjustable Legs Section', () => {
    before(() => {
        cy.brandingFixture();
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.viewport(1280, 720);
    });

    it('should display the Adjustable Legs Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Adjustable Legs').should('exist');
    });

    it('should be able to update Enable Adjustable Legs', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'enableAdjustableLegs', false);
    });

    it('should be able to update Adjustable Legs Label', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('text', 'adjustableLegsLabel', 'Movable Legs');
    });

    it('should be able to update Adjustable Legs Description', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('textarea', 'adjustableLegsDescription', 'I have legs');
    });

    it('should be able to update No Adjustable Legs Label', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('text', 'noAdjustableLegsLabel', 'No legs');
    });

    it('should be able to update No Adjustable Legs Description', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving(
            'textarea',
            'noAdjustableLegsDescription',
            'No Adjustable legs Description'
        );
    });
});
