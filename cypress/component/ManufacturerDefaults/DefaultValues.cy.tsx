import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import ManufacturerDefaults from 'components/manufacturer/ManufacturerDefaults';
import assertSaving from '../shared/assertSaving';

describe('Default Values Section', () => {
    before(() => {
        cy.brandingFixture();
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.viewport(1280, 720);
    });

    it('should display the Default Values Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Default Values').should('exist');
    });

    it('should be able to update Default Filler Fix Depth', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'fillerFixDepth', 10);
    });

    it('should be able to update Default Filler Min Width', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'fillerMinWidth', 100);
    });

    it('should be able to update Drilling Cost', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'defaultDrillingCost', 70);
    });

    it('should be able to update Default Hinge Positions', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'defaultHingePositions', 95);
    });

    it('should be able to update Default Min Charge', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'defaultMinCharge', 90);
    });

    it('should be able to update Default Price Adjustment', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('number', 'defaultPriceAdjustment', 25);
    });

    it('should be able to update Allow Variation Requests', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving('checkbox', 'allowVariationRequests', false);
    });
});
