import React from 'react';
import {MockManufacturerApp} from '../MockManufacturerApp';
import ManufacturerDefaults from 'components/manufacturer/ManufacturerDefaults';
import assertSaving from '../shared/assertSaving';

describe('Job Submission Prompts', () => {
    before(() => {
        cy.brandingFixture();
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.viewport(1280, 720);
    });

    it('should display the Job Submission Prompts Header', () => {
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        cy.contains('Job Submission Prompts').should('exist');
    });

    it('should be able to update No Variations Prompt Text', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving(
            'richtext',
            'noVariationsText',
            'Hello World! No Variations!'
        );
    });

    it('should be able to update With Variations Prompt Text', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving(
            'richtext',
            'withVariationsText',
            'Hello World! With Variations!'
        );
    });

    it('should be able to update With Variations Accepted Prompt Text', () => {
        cy.manufacturerDefaultsFixture('saveManufacturerDefaults').as(
            'saveApi'
        );
        cy.manufacturerDefaultsFixture('getManufacturerDefaults');
        cy.mount(
            <MockManufacturerApp redux notifications router>
                <ManufacturerDefaults />
            </MockManufacturerApp>
        );
        assertSaving(
            'richtext',
            'withAcceptedVariationsText',
            'Hello World! Accepted!'
        );
    });
});
