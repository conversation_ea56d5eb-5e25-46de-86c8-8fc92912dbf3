import React from 'react';
import {
    Route,
    RouterProvider,
    createM<PERSON>oryRouter,
    createRoutesFromElements,
} from 'react-router-dom';
import {MockApp} from '../MockApp';
import {TestComponentOne} from './helper/TestComponentOne';
import {TestComponentTwo} from './helper/TestComponentTwo';

describe('NavigatorConfirmationDialog', () => {
    beforeEach(() => {
        cy.userFixture();
    });

    it('Should show confirmation dialog when route is changed', () => {
        const testFunction = cy.stub();

        const routes = createMemoryRouter(
            createRoutesFromElements(
                <Route path="/">
                    <Route
                        path="/one"
                        element={
                            <TestComponentOne testFunction={testFunction} />
                        }
                    />
                    <Route
                        path="/two"
                        element={
                            <TestComponentTwo testFunction={testFunction} />
                        }
                    />
                </Route>
            ),
            {
                initialEntries: ['/one'],
            }
        );

        cy.mount(
            <MockApp
                redux={true}
                router={false}
                application={true}
                notifications={true}>
                <RouterProvider router={routes} />
            </MockApp>
        );

        // Initial render should show Test 1 and link to page 2
        cy.contains('Test 1').should('exist');
        cy.contains('Page 2').should('exist');

        // supplied onclick is not clicked, should only be clicked when customer
        // clicks on proceed
        expect(testFunction).to.not.have.been.called;

        // clicking on the Page 2 link opens up the naviation confirmation dialog
        cy.get('a').click();
        cy.then(() => {
            // assert components of nagivation confirmation dialog
            cy.contains('Dialog Title').should('exist');
            cy.contains('Dialog Message').should('exist');
            cy.contains('Proceed').should('exist');
            cy.contains('Cancel').should('exist');
        });

        // clicking on cancel should hide elements of navigation confirmation dialog
        cy.get('.btn-primary').click();
        cy.then(() => {
            // assert that none of popup components are visible or exist on current page
            cy.contains('Dialog Title').should('not.exist');
            cy.contains('Dialog Message').should('not.exist');
            cy.contains('Proceed').should('not.exist');
            cy.contains('Cancel').should('not.exist');
        });

        // click on Page 2 link again
        cy.get('a').click();
        cy.then(() => {
            // assert popup is open
            cy.contains('Proceed').should('exist');
        });

        // click on proceed button in popup
        cy.get('.btn-danger').click();
        cy.then(() => {
            // assert the onClick function is called from within Confirmation dialog
            expect(testFunction).to.have.been.called;
            // assert second page is open now
            cy.contains('Test 2').should('exist');
            cy.contains('Page 1').should('exist');
        });
    });

    it('should not show confirmation dialog when route is changed', () => {
        const testFunction = cy.stub();

        const routes = createMemoryRouter(
            createRoutesFromElements(
                <Route path="/">
                    <Route
                        path="/one"
                        element={
                            <TestComponentOne testFunction={testFunction} />
                        }
                    />
                    <Route
                        path="/two"
                        element={
                            <TestComponentTwo testFunction={testFunction} />
                        }
                    />
                </Route>
            ),
            {
                initialEntries: ['/two'],
            }
        );

        cy.mount(
            <MockApp
                redux={true}
                router={false}
                application={true}
                notifications={true}>
                <RouterProvider router={routes} />
            </MockApp>
        );

        // Initial render should show Test 2 and link to page 1
        cy.contains('Test 2').should('exist');
        cy.contains('Page 1').should('exist');

        // click on Link, should directly go to first page without showing
        // a confirmation dialog
        cy.get('a').click();
        cy.then(() => {
            // assert the onClick function is not called from within Confirmation dialog
            expect(testFunction).to.not.have.been.called;
            // assert first page is open now
            cy.contains('Test 1').should('exist');
            cy.contains('Page 2').should('exist');
        });
    });
});
