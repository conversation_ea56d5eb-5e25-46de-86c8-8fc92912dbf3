import React from 'react';
import {NavigatorConfirmationDialog} from 'shared';
import {Link} from 'react-router-dom';

export const TestComponentOne = ({
    testFunction,
}: {
    testFunction: () => void;
}) => {
    return (
        <>
            <h1>Test 1</h1>
            <Link to="/two">Page 2</Link>
            <NavigatorConfirmationDialog
                showConfirmation={true}
                onClick={testFunction}
                title={'Dialog Title'}
                message={'Dialog Message'}
            />
        </>
    );
};
