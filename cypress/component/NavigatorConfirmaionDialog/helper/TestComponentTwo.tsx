import React from 'react';
import {NavigatorConfirmationDialog} from 'shared';
import {Link} from 'react-router-dom';

export const TestComponentTwo = ({
    testFunction,
}: {
    testFunction: () => void;
}) => {
    return (
        <>
            <h1>Test 2</h1>
            <Link to="/one">Page 1</Link>
            <NavigatorConfirmationDialog
                showConfirmation={false}
                onClick={testFunction}
                title={'Dialog Title'}
                message={'Dialog Message'}
            />
        </>
    );
};
