import React from 'react';
import {MockApp} from '../MockApp';
import {UpdateCenter} from 'components';

describe('UpdateCenter', () => {
    beforeEach(() => {
        cy.userFixture();
    });

    it("renders manufacturer's contact and news", () => {
        cy.mount(
            <MockApp application redux>
                <UpdateCenter />
            </MockApp>
        );

        cy.wait('@User Api Request');

        cy.get('button').contains('Ok').should('not.exist');
        cy.get('[title*="View more contact info"]').should('exist').click();
        cy.then(() => {
            cy.get('button').contains('Ok').should('exist').click();
        });

        // news is visible
        cy.contains(
            'News Lorem ipsum is placeholder text commonly used in the graphic'
        ).should('exist');
    });
});
