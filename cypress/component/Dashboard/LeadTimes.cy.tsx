import React from 'react';
import {LeadTimes} from 'components';
import {<PERSON>ckApp} from '../MockApp';
import {DashboardProvider} from 'contexts';

const leadtimes = [
    {
        name: 'Non Supply',
        flatpack: 3,
        assembled: 3,
    },
    {
        name: 'Acrylic',
        flatpack: 1,
        assembled: 2,
    },
    {
        name: 'Polyurethane / Paint / Raw',
        flatpack: 5,
        assembled: 8,
    },
    {
        name: 'Vinyl',
        flatpack: 1,
        assembled: 10,
    },
    {
        name: 'Melamine',
        flatpack: 2,
        assembled: 5,
    },
];

describe('Dashboard', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.intercept('GET', 'api/reporting/manufacturer/leadtimes', {
            success: 1,
            leadtimes,
        }).as('getLeadTimes');
    });

    it('Renders data correctly', () => {
        cy.mount(
            <MockApp redux application notifications>
                <DashboardProvider>
                    <LeadTimes />
                </DashboardProvider>
            </MockApp>
        );

        cy.wait('@getLeadTimes');
        cy.contains('Estimated Lead Times').should('exist');

        cy.contains('Non Supply').should('exist');
        cy.contains('Melamine').should('exist');

        cy.contains('Flatpack').should('exist');
        cy.contains('Assembled').should('exist');
    });
});
