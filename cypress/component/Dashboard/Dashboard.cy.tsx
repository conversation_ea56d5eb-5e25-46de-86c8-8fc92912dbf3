import React from 'react';
import {MockApp} from '../MockApp';
import {Dashboard} from 'components';
import {useTimeOfDay} from 'hooks';

const Job = {
    id: 130201,
    displayId: 140201,
    name: 'Test Job',
    cost: 410.***********,
    dateUpdatedDateTime: {
        date: '2020-05-05 14:11:40.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    dateDeliveryDateTime: {
        date: '2020-05-19 23:58:00.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    dateSubmittedDateTime: {
        date: '2020-05-05 13:57:42.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
    dateAcceptedDateTime: {
        date: '2020-05-05 14:11:40.000000',
        timezone_type: 3,
        timezone: 'Australia/Canberra',
    },
};

const leadTimes = [
    {
        name: 'Non Supply',
        flatpack: 3,
        assembled: 3,
    },
    {
        name: 'Acrylic',
        flatpack: 1,
        assembled: 2,
    },
    {
        name: 'Polyurethane / Paint / Raw',
        flatpack: 5,
        assembled: 8,
    },
    {
        name: 'Vinyl',
        flatpack: 1,
        assembled: 10,
    },
    {
        name: '<PERSON>amine',
        flatpack: 2,
        assembled: 5,
    },
];

describe('Dashboard', () => {
    beforeEach(() => {
        cy.userFixture();
        cy.intercept('GET', 'api/reporting/manufacturer/leadtimes', {
            success: 1,
            leadTimes,
        });
        cy.viewport(1280, 720);
    });

    it('renders all tables', () => {
        cy.intercept('GET', 'api/jobs/dashboard?limit=10', {
            success: 1,
            data: {
                pendingConfirmationJobs: [Job],
                productionJobs: [Job],
                quotedJobs: [Job],
                pendingApprovalJobs: [Job],
                completedJobs: [Job],
                pendingPaymentJobs: [Job],
            },
        }).as('dashboard-data-all');

        cy.mount(
            <MockApp redux router application notifications>
                <Dashboard />
            </MockApp>
        );

        cy.wait('@dashboard-data-all');

        cy.contains('Quoted Jobs').should('exist');
        cy.contains('Pending Approval Jobs').should('exist');
        cy.contains('Production Jobs').should('exist');
        cy.contains('Completed Jobs').should('exist');
        cy.contains('Pending Payment Jobs').should('exist');
    });

    it('renders available tables', () => {
        cy.intercept('GET', 'api/jobs/dashboard?limit=10', {
            success: 1,
            data: {
                pendingConfirmationJobs: [],
                productionJobs: [],
                quotedJobs: [Job],
                pendingApprovalJobs: [Job],
                completedJobs: [],
                pendingPaymentJobs: [],
            },
        }).as('dashboard-data');

        cy.mount(
            <MockApp redux router application notifications>
                <Dashboard />
            </MockApp>
        );

        cy.wait('@dashboard-data');

        cy.contains('Quoted Jobs').should('exist');
        cy.contains('Pending Approval Jobs').should('exist');
        cy.contains('Production Jobs').should('not.exist');
        cy.contains('Completed Jobs').should('not.exist');
        cy.contains('Pending Payment Jobs').should('not.exist');
    });

    it('has correct last job', () => {
        cy.intercept('GET', 'api/jobs/dashboard?limit=10', {
            success: 1,
            data: {
                pendingConfirmationJobs: [Job],
                productionJobs: [Job],
                quotedJobs: [Job],
                pendingApprovalJobs: [Job],
                completedJobs: [Job],
                pendingPaymentJobs: [Job],
            },
        }).as('dashboard-data-all');

        cy.mount(
            <MockApp redux router application notifications>
                <Dashboard />
            </MockApp>
        );

        cy.wait('@dashboard-data-all');

        const lastJobText = `Last Job: #${Job.displayId}`;

        cy.contains(lastJobText).should('exist');
    });

    it('has correct welcome message', () => {
        cy.intercept('GET', 'api/jobs/dashboard?limit=*', {
            success: 1,
            data: {
                pendingConfirmationJobs: [Job],
                productionJobs: [Job],
                quotedJobs: [Job],
                pendingApprovalJobs: [Job],
                completedJobs: [Job],
                pendingPaymentJobs: [Job],
            },
        }).as('dashboard-data-all');

        cy.mount(
            <MockApp redux router application notifications>
                <Dashboard />
            </MockApp>
        );

        const timeOfDay = useTimeOfDay();
        const message = `Good ${timeOfDay}, I Am Developer`;

        cy.contains(message).should('exist');
    });
});
