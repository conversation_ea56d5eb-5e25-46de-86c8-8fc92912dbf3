import React from 'react';
import {<PERSON>ckApp} from '../MockApp';
import {DashboardProvider} from 'contexts';
import {LeadTimes, MobileMenu} from 'components';

describe('MobileMenu', () => {
    beforeEach(() => {
        cy.userFixture();

        cy.intercept('GET', 'api/reporting/manufacturer/leadtimes', {
            success: 1,
            leadtimes: [
                {
                    name: 'Non Supply',
                    flatpack: 3,
                    assembled: 3,
                },
            ],
        }).as('getLeadTimes');

        cy.viewport('iphone-6');
    });

    it('Renders all menu items correctly', () => {
        cy.mount(
            <MockApp application redux>
                <DashboardProvider>
                    <MobileMenu />
                </DashboardProvider>
            </MockApp>
        );

        // this button is only visible if LeadTimes component is loaded
        cy.contains('Lead Times').should('not.exist');

        cy.contains('Updates & Contacts').should('exist');
        cy.contains('Quoted Jobs').should('exist');
        cy.contains('Pending Confirmation Jobs').should('exist');
        cy.contains('Pending Payment Jobs').should('exist');

        // these menus are only fixible after clicking on more options
        cy.contains('Pending Approval Jobs').should('not.exist');
        cy.contains('Production Jobs').should('not.exist');
        cy.contains('Completed Jobs').should('not.exist');

        cy.contains('More Options').should('exist').click();

        cy.then(() => {
            cy.contains('Pending Approval Jobs').should('exist');
            cy.contains('Production Jobs').should('exist');
            cy.contains('Completed Jobs').should('exist');
        });
    });

    it('Renders Lead Times menu', () => {
        cy.mount(
            <MockApp application redux>
                <DashboardProvider>
                    <>
                        <LeadTimes />
                        <MobileMenu />
                    </>
                </DashboardProvider>
            </MockApp>
        );

        cy.wait('@getLeadTimes');

        // this button is only visible if LeadTimes component is loaded
        cy.contains('Lead Times').should('exist');
    });
});
