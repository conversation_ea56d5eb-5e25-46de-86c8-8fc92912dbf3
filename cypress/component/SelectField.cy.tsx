import React from 'react';
import <PERSON><PERSON>ield from 'shared/components/SelectField';
import {MockApp} from './MockApp';
import {Formik} from 'formik';

const doNothing = () => {
    // doNothing
};
describe('SelectField', () => {
    it('should display the placeholder if no initial value', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: null}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        options={[
                            {value: '1', label: 'Orange'},
                            {value: '2', label: 'Lemon'},
                        ]}
                        placeholder="Fancy Select"
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').should('contain.text', 'Fancy Select');
    });

    it('should display the initial value if any', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: '1'}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        options={[
                            {value: '1', label: 'Orange'},
                            {value: '2', label: 'Lemon'},
                        ]}
                        placeholder="Fancy Select"
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').should('contain.text', 'Orange');
    });

    it('should display the first value if defaultFirstValue is selected and current value is not in the options', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: '0'}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        defaultFirstValue
                        options={[
                            {value: '1', label: 'Mango'},
                            {value: '2', label: 'Lemon'},
                        ]}
                        placeholder="Fancy Select"
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').should('contain.text', 'Mango');
    });

    it('should be able to search from the options', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: '1'}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        options={[
                            {value: '1', label: 'Orange'},
                            {value: '2', label: 'Lemon'},
                            {value: '3', label: 'Apple'},
                            {value: '4', label: 'Pineapple'},
                        ]}
                        placeholder="Fancy Select"
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').click();
        cy.get('[role=option]').should('have.length', 4);
        cy.get('#testComponent').type('apple');
        cy.get('[role=option]').should('have.length', 2);
        cy.get('#testComponent').contains('[role=option]', 'Pineapple').click();
        cy.get('#testComponent').should('contain.text', 'Pineapple');
    });

    it('should display the disabled option but not clickable', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: '1'}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        options={[
                            {value: '1', label: 'Orange'},
                            {value: '2', label: 'Lemon'},
                            {value: '3', label: 'Tomato', disabled: true},
                        ]}
                        placeholder="Fancy Select"
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').click();
        cy.get('#testComponent')
            .contains('[role=option]', 'Tomato')
            .should('have.attr', 'aria-disabled', 'true')
            .click();
        cy.get('#testComponent').click();
        cy.get('#testComponent').should('not.contain.text', 'Tomato');
    });

    it('should be able to use multi select', () => {
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: null}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        options={[
                            {value: '1', label: 'Orange', color: 'orange'},
                            {value: '2', label: 'Lemon', color: 'yellow'},
                            {value: '3', label: 'Tomato', color: 'red'},
                        ]}
                        placeholder="Fancy Select"
                        isMulti
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').click();
        cy.get('#testComponent').contains('[role=option]', 'Orange').click();
        cy.get('#testComponent').click();
        cy.get('#testComponent').contains('[role=option]', 'Tomato').click();
        cy.get('#testComponent').should('contain.text', 'Orange');
        cy.get('#testComponent').should('contain.text', 'Tomato');
    });

    it('should be able to use async mode', () => {
        const defaultOptions = [
            {value: '1', label: 'Orange'},
            {value: '2', label: 'Lemon'},
            {value: '3', label: 'Tomato'},
            {value: '4', label: 'Apple'},
            {value: '5', label: 'Pineapple'},
        ];
        const getOptions = (inputValue: string) => {
            return defaultOptions.filter((i) =>
                i.label.toLowerCase().includes(inputValue.toLowerCase())
            );
        };

        const loadOptions = (
            inputValue: string,
            callback: (options: {value: string; label: string}[]) => void
        ) => {
            setTimeout(() => {
                callback(getOptions(inputValue));
            }, 500);
        };
        cy.mount(
            <MockApp
                redux={true}
                router={true}
                application={true}
                notifications={true}>
                <Formik
                    initialValues={{testComponent: null}}
                    validateOnBlur={false}
                    onSubmit={doNothing}>
                    <SelectField
                        id="testComponent"
                        name="testComponent"
                        placeholder="Fancy Select"
                        // eslint-disable-next-line react/jsx-no-bind
                        loadOptions={loadOptions}
                        defaultOptions
                        asyncMode
                    />
                </Formik>
            </MockApp>
        );
        cy.get('#testComponent').click();
        cy.get('[role=option]').should('have.length', 5);
        cy.get('#testComponent').type('orange');
        cy.get('[role=option]').should('have.length', 1);
    });
});
