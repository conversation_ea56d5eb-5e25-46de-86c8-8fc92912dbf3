export const xeroFixture = () => {
    cy.intercept('GET', '/api/manufacturer/xeroSettings', {
        fixture: 'xeroSettings.json',
    }).as('Xero Settings Api Request');

    cy.intercept('GET', 'api/xero/connection_status', {
        success: 1,
        status: {is_connected: true, is_enabled: 1},
    }).as('Xero Connection Api Request');

    cy.intercept('GET', 'api/xero/tenant_details', {
        success: 1,
        tenant_details: [
            {
                tenantType: 'ORGANISATION',
                tenantName: 'Demo Company (AU)',
            },
        ],
    }).as('Xero Tenant Api Request');

    cy.intercept('GET', 'api/xero/authorize', {
        success: 1,
        url: 'https://localhost:3000/xero/authorize',
    }).as('Xero Connect Api Request');

    cy.intercept('POST', 'api/xero/disconnect', {
        success: 1,
        message: 'Xero Disconnected',
    }).as('Xero Disconnect Api Request');

    cy.intercept('GET', 'api/manufacturer/syncContactStatus', {
        success: 1,
        status: false,
    }).as('Xero Sync Contact Api Request');

    cy.intercept('GET', 'api/xero/branding_themes', {
        fixture: 'xeroBrandingThemes.json',
    }).as('Xero Branding Themes Api Request');

    cy.intercept('GET', 'api/xero/account_details', {
        fixture: 'xeroAccountDetails.json',
    }).as('Xero Account Details Api Request');

    cy.intercept('POST', 'api/manufacturer/xeroSettings', {
        success: 1,
    }).as('Xero Settings Save API Request');
};
