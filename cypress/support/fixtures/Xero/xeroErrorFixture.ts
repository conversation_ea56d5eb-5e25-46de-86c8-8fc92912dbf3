export const xeroErrorFixture = () => {
    cy.intercept('GET', 'manufacturer/xeroSettings', {
        success: false,
        message: 'Xero Settings not found',
    }).as('Xero Settings Api Request');

    cy.intercept('GET', 'api/xero/connection_status', {
        success: 1,
        status: {is_connected: true, is_enabled: 1},
    }).as('Xero Connection Api Request');

    cy.intercept('GET', 'api/xero/tenant_details', {
        success: 1,
        tenant_details: [
            {
                tenantType: 'ORGANISATION',
                tenantName: 'Demo Company (AU)',
            },
        ],
    }).as('Xero Tenant Api Request');
};
