export const qfpFixture = () => {
    cy.intercept('GET', 'api/cabinets/quickflatproducts', {
        fixture: 'productStructures/productTypes.json',
    }).as('QFP Product Types API Request');
    cy.intercept('GET', 'api/jobcabinets/room/*/quickflatproducts', {
        success: 1,
        existing_quick_flat_products: [],
    }).as('Existing QFP Products API Request');
    cy.intercept('POST', 'api/cabinets/jobcabinet/cost', {
        success: 1,
        job_cabinet_cost: 0.0,
    }).as('QFP Cabinet Cost API Request');
};
