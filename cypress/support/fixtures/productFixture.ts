export const productFixture = (product: number, room: number) => {
    cy.intercept('GET', `api/cabinets/data/fields/${product}`, {
        fixture: `productStructures/${product}.json`,
    }).as('Product Structure API Request');
    cy.intercept(
        'GET',
        `api/cabinets/jobcabinet/cabinetType/${product}/roomId/${room}`,
        {fixture: `defaultProducts/${product}.json`}
    ).as('Default Product API Request');
    cy.intercept('GET', `/api/cabinets/formfields/${product}`, {
        fixture: `formFields/${product}.json`,
    }).as('Form Fields Defaults API Request');
    cy.intercept('GET', 'api/cabinets/productStyles', {
        fixture: 'productStyles.json',
    }).as('Product Styles');
};
