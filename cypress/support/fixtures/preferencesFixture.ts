interface Args {
    queryParams?: Record<string, string>;
}

const commands: {
    [key: string]: (args?: Args) => Cypress.Chainable;
} = {
    getPreferences: () =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/manufacturer/settings/preferences`,
            },
            {
                fixture: `preferences/all.json`,
            }
        ),
    savePreferences: () =>
        cy.intercept(
            {
                method: 'POST',
                pathname: `/api/manufacturer/settings/preferences`,
            },
            {
                fixture: `success.json`,
            }
        ),
};

export const preferencesFixture = (
    method: string,
    args?: Args
): Cypress.Chainable => commands[String(method)](args);
