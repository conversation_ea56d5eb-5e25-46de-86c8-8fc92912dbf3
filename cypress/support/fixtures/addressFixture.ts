interface Args {
    queryParams?: Record<string, string>;
}

const commands: {
    [key: string]: (args: Args) => Cypress.Chainable;
} = {
    getPredictions: (args: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/address/search`,
                query: args.queryParams,
            },
            {
                fixture: `address/predictions.json`,
            }
        ),
    getPredictionDetail: (args: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/address/details`,
                query: args.queryParams,
            },
            {
                fixture: `address/prediction-${args?.queryParams?.place_id}.json`,
            }
        ),
};

export const addressFixture = (method: string, args: Args): Cypress.Chainable =>
    commands[String(method)](args);
