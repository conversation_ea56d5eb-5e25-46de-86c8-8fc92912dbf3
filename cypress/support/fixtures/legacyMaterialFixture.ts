export const PANEL_90 = 350;
export const PANEL_180 = 378;
export const MICROWAVE_FRAME = 337;
export const ROLLER_FRAME = 338;
export const DOOR = 339;
export const DRAWER = 352;
export const DRAWER_PACK = 347;
export const PATTERNED_PANEL_180 = 357;

export const legacyMaterialFixture = (product: number) => {
    // Non advanced materials
    let doorFixture = 'materials/legacy/nonAdvanced/doorStyles.json';

    if (product == ROLLER_FRAME) {
        doorFixture = 'materials/legacy/nonAdvanced/doorStylesFrame.json';
    }

    cy.intercept(
        'GET',
        `api/materials/type/6/cabinetId/${product}/doorStyles`,
        {
            fixture: doorFixture,
        }
    ).as('Non Advanced Door Styles API Request');
    cy.intercept(
        'GET',
        `api/materials/type/6/doorFilter/NZPG%20Flat%20Panel/cabinetId/${product}/materialBrands`,
        {fixture: 'materials/legacy/nonAdvanced/brands.json'}
    ).as('Non Advanced Material Brands API Request');
    cy.intercept(
        'GET',
        `api/materials/type/6/brand/109/doorFilter/NZPG%20Flat%20Panel/cabinetId/${product}/materialFinishes`,
        {fixture: 'materials/legacy/nonAdvanced/finishes.json'}
    ).as('Non Advanced Material Finishes API Request');
    cy.intercept(
        'GET',
        `api/materials/type/6/brand/109/finish/Gloss/doorFilter/NZPG%20Flat%20Panel/cabinetId/${product}/materialSubstrates`,
        {fixture: 'materials/legacy/nonAdvanced/substrates.json'}
    ).as('Non Advanced Material Substrates API Request');
    cy.intercept(
        'GET',
        `api/materials/type/6/brand/109/finish/Gloss/substrate/MR%20MDF/doorFilter/NZPG%20Flat%20Panel/cabinetId/${product}/materialColours`,
        {fixture: 'materials/legacy/nonAdvanced/colours.json'}
    ).as('Non Advanced Material Colours API Request');
    cy.intercept(
        'GET',
        `api/materials/edge/3476/cabinetId/${product}/edgeColours`,
        {fixture: 'materials/legacy/nonAdvanced/edgeColours.json'}
    ).as('Non Advanced Edge Colours API Request');
    cy.intercept(
        'GET',
        `api/materials/type/6/doorFilter/NZPG%20Flat%20Panel/cabinetId/${product}/edgeColours`,
        {fixture: 'materials/legacy/nonAdvanced/edgeColours.json'}
    ).as('Non Advanced Edge Colours API Request');
    cy.intercept('GET', `api/edgefinishes/material/16163/edge/3476/door/328`, {
        fixture: 'materials/legacy/nonAdvanced/edgeFinishes.json',
    }).as('Non Advanced Edge Finishes API Request');
    cy.intercept('GET', `api/edgefinishes/material/16163/edge/3476/door/492`, {
        fixture: 'materials/legacy/nonAdvanced/edgeFinishes.json',
    }).as('Non Advanced Edge Finishes API Request');
    // Non advanced materials

    // Advanced materials
    cy.intercept(
        'GET',
        `api/materials/type/17/cabinetId/${product}/materialBrands`,
        {
            fixture: 'materials/legacy/advanced/brands.json',
        }
    ).as('Advanced Material Brands API Request');
    cy.intercept(
        'GET',
        `api/materials/type/17/doorFilter/NZPG%20Vinyl/cabinetId/${product}/materialBrands`,
        {
            fixture: 'materials/legacy/advanced/brands.json',
        }
    ).as('Advanced Material Brands API Request');
    cy.intercept(
        'GET',
        `api/materials/type/17/door/328/cabinetId/${product}/doorStyles`,
        {doorStyles: [], success: 1}
    ).as('Advanced Door Styles API Request for selected Door');

    let doorStylesFixture = 'materials/legacy/advanced/doorStyles.json';
    if (product == PANEL_90 || product == PANEL_180) {
        doorStylesFixture = 'materials/legacy/advanced/doosStylesPanel90.json';
    } else if (product == MICROWAVE_FRAME) {
        doorStylesFixture = 'materials/legacy/advanced/doorStylesFrame.json';
    }

    cy.intercept(
        'GET',
        `api/materials/type/17/cabinetId/${product}/doorStyles`,
        {
            fixture: doorStylesFixture,
        }
    ).as('Advanced Door Styles API Request');
    cy.intercept(
        'GET',
        `api/materials/type/17/brand/87/cabinetId/${product}/doorCategories`,
        {fixture: 'materials/legacy/advanced/doorCategories.json'}
    ).as('Advanced Door Categories API Request');
    cy.intercept(
        'GET',
        `api/materials/type/17/brand/87/doorFilter/NZPG%20Vinyl/cabinetId/${product}/materialFinishes`,
        {
            fixture: 'materials/legacy/advanced/finishes.json',
        }
    ).as('Advanced Material Finishes API Request');
    cy.intercept(
        'GET',
        `api/materials/type/17/brand/87/finish/Matt/doorFilter/NZPG%20Vinyl/cabinetId/${product}/materialSubstrates`,
        {
            fixture: 'materials/legacy/advanced/substrates.json',
        }
    ).as('Advanced Material Substrates API Request');
    cy.intercept(
        'GET',
        `api/materials/type/17/brand/87/finish/Matt/substrate/MR%20MDF/doorFilter/NZPG%20Vinyl/cabinetId/${product}/materialColours`,
        {
            fixture: 'materials/legacy/advanced/colours.json',
        }
    ).as('Advanced Material Colours API Request');

    let edgeColoursFixture = 'materials/legacy/advanced/edgeColours.json';

    if (product == PANEL_90) {
        edgeColoursFixture =
            'materials/legacy/advanced/edgeColoursPanel90.json';
    } else if (product == PANEL_180) {
        edgeColoursFixture =
            'materials/legacy/advanced/edgeColoursPanel180.json';
    }

    cy.intercept(
        'GET',
        `api/materials/type/17/doorFilter/NZPG%20Vinyl/cabinetId/${product}/edgeColours`,
        {
            fixture: edgeColoursFixture,
        }
    ).as('Advanced Edge Colours API Request');

    cy.intercept('GET', `api/edgefinishes/material/22711/edge/6527/door/586`, {
        fixture: 'materials/legacy/advanced/edgeFinishes.json',
    }).as('Advanced Edge Finishes API Request');
    cy.intercept('GET', `api/edgefinishes/material/22711/edge/5675/door/588`, {
        fixture: 'materials/legacy/advanced/edgeFinishes.json',
    }).as('Advanced Edge Finishes API Request');
    cy.intercept('GET', `api/edgefinishes/material/22711/edge/5681/door/586`, {
        fixture: 'materials/legacy/advanced/edgeFinishes.json',
    }).as('Advanced Edge Finishes API Request');
    cy.intercept('GET', `api/edgefinishes/material/22711/edge/5675/door/491`, {
        fixture: 'materials/legacy/advanced/edgeFinishes.json',
    }).as('Advanced Edge Finishes API Request');
    // Advanced materials
};
