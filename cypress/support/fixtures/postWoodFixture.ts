interface Args {
    queryParams?: Record<string, string>;
}

const commands: {
    [key: string]: (args: Args) => Cypress.Chainable;
} = {
    getGlobals: (args: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/postwood/woodcam-globals`,
                query: args.queryParams,
            },
            {
                fixture: `postWood/globals.json`,
            }
        ),
    getToolNumbers: (args: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/postwood/woodcam-tool-numbers`,
                query: args.queryParams,
            },
            {
                fixture: `postWood/toolNumbers.json`,
            }
        ),
    getEdgeInformation: (args: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/postwood/woodcam-edge-information`,
                query: args.queryParams,
            },
            {
                fixture: `postWood/edgeInformation.json`,
            }
        ),
};

export const postwoodFixture = (
    method: string,
    args: Args
): Cypress.Chainable => commands[String(method)](args);
