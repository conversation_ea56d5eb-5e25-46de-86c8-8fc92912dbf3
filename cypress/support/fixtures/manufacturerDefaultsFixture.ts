interface Args {
    queryParams?: Record<string, string>;
}

const commands: {
    [key: string]: (args?: Args) => Cypress.Chainable;
} = {
    getManufacturerDefaultsByID: ({queryParams}) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/manufacturer/${queryParams.id}/defaults`,
            },
            {
                fixture: `manufacturerDefaults/${queryParams.id}.json`,
            }
        ),
    getManufacturerDefaults: () =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/manufacturer/settings/defaults`,
            },
            {
                fixture: `manufacturerDefaults/defaults.json`,
            }
        ),
    saveManufacturerDefaults: () =>
        cy.intercept(
            {
                method: 'POST',
                pathname: `/api/manufacturer/settings/defaults`,
            },
            {
                fixture: `success.json`,
            }
        ),
};

export const manufacturerDefaultsFixture = (
    method: string,
    args?: Args
): Cypress.Chainable => commands[String(method)](args);
