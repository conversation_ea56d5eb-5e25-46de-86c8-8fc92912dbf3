interface Args {
    queryParams?: Record<string, string>;
}

const commands: {
    [key: string]: (args?: Args) => Cypress.Chainable;
} = {
    getShapes: () =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/shapes`,
            },
            {
                fixture: `benchtop/shapes.json`,
            }
        ),
    getJoins: () =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/joins`,
            },
            {
                fixture: `benchtop/joins.json`,
            }
        ),
    getTypes: (args?: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/types`,
                query: args.queryParams,
            },
            {
                fixture: `benchtop/types.json`,
            }
        ),
    getMaterials: (args?: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/materials`,
                query: args.queryParams,
            },
            {
                fixture: `benchtop/materials.json`,
            }
        ),
    getSubstrateThickness: (args?: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/materials/substrate-thicknesses`,
                query: args.queryParams,
            },
            {
                fixture: `benchtop/thicknesses.json`,
            }
        ),
    getEdgeProfiles: (args?: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/edge/profiles`,
                query: args.queryParams,
            },
            {
                fixture: `benchtop/profiles.json`,
            }
        ),
    getSales: (args?: Args) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/benchtop/sales/items`,
                query: args.queryParams,
            },
            {
                fixture: `benchtop/sales.json`,
            }
        ),
};

export const benchtopFixture = (
    method: string,
    args?: Args
): Cypress.Chainable => commands[String(method)](args);
