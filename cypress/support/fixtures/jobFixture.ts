export const JOB = 310367; // displyID
export const ROOM = 374038;
export const JOB_ID = 300367; // ID

// TODO: We are simply using single job.json fixture for now
// later take id as parameter and return fixture based on id
export const jobFixture = () => {
    return cy.intercept('GET', /\/api\/jobs\/\d+/, {fixture: 'job.json'});
};

export const createJobFixture = () => {
    cy.intercept('POST', '/api/jobs', {fixture: 'job/createJob.json'});
};
