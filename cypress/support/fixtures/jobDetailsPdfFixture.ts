interface Args {
    queryParams?: Record<string, string>;
}

const commands: {
    [key: string]: (args?: Args) => Cypress.Chainable;
} = {
    getPreviewDetails: ({queryParams}) =>
        cy.intercept(
            {
                method: 'GET',
                pathname: `/api/job/${queryParams.id}/preview/details`,
            },
            {
                fixture: `pdf/jobDetails-${queryParams.id}.json`,
            }
        ),
};

export const jobDetailsPdfFixture = (
    method: string,
    args?: Args
): Cypress.Chainable => commands[String(method)](args);
