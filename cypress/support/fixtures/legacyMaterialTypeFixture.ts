export const legacyMaterialTypeFixture = (
    product: number,
    restricted = false
) => {
    // NOTE: I am not sure if this is the right thing to do.
    // Did not know how else to handle restriction with basically same api
    if (restricted) {
        cy.intercept(
            'GET',
            `api/materials/cabinetId/${product}/hasDoor/types`,
            {
                fixture: 'materials/legacy/restrictedTypes.json',
            }
        ).as('Material Types API Request');
        return;
    }
    cy.intercept('GET', `api/materials/cabinetId/${product}/hasDoor/types`, {
        fixture: 'materials/legacy/types.json',
    }).as('Material Types API Request');
};
