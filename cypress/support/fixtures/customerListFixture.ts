export const customerListFixture = (
    filter = 'all',
    query: Record<string, string> = {
        current_page: '1',
        page_size: '25',
        sort_orders: 'created_date DESC',
        filter_groups: '((hidden:equals:0))AND((gfp:equals:0))',
    },
    delay?: number
) =>
    cy.intercept(
        {
            method: 'GET',
            pathname: `/api/customers`,
            query,
        },
        {
            fixture: `customerList/${filter}.json`,
            delay,
        }
    );
