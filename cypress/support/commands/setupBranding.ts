type Branding = {[key: string]: string};

const branding: Branding = {
    primary_colour: '#204380',
    secondary_colour: '#1793cd',
    header_colour: '#deeaf8',
    menu_primary_colour: '#37383a',
    menu_secondary_colour: '#000000',
};

export const setupBranding = () => {
    cy.document().then((doc) => {
        if (doc) {
            Object.keys(branding).forEach((key: string) => {
                doc.documentElement.style.setProperty(
                    `--${key}`,
                    branding[String(key)]
                );
            });
        }
    });
};
