import {mount} from 'cypress/react18';
import {getCy} from './commands/getCy';
import {userFixture} from './fixtures/userFixture';
import {jobFixture, createJobFixture} from './fixtures/jobFixture';
import {jobCostFixture} from './fixtures/jobCostFixture';
import {qfpFixture} from './fixtures/qfpFixture';
import {productFixture} from './fixtures/productFixture';
import {legacyMaterialFixture} from './fixtures/legacyMaterialFixture';
import {legacyMaterialTypeFixture} from './fixtures/legacyMaterialTypeFixture';
import {jobStatisticsFixture} from './fixtures/jobStatisticsFixture';
import {jobStatusFixture} from './fixtures/jobStatusFixture';
import {xeroFixture} from './fixtures/Xero/xeroFixture';
import {xeroErrorFixture} from './fixtures/Xero/xeroErrorFixture';
import {configurationFixture} from './fixtures/configurationFixture';
import {brandingFixture} from './fixtures/brandingFixture';
import {customerListFixture} from './fixtures/customerListFixture';
import {customerFixture} from './fixtures/customerFixture';
import {adminDepotFixture} from './fixtures/adminDepotFixture';
import {adminManufacturersFixture} from './fixtures/adminManufacturersFixture';
import {labelsFixture} from './fixtures/labelsFixture';
import {depotsFixture} from './fixtures/depotsFixture';
import {exportCustomerFixture} from './fixtures/exportCustomerFixture';
import {deliveryAddressFixture} from './fixtures/deliveryAddressFixture';
import {extraVariationFixture} from './fixtures/extraVariationFixture';
import {manufacturerDefaultsFixture} from './fixtures/manufacturerDefaultsFixture';
import {manufacturerCountryFixture} from './fixtures/manufacturerCountry';
import {setupBranding} from './commands/setupBranding';
import {postwoodFixture} from './fixtures/postWoodFixture';
import {preferencesFixture} from './fixtures/preferencesFixture';
import {benchtopFixture} from './fixtures/benchtopFixture';
import {jobCabinetVariationFixture} from './fixtures/jobCabinetVariationFixture';
import {jobDetailsPdfFixture} from './fixtures/jobDetailsPdfFixture';
import {addressFixture} from './fixtures/addressFixture';

declare global {
    namespace Cypress {
        interface Chainable {
            getCy: typeof getCy;
            userFixture: typeof userFixture;
            jobFixture: typeof jobFixture;
            createJobFixture: typeof createJobFixture;
            jobCostFixture: typeof jobCostFixture;
            deliveryAddressFixture: typeof deliveryAddressFixture;
            extraVariationFixture: typeof extraVariationFixture;
            qfpFixture: typeof qfpFixture;
            productFixture: typeof productFixture;
            legacyMaterialFixture: typeof legacyMaterialFixture;
            legacyMaterialTypeFixture: typeof legacyMaterialTypeFixture;
            jobStatisticsFixture: typeof jobStatisticsFixture;
            jobStatusFixture: typeof jobStatusFixture;
            configurationFixture: typeof configurationFixture;
            mount: typeof mount;
            xeroFixture: typeof xeroFixture;
            xeroErrorFixture: typeof xeroErrorFixture;
            brandingFixture: typeof brandingFixture;
            customerListFixture: typeof customerListFixture;
            customerFixture: typeof customerFixture;
            adminDepotFixture: typeof adminDepotFixture;
            adminManufacturersFixture: typeof adminManufacturersFixture;
            labelsFixture: typeof labelsFixture;
            depotsFixture: typeof depotsFixture;
            exportCustomerFixture: typeof exportCustomerFixture;
            manufacturerDefaultsFixture: typeof manufacturerDefaultsFixture;
            manufacturerCountryFixture: typeof manufacturerCountryFixture;
            setupBranding: typeof setupBranding;
            postwoodFixture: typeof postwoodFixture;
            preferencesFixture: typeof preferencesFixture;
            benchtopFixture: typeof benchtopFixture;
            jobCabinetVariationFixture: typeof jobCabinetVariationFixture;
            jobDetailsPdfFixture: typeof jobDetailsPdfFixture;
            addressFixture: typeof addressFixture;
        }
    }
}
