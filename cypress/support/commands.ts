// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
// declare global {
//   namespace Cypress {
//     interface Chainable {
//       login(email: string, password: string): Chainable<void>
//       drag(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       dismiss(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       visit(originalFn: CommandOriginalFn, url: string, options: Partial<VisitOptions>): Chainable<Element>
//     }
//   }
// }

import 'cypress-wait-until';
import {getCy} from './commands/getCy';
import {jobFixture, createJobFixture} from './fixtures/jobFixture';
import {jobCostFixture} from './fixtures/jobCostFixture';
import {qfpFixture} from './fixtures/qfpFixture';
import {userFixture} from './fixtures/userFixture';
import {productFixture} from './fixtures/productFixture';
import {legacyMaterialFixture} from './fixtures/legacyMaterialFixture';
import {legacyMaterialTypeFixture} from './fixtures/legacyMaterialTypeFixture';
import {jobStatisticsFixture} from './fixtures/jobStatisticsFixture';
import {jobStatusFixture} from './fixtures/jobStatusFixture';
import {xeroFixture} from './fixtures/Xero/xeroFixture';
import {xeroErrorFixture} from './fixtures/Xero/xeroErrorFixture';
import {configurationFixture} from './fixtures/configurationFixture';
import {brandingFixture} from './fixtures/brandingFixture';
import {customerListFixture} from './fixtures/customerListFixture';
import {customerFixture} from './fixtures/customerFixture';
import {adminDepotFixture} from './fixtures/adminDepotFixture';
import {adminManufacturersFixture} from './fixtures/adminManufacturersFixture';
import {labelsFixture} from './fixtures/labelsFixture';
import {depotsFixture} from './fixtures/depotsFixture';
import {exportCustomerFixture} from './fixtures/exportCustomerFixture';
import {deliveryAddressFixture} from './fixtures/deliveryAddressFixture';
import {extraVariationFixture} from './fixtures/extraVariationFixture';
import {manufacturerDefaultsFixture} from './fixtures/manufacturerDefaultsFixture';
import {manufacturerCountryFixture} from './fixtures/manufacturerCountry';
import {setupBranding} from './commands/setupBranding';
import {postwoodFixture} from './fixtures/postWoodFixture';
import {preferencesFixture} from './fixtures/preferencesFixture';
import {benchtopFixture} from './fixtures/benchtopFixture';
import {jobCabinetVariationFixture} from './fixtures/jobCabinetVariationFixture';
import {jobDetailsPdfFixture} from './fixtures/jobDetailsPdfFixture';
import {addressFixture} from './fixtures/addressFixture';

Cypress.Commands.add('getCy', getCy);
Cypress.Commands.add('userFixture', userFixture);
Cypress.Commands.add('jobFixture', jobFixture);
Cypress.Commands.add('jobCostFixture', jobCostFixture);
Cypress.Commands.add('deliveryAddressFixture', deliveryAddressFixture);
Cypress.Commands.add('extraVariationFixture', extraVariationFixture);
Cypress.Commands.add('qfpFixture', qfpFixture);
Cypress.Commands.add('productFixture', productFixture);
Cypress.Commands.add('legacyMaterialFixture', legacyMaterialFixture);
Cypress.Commands.add('legacyMaterialTypeFixture', legacyMaterialTypeFixture);
Cypress.Commands.add('jobStatisticsFixture', jobStatisticsFixture);
Cypress.Commands.add('jobStatusFixture', jobStatusFixture);
Cypress.Commands.add('xeroFixture', xeroFixture);
Cypress.Commands.add('xeroErrorFixture', xeroErrorFixture);
Cypress.Commands.add('configurationFixture', configurationFixture);
Cypress.Commands.add('brandingFixture', brandingFixture);
Cypress.Commands.add('customerListFixture', customerListFixture);
Cypress.Commands.add('customerFixture', customerFixture);
Cypress.Commands.add('adminDepotFixture', adminDepotFixture);
Cypress.Commands.add('adminManufacturersFixture', adminManufacturersFixture);
Cypress.Commands.add('labelsFixture', labelsFixture);
Cypress.Commands.add('depotsFixture', depotsFixture);
Cypress.Commands.add('exportCustomerFixture', exportCustomerFixture);
Cypress.Commands.add(
    'manufacturerDefaultsFixture',
    manufacturerDefaultsFixture
);
Cypress.Commands.add('manufacturerCountryFixture', manufacturerCountryFixture);
Cypress.Commands.add('setupBranding', setupBranding);
Cypress.Commands.add('postwoodFixture', postwoodFixture);
Cypress.Commands.add('preferencesFixture', preferencesFixture);
Cypress.Commands.add('benchtopFixture', benchtopFixture);
Cypress.Commands.add('jobCabinetVariationFixture', jobCabinetVariationFixture);
Cypress.Commands.add('createJobFixture', createJobFixture);
Cypress.Commands.add('jobDetailsPdfFixture', jobDetailsPdfFixture);
Cypress.Commands.add('addressFixture', addressFixture);
