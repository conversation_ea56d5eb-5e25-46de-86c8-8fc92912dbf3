{"success": 1, "data": {"id": 300367, "customerId": 928, "clientId": -1, "status": 0, "name": "test yolo", "description": "", "endCustomerName": "", "dispatchMethod": 2, "address": "123 Road St", "suburb": "Charlemont", "state": "", "postcode": "3217", "endContactNumber": "0400123456", "customerPriceAdjust": 5, "customerMinCharge": 100, "supplierPriceAdjust": 0, "percentageCharged": 0, "variationCost": 0, "freightCost": 150, "dateEntered": "2023-09-15 16:21:06", "dateUpdated": "2023-09-21 14:41:30", "dateSubmitted": null, "dateAccepted": null, "dateDelivery": null, "dateEnteredDateTime": {"date": "2023-09-15 16:21:06.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateUpdatedDateTime": {"date": "2023-09-21 14:41:30.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateSubmittedDateTime": {"date": "2023-09-21 14:41:33.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateAcceptedDateTime": {"date": "2023-09-21 14:41:33.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateDeliveryDateTime": {"date": "2023-09-21 14:41:33.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "datePaid": null, "accepted": 0, "couponCodeId": null, "couponDiscountPrice": null, "couponCodeData": null, "rooms": [{"id": "374038", "jobId": "300367", "name": "Room 1", "description": "", "carcColour": "22943", "carcCustomColour": "", "carcEdgeColour": "2570", "extColour": "6797", "extEdgeColour": "1449", "extCustomColour": "1", "baseHeight": "720", "baseDepth": "560", "upperHeight": "550", "upperDepth": "300", "tallHeight": "1950", "tallDepth": "590", "doorStyle": "79", "doorSuffix": null, "doorSuffixId": null, "hingeStyle": "5", "drawerStyle": "42", "toeKickHeight": "100", "drawerGap": "3", "doorGap": "3", "upperTopGap": "0", "upperBottomGap": "0", "baseTopGap": "3", "baseBottomGap": "0", "leftGap": "1.5", "rightGap": "1.5", "includeAssembly": "1", "excludeHardware": "0", "adjustableLegs": "0", "extHorGrain": "0", "carcHorGrain": "0", "extDoubleSided": "0", "carcDoubleSided": "0", "shelfSetBack": "15", "doorBorderWidthTop": "0", "doorBorderWidthBottom": "0", "doorBorderWidthLeft": "0", "doorBorderWidthRight": "0", "doorAdvancedEdgeTop": "2", "doorAdvancedEdgeBottom": "2", "doorAdvancedEdgeLeft": "2", "doorAdvancedEdgeRight": "2", "doorAdvancedRailHeight": "55", "doorAdvancedRailWidth": "55", "doorAdvancedBarWidth": "55", "defaultSize": "2706", "defaultGap": "7516", "topPart": "24", "jobCabinets": [{"id": 1, "variationCost": 100, "comment": "Test"}, {"id": 2, "variationCost": 200, "comment": "Test 2"}], "carcMaterial": {"id": "22943", "itemCode": null, "name": "White Carcase", "finish": "Available", "brand": 14, "substrate": "HMR PB", "thickness": 33.5, "defaultDoubleSidedCost": 0, "defaultHorGrainSurcharge": null, "defaultHandlingCost": 1.46, "defaultAreaCost": 22.77, "defaultAreaAssemblyCost": 4.15, "defaultMinJobArea": 1, "defaultLength": 3600, "defaultWidth": 1800, "changedItemCode": null, "changedHorGrainSurcharge": null, "changedDoubleSidedCost": null, "changedHandlingCost": null, "changedAreaCost": null, "changedAreaAssemblyCost": null, "changedMinJobArea": null, "changedLength": null, "changedWidth": null, "prefix": "PS", "prefixId": "1", "doorFilter": "Flat Panel", "image": "Shop Materials/White Carcase.jpg", "horGrain": "0", "doubleSided": "0", "manufacturers": "-1", "type": "2", "typeName": "Melamine", "hiddenAsDefault": "0", "gfpName": "", "gfpBrandId": "-1", "gfpTypeId": "-1", "gfpFinishId": "-1", "gfpBrandName": "", "gfpTypeName": "", "gfpFinishName": "", "changed": false, "customerChanged": false, "hidden": false, "manufacturerId": 6, "carcHorGrain": null, "extHorGrain": null, "brandName": "Shop Materials"}, "carcEdge": {"id": 2570, "itemCode": null, "name": "White Carcase", "brand": 17, "finish": "Available", "thickness": 1, "defaultLengthCost": 0.52, "defaultHandlingCost": 0.6, "defaultAreaHandlingCost": 1.35, "defaultApplicationCost": 0.95, "changedItemCode": null, "changedHandlingCost": null, "changedLengthCost": null, "changedAreaHandlingCost": null, "changedApplicationCost": null, "doorFilter": "Flat Panel", "image": "Shop Materials/White Carcase.jpg", "manufacturers": -1, "manufacturerId": 6, "changed": false, "customerChanged": false, "hidden": false, "type": 2, "typeName": "Melamine", "brandName": "Shop Materials"}, "extMaterial": {"id": "6797", "itemCode": null, "name": "Custom Colour", "finish": "Gloss", "brand": 26, "substrate": "MR MDF", "thickness": 21.5, "defaultDoubleSidedCost": 130, "defaultHorGrainSurcharge": null, "defaultHandlingCost": 3.25, "defaultAreaCost": 149.5, "defaultAreaAssemblyCost": 3.15, "defaultMinJobArea": 2, "defaultLength": 2400, "defaultWidth": 1200, "changedItemCode": null, "changedHorGrainSurcharge": null, "changedDoubleSidedCost": null, "changedHandlingCost": null, "changedAreaCost": null, "changedAreaAssemblyCost": null, "changedMinJobArea": null, "changedLength": null, "changedWidth": null, "prefix": "PS", "prefixId": "1", "doorFilter": "Paint Door", "image": "", "horGrain": "0", "doubleSided": "1", "manufacturers": "-1", "type": "5", "typeName": "Polyurethane / Paint / Raw", "hiddenAsDefault": "0", "gfpName": "", "gfpBrandId": "-1", "gfpTypeId": "-1", "gfpFinishId": "-1", "gfpBrandName": "", "gfpTypeName": "", "gfpFinishName": "", "changed": false, "customerChanged": false, "hidden": false, "manufacturerId": 6, "carcHorGrain": null, "extHorGrain": null, "brandName": "Dulux"}, "extEdge": {"id": 1449, "itemCode": null, "name": "Ariss", "brand": 17, "finish": "Paint", "thickness": 1, "defaultLengthCost": 0.86, "defaultHandlingCost": 0.25, "defaultAreaHandlingCost": 0.25, "defaultApplicationCost": 0.9, "changedItemCode": null, "changedHandlingCost": null, "changedLengthCost": null, "changedAreaHandlingCost": null, "changedApplicationCost": null, "doorFilter": "Paint Door", "image": "Shop Materials/Ariss.jpg", "manufacturers": -1, "manufacturerId": 6, "changed": false, "customerChanged": false, "hidden": false, "type": 5, "typeName": "Polyurethane / Paint / Raw", "brandName": "Shop Materials"}, "door": {"defaultEdgeFinishTop": 1, "defaultEdgeFinishBottom": 1, "defaultEdgeFinishLeft": 1, "defaultEdgeFinishRight": 1, "defaultEdgeFinishJoin": 1, "setAsDefaultEdgeFinish": 1, "lockDefaultEdgeFinish": 0, "doorChangedId": 14060, "id": 79, "name": "Paint Door", "outsourced": false, "filterName": "Paint Door", "defaultHandlingCost": 8, "defaultAreaHandlingCost": 2, "defaultMachiningCost": 5, "defaultAreaMachiningCost": 2, "defaultUnitCost": 1, "defaultAssemblyCost": 3.2, "defaultAreaAssemblyCost": 3.2, "changedHandlingCost": null, "changedAreaHandlingCost": null, "changedMachiningCost": null, "changedAreaMachiningCost": null, "changedUnitCost": null, "changedAssemblyCost": null, "changedAreaAssemblyCost": null, "suffix": "", "suffixId": null, "advanced": 0, "image": 0, "manufacturers": -1, "defaultBrand": null, "defaultFinish": null, "defaultColour": null, "setAsDefaultEdgeType": false, "lockDefaultEdgeType": false, "minimumHeight": null, "minimumHeightDrawer": null, "maximumHeight": null, "minimumWidth": null, "maximumWidth": null, "minimumBorderWidthTop": null, "minimumBorderWidthBottom": null, "minimumBorderWidthLeft": null, "minimumBorderWidthRight": null, "minimumDistanceTopBottom": null, "minimumDistanceLeftRight": null, "defaultBorderWidthTop": null, "defaultBorderWidthBottom": null, "defaultBorderWidthLeft": null, "defaultBorderWidthRight": null, "minimumRailsHorizontalHeight": null, "maximumRailsHorizontalHeight": null, "minimumRailsVerticalWidth": null, "maximumRailsVerticalWidth": null, "defaultRailsHorizontalHeight": null, "defaultRailsVerticalWidth": null, "changed": false, "customerChanged": false, "hidden": false, "manufacturerId": 6, "brand": "", "style": "", "imageName": null}, "drawer": null, "jobBenchtops": [], "UpperBottomGap": null, "depth": null, "height": null, "extEdgeThick": null, "carcEdgeThick": null, "cost": 0, "productCount": 0}], "customerName": "<PERSON>s (Justin <PERSON>)", "manufacturerName": "Manufacturer 1", "supplierName": null, "MaxLayoutStatus": 0, "supplierId": -1, "additionalManufacturerServices": [], "variationsConfirmed": 0, "customer": {"manufacturerId": 6, "supplierId": -1, "dateAdded": "2015-06-26 20:36:00", "discount": 5, "minCharge": 100, "minFreight": 150, "phNumber": "0400123456", "address": "321 Street Rd", "suburb": "Geelong", "postcode": "3220", "state": "VIC", "defaultCarcMaterial": 22943, "defaultCarcEdge": 2570, "defaultExtMaterial": 6797, "defaultExtEdge": 1449, "defaultDoor": 79, "defaultHinge": 5, "defaultDrawer": 42, "defaultToeKickHeight": 100, "defaultIncludeAssembly": 1, "defaultExcludeHardware": 0, "defaultAdjustableLegs": 0, "defaultShelfSetBack": 15, "defaultBorderWidthTop": 0, "defaultBorderWidthBottom": 0, "defaultBorderWidthLeft": 0, "defaultBorderWidthRight": 0, "defaultAdvancedEdgeTop": 2, "defaultAdvancedEdgeBottom": 2, "defaultAdvancedEdgeLeft": 2, "defaultAdvancedEdgeRight": 2, "defaultAdvancedRailHeight": 55, "defaultAdvancedRailWidth": 55, "defaultAdvancedBarWidth": 55, "defaultSize": 2706, "defaultGap": 7516, "addDefaultRoomOnNewJobCreate": 0, "deliveryAddress": "123 Road St", "deliverySuburb": "Charlemont", "deliveryPostcode": "3217", "deliveryState": "7", "deliveryRegion": null, "defaultMarkup": 30, "defaultTermsText": "50% payable on acceptance of quotation prior to commencement of work. 50% payable upon completion of job. Quotation is valid for 30 days.", "manuTest": 1, "percentageCharged": 0, "premium": 1, "lastOnPremium": "2021-03-23 13:38:14", "suspended": 0, "manuPermission": 1, "cc": "", "defaultFreightOption": "FREIGHT_MY_ADDRESS", "defaultMeasurementUnit": "METRIC", "defaultRoomTop": 24, "allowedToEditInfoText": 1, "isPremium": 1, "countryId": 1, "countryName": "Australia", "addressState": 7, "addressRegion": null, "signupCountryId": null, "currencyType": "$", "gfpAccessAllowed": 1, "postalCodeName": "Postcode", "password2": null, "ccEmail": null, "referral": "", "defaultExtHorGrain": null, "defaultExtDoubleSided": 0, "defaultExtCustomColor": "1", "defaultCarcHorGrain": null, "defaultCarcDoubleSided": null, "defaultCarcCustomColor": null, "defaultDepotId": null, "gfp": 0, "gfpRegisteredPostcode": "", "gfpVerified": 0, "gfpMarkup": 40, "gfpMinJobCost": 0, "enableAssembly": null, "enableVariation": null, "updateImage": false, "premiumDetails": {"id": 26, "customerId": 928, "themeId": -1, "logo": "cabpartslogo3.jpg", "address": "123 Fake Street", "suburb": "Geelong", "postcode": "3220", "state": "VIC", "phone": "0412 345 678", "fax": "", "email": "<EMAIL>", "prefPaymentMethod": "banktransfer", "abn": "*********", "bankName": "ANZ", "bankAccountName": "Cooper Cabinets", "bankBSB": "123456", "bankAccountNumber": "*********", "addressState": 7, "addressRegion": null}, "manufacturer": {"contact": "<PERSON><PERSON><PERSON>", "adminEmail": "", "accountingEmail": "", "address": "192 <PERSON><PERSON> Rd", "suburb": "<PERSON><PERSON><PERSON><PERSON>", "postcode": "3227", "phNumber": "03 5254 3274", "ccEmail": "", "bccEmail": "", "distanceValue": 9999999, "hasAcceptedTermsAndConditions": 1, "fillerFixDepth": 150, "fillerMinimumWidth": 50, "drillCost": 0.07, "hingePosition": 96, "hinges3": 900, "hinges4": 1500, "hinges5": 2200, "deliveryTime": 14, "materialPricing": 0, "enableVariation": 1, "enableAssembly": 1, "enableAssemblyYesOptionDescription": "Includes carcases screwed together, hardware and doors affixed. Does not include  installation or applied panel and kickers being attached.", "enableAssemblyYesOptionName": "Assembled", "state": null, "password2": null, "enableAssemblyNoOptionDescription": "Individual componentry packed flat and loaded onto a pallet or similar which is wrapped in plastic film.", "enableAssemblyNoOptionName": "Flat Pack", "defaultAttachPDF": 1, "defaultCarcMaterial": 749, "defaultCarcEdge": 2570, "defaultExtMaterial": 1121, "defaultExtEdge": 1538, "defaultDoor": 7, "defaultHinge": 5, "defaultDrawer": 6, "defaultToeKickHeight": 150, "defaultIncludeAssembly": 0, "defaultExcludeHardware": 1, "defaultAdjustableLegs": 1, "defaultShelfSetBack": 5, "defaultBorderWidthTop": 55, "defaultBorderWidthBottom": 55, "defaultBorderWidthLeft": 55, "defaultBorderWidthRight": 55, "defaultAdvancedEdgeTop": 2, "defaultAdvancedEdgeBottom": 2, "defaultAdvancedEdgeLeft": 2, "defaultAdvancedEdgeRight": 2, "defaultAdvancedRailHeight": 55, "defaultAdvancedRailWidth": 55, "defaultAdvancedBarWidth": 55, "defaultSize": 63, "defaultGap": 53, "addDefaultRoomOnNewJobCreate": 0, "pickupAvailable": 1, "freightAvailable": 1, "defaultFreightOption": "PICKUP", "defaultMeasurementUnit": "METRIC", "allowNonmmUnits": 0, "allowEditPricingMatrix": 0, "permitCustomerSelfRegistration": 1, "allowJobReportBreakdown": 1, "allowOrderAcknoweledgementPdf": 0, "allowKDMaxImport": 1, "showOrderAcknowledgementPdf": 0, "showJobPropertiesTablePdf": 1, "defaultJobPropertiesPdf": 2, "defaultRoomTop": 26, "percentageCharged": 6, "minCharge": 100, "customerDiscount": 5, "statementDays": 14, "ABN": "***********", "bankAccountName": "Cabinets by Computer", "bankName": "ANZ", "bankBSB": "013645", "bankAccountNumber": "*********", "defaultMinFreightCost": 150, "dateAdded": "0000-00-00 00:00:00", "newsText": "", "newsUpdated": "2021-04-26 01:00:00", "linkEmail": "<EMAIL>", "linkFacebook": "www.facebook.com/gocabinets/", "linkTwitter": "", "linkLinkedin": "", "linkInstagram": "https://www.instagram.com/gocabinets/", "linkGoogle": "", "linkYoutube": "https://www.youtube.com/channel/UCQAR5TmUEb5XDC9AFxtmYwA", "selectedTheme": -1, "image": "man1 logo.png", "loginRegistrationApi": "GLyLWA2JuU2KX06CuZqh", "apiKey": "xQiGIq5A3gbKQHX", "updateImage": false, "signupRedirect": "https://goflatpacks.com.au/", "custContactInfo": "<b>**NOTE - You're currently using a demo account. Please contact us to have your account aligned with your nearest CNC manufacturer. **</b>\r\n\r\n<b><i><u>Contact:</u></i></b>\r\n<PERSON><PERSON><PERSON>ey\r\ngoCabinets Head Office\r\n\r\n<b>Phone Number:</b>\r\n03 5254 3274\r\n\r\n<b>Email:</b>\r\n<EMAIL>", "gfpCustContactInfo": "<b><i><u>Contact:</u></i></b>\r\n<PERSON><PERSON><PERSON>\r\ngoFlatpacks Head Office\r\n\r\n<b>Email:</b>\r\n<EMAIL>", "manufacturerApplicationLogo": "manufacturer_application_logo_6.png", "manufacturerApplicationLoadingGif": "manufacturer_application_loading_gif_6", "manufacturerApplicationFavicon": "manufacturer_application_favicon_6 (1).png", "disableCustomerPremiumUpgrade": 1, "applicationSubdomain": "", "manufacturerLoginPageLogo": null, "manufacturerLoginPageImage": null, "goFlatpacksManufacturer": 1, "gfpPaypalEmail": "<EMAIL>", "gfpHomeText": "<p><strong><u>General</u></strong></p><p>By using the goFlatpacks website you agree to be bound to the terms and conditions of goFlatpacks as well as the terms and conditions of the specific manufacturer relevant to you. If you do not agree with them, you are not authorised to use the website.</p><p>1. This website and its contents are subject to copyright which is owned by Cabinets by Computer T/A goFlatpacks or a third party. Cabinets by Computer does not grant you any intellectual property rights in this website, its applications, interface or contents. You must not use any trademark displayed on this website.</p><p>2. We do not warrant the accuracy or completeness of any information you derive from this website and we exclude liability for loss or damage arising from any errors or omissions in this website or your use of this website (including any interference with or damage to your computer system). If any liability is not able to be excluded by law, we limit our liability to the resupply of the relevant information or services.</p>###<p>3. You release Cabinets by Computer, its servants and agents to the fullest extent permitted by law from any and all claims arising out of or related to the use of material or information made available through this website.</p><p>4. This website may contain links to external Internet websites. Cabinets by Computer does not sponsor, guarantee or approve of any material or representations in those websites. Nor do we warrant that material on linked sites is free of any computer virus, defects or infringements.</p><p>5. Links to this site may be allowed provided written permission is sought.</p><p>6. You must only use this website, and you must only display, copy, distribute download and print portions of this website for your own personal use. You must not attempt to change, reproduce, add to, remove, hack or interfere with this website or its material.</p><p>7. We cannot guarantee any file, data or program available for download from this website (or any linked website) is free of viruses and you assume the risk of any damage to your computer as a result of using this website. This website may be inaccessible from time to time due to events outside goFlatpacks control or maintenance requirements.</p><p>8. If we collect your personal information, we do so subject to the terms of our Privacy Policy.</p><p>9. goFlatpacks offers you access to this website and the functionality attached to this website but reserves the right to charge a fee for this service in the future (no fee is currently applicable).</p><p>10. We may use cookies to gather data in relation to this website and you consent to us doing so (although you may be able to disable cookies on your web browser).</p><p>11. The goFlatpacks website serves as the software connecting cabinet makers, builders and other users (members) to CNC Manufacturers for ease of ordering.</p><p>12. By signing up to goFlatpacks, the user (member) agrees to goFlatpacks passing information provided on to a manufacturer. goFlatpacks will take reasonable measures to ensure the security of all personal information.</p><p>13. The Manufacturer 1 account is a default manufacturer that serves to provide an example of the goFlatpacks software.<strong>&nbsp;</strong>Any jobs submitted to Manufacturer 1 will be considered complete and may be passed on to a manufacturer for completion.</p><p>14. Prices may vary between goFlatpacks Manufacturer 1 to when a job is transferred to a CNC manufacturer. goFlatpacks does not make any price guarantees under the manufacturer1 account.</p><p>15. Once a job has been submitted (paid) and is accepted by a manufacturer, it is considered complete.</p><p>16. goFlatpacks takes no responsibility for any details of the order once a member has been assigned to a manufacturer. Where prices vary, the member will be given a choice on whether they wish to leave a job submitted.</p><p>17. goFlatpacks takes no responsibility for the timeliness, quality or price of products provided. It is the responsibility of the user to ensure that the products they order are to their specifications. Any issues that arise to do with products will be between the manufacturer and the member. goFlatpacks strives to ensure that all manufacturers are reputable and provide quality products.</p><p>18.&nbsp;Manufacturers reserve the right to make changes to products and price information on the goFlatpacks software at any time without notice to member.</p><p>19. goFlatpacks receives fees for use of the software from participating manufacturers. Members are not required to pay for the use of the goFlatpacks website or software.</p><p>20. All prices are quoted in Australian dollars. Prices are subject to change at any time and may vary depending on payment method. goFlatpacks strives to provide up-to-date information on products and pricing but accuracy is not guaranteed at all times.</p><p>21. goFlatpacks aims to provide a cutting edge system for all website users. Constant improvements and developments in software mean that the website and the content of the website is subject to change at any time, without notice.</p><p>22. By agreeing to the goFlatpacks terms and conditions, the member accepts the terms and conditions of the manufacturer that they are assigned to.</p><p>23. These conditions are governed by laws of the State of Victoria, Australia. You submit to the non-exclusive jurisdiction of the courts of that State.</p><p><em>This website is for use by Australian residents and people aged 18 years and over.</em></p><p>&nbsp;</p><p><strong><u>Descriptions and product information</u></strong></p><p>All images and descriptions on the goFlatpacks website are purely for demonstration purposes. We have taken care to describe and show items as accurately as possible, however the composition of your final item will depend upon your selections through the ordering process</p><p>&nbsp;</p><p><strong><u>Payment and acceptance:</u></strong></p><p>Your order through goFlatpacks is an offer to buy from your allocated manufacturer. Acceptance of this offer will take place on receipt of payment. After this time, no further amendments can be made to your order. Due to the streamlined nature of goFlatpacks, as soon as you submit and pay for your job it will be sent to your manufacturer&rsquo;s machine for cutting.</p><p>&nbsp;</p><p><strong><u>Returns and Refunds</u></strong></p><p>Due to the custom-made nature of the products you order through Cabinets by Computer&rsquo;s online ordering system &lsquo;goFlatpacks&rsquo;, we are unable to offer any returns or refunds. As such, we ask that you please make your selections very carefully before completing your order and check thoroughly that the measurements you have provided for your cabinetry and components is correct. Cabinets by Computers and our Manufacturers will not be held liable for products supplied that meet the specifications made by you (the customer) during the ordering process, if the customer&rsquo;s specifications are inaccurate.</p><p>&nbsp;</p><p>Please contact&nbsp;us at&nbsp;<EMAIL>&nbsp;if&nbsp;you require any clarification on this policy.</p><p>&nbsp;</p><p><strong><u>Faulty or Damaged Products</u></strong></p><p>&nbsp;</p><p>Please note that the act of you signing for your goFlatpacks product/s on delivery is your acceptance that damage has not occurred in shipping. In the case of product pick up from your manufacturer, the act of you signing for your goFlatpacks product/s on pick-up is your acceptance that the product/s are free from damage. In the unlikely event that a delivered product is faulty or damaged, we will exchange it with a like for like replacement as long as we are notified of the product's fault prior to delivery being accepted.</p><p>&nbsp;</p><p>In such a case we will ask you to email us with photographs of the offending item (s) and on approval from our Quality control officer we will happily process replacement part(s) for delivery to you.</p><p>&nbsp;</p><p>Cabinets by Computer and our Manufacturers will not be responsible for any removal, installation and/or setup costs incurred during and in relation to any replacement of product. Cabinets by Computer and our Manufacturers will not, under this Manufacturer&rsquo;s Warranty, be responsible for consequential damages (such as loss of use) inconvenience, loss or damage to personal property, whether direct, indirect or economic.</p><p>&nbsp;</p><p>Cabinets by Computer and our Manufacturer&rsquo;s responsibility will not, under this Manufacturer&rsquo;s Warranty, exceed the purchase price of the product or its replacement. We reserve the right to arrange for inspection of any claimed faulty or damaged products prior to agreeing to any product exchange.</p><p>&nbsp;</p><p>Any approved refunds will be made to the account used to purchase the product. Please contact us&nbsp;at <EMAIL> if&nbsp;you require any clarification on this policy.</p><p>&nbsp;</p><p><span style=\"text-decoration: underline;\"><strong>Pricing Errors/malfunctioning ordering system</strong></span></p><p>Any pricing errors that arise from bugs/malfunctions in the goFlatpacks ordering system will not be honoured. Your original payment will be returned and re-negotiated at the correct pricing. The customer may accept to purchase their items at the new price, or can choose to cancel order and retain refunded payment. Ultimate discrection over pricing will remain with your allocated manufacturer.</p>", "gfpJobText": "<p><strong><u>Descriptions and product information</u></strong></p><p>All images and descriptions on the goFlatpacks website are purely for demonstration purposes. We have taken care to describe and show items as accurately as possible, however the composition of your final item will depend upon your selections through the ordering process</p><p>&nbsp;</p><p><strong><u>Payment and acceptance:</u></strong></p><p>Your order through goFlatpacks is an offer to buy from your allocated manufacturer. Acceptance of this offer will take place on receipt of payment. After this time, no further amendments can be made to your order. Due to the streamlined nature of goFlatpacks, as soon as you submit and pay for your job it will be sent to your manufacturer&rsquo;s machine for cutting.</p><p>&nbsp;</p><p><strong><u>Returns and Refunds</u></strong></p><p>Due to the custom-made nature of the products you order through Cabinets by Computer&rsquo;s online ordering system &lsquo;goFlatpacks&rsquo;, we are unable to offer any returns or refunds. As such, we ask that you please make your selections very carefully before completing your order and check thoroughly that the measurements you have provided for your cabinetry and components is correct. Cabinets by Computers and our Manufacturers will not be held liable for products supplied that meet the specifications made by you (the customer) during the ordering process, if the customer&rsquo;s specifications are inaccurate.</p><p>Please contact&nbsp;us at&nbsp;<EMAIL>&nbsp;if&nbsp;you require any clarification on this policy.</p><p><strong><u>Faulty or Damaged Products</u></strong></p><p>Please note that the act of you signing for your goFlatpacks product/s on delivery is your acceptance that damage has not occurred in shipping. In the case of product pick up from your manufacturer, the act of you signing for your goFlatpacks product/s on pick-up is your acceptance that the product/s are free from damage. In the unlikely event that a delivered product is faulty or damaged, we will exchange it with a like for like replacement, as long as we are notified of the product's fault prior to delivery being accepted.</p><p>In such a case we will ask you to email us with photographs of the offending item (s) and on approval from our Quality control officer we will happily process replacement part(s) for delivery to you.</p><p>Cabinets by Computer and our Manufacturers will not be responsible for any removal, installation and/or setup costs incurred during and in relation to any replacement of product. Cabinets by Computer and our Manufacturers will not, under this Manufacturer&rsquo;s Warranty, be responsible for consequential damages (such as loss of use) inconvenience, loss or damage to personal property, whether direct, indirect or economic.</p><p>Cabinets by Computer and our Manufacturer&rsquo;s responsibility will not, under this Manufacturer&rsquo;s Warranty, exceed the purchase price of the product or its replacement. We reserve the right to arrange for inspection of any claimed faulty or damaged products prior to agreeing to any product exchange.</p><p>Any approved refunds will be made to the account used to purchase the product. Please contact us&nbsp;at <EMAIL> if&nbsp;you require any clarification on this policy.</p><p>&nbsp;</p><p><span style=\"text-decoration: underline;\"><strong>Pricing Errors/malfunctioning ordering system</strong></span></p><p>Any pricing errors that arise from bugs/malfunctions in the goFlatpacks ordering system will not be honoured. Your original payment will be returned and re-negotiated at the correct pricing. The customer may accept to purchase their items at the new price, or can choose to cancel order and retain refunded payment. Ultimate discrection over pricing will remain with your allocated manufacturer.</p>", "gfpMinJobCost": 100, "gfpMarkup": 35, "gfpPickupDefaultText": "<p>Pick-up times are:</p><p><strong>9am - 5pm, Monday to Friday.</strong></p><p>Pick-up location is:</p><p><strong>192 Staceys Rd, Connewarre, VIC 3227</strong>. Please enter via the first, unmarked driveway to the left, heading north on Staceys road. Please do not enter driveway marked with the 192 Letterbox.</p><p><img src=\"https://goflatpacks.com.au/wp-content/uploads/2018/11/goflatpacks-office.jpg\" alt=\"GFP Office\" width=\"500\" height=\"478\" /></p><p>For any assistance with pick-up location, please contact 03 5254 3274.</p><p>&nbsp;</p><p>Your products are loaded onto a small pallet and shink wrapped and can be loaded onto an open tray, or the pallet can be unstacked and loaded by hand.</p>", "gfpDeliveryDefaultText": "<p>To schedule your delivery date and time, please contact (03) 5254 3274 and quote your order #.</p><p>Please have at least 1 person available to assist in the unloading of your order. Delivery is to first floor of a premises only and during 9am - 5pm Monday to Friday.</p>", "gfpPaypalSurcharge": 2, "gfpAutoAssignCustomer": 1, "gfpAllowBankDeposit": 1, "gfpAllowInStorePayment": 0, "gfpBankAccountNumber": "*********", "gfpBsbNumber": "013645", "countryId": 1, "taxRateId": 1, "statementTaxRateId": 1, "addressState": 7, "addressRegion": null, "gfpAccessAllowed": 1, "postalCodeName": "Postcode", "allowAddQuickDoor": 0, "allowTopDownLayoutDesigner": 1, "allowAddProduct": 1, "productSpecificEdgeLabels": 1, "allowAddProductSearch": 1, "allowAddRoom": 1, "enableSupplyHardware": 1, "enableAdjustableLegs": 1, "supplyHardwareYesOptionDescription": "Includes the supply of hinges and drawers applicable to the products selected. Does not include screws or shelf pins if product is not assembled.", "supplyHardwareYesOptionName": "Supply Hardware", "supplyHardwareNoOptionDescription": "Supply of cut, edged and drilled panels only. Not inclusive of any cabinet hardware or screws.", "supplyHardwareNoOptionName": "Drill Panels Only", "allowAddAdditionalHardware": 1, "addAdditionalHardwareText": "> Hardware Store <", "allowQuickFlatProduct": 1, "addQuickFlatProductText": "Doors & Panels", "allowCustomerInvoicingPdf": 1, "accessPdfBeforeAcceptance": 1, "enableMaterialTypeInCsv": 0, "enableItemPricingInCsv": 0, "enableCustomerIdInCsv": 1, "enableVariationRequestInCsv": 1, "isXeroIntegrationEnabled": 1, "isDepotFunctionalityEnabled": 0, "xeroInvoicePrefix": "MAN1", "xeroInvoicePaymentTerm": 14, "xeroInvoiceAccountCode": "6215", "xeroBillAccountCode": null, "xeroBillFrequency": null, "xeroBrandingThemeId": "8f458487-1f8f-4cad-8d29-b86e4d21e698", "isBillEnabled": 0, "isInvoiceEnabled": 1, "defaultMaterialSelector": 0, "notEdgedName": "", "notEdgedOutput": "", "edgedName": "", "edgedOutput": "", "squareName": "", "squareOutput": "", "profileName": "", "profileOutput": "", "invoiceTermsConditions": "", "isBenchtopEnabledForManufacturer": 1, "BTDPricingGenerated": 1, "benchtopMarkup": 25, "suspended": 0, "inActive": 0, "cabinetTopsDisabled": "[26]", "defaultExtHorGrain": null, "defaultExtDoubleSided": null, "defaultExtCustomColor": null, "defaultCarcCustomColor": null, "defaultCarcHorGrain": null, "defaultCarcDoubleSided": null, "glassSubPanel": 1, "isBenchtopDesignerEnabled": 1, "isBenchtopModuleEnabled": 1, "bankAccountId": null, "depotIdentification": null, "depotSelectable": null, "enableMultipleDepot": null, "doorHangReversal": 0, "displayEstimatedCompletion": 0, "id": 6, "name": "Manufacturer 1", "email": "<EMAIL>", "password": "798a4cbc74af350b05804fd7ecd292d9bc3699b415ea95183885eb49819acbd5", "salt": "salt", "level": 1, "currencyType": "$", "lastlogin": "2023-06-02 09:04:58", "lastPageAccessed": null, "newPassword": null, "newPassword2": null, "superAdmin": null}, "id": 928, "name": "<PERSON>s (Justin <PERSON>)", "email": "<EMAIL>", "password": "798a4cbc74af350b05804fd7ecd292d9bc3699b415ea95183885eb49819acbd5", "salt": "salt", "level": 2, "lastlogin": "2023-09-21 09:51:27", "lastPageAccessed": null, "newPassword": null, "newPassword2": null, "superAdmin": null}, "sundryItems": [], "addressState": 7, "addressRegion": null, "countryTaxRateName": "GST", "taxDescription": "10% Government Service Tax", "taxRate": 10, "taxOnlyRate": 0.1, "taxInclusiveRate": 1.1, "currencyAbbreviation": "AUD", "countryName": "Australia", "currencyType": "$", "priceComponents": null, "couponDetails": [], "couponCost": null, "totalJobCostInclTax": 275, "totalCostExclTax": 250, "taxCost": 25, "displayId": 310367, "attachments": [], "jobMinChargeFromCustMin": 100, "totalMinUsagePrice": 0, "cost": 0, "totalVariationCost": 0, "minUsage": [], "priceExpireDate": "21/10/2023", "priceExpire": false, "resetPriceStatus": false, "currency": null, "datePaidDateTime": {"date": "2023-09-21 14:41:33.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "depotId": null, "totalProductCount": 0}}