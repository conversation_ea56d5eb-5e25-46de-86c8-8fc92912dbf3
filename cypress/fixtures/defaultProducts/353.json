{"success": 1, "job_cabinet": {"room_id": "374038", "room_cab_number": -1, "type": 353, "note": "", "include_drawer_faces": 1, "comment": "", "variation_id": -1, "variation_cost": 0, "cost": 0, "quantity": 1, "mirror": -1, "favourites": "0", "height": 900, "depth": "590", "width": 550, "width2": 0, "length_depth": "590", "width_depth": "590", "length1": 300, "length2": 300, "width1": 300, "top": -1, "ladder_frames": 1, "carc_colour": "22943", "carc_edge_colour": "2570", "ext_colour": "6797", "ext_edge_colour": "1449", "carc_min_job_area": 0, "ext_min_job_area": 0, "carc_material_used": 0, "ext_material_used": 0, "ext_hor_grain": "0", "carc_hor_grain": "0", "ext_double_sided": "0", "carc_double_sided": "0", "applied_panel_depth": 610, "upper_filler_depth": 610, "toe_kick_height": "100", "door_style": "79", "hinge_style": "3376", "drill_only_hinge_details": {"name": "<PERSON><PERSON>", "drill_only_hinge": false}, "door_hinge_amount": 3, "drawer_gap": "3", "drawer_bottom": "0", "drawer_top": "3", "drawer_left": "1.5", "drawer_right": "1.5", "door_gap": "3", "door_top": "3", "door_bottom": "0", "door_left": "1.5", "door_right": "1.5", "include_assembly": "1", "exclude_hardware": "0", "include_hardware": 1, "drawer_amount": 0, "drawer_face_type": 2, "drawer_face_heights": "", "partition_height": 0, "partition_width": 0, "upper_partition_height": "0", "lower_partition_height": "0", "shelf_set_back": "15", "total_drawer_height": "720", "oven_check": 0, "microwave_check": 0, "oven_height": 600, "microwave_height": 600, "hori_shelf_amount": -1, "vert_shelf_amount": -1, "middle_shelf_amount": 0, "simple_shelves": 1, "type_name": "Door", "carc_material": {"id": "22943", "item_code": null, "name": "White Carcase", "finish": "Available", "brand": 14, "substrate": "HMR PB", "thickness": 33.5, "default_double_sided_cost": 0, "default_hor_grain_surcharge": null, "default_handling_cost": 1.46, "default_area_cost": 22.77, "default_area_assembly_cost": 4.15, "default_min_job_area": 1, "default_length": 3600, "default_width": 1800, "changed_item_code": null, "changed_hor_grain_surcharge": null, "changed_double_sided_cost": null, "changed_handling_cost": null, "changed_area_cost": null, "changed_area_assembly_cost": null, "changed_min_job_area": null, "changed_length": null, "changed_width": null, "prefix": "PS", "prefix_id": "1", "door_filter": "Flat Panel", "image": "Shop Materials/White Carcase.jpg", "hor_grain": "0", "double_sided": "0", "manufacturers": "-1", "type": "2", "type_name": "Melamine", "hidden_as_default": "0", "gfp_name": "", "gfp_brand_id": "-1", "gfp_type_id": "-1", "gfp_finish_id": "-1", "gfp_brand_name": "", "gfp_type_name": "", "gfp_finish_name": "", "changed": false, "customer_changed": false, "hidden": false, "manufacturer_id": 6, "carc_hor_grain": null, "ext_hor_grain": null, "brand_name": "Shop Materials"}, "carc_edge": {"id": 2570, "item_code": null, "name": "White Carcase", "brand": 17, "finish": "Available", "thickness": 1, "default_length_cost": 0.52, "default_handling_cost": 0.6, "default_area_handling_cost": 1.35, "default_application_cost": 0.95, "changed_item_code": null, "changed_handling_cost": null, "changed_length_cost": null, "changed_area_handling_cost": null, "changed_application_cost": null, "door_filter": "Flat Panel", "image": "Shop Materials/White Carcase.jpg", "manufacturers": -1, "manufacturer_id": 6, "changed": false, "customer_changed": false, "hidden": false, "type": 2, "type_name": "Melamine", "brand_name": "Shop Materials"}, "ext_material": {"id": "6797", "item_code": null, "name": "Custom Colour", "finish": "Gloss", "brand": 26, "substrate": "MR MDF", "thickness": 21.5, "default_double_sided_cost": 130, "default_hor_grain_surcharge": null, "default_handling_cost": 3.25, "default_area_cost": 149.5, "default_area_assembly_cost": 3.15, "default_min_job_area": 2, "default_length": 2400, "default_width": 1200, "changed_item_code": null, "changed_hor_grain_surcharge": null, "changed_double_sided_cost": null, "changed_handling_cost": null, "changed_area_cost": null, "changed_area_assembly_cost": null, "changed_min_job_area": null, "changed_length": null, "changed_width": null, "prefix": "PS", "prefix_id": "1", "door_filter": "Paint Door", "image": "", "hor_grain": "0", "double_sided": "1", "manufacturers": "-1", "type": "5", "type_name": "Polyurethane / Paint / Raw", "hidden_as_default": "0", "gfp_name": "", "gfp_brand_id": "-1", "gfp_type_id": "-1", "gfp_finish_id": "-1", "gfp_brand_name": "", "gfp_type_name": "", "gfp_finish_name": "", "changed": false, "customer_changed": false, "hidden": false, "manufacturer_id": 6, "carc_hor_grain": null, "ext_hor_grain": null, "brand_name": "Dulux"}, "ext_custom_colour": "1", "ext_edge": {"id": 1449, "item_code": null, "name": "Ariss", "brand": 17, "finish": "Paint", "thickness": 1, "default_length_cost": 0.86, "default_handling_cost": 0.25, "default_area_handling_cost": 0.25, "default_application_cost": 0.9, "changed_item_code": null, "changed_handling_cost": null, "changed_length_cost": null, "changed_area_handling_cost": null, "changed_application_cost": null, "door_filter": "Paint Door", "image": "Shop Materials/Ariss.jpg", "manufacturers": -1, "manufacturer_id": 6, "changed": false, "customer_changed": false, "hidden": false, "type": 5, "type_name": "Polyurethane / Paint / Raw", "brand_name": "Shop Materials"}, "type_image": "edgar-f7pTMJ1ekqI-unsplash.jpg", "cabinet": {"primary_key": "type", "attributes": {"type": 353, "name": "Door", "description": "Door", "gfp_product": 0, "gfp_name": "Door with Hinges", "gfp_description": "Door with Hinges", "gfp_long_description": "A door made to your sizing, in your colour selected. Your door will be pre-drilled to suit your hinge choice. Hinges also supplied. All door sizing is relevant as a per door measurement, please order accordingly for pair, and straight bi-fold. (Do not order overall sizes for these products)\r\n\r\n*Please bear in mind that a track system will not be supplied with the straight bi-fold door. ", "gfp_help_text": "", "gfp_sub_style_id": 0, "gfp_hinge1": 20, "gfp_hinge2": 474, "gfp_hinge3": 474, "gfp_drawer1": -1, "gfp_drawer2": -1, "gfp_drawer3": -1, "code": "DoorQFP", "style": 5, "sub_style_id": 45, "product_type": 8, "mat_type": 1, "ext_label": "Exterior Material", "int_label": "Carcase Material", "lshape": 0, "bifold": 0, "door_hang": "", "image": "edgar-f7pTMJ1ekqI-unsplash.jpg", "image_large": "edgar-f7pTMJ1ekqI-unsplash (1).jpg", "drawers": 0, "shelves": 0, "shelf_type": -1, "default_shelves": 0, "default_upper_shelves": -1, "default_lower_shelves": -1, "doors": 1, "cabinet_finger_pull": 0, "is_recessed_rail": 0, "return_panel": 0, "default_return_width": 0, "oven": 0, "fixed_oven": 0, "rangehood": 0, "fixed_rangehood": 0, "microwave": -1, "fixed_microwave": 0, "key_shape": 0, "partition": 0, "fixed_partition": 0, "hinge_direction": 0, "min_height": 100, "max_height": 0, "min_width": 100, "max_width": 0, "min_depth": 100, "max_depth": 0, "requires_variation": 0, "default_top": -1, "default_total_drawer_height": 0, "default_bottom_clearance": 0, "available_cabinet_tops": "", "available_doors": "138,370,371,451,369,601,375,374,373,376,372,684,379,497,274,100,505,553,548,554,549,555,498,556,161,279,101,278,513,262,60,143,61,172,117,134,129,122,135,132,131,124,127,148,118,145,128,120,119,126,125,121,144,130,136,133,123,169,283,108,294,228,772,170,163,32,171,173,758,164,252,514,165,227,281,280,96,166,454,588,702,591,251,250,62,63,510,511,725,726,493,729,773,494,730,731,254,261,260,226,167,276,253,249,185,353,455,168,499,557,558,225,248,344,705,456,224,282,247,317,361,457,689,734,732,735,776,444,442,443,490,736,733,579,336,223,311,739,755,98,302,222,315,359,740,458,246,190,191,221,741,360,459,189,220,104,219,365,460,742,245,743,709,710,188,264,388,389,390,391,392,393,394,395,397,398,399,400,401,402,403,404,405,411,409,412,413,410,414,415,416,417,408,407,406,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,441,440,500,559,102,603,604,605,606,608,607,609,610,611,612,613,614,615,616,617,624,623,622,621,620,619,618,652,649,648,646,644,642,638,636,630,670,668,666,664,662,660,658,656,655,654,681,680,679,678,677,676,675,674,673,672,671,151,327,292,187,218,244,501,560,217,363,461,321,320,322,95,299,277,94,68,69,70,314,90,310,312,255,243,691,687,690,692,242,744,450,512,449,155,154,153,7,328,64,378,56,57,58,59,339,452,491,492,241,152,65,301,66,67,488,240,184,83,183,521,186,502,561,562,563,515,337,571,338,572,575,573,574,577,495,578,291,745,323,340,504,564,239,496,508,712,713,714,720,715,717,718,719,721,722,290,503,565,324,349,462,259,300,182,289,200,142,139,140,141,181,75,38,76,78,77,73,216,358,463,215,288,238,199,286,341,706,464,237,158,295,198,351,465,297,466,180,346,467,468,236,287,197,354,469,285,25,159,746,296,342,470,471,343,707,600,472,196,516,195,350,473,303,306,194,193,179,178,106,355,474,747,116,113,33,509,566,749,750,751,235,445,234,233,93,284,357,475,79,489,506,567,352,476,192,364,477,177,232,523,524,525,526,446,711,109,258,115,162,362,478,207,590,703,214,592,206,527,528,529,530,531,532,533,534,535,536,537,783,782,781,538,539,540,541,542,543,544,545,778,546,779,780,517,319,304,307,348,479,585,213,763,764,765,766,767,768,769,345,480,481,356,704,482,257,366,483,176,293,756,757,212,752,753,347,484,5,335,754,330,263,583,771,686,105,305,308,298,396,211,748,175,22,111,231,146,520,230,316,447,380,381,382,387,386,385,383,448,485,205,313,486,210,204,137,209,71,309,174,487,507,568,208,203,202,518,519,229,201,762,761,72", "available_hinges": ",5,23,6,38,8,39,21,12,11,13,35,37,36,15,14,16,33,32,34,41,46", "available_shelves": "", "available_drawers": "", "status": 0, "manufacturer_availability": "LIST", "deleted": 0, "default_middle_shelves": 0, "upper_partition_height": "0", "lower_partition_height": "0", "fixed_upper_partition_height": 0, "fixed_lower_partition_height": 0, "default_width": 450, "inner_drawers": 0, "minimum_opening_width": 0, "gfp_allow_colour_edit": 1, "is_robe_with_bottom": 0, "has_fascia": 0, "is_quick_flat_product": 1, "has_form_fields": 0, "edge_l1_label": "Left", "edge_l2_label": "Right", "edge_w1_label": "Top", "edge_w2_label": "Bottom", "edge_inner_label": "Joining Border", "created_date": "2021-01-18 03:19:55", "created_by": "0__16__<PERSON>", "modified_date": "2023-09-01 05:43:56", "modified_by": "0__6__Admin", "deleted_date": null, "manufacturer_description": ""}}, "carc_area_assembly_cost": 0, "carc_area_cost": 0, "carc_handling_cost": 0, "carc_double_sided_cost": 0, "ext_area_assembly_cost": 0, "ext_area_cost": 0, "ext_handling_cost": 0, "ext_double_sided_cost": 0, "carc_edge_application_cost": 0, "carc_edge_area_handling_cost": 0, "carc_edge_length_cost": 0, "carc_edge_handling_cost": 0, "ext_edge_application_cost": 0, "ext_edge_area_handling_cost": 0, "ext_edge_length_cost": 0, "ext_edge_handling_cost": 0, "material_prefix": "", "door_suffix": null, "door_suffix_id": null, "hardware": [], "parts": [], "shelves": [], "upper_shelves": [], "lower_shelves": [], "middle_shelves": [], "drillings": [], "drawer_face_options": false, "cabinet_adjustable_legs": "0", "job_id": "300367", "has_none_in_tops": true, "doors": [{"id": -1, "job_cabinet_id": -1, "door_id": -1, "door_hang_type": 0, "door_hang": 2, "face_type": 0, "handling_cost": 0, "area_handling_cost": 0, "machining_cost": 0, "area_machining_cost": 0, "unit_cost": 0, "assembly_cost": 0, "area_assembly_cost": 0, "advanced": 0, "sub_panel1": 0, "sub_panel2": 0, "sub_panel3": 0, "sub_panel4": 0, "sub_panel5": 0, "sub_panel6": 0, "sub_panel7": 0, "sub_panel8": 0, "sub_panel9": 0, "no_vert_bars": 0, "no_hori_bars": 0, "bar_width": "55", "border_width_top": "0", "border_width_bottom": "0", "border_width_left": "0", "border_width_right": "0", "panel_edge_top": "2", "panel_edge_bottom": "2", "panel_edge_left": "2", "panel_edge_right": "2", "hori_height": "55", "hori_amount": 0, "vert_width": "55", "vert_amount": 0, "edge_costing": {"handling": 0, "length": 0, "area_handling": 0, "application": 0}, "door_costing": [], "outsourced_costing": {"type": "", "percent_markup": null, "cost": 0}, "have_edges": 0, "is_outsourced": false, "horizontal_positions": [], "vertical_positions": [], "material_price_breakdown": null, "have_door_drawer": null, "have_hardware": null, "have_materials": null, "have_parts": null, "door_name": null}], "door": {"default_edge_finish_top": 1, "default_edge_finish_bottom": 1, "default_edge_finish_left": 1, "default_edge_finish_right": 1, "default_edge_finish_join": 1, "set_as_default_edge_finish": 1, "lock_default_edge_finish": 0, "door_changed_id": 14060, "id": 79, "name": "Paint Door", "outsourced": false, "filter_name": "Paint Door", "default_handling_cost": 8, "default_area_handling_cost": 2, "default_machining_cost": 5, "default_area_machining_cost": 2, "default_unit_cost": 1, "default_assembly_cost": 3.2, "default_area_assembly_cost": 3.2, "changed_handling_cost": null, "changed_area_handling_cost": null, "changed_machining_cost": null, "changed_area_machining_cost": null, "changed_unit_cost": null, "changed_assembly_cost": null, "changed_area_assembly_cost": null, "suffix": "", "suffix_id": null, "advanced": 0, "image": 0, "manufacturers": -1, "default_brand": null, "default_finish": null, "default_colour": null, "set_as_default_edge_type": false, "lock_default_edge_type": false, "minimum_height": null, "minimum_height_drawer": null, "maximum_height": null, "minimum_width": null, "maximum_width": null, "minimum_border_width_top": null, "minimum_border_width_bottom": null, "minimum_border_width_left": null, "minimum_border_width_right": null, "minimum_distance_top_bottom": null, "minimum_distance_left_right": null, "default_border_width_top": null, "default_border_width_bottom": null, "default_border_width_left": null, "default_border_width_right": null, "minimum_rails_horizontal_height": null, "maximum_rails_horizontal_height": null, "minimum_rails_vertical_width": null, "maximum_rails_vertical_width": null, "default_rails_horizontal_height": null, "default_rails_vertical_width": null, "changed": false, "customer_changed": false, "hidden": false, "manufacturer_id": 6, "brand": "", "style": "", "image_name": null}, "cabinet_details": {"cabinet_tops": [], "drawers": [], "hinges": [[5, "<PERSON><PERSON>", 0, "uploads/images/if6122e72850232/Blum Inserta.png", 2070], [23, "Blum Inserta Soft Close", 0, "uploads/images/if6122e72850232/Blum Inserta Soft close.png", 2071], [6, "<PERSON><PERSON>", 0, "uploads/images/if6122e72850232/Blu<PERSON> In.png", 2072], [38, "<PERSON><PERSON> In Soft Close", 0, "uploads/images/if6122e72850232/<PERSON><PERSON> In Soft Close.png", 2069], [8, "<PERSON><PERSON>ip <PERSON>", 0, null, null], [39, "Expando T", 1, null, null], [46, "Generic 35mm Hole Only", 0, null, 0], [12, "Intermat Flash", 0, "uploads/images/if6122e72850232/intermat flash.jpg", 2446], [11, "Intermat <PERSON> In", 0, "uploads/images/if6122e72850232/Intermat.jpg", 2445], [13, "Intermat Toolless", 0, "uploads/images/if6122e72850232/Intermat toolless.png", 2448], [21, "None", 0, null, null], [35, "Salice Knock In", 0, null, null], [37, "Salice Logica", 0, null, null], [36, "Salice Rapido", 0, null, null], [15, "Sensys Flash", 0, "uploads/images/if6122e72850232/Sensys.jpg", 2444], [14, "Sensys Knock In", 0, "uploads/images/if6122e72850232/Sensys Knock In.jpg", 2449], [16, "<PERSON><PERSON>s <PERSON>", 0, "uploads/images/if6122e72850232/Sensys.jpg", 2444], [33, "Tiomos Impresso", 0, null, null], [34, "Tiomos Impresso Soft", 0, null, null], [32, "Tiomos Knock In", 0, null, null], [41, "Titus T-Type", 0, "uploads/images/if6122e72850232/T-type Hinge (1).jpg", 2073]], "shelves": [], "parts": [{"id": "2254", "cabinetType": "353", "partId": "41", "partName": "Door"}], "hardware": []}, "cabinet_type": 353, "cabinet_quantity": 1, "cabinet_note": "", "cabinet_comment": "", "cabinet_include_assembly": "1", "cabinet_include_hardware": 1, "cabinet_exclude_hardware": "0", "cabinet_carc_colour": "22943", "cabinet_carc_edge_colour": "2570", "cabinet_ext_colour": "6797", "cabinet_ext_edge_colour": "1449", "hor_grain_ext": "0", "hor_grain_carc": "0", "double_sided_ext": "0", "double_sided_carc": "0", "custom_colour_ext": "1", "cabinet_door": "79", "cabinet_height": 900, "cabinet_width": 550, "cabinet_depth": "590", "cabinet_right_width": 550, "cabinet_left_width": 0, "cabinet_length_depth": "590", "cabinet_width_depth": "590", "cabinet_ladder_frames": 1, "cabinet_upper_filler_depth": 610, "cabinet_toekick": "100", "cabinet_top": -1, "cabinet_applied_panel_depth": 610, "cabinet_total_drawer_height": "720", "microwave_advanced_checkbox": 0, "microwave_opening_height": 600, "cabinet_panel_length": 900, "cabinet_panel_width": 550, "cabinet_width1": 300, "cabinet_length1": 300, "cabinet_length2": 300, "drawer_face_height": [""], "cabinet_drawer_gap": "3", "cabinet_drawer_top": "3", "cabinet_drawer_bottom": "0", "cabinet_drawer_left": "1.5", "cabinet_drawer_right": "1.5", "cabinet_mirror": -1, "cabinet_door_gap": "3", "cabinet_door_bottom": "0", "cabinet_door_top": "3", "cabinet_door_left": "1.5", "cabinet_door_right": "1.5", "available_options": {"is_exterior_material_available": true, "is_carcase_material_available": true, "is_exterior_edge_material_available": true, "is_carcase_edge_material_available": true, "is_door_available": true, "is_cabinet_top_available": true}, "invalid_cabinet": false, "invalid_cabinet_message": ""}}