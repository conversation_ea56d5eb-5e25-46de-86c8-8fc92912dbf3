{"jobStatuses": [], "isBenchtopEnabled": true, "manufacturerName": "Manufacturer 1", "createJobLandingPageOptions": [1, 2, 3, 4, 5, 6, 7], "pricingWebhookDataFormatOptions": [1, 3, 2], "preferences": {"allowOrderAcknowledgementPdf": false, "showJobPropertiesTablePdf": false, "showOrderAcknowledgementPdf": false, "permitGlassSubPanel": false, "permitCustomerSelfRegistration": false, "allowFileUpload": false, "doorHangReversal": false, "displayEstimatedCompletion": false, "maxAgeJobPrice": 30, "permitCoupons": false, "defaultJobPropertiesPdf": 2, "defaultEmailNotificationType": 1, "allowDeliveryDateRequest": false, "requireDeliveryDateRequest": false, "minimumLeadTime": null, "simpleJobWorkflow": 0, "requestedDeliveryDateFrequency": 1, "allowDirectLogin": false, "parentApplicationName": "", "parentApplicationUrl": "", "allowNotchesAndClips": false, "isEnabledJwt": false, "tokenHeaderName": "", "keys": null, "tokenIssuer": "", "tokenAudience": "", "customerMatchRule": "", "manufacturerTokenRule": "", "isEnabledAuth0Validation": false, "createJobLandingPage": 1}, "success": 1}