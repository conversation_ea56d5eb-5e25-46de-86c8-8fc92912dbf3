{"success": 1, "structure": [{"name": "Sidebar", "fieldsets": [{"name": "notes", "title": "My Notes", "fields": [{"name": "cabinet_note", "displayName": "My Notes", "type": "text", "value": "", "options": {"moreInfo": 120, "multiLine": false, "maxLength": 255, "jobCabinetField": "note"}}], "options": {"collapsible": true, "collapsed": "LEN(cabinet_note) = 0", "layout": "add"}}], "layout": "sidebar"}, {"name": "Specs", "preview": "standard", "canvasPreview": true, "fieldsets": [{"name": "sizes", "title": null, "fields": [{"name": "cabinet_type", "displayName": "Cabinet Type", "type": "numeric", "value": 347, "options": {"visible": false, "enabled": false, "jobCabinetField": "type"}}, {"name": "cabinet_quantity", "displayName": "Quantity", "type": "numeric", "value": "1", "options": {"mandatory": true, "qfpField": "quantity", "jobCabinetField": "quantity"}}]}, {"name": "additional", "title": null, "fields": [{"name": "cabinet_panel_length", "displayName": "Height", "type": "size", "value": 0, "options": {"mandatory": true, "moreInfo": 100}}, {"name": "cabinet_panel_width", "displayName": "<PERSON><PERSON><PERSON>", "type": "size", "value": 0, "options": {"mandatory": true, "moreInfo": 101}}]}, {"name": "variations", "title": "Variation Request", "qfpTitle": "Variation Request", "fields": [{"name": "cabinet_comment", "displayName": "Variation Request", "type": "text", "value": "", "options": {"moreInfo": 121, "multiLine": true, "jobCabinetField": "comment", "visible": "0", "enabled": "0"}}, {"name": "variation_request_note", "displayName": null, "type": "label", "value": "Variations may result in an extension of lead time on your order and additional charges may be applied", "options": {"style": "warning"}}], "options": {"collapsible": true, "collapsed": "LEN(cabinet_comment) = 0", "layout": "add"}}]}, {"name": "Materials", "fieldsets": [{"name": "materials", "title": null, "fields": [{"name": "cabinet_ext_colour", "displayName": "Exterior Material", "type": "material", "value": null, "options": {"visible": "1", "mandatory": true, "moreInfo": 115, "fieldPrefix": "cabinet_ext_", "jobCabinetField": "extColour", "hasDoorStyle": true, "doorStyleFieldName": "cabinet_door", "doorStyleMoreInfo": 106, "doorStyleJobCabinetField": "doorStyle", "customColourFieldName": "custom_colour_ext", "customColourJobCabinetField": "extCustomColour", "horizontalGrainFieldName": "hor_grain_ext", "horizontalGrainJobCabinetField": "extHorGrain", "doubleSidedFieldName": "double_sided_ext", "doubleSidedJobCabinetField": "extDoubleSided", "horGrainVisible": "0", "horGrainEnabled": "0", "doubleSidedVisible": "0", "doubleSidedEnabled": "0", "customColourVisible": "0", "customColourEnabled": "0"}}, {"name": "cabinet_ext_edge_colour", "displayName": "Exterior Edge Material", "type": "edge_material", "value": null, "options": {"mandatory": true, "moreInfo": 116, "fieldPrefix": "cabinet_ext_", "materialField": "cabinet_ext_colour", "jobCabinetField": "extEdgeColour", "visible": "1"}}]}]}, {"name": "Drawers", "preview": "edging", "preview_options": {"hingeDirection": "Vertical"}, "fieldsets": [{"name": "drawer_fields", "title": null, "fields": [{"name": "drawer_amount", "displayName": "Drawer Amount", "type": "numeric", "value": "1", "options": {"mandatory": true, "moreInfo": 68, "numericType": "range", "minimum": "1", "maximum": "5"}}, {"name": "drawer_face_type", "displayName": "Drawer Face Type", "type": "select", "value": "2", "options": {"mandatory": true, "selectOptions": [{"value": 2, "label": "Collective Faces"}, {"value": 1, "label": "Individual Faces", "condition": "cabinet_door.advanced"}, {"value": 3, "label": "Reduced Rails", "condition": "cabinet_door.advanced"}], "visible": "drawer_amount > 1", "enabled": "cabinet_door.advanced", "forceDefaultValueWhenDisabled": true}}, {"name": "cabinet_drawer_gap", "displayName": "Drawer Gap", "type": "size", "value": "0", "options": {"moreInfo": 69, "visible": "drawer_amount > 1", "enabled": "1", "forceDefaultValueWhenDisabled": true}}]}, {"name": "border_widths", "title": "Border Widths", "fields": [{"name": "border_width_top", "displayName": "Top", "type": "size", "value": 0, "options": []}, {"name": "border_width_bottom", "displayName": "Bottom", "type": "size", "value": 0, "options": []}, {"name": "border_width_left", "displayName": "Left", "type": "size", "value": 0, "options": []}, {"name": "border_width_right", "displayName": "Right", "type": "size", "value": 0, "options": []}, {"name": "hori_height", "displayName": "Joining", "type": "size", "value": null, "options": {"visible": "AND(drawer_face_type = 1, drawer_amount > 1)"}}, {"name": "nzpg_border_width_variation_note", "displayName": null, "type": "label", "value": "Variations to rail widths may result in additional charges being applied", "options": {"style": "warning", "visible": "OR(\n            border_width_top != cabinet_door.defaultBorderWidthTop,\n            border_width_bottom != cabinet_door.defaultBorderWidthBottom,\n            border_width_left != cabinet_door.defaultBorderWidthLeft,\n            border_width_right != cabinet_door.defaultBorderWidthRight\n        )"}}], "options": {"visible": "cabinet_door.advanced", "enabled": "cabinet_door.advanced", "layout": "compound"}}, {"name": "door_edges", "title": "Panel Edging Design", "fields": [{"name": "panel_edge_top", "displayName": "Top", "type": "edge_finish", "value": null, "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_left", "panel_edge_right"]}}, {"name": "panel_edge_bottom", "displayName": "Bottom", "type": "edge_finish", "value": null, "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_left", "panel_edge_right"]}}, {"name": "panel_edge_left", "displayName": "Left", "type": "edge_finish", "value": null, "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_top", "panel_edge_bottom", "panel_edge_join"]}}, {"name": "panel_edge_right", "displayName": "Right", "type": "edge_finish", "value": null, "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_top", "panel_edge_bottom", "panel_edge_join"]}}, {"name": "panel_edge_join", "displayName": "Joining Border", "type": "edge_finish", "value": null, "options": {"materialField": "cabinet_ext_colour", "visible": "0", "adjacentFields": ["panel_edge_left", "panel_edge_right"], "enabled": "0"}}], "options": {"visible": "1", "enabled": "1", "layout": "compound", "qfpRotateBorderFields": "0"}}, {"name": "drawer_face_heights", "title": "Drawer Faces", "quantity": "drawer_amount", "fields": [{"name": "drawer_face_height", "displayName": "Height {fieldset.index + 1}", "type": "size", "value": null, "options": []}], "options": {"layout": "drawer_face_heights", "totalHeight": "cabinet_panel_length - cabinet_drawer_gap * (drawer_amount - 1)", "drawerGap": "cabinet_drawer_gap", "faceHeightField": "drawer_face_height"}}]}], "validation": [{"type": "range", "value": "cabinet_panel_width", "label": "<PERSON><PERSON><PERSON>", "minimum": 100, "maximum": null, "fields": ["cabinet_panel_width"]}, {"type": "range", "value": "cabinet_panel_length", "label": "Height", "minimum": 150, "maximum": null, "fields": ["cabinet_panel_length"]}, {"type": "range", "value": "drawer_face_height[index]", "minimum": 0, "label": "Drawer face #{index} height", "fields": ["drawer_face_height[index]"], "quantity": "drawer_amount"}, {"type": "panel", "height": "drawer_face_height[index]", "width": "cabinet_panel_width", "field": "cabinet_ext_colour", "horizontal_grain": "hor_grain_ext", "message": "Drawer face #{index} is too large to be cut from the exterior material", "heightFields": ["drawer_face_height[index]"], "widthFields": ["cabinet_panel_width"], "quantity": "drawer_amount", "enabled": "drawer_amount > 1"}, {"type": "panel", "height": "drawer_face_height[1]", "width": "cabinet_panel_width", "field": "cabinet_ext_colour", "horizontal_grain": "hor_grain_ext", "message": "Drawer face is too large to be cut from the exterior material", "heightFields": ["drawer_face_height[1]", "cabinet_panel_length"], "widthFields": ["cabinet_panel_width"], "enabled": "drawer_amount = 1"}, {"type": "range", "value": "drawer_face_height[index]", "minimum": "IF(ISNUMBER(cabinet_door.minimumHeightDrawer), cabinet_door.minimumHeightDrawer, cabinet_door.minimumHeight)", "maximum": "cabinet_door.maximumHeight", "label": "Height of drawer face #{index}", "fields": ["drawer_face_height[index]"], "quantity": "drawer_amount", "enabled": "AND(drawer_face_type = 2, drawer_amount > 1)"}, {"type": "range", "value": "drawer_face_height[index]", "minimum": "cabinet_door.minimumHeight", "maximum": "cabinet_door.maximumHeight", "label": "Height of drawer face #{index}", "fields": ["drawer_face_height[index]"], "quantity": "drawer_amount", "enabled": "OR(drawer_face_type != 2, drawer_amount = 1)"}, {"type": "range", "value": "cabinet_panel_width", "minimum": "cabinet_door.minimumWidth", "maximum": "cabinet_door.maximumWidth", "label": "Width of drawer faces", "fields": ["cabinet_panel_width"]}, {"type": "range", "value": "drawer_face_height[1] - border_width_top - border_width_bottom", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face is too short to accommodate the specified border widths", "fields": ["border_width_top", "border_width_bottom", "drawer_face_height[1]", "cabinet_panel_length"], "enabled": "AND( cabinet_door.advanced, drawer_amount = 1 )"}, {"type": "range", "value": "drawer_face_height[1] - IF(drawer_face_type = 1, border_width_top + hori_height, border_width_top * 1.5)", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #1 is too short to accommodate the specified border widths", "fields": ["border_width_top", "hori_height", "drawer_face_height[1]"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, OR(drawer_face_type = 1, drawer_face_type = 3))"}, {"type": "range", "value": "drawer_face_height[1] - border_width_top", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #1 is too short to accommodate the specified border width", "fields": ["drawer_face_height[1]", "border_width_top"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, drawer_face_type = 2)"}, {"type": "range", "value": "drawer_face_height[index] - hori_height * 2", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #{index} is too short to accommodate the specified border widths", "fields": ["hori_height", "drawer_face_height[index]"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, index > 1, index < drawer_amount, drawer_face_type = 1)", "quantity": "drawer_amount"}, {"type": "range", "value": "drawer_face_height[index] - border_width_top", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #{index} is too short to accommodate the specified border widths", "fields": ["border_width_top", "drawer_face_height[index]"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, index > 1, index < drawer_amount, drawer_face_type = 3)", "quantity": "drawer_amount"}, {"type": "range", "value": "drawer_face_height[index] - border_width_bottom - hori_height", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #{index} is too short to accommodate the specified border widths", "fields": ["border_width_bottom", "hori_height", "drawer_face_height[index]"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, index = drawer_amount, drawer_face_type = 1)", "quantity": "drawer_amount"}, {"type": "range", "value": "drawer_face_height[index] - border_width_bottom - (border_width_top / 2)", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #{index} is too short to accommodate the specified border widths", "fields": ["border_width_bottom", "border_width_top", "drawer_face_height[index]"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, index = drawer_amount, drawer_face_type = 3)", "quantity": "drawer_amount"}, {"type": "range", "value": "drawer_face_height[index] - border_width_bottom", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Drawer face #{index} is too short to accommodate the specified border width", "fields": ["drawer_face_height[index]", "border_width_bottom"], "enabled": "AND(cabinet_door.advanced, drawer_amount > 1, index = drawer_amount, drawer_face_type = 2)", "quantity": "drawer_amount"}, {"type": "range", "value": "cabinet_panel_width - border_width_left - border_width_right", "minimum": "MAX(0, cabinet_door.minimumDistanceLeftRight)", "message": "The drawer faces are too narrow to accommodate the specified border widths", "fields": ["cabinet_panel_width", "border_width_left", "border_width_right"], "enabled": "cabinet_door.advanced"}, {"type": "range", "value": "border_width_top", "minimum": "cabinet_door.minimumBorderWidthTop", "label": "Top", "enabled": "cabinet_door.advanced", "fields": ["border_width_top"]}, {"type": "range", "value": "border_width_bottom", "minimum": "cabinet_door.minimumBorderWidthBottom", "label": "Bottom", "enabled": "cabinet_door.advanced", "fields": ["border_width_bottom"]}, {"type": "range", "value": "border_width_left", "minimum": "cabinet_door.minimumBorderWidthLeft", "label": "Left", "enabled": "cabinet_door.advanced", "fields": ["border_width_left"]}, {"type": "range", "value": "border_width_right", "minimum": "cabinet_door.minimumBorderWidthRight", "label": "Right", "enabled": "cabinet_door.advanced", "fields": ["border_width_right"]}, {"type": "single", "rule": "cabinet_quantity > 0", "message": "Quantity must be greater than 0", "fields": ["cabinet_quantity"]}], "majorDimensions": [["cabinet_height"], ["cabinet_width"]]}