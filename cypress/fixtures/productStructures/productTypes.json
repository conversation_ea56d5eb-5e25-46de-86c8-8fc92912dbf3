{"success": 1, "quick_flat_product_types": [{"id": 381, "type": 381, "order": null, "name": "Cornice <PERSON> - Bullnose", "code": "Cornice Bul", "image": "bullnose_sml_1.png", "image_url": null, "form_fields": {"id": 263, "cabinetId": 381, "cabinet_quantity": 3, "cabinet_note": 3, "cabinet_comment": 0, "cabinet_include_assembly": 3, "cabinet_exclude_hardware": 3, "supply_hardware": 3, "cabinet_carc_colour": 3, "cabinet_carc_edge_colour": 3, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 4, "hor_grain_carc": 3, "double_sided_ext": 3, "double_sided_carc": 3, "custom_colour_carc": 3, "custom_colour_ext": 3, "cabinet_door": 3, "hinge_style": 3, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 3, "cabinet_left_width": 3, "cabinet_length_depth": 3, "cabinet_width_depth": 3, "cabinet_ladder_frames": 3, "cabinet_upper_filler_depth": 3, "cabinet_extend": 3, "cabinet_toekick": 3, "cabinet_top": 3, "cabinet_applied_panel_depth": 3, "cabinet_return_panel_width": 3, "cabinet_cover_void": 3, "cabinet_void_width": 3, "cabinet_total_drawer_height": 3, "microwave_id": 3, "microwave_advanced_checkbox": 3, "microwave_opening_height": 3, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 3, "cabinet_edge_inner": 3, "cabinet_ext_radius": 3, "cabinet_width1": 3, "cabinet_length1": 3, "cabinet_length2": 3, "door_hang": 3, "door_hang_type": 3, "hinge": 3, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 3, "drawer_face_type": 3, "drawer_face_height": 3, "cabinet_drawer_gap": 3, "cabinet_drawer_top": 3, "cabinet_drawer_bottom": 3, "cabinet_drawer_left": 3, "cabinet_drawer_right": 3, "panel_edge_join": 3, "cabinet_mirror": 3, "drilling_offset_x": 3, "drilling_offset_y": 3, "drilling_num_holes": 3, "drilling_pitch": 3, "panel_edge_top": 0, "panel_edge_bottom": 0, "panel_edge_left": 0, "panel_edge_right": 0, "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 3, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 3, "cabinet_door_bottom": 3, "cabinet_door_top": 3, "cabinet_door_left": 3, "cabinet_door_right": 3, "door_hinge_amount": 3, "drawer_runner_specs": 3, "drawer_type": 3, "shelf_type": 3, "shelf_style": 3, "shelf_position": 3, "shelf_offset": 3, "cabinet_hori_shelves": 3, "cabinet_vert_shelves": 3, "upper_shelf_type": 3, "upper_shelf_style": 3, "upper_shelf_position": 3, "upper_shelf_offset": 3, "middle_shelf_type": 3, "middle_shelf_style": 3, "middle_shelf_position": 3, "middle_shelf_offset": 3, "lower_shelf_type": 3, "lower_shelf_style": 3, "lower_shelf_position": 3, "lower_shelf_offset": 3, "cabinet_partition_height": 3, "cabinet_partition_width": 3, "cabinet_upper_partition_height": 3, "cabinet_lower_partition_height": 3, "sub_panel_1": 3, "sub_panel_2": 3, "sub_panel_3": 3, "sub_panel_4": 3, "sub_panel_5": 3, "sub_panel_6": 3, "sub_panel_7": 3, "sub_panel_8": 3, "sub_panel_9": 3, "no_vert_bars": 3, "no_hori_bars": 3, "bar_width": 3, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 3, "vert_width": 3, "vert_amount": 3, "rail_vert": 3, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_panel_length": "2400", "cabinet_panel_width": "230", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 382, "type": 382, "order": null, "name": "Cornice Mould - Colonial", "code": "Cornice Col", "image": "colonial_sml (2).png", "image_url": null, "form_fields": {"id": 264, "cabinetId": 382, "cabinet_quantity": 3, "cabinet_note": 3, "cabinet_comment": 0, "cabinet_include_assembly": 3, "cabinet_exclude_hardware": 3, "supply_hardware": 3, "cabinet_carc_colour": 3, "cabinet_carc_edge_colour": 3, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 4, "hor_grain_carc": 3, "double_sided_ext": 3, "double_sided_carc": 3, "custom_colour_carc": 3, "custom_colour_ext": 3, "cabinet_door": 3, "hinge_style": 3, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 3, "cabinet_left_width": 3, "cabinet_length_depth": 3, "cabinet_width_depth": 3, "cabinet_ladder_frames": 3, "cabinet_upper_filler_depth": 3, "cabinet_extend": 3, "cabinet_toekick": 3, "cabinet_top": 3, "cabinet_applied_panel_depth": 3, "cabinet_return_panel_width": 3, "cabinet_cover_void": 3, "cabinet_void_width": 3, "cabinet_total_drawer_height": 3, "microwave_id": 3, "microwave_advanced_checkbox": 3, "microwave_opening_height": 3, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 3, "cabinet_edge_inner": 3, "cabinet_ext_radius": 3, "cabinet_width1": 3, "cabinet_length1": 3, "cabinet_length2": 3, "door_hang": 3, "door_hang_type": 3, "hinge": 3, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 3, "drawer_face_type": 3, "drawer_face_height": 3, "cabinet_drawer_gap": 3, "cabinet_drawer_top": 3, "cabinet_drawer_bottom": 3, "cabinet_drawer_left": 3, "cabinet_drawer_right": 3, "panel_edge_join": 3, "cabinet_mirror": 3, "drilling_offset_x": 3, "drilling_offset_y": 3, "drilling_num_holes": 3, "drilling_pitch": 3, "panel_edge_top": 0, "panel_edge_bottom": 0, "panel_edge_left": 0, "panel_edge_right": 0, "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 3, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 3, "cabinet_door_bottom": 3, "cabinet_door_top": 3, "cabinet_door_left": 3, "cabinet_door_right": 3, "door_hinge_amount": 3, "drawer_runner_specs": 3, "drawer_type": 3, "shelf_type": 3, "shelf_style": 3, "shelf_position": 3, "shelf_offset": 3, "cabinet_hori_shelves": 3, "cabinet_vert_shelves": 3, "upper_shelf_type": 3, "upper_shelf_style": 3, "upper_shelf_position": 3, "upper_shelf_offset": 3, "middle_shelf_type": 3, "middle_shelf_style": 3, "middle_shelf_position": 3, "middle_shelf_offset": 3, "lower_shelf_type": 3, "lower_shelf_style": 3, "lower_shelf_position": 3, "lower_shelf_offset": 3, "cabinet_partition_height": 3, "cabinet_partition_width": 3, "cabinet_upper_partition_height": 3, "cabinet_lower_partition_height": 3, "sub_panel_1": 3, "sub_panel_2": 3, "sub_panel_3": 3, "sub_panel_4": 3, "sub_panel_5": 3, "sub_panel_6": 3, "sub_panel_7": 3, "sub_panel_8": 3, "sub_panel_9": 3, "no_vert_bars": 3, "no_hori_bars": 3, "bar_width": 3, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 3, "vert_width": 3, "vert_amount": 3, "rail_vert": 3, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_panel_length": "2400", "cabinet_panel_width": "230", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 383, "type": 383, "order": null, "name": "Cornice Mould - Single Rounded Over", "code": "Cornice SRO", "image": "SRO_sml_1.png", "image_url": null, "form_fields": {"id": 265, "cabinetId": 383, "cabinet_quantity": 3, "cabinet_note": 3, "cabinet_comment": 0, "cabinet_include_assembly": 3, "cabinet_exclude_hardware": 3, "supply_hardware": 3, "cabinet_carc_colour": 3, "cabinet_carc_edge_colour": 3, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 4, "hor_grain_carc": 3, "double_sided_ext": 3, "double_sided_carc": 3, "custom_colour_carc": 3, "custom_colour_ext": 3, "cabinet_door": 3, "hinge_style": 3, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 3, "cabinet_left_width": 3, "cabinet_length_depth": 3, "cabinet_width_depth": 3, "cabinet_ladder_frames": 3, "cabinet_upper_filler_depth": 3, "cabinet_extend": 3, "cabinet_toekick": 3, "cabinet_top": 3, "cabinet_applied_panel_depth": 3, "cabinet_return_panel_width": 3, "cabinet_cover_void": 3, "cabinet_void_width": 3, "cabinet_total_drawer_height": 3, "microwave_id": 3, "microwave_advanced_checkbox": 3, "microwave_opening_height": 3, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 3, "cabinet_edge_inner": 3, "cabinet_ext_radius": 3, "cabinet_width1": 3, "cabinet_length1": 3, "cabinet_length2": 3, "door_hang": 3, "door_hang_type": 3, "hinge": 3, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 3, "drawer_face_type": 3, "drawer_face_height": 3, "cabinet_drawer_gap": 3, "cabinet_drawer_top": 3, "cabinet_drawer_bottom": 3, "cabinet_drawer_left": 3, "cabinet_drawer_right": 3, "panel_edge_join": 3, "cabinet_mirror": 3, "drilling_offset_x": 3, "drilling_offset_y": 3, "drilling_num_holes": 3, "drilling_pitch": 3, "panel_edge_top": 0, "panel_edge_bottom": 0, "panel_edge_left": 0, "panel_edge_right": 0, "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 3, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 3, "cabinet_door_bottom": 3, "cabinet_door_top": 3, "cabinet_door_left": 3, "cabinet_door_right": 3, "door_hinge_amount": 3, "drawer_runner_specs": 3, "drawer_type": 3, "shelf_type": 3, "shelf_style": 3, "shelf_position": 3, "shelf_offset": 3, "cabinet_hori_shelves": 3, "cabinet_vert_shelves": 3, "upper_shelf_type": 3, "upper_shelf_style": 3, "upper_shelf_position": 3, "upper_shelf_offset": 3, "middle_shelf_type": 3, "middle_shelf_style": 3, "middle_shelf_position": 3, "middle_shelf_offset": 3, "lower_shelf_type": 3, "lower_shelf_style": 3, "lower_shelf_position": 3, "lower_shelf_offset": 3, "cabinet_partition_height": 3, "cabinet_partition_width": 3, "cabinet_upper_partition_height": 3, "cabinet_lower_partition_height": 3, "sub_panel_1": 3, "sub_panel_2": 3, "sub_panel_3": 3, "sub_panel_4": 3, "sub_panel_5": 3, "sub_panel_6": 3, "sub_panel_7": 3, "sub_panel_8": 3, "sub_panel_9": 3, "no_vert_bars": 3, "no_hori_bars": 3, "bar_width": 3, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 3, "vert_width": 3, "vert_amount": 3, "rail_vert": 3, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_panel_length": "2400", "cabinet_panel_width": "230", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 339, "type": 339, "order": 1, "name": "Door", "code": "Doordzt", "image": "Door (2).png", "image_url": null, "form_fields": {"id": 12, "cabinetId": 339, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 3, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 4, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 4, "sub_panel_2": 4, "sub_panel_3": 4, "sub_panel_4": 4, "sub_panel_5": 4, "sub_panel_6": 4, "sub_panel_7": 4, "sub_panel_8": 4, "sub_panel_9": 4, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 1, "border_width_bottom": 1, "border_width_left": 1, "border_width_right": 1, "hori_height": 3, "drawer_border_width_top": 4, "drawer_border_width_bottom": 4, "drawer_border_width_left": 4, "drawer_border_width_right": 4, "drawer_hori_height": 4, "hori_amount": 4, "vert_width": 4, "vert_amount": 4, "rail_vert": 4, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "door_hang": "2", "door_hang_type": "1", "border_width_top": "103", "border_width_bottom": "103", "border_width_left": "103", "border_width_right": "103", "rail_vert": "0", "rail_hori": "0", "has_advanced_materials": "0"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 352, "type": 352, "order": 2, "name": "Drawer", "code": "Drawerdzt", "image": "drawerdzt_sml.png", "image_url": null, "form_fields": {"id": 219, "cabinetId": 352, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 0, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 0, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 0, "door_hang_type": 0, "hinge": 0, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 2, "drawer_face_type": 2, "drawer_face_height": 4, "cabinet_drawer_gap": 2, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 1, "sub_panel_2": 1, "sub_panel_3": 1, "sub_panel_4": 1, "sub_panel_5": 1, "sub_panel_6": 1, "sub_panel_7": 1, "sub_panel_8": 1, "sub_panel_9": 1, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 4, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 4, "hori_amount": 4, "vert_width": 4, "vert_amount": 4, "rail_vert": 4, "rail_hori": 4, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "drawer_amount": "1", "drawer_face_type": "2", "cabinet_drawer_gap": "0"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 347, "type": 347, "order": 3, "name": "Drawer Pack", "code": "Drawerpack", "image": "drawers_sml.png", "image_url": null, "form_fields": {"id": 214, "cabinetId": 347, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 0, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 1, "hinge_style": 0, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 0, "door_hang_type": 0, "hinge": 0, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 1, "drawer_face_type": 1, "drawer_face_height": 3, "cabinet_drawer_gap": 1, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 0, "sub_panel_2": 0, "sub_panel_3": 0, "sub_panel_4": 0, "sub_panel_5": 0, "sub_panel_6": 0, "sub_panel_7": 0, "sub_panel_8": 0, "sub_panel_9": 0, "no_vert_bars": 0, "no_hori_bars": 0, "bar_width": 0, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 4, "vert_width": 0, "vert_amount": 0, "rail_vert": 0, "rail_hori": 0, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "drawer_amount": "1", "drawer_face_type": "2", "cabinet_drawer_gap": "0"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 334, "type": 334, "order": 6, "name": "Flat Panel", "code": "FLPan", "image": "ManualPanelExterior01 (4).png", "image_url": null, "form_fields": {"id": 7, "cabinetId": 334, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 0, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 0, "hinge_style": 0, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 0, "door_hang_type": 0, "hinge": 0, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 0, "panel_edge_bottom": 0, "panel_edge_left": 0, "panel_edge_right": 0, "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 0, "sub_panel_2": 0, "sub_panel_3": 0, "sub_panel_4": 0, "sub_panel_5": 0, "sub_panel_6": 0, "sub_panel_7": 0, "sub_panel_8": 0, "sub_panel_9": 0, "no_vert_bars": 0, "no_hori_bars": 0, "bar_width": 0, "border_width_top": 0, "border_width_bottom": 0, "border_width_left": 0, "border_width_right": 0, "hori_height": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0, "hori_amount": 0, "vert_width": 0, "vert_amount": 0, "rail_vert": 0, "rail_hori": 0, "has_advanced_materials": 0, "fieldDefaults": {"cabinet_quantity": "1", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 336, "type": 336, "order": null, "name": "<PERSON>ame", "code": "GlassFrame", "image": "Frame.png", "image_url": null, "form_fields": {"id": 8, "cabinetId": 336, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 2, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 2, "sub_panel_2": 2, "sub_panel_3": 2, "sub_panel_4": 2, "sub_panel_5": 2, "sub_panel_6": 2, "sub_panel_7": 2, "sub_panel_8": 2, "sub_panel_9": 2, "no_vert_bars": 2, "no_hori_bars": 2, "bar_width": 2, "border_width_top": 1, "border_width_bottom": 1, "border_width_left": 1, "border_width_right": 1, "hori_height": 2, "drawer_border_width_top": 1, "drawer_border_width_bottom": 1, "drawer_border_width_left": 1, "drawer_border_width_right": 1, "drawer_hori_height": 2, "hori_amount": 2, "vert_width": 2, "vert_amount": 2, "rail_vert": 2, "rail_hori": 2, "has_advanced_materials": 2, "fieldDefaults": {"cabinet_quantity": "1", "supply_hardware": "0", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "sub_panel_1": "0", "sub_panel_2": "0", "sub_panel_3": "0", "sub_panel_4": "0", "sub_panel_5": "1", "sub_panel_6": "0", "sub_panel_7": "0", "sub_panel_8": "0", "sub_panel_9": "0", "no_vert_bars": "0", "no_hori_bars": "0", "bar_width": "10", "border_width_top": "60", "border_width_bottom": "60", "border_width_left": "60", "border_width_right": "60", "hori_height": "55", "hori_amount": "0", "vert_width": "55", "vert_amount": "0", "rail_vert": "0", "rail_hori": "0", "has_advanced_materials": "1"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 385, "type": 385, "order": null, "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "image": "Lattice_sml.png", "image_url": null, "form_fields": {"id": 270, "cabinetId": 385, "cabinet_quantity": 3, "cabinet_note": 3, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 4, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 4, "hor_grain_carc": 0, "double_sided_ext": 4, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 1, "hinge_style": 4, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 1, "panel_edge_bottom": 1, "panel_edge_left": 1, "panel_edge_right": 1, "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 2, "sub_panel_2": 2, "sub_panel_3": 2, "sub_panel_4": 2, "sub_panel_5": 2, "sub_panel_6": 2, "sub_panel_7": 2, "sub_panel_8": 2, "sub_panel_9": 2, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 1, "border_width_bottom": 1, "border_width_left": 1, "border_width_right": 1, "hori_height": 1, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0, "hori_amount": 3, "vert_width": 1, "vert_amount": 1, "rail_vert": 3, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"door_hang": "2", "door_hang_type": "1", "panel_edge_top": "25", "panel_edge_bottom": "25", "panel_edge_left": "25", "panel_edge_right": "25", "sub_panel_1": "1", "sub_panel_2": "1", "sub_panel_3": "1", "sub_panel_4": "1", "sub_panel_5": "1", "sub_panel_6": "1", "sub_panel_7": "1", "sub_panel_8": "1", "sub_panel_9": "1", "border_width_top": "60", "border_width_bottom": "60", "border_width_left": "60", "border_width_right": "60", "hori_height": "21", "vert_width": "21", "vert_amount": "1"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 337, "type": 337, "order": null, "name": "Microwave Frame", "code": "MicroFr", "image": "<PERSON><PERSON> (2).png", "image_url": null, "form_fields": {"id": 9, "cabinetId": 337, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 2, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 2, "sub_panel_2": 2, "sub_panel_3": 2, "sub_panel_4": 2, "sub_panel_5": 2, "sub_panel_6": 2, "sub_panel_7": 2, "sub_panel_8": 2, "sub_panel_9": 2, "no_vert_bars": 2, "no_hori_bars": 2, "bar_width": 2, "border_width_top": 1, "border_width_bottom": 1, "border_width_left": 1, "border_width_right": 1, "hori_height": 2, "drawer_border_width_top": 1, "drawer_border_width_bottom": 1, "drawer_border_width_left": 1, "drawer_border_width_right": 1, "drawer_hori_height": 2, "hori_amount": 2, "vert_width": 2, "vert_amount": 2, "rail_vert": 2, "rail_hori": 2, "has_advanced_materials": 2, "fieldDefaults": {"cabinet_quantity": "1", "supply_hardware": "0", "cabinet_door": "339", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "sub_panel_1": "0", "sub_panel_2": "0", "sub_panel_3": "0", "sub_panel_4": "0", "sub_panel_5": "1", "sub_panel_6": "0", "sub_panel_7": "0", "sub_panel_8": "0", "sub_panel_9": "0", "no_vert_bars": "0", "no_hori_bars": "0", "bar_width": "0", "border_width_top": "20", "border_width_bottom": "20", "border_width_left": "20", "border_width_right": "20", "hori_height": "0", "hori_amount": "0", "vert_width": "0", "vert_amount": "0", "rail_vert": "0", "rail_hori": "0", "has_advanced_materials": "1"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 348, "type": 348, "order": null, "name": "Paint Quality One Piece Doors", "code": "1pDoordzt", "image": "1pcpaintdoor.png", "image_url": null, "form_fields": {"id": 215, "cabinetId": 348, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 4, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 4, "sub_panel_2": 4, "sub_panel_3": 4, "sub_panel_4": 4, "sub_panel_5": 4, "sub_panel_6": 4, "sub_panel_7": 4, "sub_panel_8": 4, "sub_panel_9": 4, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 2, "hori_amount": 3, "vert_width": 2, "vert_amount": 2, "rail_vert": 2, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "hori_height": "0", "hori_amount": "0", "vert_width": "0", "vert_amount": "0", "rail_vert": "0", "rail_hori": "0"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 349, "type": 349, "order": null, "name": "Paint Quality Two Piece Doors", "code": "2pDoordzt", "image": "2pcpaintdoor.png", "image_url": null, "form_fields": {"id": 216, "cabinetId": 349, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 4, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 4, "sub_panel_2": 4, "sub_panel_3": 4, "sub_panel_4": 4, "sub_panel_5": 4, "sub_panel_6": 4, "sub_panel_7": 4, "sub_panel_8": 4, "sub_panel_9": 4, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 2, "hori_amount": 3, "vert_width": 2, "vert_amount": 2, "rail_vert": 2, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "hori_height": "0", "hori_amount": "0", "vert_width": "0", "vert_amount": "0", "rail_vert": "0", "rail_hori": "0"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 333, "type": 333, "order": null, "name": "Panel", "code": "Pan", "image": "ManualPanelExterior01 (2).png", "image_url": null, "form_fields": {"id": 10, "cabinetId": 333, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 0, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 0, "hinge_style": 0, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 0, "door_hang_type": 0, "hinge": 0, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 0, "panel_edge_bottom": 0, "panel_edge_left": 0, "panel_edge_right": 0, "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 1, "cabinet_edge_w2": 1, "cabinet_edge_l1": 1, "cabinet_edge_l2": 1, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 0, "sub_panel_2": 0, "sub_panel_3": 0, "sub_panel_4": 0, "sub_panel_5": 0, "sub_panel_6": 0, "sub_panel_7": 0, "sub_panel_8": 0, "sub_panel_9": 0, "no_vert_bars": 0, "no_hori_bars": 0, "bar_width": 0, "border_width_top": 0, "border_width_bottom": 0, "border_width_left": 0, "border_width_right": 0, "hori_height": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0, "hori_amount": 0, "vert_width": 0, "vert_amount": 0, "rail_vert": 0, "rail_hori": 0, "has_advanced_materials": 0, "fieldDefaults": {"cabinet_quantity": "1", "supply_hardware": "0", "cabinet_door": "328", "hinge_style": "21", "door_hang": "2", "door_hang_type": "1", "panel_edge_top": "0", "panel_edge_bottom": "0", "panel_edge_left": "0", "panel_edge_right": "0", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "cabinet_edge_w1": "0", "cabinet_edge_w2": "0", "cabinet_edge_l1": "0", "cabinet_edge_l2": "0", "sub_panel_1": "0", "sub_panel_2": "0", "sub_panel_3": "0", "sub_panel_4": "0", "sub_panel_5": "0", "sub_panel_6": "0", "sub_panel_7": "0", "sub_panel_8": "0", "sub_panel_9": "0", "no_vert_bars": "0", "no_hori_bars": "0", "bar_width": "0", "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0, "has_advanced_materials": "0"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 378, "type": 378, "order": 5, "name": "Panel 180", "code": "Panel180dzt", "image": "Panel_180_sml.png", "image_url": null, "form_fields": {"id": 257, "cabinetId": 378, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 4, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 1, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 2, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 1, "panel_edge_bottom": 1, "panel_edge_left": 1, "panel_edge_right": 1, "drawer_panel_edge_top": 1, "drawer_panel_edge_bottom": 1, "drawer_panel_edge_left": 1, "drawer_panel_edge_right": 1, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 3, "sub_panel_2": 3, "sub_panel_3": 3, "sub_panel_4": 3, "sub_panel_5": 3, "sub_panel_6": 3, "sub_panel_7": 3, "sub_panel_8": 3, "sub_panel_9": 3, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 3, "vert_width": 3, "vert_amount": 3, "rail_vert": 3, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "hinge": "284", "panel_edge_top": "0", "panel_edge_bottom": "0", "panel_edge_left": "0", "panel_edge_right": "32", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 350, "type": 350, "order": 4, "name": "Panel 90", "code": "Paneldzt", "image": "Panel_90_sml.png", "image_url": null, "form_fields": {"id": 217, "cabinetId": 350, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 4, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 1, "panel_edge_bottom": 1, "panel_edge_left": 1, "panel_edge_right": 1, "drawer_panel_edge_top": 1, "drawer_panel_edge_bottom": 1, "drawer_panel_edge_left": 1, "drawer_panel_edge_right": 1, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 3, "sub_panel_2": 3, "sub_panel_3": 3, "sub_panel_4": 3, "sub_panel_5": 3, "sub_panel_6": 3, "sub_panel_7": 3, "sub_panel_8": 3, "sub_panel_9": 3, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 3, "vert_width": 3, "vert_amount": 3, "rail_vert": 3, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "panel_edge_top": "0", "panel_edge_bottom": "0", "panel_edge_left": "0", "panel_edge_right": "25", "drawer_panel_edge_top": 0, "drawer_panel_edge_bottom": 0, "drawer_panel_edge_left": 0, "drawer_panel_edge_right": 0, "drawer_panel_edge_join": 0, "drawer_border_width_top": 0, "drawer_border_width_bottom": 0, "drawer_border_width_left": 0, "drawer_border_width_right": 0, "drawer_hori_height": 0}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 357, "type": 357, "order": null, "name": "Patterned Panel 180", "code": "Pat<PERSON><PERSON><PERSON>", "image": "patternedpanel.png", "image_url": null, "form_fields": {"id": 225, "cabinetId": 357, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 4, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 1, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 3, "cabinet_width_door_2": 3, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 1, "panel_edge_bottom": 1, "panel_edge_left": 1, "panel_edge_right": 1, "drawer_panel_edge_top": 1, "drawer_panel_edge_bottom": 1, "drawer_panel_edge_left": 1, "drawer_panel_edge_right": 1, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 4, "sub_panel_2": 4, "sub_panel_3": 4, "sub_panel_4": 4, "sub_panel_5": 4, "sub_panel_6": 4, "sub_panel_7": 4, "sub_panel_8": 4, "sub_panel_9": 4, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 3, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 3, "hori_amount": 3, "vert_width": 4, "vert_amount": 4, "rail_vert": 4, "rail_hori": 3, "has_advanced_materials": 3, "fieldDefaults": {"cabinet_quantity": "1", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "panel_edge_top": "32", "panel_edge_bottom": "32", "panel_edge_left": "23", "panel_edge_right": "23"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}, {"id": 338, "type": 338, "order": null, "name": "Roller Surround Frame", "code": "RollerFrame", "image": "Rollersurround.png", "image_url": null, "form_fields": {"id": 11, "cabinetId": 338, "cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 2, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 1, "cabinet_ext_edge_colour": 1, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 2, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 2, "door_hang_type": 2, "hinge": 4, "cabinet_width_door_1": 1, "cabinet_width_door_2": 1, "drawer_amount": 0, "drawer_face_type": 0, "drawer_face_height": 0, "cabinet_drawer_gap": 0, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 2, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 2, "drawer_panel_edge_left": 1, "drawer_panel_edge_right": 1, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 2, "sub_panel_2": 2, "sub_panel_3": 2, "sub_panel_4": 2, "sub_panel_5": 2, "sub_panel_6": 2, "sub_panel_7": 2, "sub_panel_8": 2, "sub_panel_9": 2, "no_vert_bars": 2, "no_hori_bars": 2, "bar_width": 2, "border_width_top": 1, "border_width_bottom": 2, "border_width_left": 1, "border_width_right": 1, "hori_height": 2, "drawer_border_width_top": 1, "drawer_border_width_bottom": 2, "drawer_border_width_left": 1, "drawer_border_width_right": 1, "drawer_hori_height": 2, "hori_amount": 2, "vert_width": 2, "vert_amount": 2, "rail_vert": 2, "rail_hori": 2, "has_advanced_materials": 2, "fieldDefaults": {"cabinet_quantity": "1", "supply_hardware": "0", "cabinet_door": "339", "hinge_style": "284", "door_hang": "2", "door_hang_type": "1", "panel_edge_bottom": "0", "sub_panel_1": "0", "sub_panel_2": "0", "sub_panel_3": "0", "sub_panel_4": "0", "sub_panel_5": "1", "sub_panel_6": "0", "sub_panel_7": "0", "sub_panel_8": "0", "sub_panel_9": "0", "no_vert_bars": "0", "no_hori_bars": "0", "bar_width": "0", "border_width_top": "60", "border_width_bottom": "0", "border_width_left": "60", "border_width_right": "60", "hori_height": "0", "hori_amount": "0", "vert_width": "0", "vert_amount": "0", "rail_vert": "0", "rail_hori": "0", "has_advanced_materials": "1"}}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}]}