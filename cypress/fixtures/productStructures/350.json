{"success": 1, "structure": [{"name": "Sidebar", "fieldsets": [{"name": "notes", "title": "My Notes", "fields": [{"name": "cabinet_note", "displayName": "My Notes", "type": "text", "value": "", "options": {"moreInfo": 120, "multiLine": false, "maxLength": 255, "jobCabinetField": "note"}}], "options": {"collapsible": true, "collapsed": "LEN(cabinet_note) = 0", "layout": "add"}}], "layout": "sidebar"}, {"name": "Specs", "preview": "standard", "canvasPreview": true, "fieldsets": [{"name": "sizes", "title": null, "fields": [{"name": "cabinet_type", "displayName": "Cabinet Type", "type": "numeric", "value": 350, "options": {"visible": false, "enabled": false, "jobCabinetField": "type"}}, {"name": "cabinet_quantity", "displayName": "Quantity", "type": "numeric", "value": "1", "options": {"mandatory": true, "qfpField": "quantity", "jobCabinetField": "quantity"}}]}, {"name": "additional", "title": null, "fields": [{"name": "cabinet_panel_length", "displayName": "Height", "type": "size", "value": 0, "options": {"mandatory": true, "moreInfo": 99}}, {"name": "cabinet_panel_width", "displayName": "<PERSON><PERSON><PERSON>", "type": "size", "value": "cabinet_width_door_1", "options": {"mandatory": true, "moreInfo": 101, "visible": "door_hang_type <> 3", "enabled": "door_hang_type <> 3", "forceDefaultValueWhenDisabled": true}}]}, {"name": "variations", "title": "Variation Request", "qfpTitle": "Variation Request", "fields": [{"name": "cabinet_comment", "displayName": "Variation Request", "type": "text", "value": "", "options": {"moreInfo": 121, "multiLine": true, "jobCabinetField": "comment", "visible": "0", "enabled": "0"}}, {"name": "variation_request_note", "displayName": null, "type": "label", "value": "Variations may result in an extension of lead time on your order and additional charges may be applied", "options": {"style": "warning"}}], "options": {"collapsible": true, "collapsed": "LEN(cabinet_comment) = 0", "layout": "add"}}]}, {"name": "Materials", "fieldsets": [{"name": "materials", "title": null, "fields": [{"name": "cabinet_ext_colour", "displayName": "Door Material", "type": "material", "value": null, "options": {"visible": "1", "mandatory": true, "moreInfo": 115, "fieldPrefix": "cabinet_ext_", "jobCabinetField": "extColour", "hasDoorStyle": true, "doorStyleFieldName": "cabinet_door", "doorStyleMoreInfo": 105, "doorStyleJobCabinetField": "doorStyle", "customColourFieldName": "custom_colour_ext", "customColourJobCabinetField": "extCustomColour", "horizontalGrainFieldName": "hor_grain_ext", "horizontalGrainJobCabinetField": "extHorGrain", "doubleSidedFieldName": "double_sided_ext", "doubleSidedJobCabinetField": "extDoubleSided", "horGrainVisible": "0", "horGrainEnabled": "0", "doubleSidedVisible": "0", "doubleSidedEnabled": "0", "customColourVisible": "0", "customColourEnabled": "0"}}, {"name": "cabinet_ext_edge_colour", "displayName": "Exterior Edge Material", "type": "edge_material", "value": null, "options": {"mandatory": true, "moreInfo": 116, "fieldPrefix": "cabinet_ext_", "materialField": "cabinet_ext_colour", "jobCabinetField": "extEdgeColour", "visible": "1"}}]}]}, {"name": "Doors", "preview": "edging", "preview_options": {"hingeDirection": "Vertical"}, "fieldsets": [{"name": "door_fields", "title": null, "fields": [{"name": "door_hang_type", "displayName": "Door Type", "type": "select", "value": "1", "options": {"mandatory": true, "selectOptions": [{"value": 1, "label": "Single"}, {"value": 2, "label": "Pair"}, {"value": 3, "label": "L-Shape"}, {"value": 4, "label": "Straight Bifold"}], "visible": "0", "enabled": "0"}}, {"name": "door_hang", "displayName": "Door Hang", "type": "select", "value": "2", "options": {"mandatory": true, "moreInfo": 64, "selectOptions": [{"value": 0, "label": "Left Bifold", "condition": "OR(door_hang_type = 3, door_hang_type = 4)"}, {"value": 0, "label": "Left", "condition": "door_hang_type = 1"}, {"value": 1, "label": "Pair", "condition": "door_hang_type = 2"}, {"value": 1, "label": "Right Bifold", "condition": "OR(door_hang_type = 3, door_hang_type = 4)"}, {"value": 1, "label": "Right", "condition": "door_hang_type = 1"}, {"value": 2, "label": "None"}], "visible": "0", "enabled": "0"}}, {"name": "hinge_style", "displayName": "Hinge Style", "type": "hinge_style", "value": "284", "options": {"mandatory": true, "moreInfo": 65, "visible": "0", "enabled": "0"}}, {"name": "cabinet_width_door_1", "displayName": "Width Door 1", "type": "size", "value": "cabinet_panel_width", "options": {"mandatory": true, "moreInfo": 66, "visible": "door_hang_type = 3", "enabled": "door_hang_type = 3", "forceDefaultValueWhenDisabled": true}}, {"name": "cabinet_width_door_2", "displayName": "Width Door 2", "type": "size", "value": null, "options": {"mandatory": true, "moreInfo": 67, "visible": "door_hang_type = 3", "enabled": "door_hang_type = 3"}}]}, {"name": "border_widths", "title": "Border Widths", "fields": [{"name": "border_width_top", "displayName": "Top", "type": "size", "value": 0, "options": []}, {"name": "border_width_bottom", "displayName": "Bottom", "type": "size", "value": 0, "options": []}, {"name": "border_width_left", "displayName": "Left", "type": "size", "value": 0, "options": []}, {"name": "border_width_right", "displayName": "Right", "type": "size", "value": 0, "options": []}, {"name": "nzpg_border_width_variation_note", "displayName": null, "type": "label", "value": "Variations to rail widths may result in additional charges being applied", "options": {"style": "warning", "visible": "OR(\n            border_width_top != cabinet_door.defaultBorderWidthTop,\n            border_width_bottom != cabinet_door.defaultBorderWidthBottom,\n            border_width_left != cabinet_door.defaultBorderWidthLeft,\n            border_width_right != cabinet_door.defaultBorderWidthRight\n        )"}}], "options": {"visible": "cabinet_door.advanced", "enabled": "cabinet_door.advanced", "layout": "compound"}}, {"name": "door_edges", "title": "Panel Edging Design", "fields": [{"name": "panel_edge_top", "displayName": "Top", "type": "edge_finish", "value": "0", "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_left", "panel_edge_right"]}}, {"name": "panel_edge_bottom", "displayName": "Bottom", "type": "edge_finish", "value": "0", "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_left", "panel_edge_right"]}}, {"name": "panel_edge_left", "displayName": "Left", "type": "edge_finish", "value": "0", "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_top", "panel_edge_bottom", "panel_edge_join"]}}, {"name": "panel_edge_right", "displayName": "Right", "type": "edge_finish", "value": "25", "options": {"materialField": "cabinet_ext_colour", "adjacentFields": ["panel_edge_top", "panel_edge_bottom", "panel_edge_join"]}}], "options": {"visible": "1", "enabled": "1", "layout": "compound", "qfpRotateBorderFields": "0"}}, {"fields": [{"displayName": "Position #{fieldset.index + 1}", "name": "rail_hori", "options": [], "type": "size", "value": null}, {"displayName": "Height", "name": "hori_height", "options": [], "type": "size", "value": null}, {"displayName": "Horizontal Amount", "name": "hori_amount", "options": {"maximum": "4", "minimum": "0", "numericType": "range", "visible": false}, "type": "numeric", "value": null}, {"displayName": "Position #{fieldset.index + 1}", "name": "rail_vert", "options": [], "type": "size", "value": null}, {"displayName": "Height", "name": "vert_width", "options": [], "type": "size", "value": null}, {"displayName": "Vertical Amount", "name": "vert_amount", "options": {"maximum": "4", "minimum": "0", "numericType": "range", "visible": false}, "type": "numeric", "value": null}], "name": "mid_rails", "options": {"horizontal_rail_height_field": "hori_height", "horizontal_rail_position_field": "rail_hori", "horizontal_rail_quantity_field": "hori_amount", "layout": "mid_rails", "vertical_rail_height_field": "vert_width", "vertical_rail_position_field": "rail_vert", "vertical_rail_quantity_field": "vert_amount", "visible": "cabinet_door.advanced"}, "title": "Mid Rails"}, {"name": "hori_mid_rail_positions", "title": "Horizontal Rail Positions", "fields": [{"name": "rail_hori", "displayName": "Position #{fieldset.index + 1}", "type": "size", "value": null, "options": []}], "quantity": "hori_amount", "options": {"layout": "compound", "visible": "cabinet_door.advanced"}}, {"name": "vert_mid_rail_positions", "title": "Vertical Rail Positions", "fields": [{"name": "rail_vert", "displayName": "Position #{fieldset.index + 1}", "type": "size", "value": null, "options": []}], "quantity": "vert_amount", "options": {"layout": "compound", "visible": "cabinet_door.advanced"}}, {"name": "advanced_door_glass", "title": null, "fields": [{"name": "sub_panel", "displayName": "Cut outs for glass", "type": "glass_sub_panel", "value": null, "options": {"moreInfo": 130, "horizontalAmountField": "hori_amount", "verticalAmountField": "vert_amount", "subPanelSettings": []}}, {"name": "no_vert_bars", "displayName": "No. Vertical Bars", "type": "numeric", "value": null, "options": {"visible": "0", "enabled": "0"}}, {"name": "no_hori_bars", "displayName": "No. Horizontal Bars", "type": "numeric", "value": null, "options": {"visible": "0", "enabled": "0"}}, {"name": "bar_width", "displayName": "Bar W<PERSON>th", "type": "size", "value": null, "options": {"visible": "0", "enabled": "0"}}, {"name": "door_mirror_label", "displayName": null, "type": "label", "value": "Advanced options apply to the left door and will be mirrored on the right door.", "options": {"style": "warning", "visible": "0"}}], "options": {"visible": "cabinet_door.advanced"}}]}, {"name": "Drillings", "preview": null, "fieldsets": [{"name": "door_drillings", "title": "Hinge Hole", "fields": [{"name": "hinge", "displayName": "Distance from {IF(fieldSet.index = 0, 'Top', 'Bottom')}", "type": "size", "value": null, "options": {"visible": "0", "enabled": "0"}}], "options": {"layout": "door_drillings", "drillingPositionFieldName": "hinge", "totalHeight": "cabinet_panel_length", "enabled": "door_hang != 2"}}]}], "validation": [{"type": "range", "value": "cabinet_width_door_1", "label": "Width Door 1", "minimum": 40, "maximum": null, "enabled": "door_hang_type = 3", "fields": ["cabinet_width_door_1"]}, {"type": "range", "value": "cabinet_width_door_2", "label": "Width Door 2", "minimum": 40, "maximum": null, "enabled": "door_hang_type = 3", "fields": ["cabinet_width_door_2"]}, {"type": "range", "value": "cabinet_panel_width", "label": "<PERSON><PERSON><PERSON>", "minimum": 40, "maximum": null, "enabled": "door_hang_type <> 3", "fields": ["cabinet_panel_width"]}, {"type": "panel", "height": "cabinet_panel_length", "width": "cabinet_panel_width", "field": "cabinet_ext_colour", "horizontal_grain": "hor_grain_ext", "message": "Door is too large to be cut from the exterior material", "heightFields": ["cabinet_panel_length"], "widthFields": ["cabinet_panel_width"], "enabled": "door_hang_type <> 3"}, {"type": "range", "value": "cabinet_panel_length", "minimum": "cabinet_door.minimumHeight", "maximum": "cabinet_door.maximumHeight", "label": "Height of door", "fields": ["cabinet_panel_length"], "enabled": "door_hang_type <> 3"}, {"type": "range", "value": "cabinet_panel_width", "minimum": "cabinet_door.minimumWidth", "maximum": "cabinet_door.maximumWidth", "label": "Width of door", "fields": ["cabinet_panel_width"], "enabled": "door_hang_type <> 3"}, {"type": "range", "value": "cabinet_panel_length", "minimum": "0", "maximum": "cabinet_door.maximumHeight", "label": "Height of door", "fields": ["cabinet_panel_length"], "enabled": "door_hang_type <> 3"}, {"type": "range", "value": "cabinet_panel_width", "minimum": "0", "maximum": "cabinet_door.maximumWidth", "label": "Width of door", "fields": ["cabinet_panel_width"], "enabled": "door_hang_type <> 3"}, {"type": "range", "value": "cabinet_panel_length - border_width_top - border_width_bottom", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Door is too short to accommodate the specified border widths", "fields": ["cabinet_panel_length", "border_width_top", "border_width_bottom"], "enabled": "AND(AND(cabinet_door.advanced, door_hang_type <> 3), hori_amount == 0, vert_amount == 0)"}, {"type": "range", "value": "cabinet_panel_width - border_width_left - border_width_right", "minimum": "MAX(0, cabinet_door.minimumDistanceLeftRight)", "message": "Door is too narrow to accommodate the specified border widths", "fields": ["cabinet_panel_width", "border_width_left", "border_width_right"], "enabled": "AND(AND(cabinet_door.advanced, door_hang_type <> 3), hori_amount == 0, vert_amount == 0)"}, {"type": "panel", "height": "cabinet_panel_length", "width": "cabinet_width_door_1", "field": "cabinet_ext_colour", "horizontal_grain": "hor_grain_ext", "message": "Door 1 is too large to be cut from the exterior material", "heightFields": ["cabinet_panel_length"], "widthFields": ["cabinet_width_door_1"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_panel_length", "minimum": "cabinet_door.minimumHeight", "maximum": "cabinet_door.maximumHeight", "label": "Height of door 1", "fields": ["cabinet_panel_length"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_width_door_1", "minimum": "cabinet_door.minimumWidth", "maximum": "cabinet_door.maximumWidth", "label": "Width of door 1", "fields": ["cabinet_width_door_1"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_panel_length", "minimum": "0", "maximum": "cabinet_door.maximumHeight", "label": "Height of door 1", "fields": ["cabinet_panel_length"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_width_door_1", "minimum": "0", "maximum": "cabinet_door.maximumWidth", "label": "Width of door 1", "fields": ["cabinet_width_door_1"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_panel_length - border_width_top - border_width_bottom", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Door 1 is too short to accommodate the specified border widths", "fields": ["cabinet_panel_length", "border_width_top", "border_width_bottom"], "enabled": "AND(AND(cabinet_door.advanced, door_hang_type = 3), hori_amount == 0, vert_amount == 0)"}, {"type": "range", "value": "cabinet_width_door_1 - border_width_left - border_width_right", "minimum": "MAX(0, cabinet_door.minimumDistanceLeftRight)", "message": "Door 1 is too narrow to accommodate the specified border widths", "fields": ["cabinet_width_door_1", "border_width_left", "border_width_right"], "enabled": "AND(AND(cabinet_door.advanced, door_hang_type = 3), hori_amount == 0, vert_amount == 0)"}, {"type": "panel", "height": "cabinet_panel_length", "width": "cabinet_width_door_2", "field": "cabinet_ext_colour", "horizontal_grain": "hor_grain_ext", "message": "Door 2 is too large to be cut from the exterior material", "heightFields": ["cabinet_panel_length"], "widthFields": ["cabinet_width_door_2"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_panel_length", "minimum": "cabinet_door.minimumHeight", "maximum": "cabinet_door.maximumHeight", "label": "Height of door 2", "fields": ["cabinet_panel_length"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_width_door_2", "minimum": "cabinet_door.minimumWidth", "maximum": "cabinet_door.maximumWidth", "label": "Width of door 2", "fields": ["cabinet_width_door_2"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_panel_length", "minimum": "0", "maximum": "cabinet_door.maximumHeight", "label": "Height of door 2", "fields": ["cabinet_panel_length"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_width_door_2", "minimum": "0", "maximum": "cabinet_door.maximumWidth", "label": "Width of door 2", "fields": ["cabinet_width_door_2"], "enabled": "door_hang_type = 3"}, {"type": "range", "value": "cabinet_panel_length - border_width_top - border_width_bottom", "minimum": "MAX(0, cabinet_door.minimumDistanceTopBottom)", "message": "Door 2 is too short to accommodate the specified border widths", "fields": ["cabinet_panel_length", "border_width_top", "border_width_bottom"], "enabled": "AND(AND(cabinet_door.advanced, door_hang_type = 3), hori_amount == 0, vert_amount == 0)"}, {"type": "range", "value": "cabinet_width_door_2 - border_width_left - border_width_right", "minimum": "MAX(0, cabinet_door.minimumDistanceLeftRight)", "message": "Door 2 is too narrow to accommodate the specified border widths", "fields": ["cabinet_width_door_2", "border_width_left", "border_width_right"], "enabled": "AND(AND(cabinet_door.advanced, door_hang_type = 3), hori_amount == 0, vert_amount == 0)"}, {"type": "range", "value": "cabinet_panel_length", "label": "Height", "minimum": 40, "maximum": null, "fields": ["cabinet_panel_length"]}, {"type": "range", "value": "border_width_top", "minimum": "cabinet_door.minimumBorderWidthTop", "label": "Top", "enabled": "cabinet_door.advanced", "fields": ["border_width_top"]}, {"type": "range", "value": "border_width_bottom", "minimum": "cabinet_door.minimumBorderWidthBottom", "label": "Bottom", "enabled": "cabinet_door.advanced", "fields": ["border_width_bottom"]}, {"type": "range", "value": "border_width_left", "minimum": "cabinet_door.minimumBorderWidthLeft", "label": "Left", "enabled": "cabinet_door.advanced", "fields": ["border_width_left"]}, {"type": "range", "value": "border_width_right", "minimum": "cabinet_door.minimumBorderWidthRight", "label": "Right", "enabled": "cabinet_door.advanced", "fields": ["border_width_right"]}, {"type": "range", "value": "hori_height", "minimum": "cabinet_door.minimumRailsHorizontalHeight", "maximum": "cabinet_door.maximumRailsHorizontalHeight", "enabled": "cabinet_door.advanced", "label": "Horizontal Height", "fields": ["hori_height"]}, {"type": "range", "value": "hori_amount", "minimum": "0", "enabled": "cabinet_door.advanced", "label": "Horizontal Amount", "fields": ["hori_amount"]}, {"type": "range", "value": "vert_amount", "minimum": "0", "enabled": "cabinet_door.advanced", "label": "Vertical Amount", "fields": ["vert_amount"]}, {"type": "range", "value": "vert_width", "minimum": "cabinet_door.minimumRailsVerticalWidth", "maximum": "cabinet_door.maximumRailsVerticalWidth", "enabled": "cabinet_door.advanced", "label": "Vertical Height", "fields": ["vert_width"]}, {"type": "single", "rule": "cabinet_quantity > 0", "message": "Quantity must be greater than 0", "fields": ["cabinet_quantity"]}], "majorDimensions": [["cabinet_height"], ["cabinet_width"]]}