/* this is a copy of src/assets/css/pdf.css */
@page {
    size: A4;
    margin: 13mm 13mm 13mm 13mm;
    @bottom-right {
        content: 'PAGE ' counter(page) ' of ' counter(pages);
        color: #918f8f;
        font-size: 14px;
        font-weight: bold;
    }
}
.link::after {
    content: target-counter(attr(data-href string), page);
}

.new-page {
    page-break-before: always;
}

section, img {
    break-inside: avoid;
}
