{"success": 1, "id": 219, "cabinet_form_fields": {"cabinet_quantity": 1, "cabinet_note": 1, "cabinet_comment": 0, "cabinet_include_assembly": 0, "cabinet_exclude_hardware": 0, "supply_hardware": 0, "cabinet_carc_colour": 0, "cabinet_carc_edge_colour": 0, "cabinet_ext_colour": 3, "cabinet_ext_edge_colour": 3, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": 0, "custom_colour_ext": 0, "cabinet_door": 3, "hinge_style": 0, "cabinet_height": 0, "cabinet_width": 0, "cabinet_depth": 0, "cabinet_right_width": 0, "cabinet_left_width": 0, "cabinet_length_depth": 0, "cabinet_width_depth": 0, "cabinet_ladder_frames": 0, "cabinet_upper_filler_depth": 0, "cabinet_extend": 0, "cabinet_toekick": 0, "cabinet_top": 0, "cabinet_applied_panel_depth": 0, "cabinet_return_panel_width": 0, "cabinet_cover_void": 0, "cabinet_void_width": 0, "cabinet_total_drawer_height": 0, "microwave_id": 0, "microwave_advanced_checkbox": 0, "microwave_opening_height": 0, "cabinet_panel_length": 1, "cabinet_panel_width": 1, "cabinet_int_radius": 0, "cabinet_edge_inner": 0, "cabinet_ext_radius": 0, "cabinet_width1": 0, "cabinet_length1": 0, "cabinet_length2": 0, "door_hang": 0, "door_hang_type": 0, "hinge": 0, "cabinet_width_door_1": 0, "cabinet_width_door_2": 0, "drawer_amount": 2, "drawer_face_type": 2, "drawer_face_height": 4, "cabinet_drawer_gap": 2, "cabinet_drawer_top": 0, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 0, "cabinet_drawer_right": 0, "panel_edge_join": 0, "cabinet_mirror": 0, "drilling_offset_x": 0, "drilling_offset_y": 0, "drilling_num_holes": 0, "drilling_pitch": 0, "panel_edge_top": 3, "panel_edge_bottom": 3, "panel_edge_left": 3, "panel_edge_right": 3, "drawer_panel_edge_top": 3, "drawer_panel_edge_bottom": 3, "drawer_panel_edge_left": 3, "drawer_panel_edge_right": 3, "drawer_panel_edge_join": 0, "cabinet_edge_w1": 0, "cabinet_edge_w2": 0, "cabinet_edge_l1": 0, "cabinet_edge_l2": 0, "cabinet_door_gap": 0, "cabinet_door_bottom": 0, "cabinet_door_top": 0, "cabinet_door_left": 0, "cabinet_door_right": 0, "door_hinge_amount": 0, "drawer_runner_specs": 0, "drawer_type": 0, "shelf_type": 0, "shelf_style": 0, "shelf_position": 0, "shelf_offset": 0, "cabinet_hori_shelves": 0, "cabinet_vert_shelves": 0, "upper_shelf_type": 0, "upper_shelf_style": 0, "upper_shelf_position": 0, "upper_shelf_offset": 0, "middle_shelf_type": 0, "middle_shelf_style": 0, "middle_shelf_position": 0, "middle_shelf_offset": 0, "lower_shelf_type": 0, "lower_shelf_style": 0, "lower_shelf_position": 0, "lower_shelf_offset": 0, "cabinet_partition_height": 0, "cabinet_partition_width": 0, "cabinet_upper_partition_height": 0, "cabinet_lower_partition_height": 0, "sub_panel_1": 1, "sub_panel_2": 1, "sub_panel_3": 1, "sub_panel_4": 1, "sub_panel_5": 1, "sub_panel_6": 1, "sub_panel_7": 1, "sub_panel_8": 1, "sub_panel_9": 1, "no_vert_bars": 4, "no_hori_bars": 4, "bar_width": 4, "border_width_top": 3, "border_width_bottom": 3, "border_width_left": 3, "border_width_right": 3, "hori_height": 4, "drawer_border_width_top": 3, "drawer_border_width_bottom": 3, "drawer_border_width_left": 3, "drawer_border_width_right": 3, "drawer_hori_height": 4, "hori_amount": 4, "vert_width": 4, "vert_amount": 4, "rail_vert": 4, "rail_hori": 4, "has_advanced_materials": 3}, "cabinet_form_field_defaults": {"cabinet_quantity": "1", "drawer_amount": "1", "drawer_face_type": "2", "cabinet_drawer_gap": "0"}, "edge_field_labels": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "uses_edge_finishes": true}