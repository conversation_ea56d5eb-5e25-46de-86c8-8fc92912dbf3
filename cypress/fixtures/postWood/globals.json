{"items": [{"Id": 1, "Name": "AdditionalLsCornerRadius", "Value": "0", "Comment": "The added Value for the internal corner radius on L Shaped Cabinets.", "Category": "Construction", "VariType": "UserSpecific", "Override": {"ParentId": 1, "AccountId": "", "Id": 1, "Name": "AdditionalLsCornerRadius", "Value": "0", "Comment": "The added Value for the internal corner radius on L Shaped Cabinets.", "Category": "Construction", "VariType": "UserSpecific"}}, {"Id": 2, "Name": "AdjLegVarHCompany", "Value": "AdjustableLegRev01", "Comment": "Forces the use of the mentioned Adjustable Leg workbook", "Category": null, "VariType": null}, {"Id": 3, "Name": "AdjShelfAbove", "Value": "32", "Comment": "Minimum clearance above the top adjustable shelf Pin hole Default=46.", "Category": "HingeLocationParam", "VariType": "UserSpecificMetric"}, {"Id": 4, "Name": "AdjShelfBelow", "Value": "32", "Comment": "Value the hinge will be pushed below Adjustable <PERSON><PERSON>.", "Category": "HingeLocationParam", "VariType": "UserSpecificMetric"}, {"Id": 5, "Name": "AdjShelfHoleCentres", "Value": "32", "Comment": "The measurement between hole centres eg. 32-50", "Category": "Adjustable <PERSON><PERSON>", "VariType": "UserSpecific"}, {"Id": 6, "Name": "AdjShelfHoleDepth", "Value": "13", "Comment": "Adjustable shelf hole drilling depth", "Category": "Adjustable <PERSON><PERSON>", "VariType": "UserSpecific"}, {"Id": 7, "Name": "AdjShelfHolesInBack", "Value": "700", "Comment": "When adjustable shelf length exceeds value adjustable holes placed in the back", "Category": "Adjustable <PERSON><PERSON>", "VariType": "UserSpecific"}, {"Id": 8, "Name": "AdjShelfHolesInSides", "Value": "750", "Comment": "When adjustable shelf width exceeds value extra adjustable holes placed in the side", "Category": "Adjustable <PERSON><PERSON>", "VariType": "UserSpecific"}, {"Id": 9, "Name": "AdjustableLegCompany", "Value": "Sub.Accessories\\AdjustableLegRev01", "Comment": "Forces the use of the mentioned Adjustable Leg workbook", "Category": "Machining-Workbooks", "VariType": "UserSpecific"}, {"Id": 10, "Name": "AdjustableShelfHoleDiameter", "Value": "5", "Comment": "Diameter of adjustable shelf holes", "Category": "Adjustable <PERSON><PERSON>", "VariType": "UserSpecific"}, {"Id": 11, "Name": "AdjustableShelfPegVerticalOffset", "Value": "4", "Comment": "A value specified by the hardware manufacturer See manufacturer's manual.", "Category": "Adjustable <PERSON><PERSON>", "VariType": "UserSpecific"}, {"Id": 12, "Name": "AdjustableShelfWidthClearanceTotal", "Value": "0", "Comment": "This value is for all cabinets and is the total value deducted", "Category": "Adjustable <PERSON><PERSON>", "VariType": "Construction"}, {"Id": 13, "Name": "AngleSetback45Cabinet", "Value": "8.5", "Comment": "The ammount the 45 degree cabinet extends forward.", "Category": "Construction", "VariType": "UserSpecific"}, {"Id": 14, "Name": "AngleSetback45Disabled", "Value": "0", "Comment": "This wil disable the adding setback value and must be used in product depth to have correc construction.", "Category": "Construction", "VariType": "UserSpecific"}, {"Id": 15, "Name": "AppBottomHingeAdjustment", "Value": "1", "Comment": "This will adjust the Bottom Hinge Height on Applied Bottom cabinets.", "Category": "Upper", "VariType": "Construction"}, {"Id": 16, "Name": "ApplianceHardware", "Value": "1", "Comment": "Show Ubo and Rangehoods in Hardware List.", "Category": "Miscellaneous", "VariType": "UserSpecific"}, {"Id": 17, "Name": "ApplianceJoiningBoltHolesBack", "Value": "35", "Comment": "The ammount the connecting bolt hole is in from the back on Appliance cabinets", "Category": "JoiningBoltHoles", "VariType": "UserSpecific"}, {"Id": 18, "Name": "ApplianceJoiningBoltHolesBottom", "Value": "50", "Comment": "The ammount the connecting bolt hole is up from the bottom on Appliance cabinets", "Category": "JoiningBoltHoles", "VariType": "UserSpecific"}, {"Id": 19, "Name": "ApplianceJoiningBoltHolesFront", "Value": "13", "Comment": "The ammount the connecting bolt hole is in from the front on Appliance cabinets", "Category": "JoiningBoltHoles", "VariType": "UserSpecific"}, {"Id": 20, "Name": "ApplianceJoiningBoltHolesTop", "Value": "50", "Comment": "The ammount the connecting bolt hole is down from the top on Appliance cabinets", "Category": "JoiningBoltHoles", "VariType": "UserSpecific"}, {"Id": 21, "Name": "ApplianceKickboardEndNotchDepth", "Value": "-12.5", "Comment": "When butting an appliance eg(Dishwasher or stove) toe kick inset", "Category": "KickInformation", "VariType": "UserSpecific"}, {"Id": 22, "Name": "AppliedKickboardEndNotchDepth", "Value": "-12.5", "Comment": "When butting an Applied Panel.", "Category": "KickInformation", "VariType": "UserSpecific"}, {"Id": 23, "Name": "ApplyCentreGroove", "Value": "1", "Comment": "When Profile size is to small for V Groove spacings.", "Category": "Doors", "VariType": "UserSpecificPart"}, {"Id": 24, "Name": "Back<PERSON><PERSON><PERSON><PERSON>urn<PERSON>ff", "Value": "250", "Comment": "The distance between feet when back on turns off.", "Category": "KickInformation", "VariType": "UserSpecific"}, {"Id": 25, "Name": "BackGrainVertical", "Value": "1", "Comment": "The backs on cabinets if grained will run grain vertical", "Category": "Grain", "VariType": "Construction"}], "pagination": {"current_page": 1, "page_count": 23, "page_size": 25, "total_count": 572}, "success": true}