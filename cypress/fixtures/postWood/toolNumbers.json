{"items": [{"Id": 1, "ToolJob": "BoltBody", "Description": "Used for bolt body section of the joiner", "ToolNumber": "340", "FeedSpeed": 5, "SequenceNumber": 555, "ToolRadius": 5, "SpecialInstructions": null, "Override": {"Id": 1, "ParentId": 1, "AccountId": "1ef3b62d-571f-6750-9dd3-2255296214de", "ToolJob": "BoltBody", "Description": "Used for bolt body section of the joiner", "ToolNumber": "340", "FeedSpeed": 5, "SequenceNumber": 555, "ToolRadius": 5, "SpecialInstructions": "Test 2"}}, {"Id": 2, "ToolJob": "BoltCut", "Description": "Used for bolt section of the joiner", "ToolNumber": "340", "FeedSpeed": 5, "SequenceNumber": 550, "ToolRadius": 5, "SpecialInstructions": null, "Override": {"Id": 2, "ParentId": 2, "AccountId": "1ef3b62d-571f-6750-9dd3-2255296214de", "ToolJob": "BoltCut", "Description": "Used for bolt section of the joiner", "ToolNumber": "340", "FeedSpeed": 56, "SequenceNumber": 550, "ToolRadius": 5, "SpecialInstructions": null}}, {"Id": 3, "ToolJob": "CCWCutOut", "Description": "Counter Clockwise used for cut outs", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 610, "ToolRadius": 10, "SpecialInstructions": null, "Override": {"Id": 3, "ParentId": 3, "AccountId": "1ef3b62d-571f-6750-9dd3-2255296214de", "ToolJob": "CCWCutOut", "Description": "Counter Clockwise used for cut outs", "ToolNumber": "330", "FeedSpeed": 50, "SequenceNumber": 610, "ToolRadius": 10, "SpecialInstructions": null}}, {"Id": 4, "ToolJob": "CCWMitreCut", "Description": "Counter Clockwise  used for mitre cuts", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 510, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 5, "ToolJob": "CCWRoughDock", "Description": "Counter Clockwise used for hogging out", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 210, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 6, "ToolJob": "CCWRoughMitreCut", "Description": "Counter Clockwise rough cut  used for mitre cuts", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 410, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 7, "ToolJob": "CCWTrim", "Description": "Counter Clockwise trims the panel to size where required", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 110, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 8, "ToolJob": "CWCutOut", "Description": "Clockwise used for cut outs", "ToolNumber": "320", "FeedSpeed": 5, "SequenceNumber": 600, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 9, "ToolJob": "CWMitreCut", "Description": "Clockwise used for mitre cuts", "ToolNumber": "320", "FeedSpeed": 5, "SequenceNumber": 500, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 10, "ToolJob": "CWRoughDock", "Description": "Clockwise used for hogging out", "ToolNumber": "320", "FeedSpeed": 5, "SequenceNumber": 200, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 11, "ToolJob": "CWRoughMitreCut", "Description": "Clockwise rough cut used for mitre cuts", "ToolNumber": "320", "FeedSpeed": 5, "SequenceNumber": 400, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 12, "ToolJob": "CWTrim", "Description": "Clockwise trims the panel to size where required", "ToolNumber": "320", "FeedSpeed": 5, "SequenceNumber": 100, "ToolRadius": 10, "SpecialInstructions": null, "Override": null}, {"Id": 13, "ToolJob": "DripLipRebate", "Description": "Default tool used in Drip Lip Rebate", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 700, "ToolRadius": 5, "SpecialInstructions": null, "Override": null}, {"Id": 14, "ToolJob": "LamelloRebate", "Description": "Default tool used Lamello Biscuits", "ToolNumber": "1014", "FeedSpeed": 3, "SequenceNumber": 750, "ToolRadius": 20, "SpecialInstructions": "Lam<PERSON>Length:75~<PERSON><PERSON><PERSON><PERSON><PERSON>:25~Invoke<PERSON><PERSON><PERSON>:1~InvokePause:1~ZCentreOf<PERSON><PERSON><PERSON><PERSON>utter:7", "Override": null}, {"Id": 15, "ToolJob": "LaminateCut", "Description": "Default tool used for cutting Laminate Sheet", "ToolNumber": "1025", "FeedSpeed": 5, "SequenceNumber": 950, "ToolRadius": 3, "SpecialInstructions": null, "Override": null}, {"Id": 16, "ToolJob": "MitreRebate", "Description": "Mitre rebate cutter for left angle mitre and male mason mitres.", "ToolNumber": "Hello", "FeedSpeed": 0, "SequenceNumber": 760, "ToolRadius": 40, "SpecialInstructions": "MitreRebateInsertDepth:1.6~InvokePause:1~InvokeMitreRebate:2~ZCentreOfRebateCutter:6~MitreRebateStartFrontedge:40", "Override": null}, {"Id": 17, "ToolJob": "SiteFittingRebate", "Description": "Default tool used in Site Fitting Rebate", "ToolNumber": "330", "FeedSpeed": 5, "SequenceNumber": 105, "ToolRadius": 20, "SpecialInstructions": null, "Override": null}, {"Id": 18, "ToolJob": "SubsTempsTool", "Description": "Default tool used in Substrate andTemplate workbook", "ToolNumber": "1013", "FeedSpeed": 5, "SequenceNumber": 270, "ToolRadius": 20, "SpecialInstructions": null, "Override": null}], "pagination": {"current_page": 1, "page_count": 1, "page_size": 25, "total_count": 18}, "success": true}