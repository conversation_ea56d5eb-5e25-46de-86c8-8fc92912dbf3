[{"id": 3684800, "roomId": 374038, "gfpProductId": -1, "roomCabNumber": 1, "type": 352, "note": "", "comment": "", "variationId": -1, "includeDrawerFaces": 1, "variationCost": 0, "cost": 230.83193805, "quantity": 1, "mirror": -1, "width": 550, "width2": 0, "height": 900, "depth": 590, "top": -1, "fingerPull": null, "ladderFrames": 1, "carcColour": 918, "carcEdgeColour": 1452, "extColour": 7189, "extEdgeColour": 3169, "carcMinJobArea": 0, "extMinJobArea": 0.5, "carcMaterialUsed": 0, "extMaterialUsed": 0.495, "extHorGrain": 0, "carcHorGrain": 0, "extDoubleSided": 0, "carcDoubleSided": 0, "doorStyle": 161, "hingeStyle": 3377, "doorHingeAmount": 3, "drawerGap": 3, "drawerBottom": 0, "drawerTop": 3, "drawerLeft": 1.5, "drawerRight": 1.5, "doorGap": 3, "doorTop": 3, "doorBottom": 0, "doorLeft": 1.5, "doorRight": 1.5, "includeAssembly": 0, "excludeHardware": 1, "adjustableLegs": 1, "drawerAmount": 3, "drawerFaceType": 2, "drawerFaceHeights": "448.5,448.5", "partitionHeight": 0, "partitionWidth": 0, "totalDrawerHeight": 720, "ovenCheck": 0, "microwaveCheck": 0, "ovenHeight": 600, "microwaveHeight": 600, "applianceId": -1, "microwaveId": -1, "horiShelfAmount": -1, "vertShelfAmount": -1, "simpleShelves": 1, "carcCustomColour": "", "extCustomColour": "", "typeName": "Drawer", "carcMaterial": {"id": 918, "itemCode": "", "name": "Na", "finish": "Non Supply", "brand": 23, "substrate": "Na", "thickness": 16.5, "defaultDoubleSidedCost": 1, "defaultHorGrainSurcharge": null, "defaultHandlingCost": 1, "defaultAreaCost": 1, "defaultAreaAssemblyCost": 1, "defaultMinJobArea": 0, "defaultLength": 10000, "defaultWidth": 10000, "changedItemCode": "", "changedHorGrainSurcharge": null, "changedDoubleSidedCost": null, "changedHandlingCost": null, "changedAreaCost": null, "changedAreaAssemblyCost": null, "changedMinJobArea": null, "changedLength": null, "changedWidth": null, "prefix": "PS", "prefixId": 1, "doorFilter": "Non Supply", "image": "Non Supply/Na.jpg", "horGrain": 0, "doubleSided": 0, "manufacturers": -1, "type": 7, "typeName": null, "hiddenAsDefault": 0, "gfpName": "DoorsNotSupplied", "gfpBrandId": 7, "gfpTypeId": 1, "gfpFinishId": 18, "gfpBrandName": "goFlatpacks", "gfpTypeName": "Solid", "gfpFinishName": "Includes hardware but no doors", "changed": false, "customerChanged": false, "hidden": false, "manufacturerId": 6, "minimumUsageRollover": 1, "expectedYieldFactor": 100, "changedMinimumUsageRollover": 1, "changedExpectedYieldFactor": 100, "carcHorGrain": null, "extHorGrain": null, "brandName": "Non Supply"}, "carcEdge": {"id": 1452, "itemCode": "", "name": "Na", "brand": 9, "finish": "Non Supply", "thickness": 0.01, "defaultLengthCost": 0, "defaultHandlingCost": 0, "defaultAreaHandlingCost": 0, "defaultApplicationCost": 0, "changedItemCode": "", "changedHandlingCost": null, "changedLengthCost": null, "changedAreaHandlingCost": null, "changedApplicationCost": null, "doorFilter": "Non Supply", "image": "Non Supply/Na.jpg", "manufacturers": -1, "manufacturerId": 6, "changed": false, "customerChanged": false, "hidden": false, "type": 7, "typeName": null, "brandName": null}, "extMaterial": {"id": 7189, "itemCode": null, "name": "Caraway", "finish": "Solid Gloss", "brand": 3, "substrate": "MR MDF", "thickness": 18.5, "defaultDoubleSidedCost": 0, "defaultHorGrainSurcharge": null, "defaultHandlingCost": 3.15, "defaultAreaCost": 0.01, "defaultAreaAssemblyCost": 3.15, "defaultMinJobArea": 0.5, "defaultLength": 3050, "defaultWidth": 1200, "changedItemCode": null, "changedHorGrainSurcharge": null, "changedDoubleSidedCost": null, "changedHandlingCost": null, "changedAreaCost": null, "changedAreaAssemblyCost": null, "changedMinJobArea": null, "changedLength": null, "changedWidth": null, "prefix": "SolG", "prefixId": 6, "doorFilter": "Polytec Vinyl", "image": "Polytec/Caraway.jpg", "horGrain": 0, "doubleSided": 0, "manufacturers": -1, "type": 4, "typeName": null, "hiddenAsDefault": 0, "gfpName": "", "gfpBrandId": -1, "gfpTypeId": -1, "gfpFinishId": -1, "gfpBrandName": "", "gfpTypeName": "", "gfpFinishName": "", "changed": false, "customerChanged": false, "hidden": false, "manufacturerId": 6, "minimumUsageRollover": 1, "expectedYieldFactor": 100, "changedMinimumUsageRollover": 1, "changedExpectedYieldFactor": 100, "carcHorGrain": null, "extHorGrain": null, "brandName": "Polytec"}, "extEdge": {"id": 3169, "itemCode": "", "name": "EM0 - Square", "brand": 3, "finish": "<PERSON>", "thickness": 0.01, "defaultLengthCost": 0.01, "defaultHandlingCost": 0.5, "defaultAreaHandlingCost": 1.5, "defaultApplicationCost": 0.01, "changedItemCode": "", "changedHandlingCost": null, "changedLengthCost": null, "changedAreaHandlingCost": null, "changedApplicationCost": null, "doorFilter": "Polytec Vinyl", "image": "Polytec/EM0 - Square.jpg", "manufacturers": -1, "manufacturerId": 6, "changed": false, "customerChanged": false, "hidden": false, "type": 4, "typeName": null, "brandName": null}, "typeImage": "/uploads/cabinet_images/DrawersS (1).png", "widthFunction": null, "hingeCosting": null, "edgeCosting": [], "haveEdges": 0, "areaCabtop": 0, "hingeCostingTotal": [], "materialPriceBreakdown": [], "cabinet": {"type": 355, "name": "Drawer", "originalName": "Drawer", "description": "Drawer Faces only - no drilling or hardware included", "gfpProduct": 0, "gfpName": "", "gfpDescription": "", "gfpLongDescription": "", "gfpHelpText": "", "gfpSubStyleId": -1, "gfpHinge1": -1, "gfpHinge2": -1, "gfpHinge3": -1, "gfpDrawer1": -1, "gfpDrawer2": -1, "gfpDrawer3": -1, "code": "DrawerQFP", "style": 5, "subStyleId": 45, "productType": 10, "matType": 1, "extLabel": "Exterior Material", "intLabel": "Carcase Material", "lshape": 0, "bifold": 0, "doorHang": "", "image": "DrawersS (1).png", "imageLarge": "DrawersL (1).png", "drawers": 0, "innerDrawers": 0, "shelves": 0, "shelfType": -1, "defaultShelves": 0, "defaultUpperShelves": -1, "defaultMiddleShelves": 0, "defaultLowerShelves": -1, "doors": 0, "cabinetFingerPull": 0, "isRecessedRail": 0, "returnPanel": 0, "defaultReturnWidth": 0, "oven": 0, "fixedOven": 0, "rangehood": 0, "fixedRangehood": 0, "microwave": -1, "fixedMicrowave": 0, "keyShape": 0, "partition": 0, "fixedPartition": 0, "hingeDirection": 0, "minHeight": 150, "maxHeight": null, "minWidth": 100, "maxWidth": null, "defaultWidth": 450, "minDepth": 100, "maxDepth": null, "requiresVariation": 0, "defaultTop": -1, "defaultTotalDrawerHeight": 0, "defaultBottomClearance": 0, "availableCabinetTops": "", "availableDoors": "138,801,370,371,451,836,369,601,375,374,373,835,834,376,372,684,379,800,497,887,274,100,505,553,548,554,549,555,498,556,161,279,101,872,875,278,513,262,60,143,61,172,117,134,129,122,135,132,131,124,127,148,118,145,128,120,119,126,125,121,144,130,136,133,123,876,169,283,108,294,228,772,792,170,163,32,171,173,758,164,252,514,864,165,227,281,280,96,166,454,588,702,591,251,250,62,63,845,510,511,725,726,493,729,773,494,730,731,254,261,260,226,167,276,253,249,830,831,185,353,455,879,168,855,854,852,499,557,558,225,248,344,705,456,224,282,247,317,361,457,791,689,797,734,732,735,776,444,442,443,490,736,798,733,579,336,223,311,809,739,755,98,302,222,859,315,806,359,740,458,246,190,191,221,810,741,858,360,459,189,220,104,219,365,460,811,742,245,812,743,709,710,188,264,388,389,390,391,392,393,394,395,397,398,399,400,401,402,403,404,405,411,409,412,413,410,414,415,416,417,408,407,406,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,441,440,500,559,102,837,838,839,603,604,605,606,608,607,609,610,611,612,613,614,615,616,617,624,623,622,621,620,619,618,652,649,648,646,644,642,638,636,630,670,668,666,664,662,660,658,656,655,654,681,680,679,678,677,676,675,674,673,672,671,151,327,840,784,843,844,841,842,785,292,187,218,244,501,560,217,873,363,461,321,320,322,95,299,277,94,68,69,70,314,90,310,312,255,243,799,691,687,690,692,787,788,790,789,242,744,450,512,449,155,154,153,7,328,64,378,56,57,58,59,339,452,491,492,241,152,65,301,66,67,488,240,184,83,183,521,186,502,561,562,563,515,833,337,571,338,572,575,573,574,577,495,578,878,291,813,745,860,323,340,504,564,795,239,496,508,874,712,713,714,720,715,717,718,719,721,722,290,503,565,324,349,462,259,300,182,289,200,142,139,140,141,181,75,38,76,78,77,73,216,358,463,215,288,238,199,286,866,341,706,464,237,158,295,198,877,351,465,297,466,180,346,467,468,236,287,197,354,469,863,285,25,159,814,746,296,342,470,471,343,707,600,472,196,516,195,350,473,303,306,194,193,828,829,179,178,106,355,474,816,747,116,113,33,509,566,807,749,817,750,818,751,235,445,234,233,93,284,862,357,475,79,489,506,567,352,476,192,364,477,177,232,523,524,525,526,446,711,109,880,258,115,162,362,478,207,590,703,214,592,206,527,528,529,530,531,532,533,534,535,536,537,783,782,781,538,539,540,541,542,543,544,545,778,546,779,780,517,319,304,307,348,479,585,826,827,213,763,764,765,766,767,768,769,345,480,481,356,704,482,257,366,483,176,865,293,793,756,757,212,819,752,821,753,347,484,868,869,870,871,5,820,335,881,884,754,330,263,883,583,771,850,686,105,305,308,822,823,298,396,211,808,748,175,22,861,111,851,824,825,867,231,146,520,230,316,447,380,381,382,387,386,385,383,448,485,205,313,486,210,204,137,882,209,71,309,174,487,507,568,208,203,202,518,519,229,201,848,849,762,761,72,888,889,892,891,890,894,905,906,901,902,903,904,907,908,909,910,911,912,913,919,921,920,922,924,925,926,923,927,928,929,930,931,933,932,934,935,936", "availableHinges": "", "availableShelves": "", "availableDrawers": "", "status": 0, "upperPartitionHeight": "0", "lowerPartitionHeight": "0", "fixedUpperPartitionHeight": 0, "fixedLowerPartitionHeight": 0, "minimumOpeningWidth": 0, "gfpAllowColourEdit": 1, "isRobeWithBottom": 0, "favourites": "0", "parts": [{"id": "2256", "cabinetType": "355", "partId": "192", "partName": "Drawer"}], "hardware": [], "inserts": [], "cabinetProperties": [], "hasFascia": 0, "edgeL1Label": "Left", "edgeL2Label": "Right", "edgeW1Label": "Top", "edgeW2Label": "Bottom", "edgeInnerLabel": "Joining Border", "edgeLabelDefaults": {"cabinet_edge_L1": "Top", "cabinet_edge_L2": "Bottom", "cabinet_edge_W1": "Left", "cabinet_edge_W2": "Right", "cabinet_edge_inner": "Joining Border", "panel_edge_top": "Top", "panel_edge_left": "Left", "panel_edge_right": "Right", "panel_edge_bottom": "Bottom", "panel_edge_join": "Joining Border"}, "images": []}, "step": 1, "carcDoubleSidedCost": 1, "carcHandlingCost": 1, "carcAreaCost": 1, "carcAreaAssemblyCost": 1, "extDoubleSidedCost": 0, "extHandlingCost": 3.15, "extAreaCost": 0.01, "extAreaAssemblyCost": 3.15, "carcEdgeHandlingCost": 0, "carcEdgeLengthCost": 0, "carcEdgeAreaHandlingCost": 0, "carcEdgeApplicationCost": 0, "extEdgeHandlingCost": 0.5, "extEdgeLengthCost": 0.01, "extEdgeAreaHandlingCost": 1.5, "extEdgeApplicationCost": 0.01, "materialPrefix": "SolG_18.5", "doorSuffix": "Style2", "doorSuffixId": 2, "extendToFloor": false, "toeKickHeight": 150, "appliedPanelDepth": 610, "upperFillerDepth": 610, "returnPanelWidth": 0, "coverVoid": -1, "voidWidth": 0, "lengthDepth": 590, "widthDepth": 590, "length1": 300, "length2": 300, "width1": 300, "edgeL1": 1, "edgeL2": 1, "edgeW1": 1, "edgeW2": 1, "edgeInner": 1, "intRadius": 50, "extRadius": 50, "drillingCost": 0.07, "hardware": [], "parts": [{"id": 18844883, "jobCabinetId": 3684800, "partId": 192, "handlingCost": 1.8, "areaHandlingCost": 2.5, "machiningCost": 1.5, "areaMachiningCost": 1.95, "assemblyCost": 3, "areaAssemblyCost": 3, "edgeSizes": [], "haveEdges": 0, "edgeCosting": {"handling": 0, "length": 0, "areaHandling": 0, "application": 0}, "materialPriceBreakdown": [], "outsourcedCosting": {"type": "", "percentMarkup": null, "cost": 0}, "unitCost": 0, "isOutsourced": false, "totalEdgeLength": null}], "drawers": [], "shelves": [], "upperShelves": [], "middleShelves": [], "lowerShelves": [], "doors": [], "drillings": [], "jobCabinetDoorHinges": null, "upperPartitionHeight": 0, "lowerPartitionHeight": 0, "cabinetTopPart": {}, "jobId": 386409, "manufacturerId": 6, "favourites": "0", "variation": null, "customerId": 928, "cabinetProperties": [], "inQfpSummary": 1, "lastPriceCalculated": "2024-08-05 15:53:40", "outsourcedCosting": null, "middleShelfAmount": null, "edgeSizes": {}, "dimensions": "900 x 550", "areaCabTop": null, "changedImage": "", "drawerPanelEdges": "[{\"drawer_edge_top\": 1, \"drawer_edge_bottom\": 1}, {\"drawer_edge_top\": 0, \"drawer_edge_bottom\": 0}]"}]