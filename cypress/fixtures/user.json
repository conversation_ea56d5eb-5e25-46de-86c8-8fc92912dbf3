{"success": 1, "user": {"user_type": 2, "domain": "dezignatek.localhost:8500", "homePage": "v2/", "analyticsMetrics": {"user_type_id": 2, "user_type": "customer", "manufacturer_id": 103, "manufacturer": "New Zealand Panels Group Ltd"}, "name": "<PERSON> <PERSON>", "email": "<EMAIL>", "level": 2, "phNumber": "***********", "mobile": null, "businessName": null, "abn": null, "acn": null, "address2": null, "websiteUrl": null, "address": "16 Formby Avenue", "suburb": "Point Chevalier", "postcode": "1021", "addressState": 10, "minCharge": 0, "cc": "", "manuPermission": 0, "defaultMeasurementUnit": "METRIC", "deliveryAddress": "62 Third Avenue", "deliverySuburb": "Kingsland", "deliveryPostcode": "1022", "defaultFreightOption": "PICKUP", "defaultExcludeHardware": 0, "defaultIncludeHardware": 0, "deliveryState": 10, "countryName": "New Zealand", "premium": 0, "id": 48743, "defaultExtMaterial": 20879, "defaultExtEdge": 5675, "defaultCarcMaterial": 17758, "defaultExtHorGrain": null, "defaultExtCustomColor": null, "defaultCarcCustomColor": null, "defaultExtDoubleSided": null, "defaultCarcHorGrain": null, "defaultCarcDoubleSided": null, "defaultCarcEdge": 4997, "defaultIncludeAssembly": 0, "defaultAdjustableLegs": 0, "defaultToeKickHeight": 0, "defaultAdvancedEdgeTop": 2, "defaultAdvancedEdgeBottom": 2, "defaultAdvancedEdgeLeft": 2, "defaultAdvancedEdgeRight": 2, "defaultBorderWidthTop": 58, "defaultBorderWidthBottom": 58, "defaultBorderWidthLeft": 58, "defaultBorderWidthRight": 58, "defaultAdvancedRailHeight": 55, "defaultAdvancedRailWidth": 55, "defaultAdvancedBarWidth": 55, "defaultDoorCategory": 14, "defaultDoor": 344, "defaultHinge": 21, "defaultDrawer": 6, "defaultSize": 58318, "defaultShelfSetBack": 0, "defaultGap": 36215, "defaultRoomTop": 26, "addDefaultRoomOnNewJobCreate": 1, "room_assistant": 0, "defaultDepotId": null, "depotIdentification": "Depot", "depotSelectable": null, "isDepotFunctionalityEnabled": null, "isCouponAvailable": 0, "midRailIndicator": true, "defaultJobPropertiesPdf": 3, "showJobPropertiesTablePdf": 0, "showOrderAcknowledgementPdf": 1, "allowCustomerInvoicingPdf": 0, "allowDrawerFaceEdgeFinish": false, "defaultExtMaterialType": "17", "defaultExtBrand": 87, "defaultExtFinish": "Satin", "defaultExtSubstrate": "MR MDF", "defaultCarcMaterialType": "2", "defaultCarcBrand": 64, "defaultCarcFinish": "Gloss", "defaultCarcSubstrate": "MR MDF", "defaultExtEdgeBrand": 75, "defaultExtEdgeFinish": "Thermoform", "defaultCarcEdgeBrand": 50, "defaultCarcEdgeFinish": "Gloss", "isSupplyMethodVisible": false, "isHardwareInclusionVisible": false, "isAdjustableLegsVisible": true, "manufacturerContactInfo": "<b>Contact:</b><br />\r\nDezignatek<br />\r\nwww.dezignatek.co.nz<br />\r\n15 Kerwyn Avenue, <br />\r\n<br />\r\n<b>Phone Number:</b><br />\r\n0800 333 350 <br />\r\n<br />\r\n<b>Email:</b><br />\r\n<EMAIL>", "manufacturerNews": "<div><div>News Lorem ipsum is placeholder text commonly used in the graphic, print, and publishing industries for previewing layouts and visual mockups.</div><div>From its medieval origins to the digital era, learn everything there is to know about the ubiquitous lorem ipsum passage.</div><div>News Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown typesetter in the 15th century who is thought to have scrambled parts of <PERSON>'s De Finibus Bonorum et Malorum for use in a type specimen book. It usually ends with</div></div>", "manufacturerName": "New Zealand Panels Group Ltd", "manufacturerId": 103, "manufacturerEmail": "<EMAIL>", "manufacturerPhone": "0800 333 350 ", "manufacturerFavicon": "manufacturer_application_favicon_103.ico", "manufacturerLogo": "Dezignatek_RGB_NEW (1).png", "manufacturerPickupAvailable": 1, "manufacturerFreightAvailable": 1, "manufacturerAddress": "15 Kerwyn Avenue", "manufacturerSuburb": "East Tamaki", "manufacturerPostCode": "2013", "manufacturerState": -1, "tutorialOptions": null, "isBTDAvailable": false, "isBenchtopAvailable": false, "isTDLDAvailable": 0, "isAddProductAvailable": 0, "isQFPAvailable": 1, "isAddRoomAvailable": 0, "isKDMaxImportAvailable": 0, "isSundryAvailable": 0, "sundryMenuText": "Add Additional Hardware", "qFPMenuText": "Quick Entry", "doorHingeHoles": {"3": 900, "4": 1500, "5": 2000}, "isSuspended": 0, "manufacturerDefaultHingePosition": 100, "show_pricing": true, "isAllowedToEditInfoText": false, "isDefaultDoorAdvanced": 1, "isAllowedNonmmUnits": 0, "manufacturerHelpAndSupportDefaultOptions": [{"id": 300, "manufacturerId": 103, "optionName": "Privacy", "optionUrl": "https://www.dezignatek.co.nz/privacy/"}, {"id": 301, "manufacturerId": 103, "optionName": "Terms & Conditions", "optionUrl": "https://www.dezignatek.co.nz/terms-conditions/"}], "jobSubmitTextNoVariations": "<p>Are you sure you want to submit this job? <br /><br />Once submitted, you will be unable to make any changes to it.</p>", "jobSubmitTextWithVariations": "<p>Are you sure you want to submit this job? <br /><br />This job includes variation requests and may incur extra costs.<br /><br />Once submitted, you will be unable to make any changes to it.</p>", "jobSubmitTextVariationsAccepted": "<p>Are you sure you want to submit this job with the new change? <br /><br />Variation charge:@variationCost.<br /><br />Once submitted, you will be unable to make any changes to it.</p>", "inActiveManufacturer": 0, "currencyType": "$", "maxAgeJobPrice": null, "doorHangReversal": 0, "displayEstimatedCompletion": 0, "materialSelector": 0, "isBetaCustomer": false, "is_room_assistant_available": 0, "job": {"room": {"exteriorMaterial": {"id": 20879, "manufacturer_id": 103, "prefix_id": 35, "brand_id": 87, "type_id": 17, "item_code": null, "name": "Alabaster", "image": "Dezignatek/Alabaster.jpg", "finish": "Satin", "substrate": "MR MDF", "thickness": 30, "prefix_name": "NZTF", "door_filter": "NZPG Vinyl", "is_grained": false, "is_double_sided": false, "double_sided_cost": 0, "handling_cost": 0, "area_cost": 0, "area_assembly_cost": 0, "min_job_area": 0.5, "length": 2400, "width": 1200, "gfp_name": "", "gfp_brand_id": -1, "gfp_type_id": -1, "gfp_finish_id": -1, "is_deleted": false, "is_hidden_by_default": false, "brand": {"id": 87, "name": "Dezignatek", "is_deleted": false}}, "carcaseMaterial": {"id": 17758, "manufacturer_id": 103, "prefix_id": 21, "brand_id": 64, "type_id": 2, "item_code": null, "name": "Prime White", "image": "Prime/Prime White.jpg", "finish": "Gloss", "substrate": "MR MDF", "thickness": 16, "prefix_name": "NZWMEL", "door_filter": "NZPG Flat Panel", "is_grained": false, "is_double_sided": false, "double_sided_cost": 0, "handling_cost": 0, "area_cost": 0, "area_assembly_cost": 0, "min_job_area": 0.05, "length": 2430, "width": 1200, "gfp_name": "", "gfp_brand_id": -1, "gfp_type_id": -1, "gfp_finish_id": -1, "is_deleted": false, "is_hidden_by_default": false, "brand": {"id": 64, "name": "Prime", "is_deleted": false}}, "exteriorEdgeMaterial": {"id": 5675, "manufacturer_id": 103, "brand_id": 75, "material_type_id": 17, "item_code": null, "name": "Profiled edges", "image": "", "finish": "Thermoform", "thickness": 0, "door_filter": "NZPG Vinyl", "length_cost": 0, "handling_cost": 0, "area_handling_cost": 0, "application_cost": 0, "is_deleted": false, "brand": {"id": 75, "name": "Dezignatek", "is_deleted": false}}, "carcaseEdgeMaterial": {"id": 4997, "manufacturer_id": 103, "brand_id": 50, "material_type_id": 2, "item_code": null, "name": "Prime White", "image": "Prime/Prime White.jpg", "finish": "Gloss", "thickness": 1, "door_filter": "NZPG Flat Panel", "length_cost": 0, "handling_cost": 0, "area_handling_cost": 0, "application_cost": 0, "is_deleted": false, "brand": {"id": 50, "name": "Prime", "is_deleted": false}}, "door": {"id": 344, "manufacturer_id": 103, "image_id": 1048, "suffix_id": 14, "name": "<PERSON><PERSON>", "filter_name": "NZPG Vinyl", "suffix_name": "DS1", "handling_cost": 0, "area_handling_cost": 0, "machining_cost": 0, "area_machining_cost": 0, "unit_cost": 0, "assembly_cost": 0, "area_assembly_cost": 0, "is_outsourced": true, "is_advanced": true, "is_deleted": false, "is_default_edge_type": false, "is_default_edge_type_locked": false, "default_brand_id": null, "default_material_id": null, "default_finish": null, "minimum_width": 160, "maximum_width": null, "minimum_height": 140, "maximum_height": null, "minimum_drawer_height": 80, "minimum_border_width_top": 0, "minimum_border_width_bottom": 0, "minimum_border_width_left": 0, "minimum_border_width_right": 0, "default_border_width_top": 58, "default_border_width_bottom": 58, "default_border_width_left": 58, "default_border_width_right": 58, "minimum_distance_top_bottom": null, "minimum_distance_left_right": null, "minimum_rails_horizontal_height": null, "maximum_rails_horizontal_height": null, "minimum_rails_vertical_width": null, "maximum_rails_vertical_width": null, "default_rails_horizontal_height": 58, "default_rails_vertical_width": 58, "default_edge_finish_top": 1, "default_edge_finish_bottom": 1, "default_edge_finish_left": 1, "default_edge_finish_right": 1, "default_edge_finish_join": 1, "is_default_edge_finish": true, "is_locked_edge_finish": false}, "hingeStyle": {"id": 21, "hidden_from_room_select": 0, "image": null, "name": "None"}, "drawerSystem": {"id": 6, "name": "<PERSON><PERSON>", "image": "uploads/images/if6122e72080ea4/Blum-Metabox.jpg", "drawerRunnerType": "Metabox", "hiddenFromRoom": 0}, "cabinetTop": {"id": 26, "name": "Rail On Flat", "visibleInRoom": 1, "image": "uploads/images/if611ee7f4be95a/Rail on Flat_white.jpg"}, "sizeDefaults": {"id": 58318, "customer_id": 48743, "user_level": 2, "name": "Standard 1", "base_depth": 560, "base_height": 720, "tall_depth": 590, "tall_height": 2100, "upper_depth": 300, "upper_height": 700}, "gapDefaults": {"id": 36215, "customer_id": 48743, "user_level": 2, "name": "Standard 2.0", "base_bottom_gap": 0, "base_top_gap": 3, "door_gap": 2, "drawer_gap": 2, "left_gap": 1.7, "right_gap": 1.7, "upper_bottom_gap": 0, "upper_top_gap": 0}}}}}