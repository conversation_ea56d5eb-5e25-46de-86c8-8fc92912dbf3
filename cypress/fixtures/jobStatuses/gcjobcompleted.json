{"data": [{"id": 300056, "customerId": 928, "clientId": -1, "status": 4, "name": "shaped shelves", "description": "", "endCustomerName": "", "dispatchMethod": 0, "address": "", "suburb": "", "state": null, "postcode": "", "endContactNumber": "", "customerPriceAdjust": 5, "customerMinCharge": 100, "supplierPriceAdjust": 0, "percentageCharged": 6, "variationCost": 0, "freightCost": 0, "dateEntered": "2023-03-22T10:49:52+00:00", "dateUpdated": "2023-03-22T11:36:05+00:00", "dateSubmitted": "2023-03-22T10:50:04+00:00", "dateAccepted": "2023-03-22T11:36:05+00:00", "dateDelivery": "2023-04-05T10:00:00+00:00", "datePaid": "0000-00-00T00:00:00+00:00", "dateEnteredDateTime": {"date": "2023-03-22 10:49:52.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateUpdatedDateTime": {"date": "2023-03-22 11:36:05.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateSubmittedDateTime": {"date": "2023-03-22 10:50:04.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateAcceptedDateTime": {"date": "2023-03-22 11:36:05.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateDeliveryDateTime": {"date": "2023-04-05 10:00:00.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "datePaidDateTime": null, "accepted": 1, "couponCodeData": null, "customerName": "<PERSON> (<PERSON>)", "manufacturerName": "Manufacturer 1", "supplierName": null, "MaxLayoutStatus": 0, "supplierId": null, "additionalManufacturerServices": [], "variationsConfirmed": 0, "variationsAllAccepted": 0, "addressState": null, "addressRegion": null, "countryTaxRateName": "GST", "taxDescription": "10% Government Service Tax", "taxRate": 10, "taxOnlyRate": 0.1, "taxInclusiveRate": 1.1, "currencyAbbreviation": "AUD", "countryName": "Australia", "currencyType": "$", "cost": 685.86, "roomCount": "1", "productCount": 2, "sundryItems": {}, "resetPriceStatus": false}, {"id": 298800, "customerId": 928, "clientId": -1, "status": 4, "name": "shaped shelves", "description": "", "endCustomerName": "", "dispatchMethod": 0, "address": "", "suburb": "", "state": null, "postcode": "", "endContactNumber": "", "customerPriceAdjust": 5, "customerMinCharge": 100, "supplierPriceAdjust": 0, "percentageCharged": 6, "variationCost": 0, "freightCost": 0, "dateEntered": "2023-03-15T08:39:35+00:00", "dateUpdated": "2023-03-22T10:48:45+00:00", "dateSubmitted": "2023-03-22T10:48:06+00:00", "dateAccepted": "2023-03-22T10:48:45+00:00", "dateDelivery": "2023-04-05T10:00:00+00:00", "datePaid": "0000-00-00T00:00:00+00:00", "dateEnteredDateTime": {"date": "2023-03-15 08:39:35.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateUpdatedDateTime": {"date": "2023-03-22 10:48:45.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateSubmittedDateTime": {"date": "2023-03-22 10:48:06.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateAcceptedDateTime": {"date": "2023-03-22 10:48:45.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateDeliveryDateTime": {"date": "2023-04-05 10:00:00.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "datePaidDateTime": null, "accepted": 1, "couponCodeData": null, "customerName": "<PERSON> (<PERSON>)", "manufacturerName": "Manufacturer 1", "supplierName": null, "MaxLayoutStatus": 0, "supplierId": null, "additionalManufacturerServices": [], "variationsConfirmed": 0, "variationsAllAccepted": 0, "addressState": null, "addressRegion": null, "countryTaxRateName": "GST", "taxDescription": "10% Government Service Tax", "taxRate": 10, "taxOnlyRate": 0.1, "taxInclusiveRate": 1.1, "currencyAbbreviation": "AUD", "countryName": "Australia", "currencyType": "$", "cost": 685.86, "roomCount": "1", "productCount": 2, "sundryItems": {}, "resetPriceStatus": false}], "total": 2, "success": 1}