{"data": [{"id": 349381, "customerId": 928, "clientId": 363, "status": 0, "name": "sdfadf", "description": "", "endCustomerName": "", "dispatchMethod": 0, "address": "", "suburb": "", "state": null, "postcode": "", "endContactNumber": "", "customerPriceAdjust": 5, "customerMinCharge": 100, "supplierPriceAdjust": 0, "percentageCharged": 0, "variationCost": 0, "freightCost": 0, "dateEntered": "2024-04-23T10:31:45+00:00", "dateUpdated": "2024-04-24T11:40:46+00:00", "dateSubmitted": "0000-00-00T00:00:00+00:00", "dateAccepted": "0000-00-00T00:00:00+00:00", "dateDelivery": "0000-00-00T00:00:00+00:00", "datePaid": "0000-00-00T00:00:00+00:00", "dateEnteredDateTime": {"date": "2024-04-23 10:31:45.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateUpdatedDateTime": {"date": "2024-04-24 11:40:46.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateSubmittedDateTime": null, "dateAcceptedDateTime": null, "dateDeliveryDateTime": null, "datePaidDateTime": null, "accepted": 0, "couponCodeData": null, "customerName": "<PERSON> (<PERSON>)", "manufacturerName": "Manufacturer 1", "supplierName": null, "MaxLayoutStatus": 0, "supplierId": null, "additionalManufacturerServices": [], "variationsConfirmed": 0, "variationsAllAccepted": 0, "addressState": null, "addressRegion": null, "countryTaxRateName": "GST", "taxDescription": "10% Government Service Tax", "taxRate": 10, "taxOnlyRate": 0.1, "taxInclusiveRate": 1.1, "currencyAbbreviation": "AUD", "countryName": "Australia", "currencyType": "$", "cost": 280.5, "roomCount": "1", "productCount": 1, "sundryItems": {}, "resetPriceStatus": false}, {"id": 349380, "customerId": 928, "clientId": -1, "status": 4, "name": "test", "description": "", "endCustomerName": "", "dispatchMethod": 0, "address": "", "suburb": "", "state": null, "postcode": "", "endContactNumber": "", "customerPriceAdjust": 5, "customerMinCharge": 100, "supplierPriceAdjust": 0, "percentageCharged": 0, "variationCost": 0, "freightCost": 0, "dateEntered": "2024-04-02T10:08:30+00:00", "dateUpdated": "2024-04-02T10:08:42+00:00", "dateSubmitted": "0000-00-00T00:00:00+00:00", "dateAccepted": "0000-00-00T00:00:00+00:00", "dateDelivery": "0000-00-00T00:00:00+00:00", "datePaid": "0000-00-00T00:00:00+00:00", "dateEnteredDateTime": {"date": "2024-04-02 10:08:30.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateUpdatedDateTime": {"date": "2024-04-02 10:08:42.000000", "timezone_type": 3, "timezone": "Australia/Canberra"}, "dateSubmittedDateTime": null, "dateAcceptedDateTime": null, "dateDeliveryDateTime": null, "datePaidDateTime": null, "accepted": 0, "couponCodeData": null, "customerName": "<PERSON> (<PERSON>)", "manufacturerName": "Manufacturer 1", "supplierName": null, "MaxLayoutStatus": 0, "supplierId": null, "additionalManufacturerServices": [], "variationsConfirmed": 0, "variationsAllAccepted": 0, "addressState": null, "addressRegion": null, "countryTaxRateName": "GST", "taxDescription": "10% Government Service Tax", "taxRate": 10, "taxOnlyRate": 0.1, "taxInclusiveRate": 1.1, "currencyAbbreviation": "AUD", "countryName": "Australia", "currencyType": "$", "cost": 299.29, "roomCount": "1", "productCount": 1, "sundryItems": {}, "resetPriceStatus": false}], "total": 2, "success": 1}