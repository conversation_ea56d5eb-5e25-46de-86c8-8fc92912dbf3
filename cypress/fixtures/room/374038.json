{"success": 1, "existing_quick_flat_products": [{"id": 3684800, "room_id": 374038, "gfp_product_id": -1, "room_cab_number": 1, "type": 352, "note": "", "comment": "", "variation_id": -1, "include_drawer_faces": 1, "variation_cost": 0, "cost": 57.8104415, "quantity": 1, "mirror": -1, "width": 550, "width2": 0, "height": 900, "depth": 590, "top": -1, "ladder_frames": 1, "carc_colour": 918, "carc_edge_colour": 1452, "ext_colour": 7125, "ext_edge_colour": 3158, "carc_min_job_area": 1, "ext_min_job_area": 2.88, "carc_material_used": 0, "ext_material_used": 0.495, "ext_hor_grain": 0, "carc_hor_grain": 0, "ext_double_sided": 0, "carc_double_sided": 0, "door_style": 7, "hinge_style": -1, "door_hinge_amount": 3, "drawer_gap": 3, "drawer_top": 3, "drawer_bottom": 0, "drawer_left": 1.5, "drawer_right": 1.5, "door_gap": 3, "door_top": 3, "door_bottom": 0, "door_left": 1.5, "door_right": 1.5, "carc_double_sided_cost": 0, "carc_handling_cost": 1.45, "carc_area_cost": 15.61, "carc_area_assembly_cost": 2.8, "ext_double_sided_cost": 0, "ext_handling_cost": 1.45, "ext_area_cost": 61.3, "ext_area_assembly_cost": 3.15, "carc_edge_handling_cost": 0, "carc_edge_length_cost": 0, "carc_edge_area_handling_cost": 0, "carc_edge_application_cost": 0, "ext_edge_handling_cost": 0.5, "ext_edge_length_cost": 1.5, "ext_edge_area_handling_cost": 1.5, "ext_edge_application_cost": 0.9, "material_prefix": "PS_16.5", "door_suffix": "", "extend_to_floor": 0, "toe_kick_height": 150, "applied_panel_depth": 610, "return_panel_width": 0, "upper_filler_depth": 610, "cover_void": -1, "void_width": 0, "length_depth": 590, "width_depth": 590, "length1": 300, "length2": 300, "width1": 300, "edge_l1": 1, "edge_l2": 1, "edge_w1": 1, "edge_w2": 1, "edge_inner": 1, "int_radius": 50, "ext_radius": 50, "drilling_cost": 0.07, "include_assembly": 0, "exclude_hardware": 1, "adjustable_legs": 1, "drawer_amount": 3, "drawer_face_type": 2, "drawer_face_heights": "298,298,298", "partition_height": 0, "partition_width": 0, "total_drawer_height": 720, "oven_check": 0, "microwave_check": 0, "oven_height": 600, "microwave_height": 600, "appliance_id": -1, "microwave_id": -1, "hori_shelf_amount": -1, "vert_shelf_amount": -1, "simple_shelves": 1, "carc_custom_colour": "", "ext_custom_colour": "", "upper_partition_height": 0, "lower_partition_height": 0, "door_suffix_id": null, "in_qfp_summary": 1, "finger_pull": null, "created_date": "0000-00-00 00:00:00", "created_by": "2__928__Cooper <PERSON>s (<PERSON>)", "modified_date": "2024-08-06 12:30:06", "modified_by": "2__928__Cooper <PERSON>s (<PERSON>)", "carc_material": {"id": "918", "item_code": "", "name": "Na", "finish": "Non Supply", "brand": 23, "substrate": "Na", "thickness": 16.5, "default_double_sided_cost": 1, "default_hor_grain_surcharge": null, "default_handling_cost": 1, "default_area_cost": 1, "default_area_assembly_cost": 1, "default_min_job_area": 0, "default_length": 10000, "default_width": 10000, "changed_item_code": "", "changed_hor_grain_surcharge": null, "changed_double_sided_cost": null, "changed_handling_cost": null, "changed_area_cost": null, "changed_area_assembly_cost": null, "changed_min_job_area": null, "changed_length": null, "changed_width": null, "prefix": "PS", "prefix_id": "1", "door_filter": "Non Supply", "image": "Non Supply/Na.jpg", "hor_grain": "0", "double_sided": "0", "manufacturers": "-1", "type": "7", "type_name": "Non Supply", "hidden_as_default": "0", "gfp_name": "DoorsNotSupplied", "gfp_brand_id": "7", "gfp_type_id": "1", "gfp_finish_id": "18", "gfp_brand_name": "goFlatpacks", "gfp_type_name": "Solid", "gfp_finish_name": "Includes hardware but no doors", "changed": false, "customer_changed": false, "hidden": false, "manufacturer_id": 6, "minimum_usage_rollover": "1", "expected_yield_factor": "100", "changed_minimum_usage_rollover": "1", "changed_expected_yield_factor": "100", "carc_hor_grain": null, "ext_hor_grain": null, "brand_name": "Non Supply"}, "carc_edge": {"id": 1452, "item_code": "", "name": "Na", "brand": 9, "finish": "Non Supply", "thickness": 0.01, "default_length_cost": 0, "default_handling_cost": 0, "default_area_handling_cost": 0, "default_application_cost": 0, "changed_item_code": "", "changed_handling_cost": null, "changed_length_cost": null, "changed_area_handling_cost": null, "changed_application_cost": null, "door_filter": "Non Supply", "image": "Non Supply/Na.jpg", "manufacturers": -1, "manufacturer_id": 6, "changed": false, "customer_changed": false, "hidden": false, "type": 7, "type_name": "Non Supply", "brand_name": "Non Supply"}, "ext_material": {"id": "7125", "item_code": null, "name": "Canterbury Grey", "finish": "SolidMatt", "brand": 3, "substrate": "MR MDF", "thickness": 16.5, "default_double_sided_cost": 0, "default_hor_grain_surcharge": null, "default_handling_cost": 1.45, "default_area_cost": 50.66, "default_area_assembly_cost": 3.15, "default_min_job_area": 2.88, "default_length": 2400, "default_width": 1200, "changed_item_code": null, "changed_hor_grain_surcharge": null, "changed_double_sided_cost": 0, "changed_handling_cost": 1.45, "changed_area_cost": 61.3, "changed_area_assembly_cost": 3.15, "changed_min_job_area": null, "changed_length": null, "changed_width": null, "prefix": "PS", "prefix_id": "1", "door_filter": "Flat Panel", "image": "Polytec/Canterbury Grey.jpg", "hor_grain": "0", "double_sided": "0", "manufacturers": "-1", "type": "2", "type_name": "Melamine", "hidden_as_default": "0", "gfp_name": "Canterbury Grey", "gfp_brand_id": "5", "gfp_type_id": "1", "gfp_finish_id": "17", "gfp_brand_name": "Polytec", "gfp_type_name": "Solid", "gfp_finish_name": "<PERSON>", "changed": true, "customer_changed": false, "hidden": false, "manufacturer_id": 6, "minimum_usage_rollover": "1", "expected_yield_factor": "100", "changed_minimum_usage_rollover": "90", "changed_expected_yield_factor": "90", "carc_hor_grain": null, "ext_hor_grain": null, "brand_name": "Polytec"}, "ext_edge": {"id": 3158, "item_code": "", "name": "Canterbury Grey", "brand": 3, "finish": "SolidMatt", "thickness": 1, "default_length_cost": 1.5, "default_handling_cost": 0.5, "default_area_handling_cost": 1.5, "default_application_cost": 0.9, "changed_item_code": "", "changed_handling_cost": null, "changed_length_cost": null, "changed_area_handling_cost": null, "changed_application_cost": null, "door_filter": "Flat Panel", "image": "Polytec/Canterbury Grey.jpg", "manufacturers": -1, "manufacturer_id": 6, "changed": false, "customer_changed": false, "hidden": false, "type": 2, "type_name": "Melamine", "brand_name": "Polytec"}, "door": {"default_edge_finish_top": 1, "default_edge_finish_bottom": 1, "default_edge_finish_left": 1, "default_edge_finish_right": 1, "default_edge_finish_join": 1, "set_as_default_edge_finish": 1, "lock_default_edge_finish": 0, "door_changed_id": 1, "item_code": null, "changed_item_code": null, "id": 7, "name": "Flat Panel", "outsourced": false, "filter_name": "Flat Panel", "default_handling_cost": 1.8, "default_area_handling_cost": 2.5, "default_machining_cost": 1.5, "default_area_machining_cost": 1.95, "default_unit_cost": 0, "default_assembly_cost": 2.2, "default_area_assembly_cost": 3.2, "changed_handling_cost": null, "changed_area_handling_cost": null, "changed_machining_cost": null, "changed_area_machining_cost": null, "changed_unit_cost": null, "changed_assembly_cost": null, "changed_area_assembly_cost": null, "suffix": "", "suffix_id": null, "advanced": 0, "image": 3053, "manufacturers": -1, "default_brand": null, "default_finish": null, "default_colour": null, "set_as_default_edge_type": false, "lock_default_edge_type": false, "minimum_height": 30, "minimum_height_drawer": null, "maximum_height": null, "minimum_width": 30, "maximum_width": null, "minimum_border_width_top": null, "minimum_border_width_bottom": null, "minimum_border_width_left": null, "minimum_border_width_right": null, "minimum_distance_top_bottom": null, "minimum_distance_left_right": null, "default_border_width_top": null, "default_border_width_bottom": null, "default_border_width_left": null, "default_border_width_right": null, "minimum_rails_horizontal_height": null, "maximum_rails_horizontal_height": null, "minimum_rails_vertical_width": null, "maximum_rails_vertical_width": null, "default_rails_horizontal_height": null, "default_rails_vertical_width": null, "changed": false, "customer_changed": false, "hidden": false, "manufacturer_id": 6, "brand": "", "style": "", "image_name": "Flat Panel.jpg"}, "doors": [{"id": 6949740, "job_cabinet_id": 3684804, "door_id": 7, "door_hang_type": 0, "door_hang": 2, "face_type": 1, "handling_cost": 1.8, "area_handling_cost": 2.5, "machining_cost": 1.5, "area_machining_cost": 1.95, "unit_cost": 0, "assembly_cost": 2.2, "area_assembly_cost": 3.2, "advanced": 0, "sub_panel1": 0, "sub_panel2": 0, "sub_panel3": 0, "sub_panel4": 0, "sub_panel5": 0, "sub_panel6": 0, "sub_panel7": 0, "sub_panel8": 0, "sub_panel9": 0, "no_vert_bars": 0, "no_hori_bars": 0, "bar_width": 60, "border_width_top": 60, "border_width_bottom": 60, "border_width_left": 60, "border_width_right": 60, "panel_edge_top": 1, "panel_edge_bottom": 1, "panel_edge_left": 1, "panel_edge_right": 1, "hori_height": 60, "hori_amount": 0, "vert_width": 60, "vert_amount": 0, "edge_costing": {"handling": 0, "length": 0, "area_handling": 0, "application": 0}, "door_costing": [], "outsourced_costing": {"type": "", "percent_markup": null, "cost": 0}, "have_edges": 0, "is_outsourced": false, "horizontal_positions": [], "vertical_positions": [], "material_price_breakdown": null, "have_door_drawer": 0, "have_hardware": 0, "have_materials": 0, "have_parts": 0, "door_name": "Flat Panel"}], "drillings": [], "hardware": [], "drawers": [], "shelves": [], "upper_shelves": [], "lower_shelves": [], "middle_shelves": [], "parts": [{"id": 18844887, "job_cabinet_id": 3684804, "part_id": 192, "handling_cost": 1.8, "area_handling_cost": 2.5, "machining_cost": 1.5, "area_machining_cost": 1.95, "assembly_cost": 3, "area_assembly_cost": 3, "edge_sizes": [], "have_edges": 0, "edge_costing": {"handling": 0, "length": 0, "area_handling": 0, "application": 0}, "material_price_breakdown": [], "outsourced_costing": {"type": "", "percent_markup": null, "cost": 0}, "unit_cost": 0, "is_outsourced": false, "total_edge_length": null}], "template_3d": [{"primary_key": "id", "attributes": {"id": 163, "cabinet_id": 352, "template": "Drawer.xml", "variables": "{\"fields\": [{\"name\": \"height\", \"expression\": \"cabinet_panel_length\"}, {\"name\": \"width\", \"expression\": \"cabinet_panel_width\"}]}", "created_date": "2024-07-03 07:12:18", "created_by": "migration_script", "modified_date": null, "modified_by": null}}], "job_cabinet_id": 3684804, "cabinet_type": 352, "cabinet_quantity": 1, "cabinet_note": "", "cabinet_comment": "", "cabinet_include_assembly": 0, "cabinet_include_hardware": 0, "cabinet_exclude_hardware": 1, "cabinet_adjustable_legs": 1, "cabinet_carc_colour": 918, "cabinet_carc_edge_colour": 1452, "cabinet_ext_colour": 7125, "cabinet_ext_edge_colour": 3158, "hor_grain_ext": 0, "hor_grain_carc": 0, "double_sided_ext": 0, "double_sided_carc": 0, "custom_colour_carc": "", "custom_colour_ext": "", "cabinet_door": 7, "cabinet_height": 900, "cabinet_width": 550, "cabinet_depth": 590, "cabinet_right_width": 550, "cabinet_left_width": 0, "cabinet_length_depth": 590, "cabinet_width_depth": 590, "cabinet_ladder_frames": 1, "cabinet_upper_filler_depth": 610, "cabinet_extend": 0, "cabinet_toekick": 150, "cabinet_top": -1, "cabinet_applied_panel_depth": 610, "cabinet_return_panel_width": 0, "cabinet_cover_void": -1, "cabinet_void_width": 0, "cabinet_total_drawer_height": 720, "microwave_advanced_checkbox": 0, "microwave_opening_height": 600, "cabinet_panel_length": 900, "cabinet_panel_width": 550, "cabinet_int_radius": 50, "cabinet_edge_inner": 1, "cabinet_ext_radius": 50, "cabinet_width1": 300, "cabinet_length1": 300, "cabinet_length2": 300, "drawer_face_height": ["298", "298", "298"], "cabinet_drawer_gap": 3, "cabinet_drawer_top": 3, "cabinet_drawer_bottom": 0, "cabinet_drawer_left": 1.5, "cabinet_drawer_right": 1.5, "cabinet_mirror": -1, "cabinet_edge_l1": 1, "cabinet_edge_l2": 1, "cabinet_edge_w1": 1, "cabinet_edge_w2": 1, "panel_edge_top": 1, "panel_edge_bottom": 1, "panel_edge_left": 1, "panel_edge_right": 1, "panel_edge_join": 1, "cabinet_door_gap": 3, "cabinet_door_bottom": 0, "cabinet_door_top": 3, "cabinet_door_left": 1.5, "cabinet_door_right": 1.5, "available_options": {"is_exterior_material_available": true, "is_carcase_material_available": true, "is_exterior_edge_material_available": true, "is_carcase_edge_material_available": true, "is_door_available": true, "is_cabinet_top_available": true}, "drawer_panel_edges": [{"drawer_edge_top": 0, "drawer_edge_bottom": 0}, {"drawer_edge_top": 0, "drawer_edge_bottom": 0}, {"drawer_edge_top": 0, "drawer_edge_bottom": 0}], "has_none_in_tops": true, "favourites": "0", "drawer_border_width_top": 60, "drawer_border_width_bottom": 60, "drawer_border_width_left": 60, "drawer_border_width_right": 60, "drawer_hori_height": 60, "drawer_panel_edge_top": 1, "drawer_panel_edge_bottom": 1, "drawer_panel_edge_left": 1, "drawer_panel_edge_right": 1, "drawer_panel_edge_join": 1}]}