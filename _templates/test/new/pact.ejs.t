---
to: tests/unit/<%= name %>.test.js
---
/**
 * @jest-environment jsdom
 */
import { pactWith } from "jest-pact";
import {Matchers} from '@pact-foundation/pact';
import {<%= service %>} from "service";
import axios from "axios";
import Axios from "service/Axios";
import {buildSuccessInteraction} from '../helpers';

pactWith(
    {
        cors: true,
        consumer: "goCabinets-customer-ui",
        provider: "goCabinets-customer",
    },
    (provider) => {
        describe("<%= service %> service test", () => {
            beforeEach(async () => {
                const interaction = buildSuccessInteraction({
                    description: "A request to get ????",
                    url: "????",
                    responseName: 'data',
                    response: Matchers.eachLike(????)
                });
                await provider.addInteraction(interaction);

                jest.spyOn(Axios, "instance").mockReturnValue(axios.create({
                    baseURL : provider.mockService.baseUrl + "/api"
                }));
            });

            it('', async () => {

            });
        });
    }
);
