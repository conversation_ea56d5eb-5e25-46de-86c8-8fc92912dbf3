---
to: tests/<%= name %>.test.js 
---
/**
 * @jest-environment jsdom
 */
import '@testing-library/jest-dom';
import React from 'react';
<% parts = name.split('/') %><% var component = parts[parts.length - 1] %>
import {<%= locals.hook ? hook : component %>} from <%- locals.hook ? "'hooks'" : locals.shared ? "'shared'" : "'components'" %>;

import {MemoryRouter} from 'react-router-dom';
import {render, screen} from '@testing-library/react';

describe('<%- locals.hook ? hook+'()': '<'+component+' />' %>', () => {
    it('', () => {

    });
});
