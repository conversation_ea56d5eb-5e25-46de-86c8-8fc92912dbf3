---
to: src/components/manufacturer/loaders/<%= name %>.tsx
---
import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import {store} from 'store/customer/storeSetup';
import <%= name %> from 'components/manufacturer/<%= name %>';

const <%= name.toLowerCase() %>Renderer = (element: HTMLElement) => {
    const root = createRoot(element);

    root.render(
        <Provider store={store}>
            <<%= name %> />
        </Provider>
    );
};

export default <%= name.toLowerCase() %>Renderer;
